import { StyleSheet, Dimensions } from "react-native";

const screenWidth = Dimensions.get("window").width * 1;

const eventStyles = StyleSheet.create({
  cardContainer: {
    marginTop: 20,
    width: "100%",
    alignSelf: "center",
  },
  cardPressable: {
    alignSelf: "center",
    width: "92%",
    borderRadius: 17,
    borderColor: "black",
    backgroundColor: "white",
    shadowColor: "grey",
    shadowRadius: 2,
    shadowOpacity: 1,
    shadowOffset: { height: 3 },
  },
  cardImage: {
    width: "100%",
    height: "100%",
    borderRadius: 17,
    overflow: "hidden",
    borderColor: "black",
    borderWidth: 1,
  },
  eventDetailsContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  gradientContainer: {
    flex: 0.23,
    paddingTop: 10,
    marginBottom: -1,
    flexDirection: "row",
  },
  eventInfoContainer: {
    flex: 1,
    alignItems: "center",
    width: "40%",
    marginLeft: 3,
    marginBottom: 0,
    bottom: -13,
  },
  eventTitleContainer: {
    alignSelf: "flex-start",
  },
  eventTitle: {
    fontSize: 13,
    fontWeight: "bold",
    marginLeft: 6,
    color: "white",
    maxWidth: "100%",
  },
  eventTimeContainer: {
    alignSelf: "flex-start",
  },
  eventTimeText: {
    fontSize: 13,
    marginLeft: 7,
    color: "white",
    fontWeight: "400",
    bottom: -3,
  },
  markerContainer: {
    height: "100%",
    width: "5%",
    marginTop: 10,
  },
  markerImage: {
    width: 35,
    height: 35,
    overflow: "visible",
  },
  addressContainer: {
    width: "35%",
    height: 67,
    justifyContent: "center",
    marginLeft: 5,
    paddingLeft: 12,
  },
  addressText: {
    color: "white",
    marginBottom: 5,
    maxWidth: "90%",
    fontWeight: "500",
    fontSize: 10,
    textAlign: "center",
  },
  loadingContainer: {
    height: screenWidth / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },
  userCountContainer: {
    position: "absolute",
    right: -3,
    borderTopRightRadius: 19,
    borderTopLeftRadius: 3,
    borderBottomRightRadius: 3,
    borderBottomLeftRadius: 36,
    shadowColor: "rgba(0,0,0,1)",
    shadowOffset: { width: 0, height: 0 },
  },
  userCountBadge: {
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: "rgba(2,2,2,0.6)",
    paddingLeft: 16,
    paddingRight: 10,
    borderTopRightRadius: 19,
    borderTopLeftRadius: 3,
    borderBottomRightRadius: 3,
    borderBottomLeftRadius: 36,
  },
  userCountText: {
    fontSize: 14,
    fontWeight: "700",
    color: "white",
  },
  creatorContainer: {
    position: "absolute",
    left: -3,
    borderTopLeftRadius: 19,
    borderTopRightRadius: 3,
    borderBottomLeftRadius: 3,
    borderBottomRightRadius: 36,
    shadowColor: "rgba(0,0,0,1)",
    shadowOffset: { width: 0, height: 0 },
  },
  creatorBadge: {
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: "rgba(2,2,2,0.6)",
    paddingLeft: 16,
    paddingRight: 10,
    borderTopLeftRadius: 19,
    borderTopRightRadius: 3,
    borderBottomLeftRadius: 3,
    borderBottomRightRadius: 36,
  },
  crownIcon: {
    alignSelf: "center",
    marginLeft: -6,
    marginTop: -2,
  },
});
export default eventStyles;