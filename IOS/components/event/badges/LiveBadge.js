import React from "react";
import { View, Text, Animated } from "react-native";

import { red } from "../../../screens/colors";

const LiveBadge = React.memo(
  ({ isVisible, opacityValue, opacityValue2, widthValue }) => {
    if (!isVisible) return null;

    return (
      <View>
        <Animated.View
          style={{
            borderRadius: 8,
            position: "absolute",
            marginTop: 7,
            alignSelf: "center",
            width: "40%",
            height: 18,
            backgroundColor: red,
            shadowColor: red,
            shadowOpacity: opacityValue2,
            shadowRadius: 5,
            shadowOffset: { width: 0, height: 0 },
            justifyContent: "center",
            opacity: opacityValue,
            transform: [
              {
                scaleX: widthValue.interpolate({
                  inputRange: [1, 1.087],
                  outputRange: [1, 1.087],
                }),
              },
            ],
          }}
        />
        <Text
          style={{
            position: "absolute",
            marginTop: 8,
            fontWeight: "800",
            alignSelf: "center",
            color: "white",
            fontSize: 13,
          }}
        >
          Live
        </Text>
      </View>
    );
  }
);
export default LiveBadge;
