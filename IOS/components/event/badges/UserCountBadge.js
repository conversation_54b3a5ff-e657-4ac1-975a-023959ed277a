import React from "react";
import { View, Text } from "react-native";

import { Ionicons } from "@expo/vector-icons";

import * as styles from "../eventStyles";

const UserCountBadge = React.memo(({ count }) => (
  <View style={styles.userCountContainer}>
    <View style={styles.userCountBadge}>
      <View style={{ flexDirection: "row" }}>
        <Text style={styles.userCountText}>{count}</Text>
        <Ionicons
          name="people"
          size={15}
          color="white"
          style={{ paddingHorizontal: 4, paddingTop: 1 }}
        />
      </View>
    </View>
  </View>
));
export default UserCountBadge;
