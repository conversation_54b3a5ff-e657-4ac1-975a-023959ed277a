import React from "react";
import { View, Dimensions } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as styles from "../eventStyles";

const CreatorBadge = React.memo(({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <View style={styles.creatorContainer}>
      <View style={styles.creatorBadge}>
        <MaterialCommunityIcons
          name="crown"
          size={22}
          color="#ffc005"
          style={styles.crownIcon}
        />
      </View>
    </View>
  );
});
export default CreatorBadge;
