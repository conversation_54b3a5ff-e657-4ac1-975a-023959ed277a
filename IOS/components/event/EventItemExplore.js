import React, { useEffect, useState, useRef, useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Pressable,
  Animated,
  Dimensions,
  AccessibilityInfo,
} from "react-native";

import { auth } from "../../config/firebase";
import { LinearGradient } from "expo-linear-gradient";
import FastImage from "react-native-fast-image";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { red } from "../../screens/colors";

const screenWidth = Dimensions.get("window").width;

// Constants for styling
const COLORS = {
  badge: "rgba(2,2,2,0.6)",
  white: "white",
  black: "black",
  shadow: "grey",
  transparent: "transparent",
};

// LiveBadge Component
const LiveBadge = React.memo(({ isVisible, animationValues }) => {
  if (!isVisible) return null;

  const { opacityValue, opacityValue2, widthValue } = animationValues;

  return (
    <View>
      <Animated.View
        style={[
          styles.liveBadge,
          {
            shadowOpacity: opacityValue2,
            opacity: opacityValue,
            transform: [
              {
                scaleX: widthValue.interpolate({
                  inputRange: [1, 1.087],
                  outputRange: [1, 1.087],
                }),
              },
            ],
          },
        ]}
        accessibilityLabel="Live event indicator"
      />
      <Text style={styles.liveText} accessibilityLabel="Live">
        Live
      </Text>
    </View>
  );
});

// UserCountBadge Component
const UserCountBadge = React.memo(({ count }) => (
  <View style={styles.userCountContainer}>
    <View style={styles.userCountBadge}>
      <View style={styles.userCountContent}>
        <Text style={styles.userCountText}>{count}</Text>
        <Ionicons
          name="people"
          size={15}
          color={COLORS.white}
          style={styles.userCountIcon}
        />
      </View>
    </View>
  </View>
));

// CreatorBadge Component
const CreatorBadge = React.memo(({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <View style={styles.creatorContainer}>
      <View style={styles.creatorBadge}>
        <MaterialCommunityIcons
          name="crown"
          size={22}
          color="#ffc005"
          style={styles.crownIcon}
        />
      </View>
    </View>
  );
});

// EventDetailsGradient Component
const EventDetailsGradient = React.memo(
  ({
    eventName,
    isLive,
    formattedEndTime,
    formattedEventTime,
    address,
    language,
  }) => (
    <View style={styles.eventDetailsContainer}>
      <LinearGradient
        colors={[
          "rgba(0,0,0,0)",
          "rgba(0,0,0,0.1)",
          "rgba(0,0,0,0.2)",
          "rgba(0,0,0,0.2)",
          "rgba(0,0,0,0.2)",
          "rgba(0,0,0,0.2)",
          "rgba(0,0,0,0.2)",
          "rgba(0,0,0,0.2)",
        ]}
        style={styles.gradientContainer}
      >
        <View style={styles.eventInfoContainer}>
          <View style={styles.eventTitleContainer}>
            <Text
              style={styles.eventTitle}
              numberOfLines={1}
              ellipsizeMode="tail"
              accessibilityLabel={`Event name: ${eventName}`}
            >
              {eventName}
            </Text>
          </View>
          <View style={styles.eventTimeContainer}>
            {isLive ? (
              <Text
                style={styles.eventTimeText}
                accessibilityLabel={`Ends at ${formattedEndTime.hours}:${formattedEndTime.minutes}`}
              >
                {language.endsAt} {formattedEndTime.hours}:
                {formattedEndTime.minutes}
              </Text>
            ) : (
              <Text
                style={styles.eventTimeText}
                accessibilityLabel={`Event time: ${formattedEventTime}`}
              >
                {formattedEventTime}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.markerContainer}>
          <FastImage
            style={styles.markerImage}
            source={require("./../../images/white-marker.png")}
          />
        </View>

        <View style={styles.addressContainer}>
          <Text
            style={styles.addressText}
            numberOfLines={3}
            ellipsizeMode="tail"
            accessibilityLabel={`Event location: ${address}`}
          >
            {address}
          </Text>
        </View>
      </LinearGradient>
    </View>
  )
);

// Main EventItemExplore Component
const EventItemExplore = React.memo(
  function EventItemExplore({ value, language, navigation }) {
    // Animation values
    const animationDriver = useRef(new Animated.Value(0)).current;
    const fadeInOpacity = useRef(new Animated.Value(0)).current;
    const scaleValue = useRef(new Animated.Value(0.95)).current;

    // Derived animated values
    const opacityValue = useMemo(() => {
      return animationDriver.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.95, 1, 0.95],
      });
    }, [animationDriver]);

    const opacityValue2 = useMemo(() => {
      return animationDriver.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [0.1, 0.7, 0.1],
      });
    }, [animationDriver]);

    const widthValue = useMemo(() => {
      return animationDriver.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [1, 1.087, 1],
      });
    }, [animationDriver]);

    // Component state
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    // Animation duration
    const animationDuration = 1250;

    // Start the animations
    useEffect(() => {
      // Live badge animation
      Animated.loop(
        Animated.timing(animationDriver, {
          toValue: 1,
          duration: animationDuration,
          useNativeDriver: true,
        })
      ).start();

      // Screen reader announcement for live events
      if (value.isLive) {
        AccessibilityInfo.announceForAccessibility(
          "This event is currently live"
        );
      }
    }, []);

    // Handle image load animation
    useEffect(() => {
      if (imageLoaded) {
        Animated.parallel([
          Animated.timing(fadeInOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(scaleValue, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }, [imageLoaded]);

    // Early return for incomplete data
    if (value.usersCount === undefined) {
      return <View style={{ height: 130 }} />;
    }

    // Format time functions
    const formatEndTime = () => {
      const endTime = new Date(value.timeEnd);
      return {
        hours: endTime.getHours().toString().padStart(2, "0"),
        minutes: endTime.getMinutes().toString().padStart(2, "0"),
      };
    };

    const formatEventTime = () => {
      const eventDate = new Date(value.date);
      const startTime = new Date(value.timeStart);
      const endTime = new Date(value.timeEnd);

      return `${eventDate.getDate()}. ${eventDate.getMonth() + 1}. ${(
        "0" + startTime.getHours()
      ).slice(-2)}:${("0" + startTime.getMinutes()).slice(-2)}-${(
        "0" + endTime.getHours()
      ).slice(-2)}:${("0" + endTime.getMinutes()).slice(
        -2
      )} ${language.weekdays[eventDate.getDay()].slice(0, 3)}.`;
    };

    // Computed values
    const formattedEndTime = formatEndTime();
    const formattedEventTime = formatEventTime();
    const isCreator = value.creator === auth.currentUser.uid;
    const imageSource = {
      uri: value.courtObj.imageRefs[0],
      // Add default failure handling
      priority: FastImage.priority.normal,
    };

    // Animation values for the LiveBadge
    const animationValues = {
      opacityValue,
      opacityValue2,
      widthValue,
    };

    return (
      <View key={value.eventID}>
        <Animated.View
          key={value.eventID + "-animated-view"}
          style={[
            styles.cardContainer,
            {
              opacity: fadeInOpacity,
              transform: [{ scale: scaleValue }],
            },
          ]}
        >
          <Pressable
            style={({ pressed }) => [
              styles.cardPressable,
              {
                height: imageLoaded ? screenWidth / 1.5 : screenWidth / 3,
                transform: [{ scale: pressed ? 0.99 : 1 }],
              },
            ]}
            onPress={() => {
              navigation.navigate("Game", {
                gameId: value.eventID,
              });
            }}
            accessible={true}
            accessibilityLabel={`${value.eventName} event${
              value.isLive ? ", currently live" : ""
            }`}
            accessibilityRole="button"
          >
            <FastImage
              onLoadEnd={() => setImageLoaded(true)}
              onError={() => {
                setImageError(true);
                setImageLoaded(true); // Ensure we still show something
              }}
              style={styles.cardImage}
              source={imageSource}
              fallback={imageError}
            >
              {/* Display badges */}
              <LiveBadge
                isVisible={value.isLive}
                animationValues={animationValues}
              />
              <UserCountBadge count={value.usersCount} />
              <CreatorBadge isVisible={isCreator} />

              {/* Display event details */}
              <EventDetailsGradient
                eventName={value.eventName}
                isLive={value.isLive}
                formattedEndTime={formattedEndTime}
                formattedEventTime={formattedEventTime}
                address={value.courtObj.address}
                language={language}
              />
            </FastImage>
          </Pressable>
        </Animated.View>

        {/* Loading indicator */}
        {!imageLoaded && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={red} />
          </View>
        )}
      </View>
    );
  },
  (prevProps, nextProps) => {
    // Enhanced memoization with more precise comparison
    if (prevProps.value === nextProps.value) return true;

    // If references are different, check key properties for potential optimization
    return (
      prevProps.value.eventID === nextProps.value.eventID &&
      prevProps.value.isLive === nextProps.value.isLive &&
      prevProps.value.usersCount === nextProps.value.usersCount &&
      prevProps.value.eventName === nextProps.value.eventName &&
      prevProps.value.timeStart === nextProps.value.timeStart &&
      prevProps.value.timeEnd === nextProps.value.timeEnd
    );
  }
);

// StyleSheet for all components
const styles = StyleSheet.create({
  // Card container styles
  cardContainer: {
    marginTop: 20,
    width: "100%",
    alignSelf: "center",
  },
  cardPressable: {
    alignSelf: "center",
    width: "92%",
    borderRadius: 17,
    borderColor: COLORS.black,
    backgroundColor: COLORS.white,
    shadowColor: COLORS.shadow,
    shadowRadius: 2,
    shadowOpacity: 1,
    shadowOffset: { height: 3 },
  },
  cardImage: {
    width: "100%",
    height: "100%",
    borderRadius: 17,
    overflow: "hidden",
    borderColor: COLORS.black,
    borderWidth: 1,
  },
  loadingContainer: {
    height: screenWidth / 1.5,
    justifyContent: "center",
    alignItems: "center",
  },

  // LiveBadge styles
  liveBadge: {
    borderRadius: 8,
    position: "absolute",
    marginTop: 7,
    alignSelf: "center",
    width: "40%",
    height: 18,
    backgroundColor: red,
    shadowColor: red,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 0 },
    justifyContent: "center",
  },
  liveText: {
    position: "absolute",
    marginTop: 8,
    fontWeight: "800",
    alignSelf: "center",
    color: COLORS.white,
    fontSize: 13,
  },

  // UserCountBadge styles
  userCountContainer: {
    position: "absolute",
    right: -3,
    borderTopRightRadius: 19,
    borderTopLeftRadius: 3,
    borderBottomRightRadius: 3,
    borderBottomLeftRadius: 36,
    shadowColor: "rgba(0,0,0,1)",
    shadowOffset: { width: 0, height: 0 },
  },
  userCountBadge: {
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: COLORS.badge,
    paddingLeft: 16,
    paddingRight: 10,
    borderTopRightRadius: 19,
    borderTopLeftRadius: 3,
    borderBottomRightRadius: 3,
    borderBottomLeftRadius: 36,
  },
  userCountContent: {
    flexDirection: "row",
  },
  userCountText: {
    fontSize: 14,
    fontWeight: "700",
    color: COLORS.white,
  },
  userCountIcon: {
    paddingHorizontal: 4,
    paddingTop: 1,
  },

  // CreatorBadge styles
  creatorContainer: {
    position: "absolute",
    left: -3,
    borderTopLeftRadius: 19,
    borderTopRightRadius: 3,
    borderBottomLeftRadius: 3,
    borderBottomRightRadius: 36,
    shadowColor: "rgba(0,0,0,1)",
    shadowOffset: { width: 0, height: 0 },
  },
  creatorBadge: {
    paddingTop: 4,
    paddingBottom: 4,
    backgroundColor: COLORS.badge,
    paddingLeft: 16,
    paddingRight: 10,
    borderTopLeftRadius: 19,
    borderTopRightRadius: 3,
    borderBottomLeftRadius: 3,
    borderBottomRightRadius: 36,
  },
  crownIcon: {
    alignSelf: "center",
    marginLeft: -6,
    marginTop: -2,
  },

  // EventDetailsGradient styles
  eventDetailsContainer: {
    flex: 1,
    justifyContent: "flex-end",
  },
  gradientContainer: {
    flex: 0.23,
    paddingTop: 10,
    marginBottom: -1,
    flexDirection: "row",
  },
  eventInfoContainer: {
    flex: 1,
    alignItems: "center",
    width: "40%",
    marginLeft: 3,
    marginBottom: 0,
    bottom: -13,
  },
  eventTitleContainer: {
    alignSelf: "flex-start",
  },
  eventTitle: {
    fontSize: 13,
    fontWeight: "bold",
    marginLeft: 6,
    color: COLORS.white,
    maxWidth: "100%",
  },
  eventTimeContainer: {
    alignSelf: "flex-start",
  },
  eventTimeText: {
    fontSize: 13,
    marginLeft: 7,
    color: COLORS.white,
    fontWeight: "400",
    bottom: -3,
  },
  markerContainer: {
    height: "100%",
    width: "5%",
    marginTop: 10,
  },
  markerImage: {
    width: 35,
    height: 35,
    overflow: "visible",
  },
  addressContainer: {
    width: "35%",
    height: 67,
    justifyContent: "center",
    marginLeft: 5,
    paddingLeft: 12,
  },
  addressText: {
    color: COLORS.white,
    marginBottom: 5,
    maxWidth: "90%",
    fontWeight: "500",
    fontSize: 10,
    textAlign: "center",
  },
});

export default EventItemExplore;
