// File: components/CustomSwitch.js (Fixed unified version)
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { red } from '../screens/colors';

/**
 * Unified CustomSwitch component that supports both Map and AddCourt usage
 * 
 * @param {Object} props - Component properties
 */
const CustomSwitch = (props) => {
  
  // Otherwise, render the AddCourt version with multiple selection options
  const {
    roundCorner = true,
    options = [{ label: 'Option 1', value: 1 }, { label: 'Option 2', value: 2 }],
    selectedValue,
    onValueChange = () => {},
    selectionColor = red,
  } = props;
  
  return (
    <View style={addCourtStyles.switchContainer}>
      <View
        style={[
          addCourtStyles.switchWrapper,
          { borderRadius: roundCorner ? 10 : 0, borderColor: selectionColor }
        ]}
      >
        {options.map((option, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={0.7}
            onPress={() => onValueChange(option.value)}
            style={[
              addCourtStyles.switchOption,
              {
                backgroundColor: selectedValue === option.value ? selectionColor : "white",
                borderRadius: roundCorner ? 8 : 0,
              }
            ]}
          >
            <Text
              style={{
                color: selectedValue === option.value ? "white" : selectionColor,
                fontWeight: "500",
                fontSize: 13,
                textAlign: "center",
              }}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

// Styles for the Map version
const mapStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 40,
    borderRadius: 20,
    backgroundColor: 'white',
    overflow: 'hidden',
    padding: 2,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  option: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    backgroundColor: 'white',
    marginHorizontal: 2,
    borderRadius: 18,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  }
});

// Styles for the AddCourt version
const addCourtStyles = StyleSheet.create({
  switchContainer: {
    alignItems: "center",
  },
  switchWrapper: {
    height: 40,
    width: "100%",
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "center",
    padding: 2,
  },
  switchOption: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
  }
});

export default CustomSwitch;