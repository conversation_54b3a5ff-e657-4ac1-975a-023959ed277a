// components/NavigationButton.js
import React, { useEffect, useRef } from "react";
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  Animated,
  View,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { red } from "../screens/colors";
import * as Haptics from "expo-haptics";

const NavigationButton = ({
  onPress,
  onBackPress,
  disabled = false,
  label = "Continue",
  showBackButton = false,
  isLoading = false,
}) => {
  // Animation ref for back button opacity
  const backButtonOpacity = useRef(
    new Animated.Value(showBackButton ? 1 : 0)
  ).current;
  // Animation ref for continue button left margin (instead of width)
  const continueButtonMargin = useRef(
    new Animated.Value(showBackButton ? 54 : 0)
  ).current;

  // Update animations when showBackButton changes
  useEffect(() => {
    Animated.parallel([
      Animated.timing(backButtonOpacity, {
        toValue: showBackButton ? 1 : 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(continueButtonMargin, {
        toValue: showBackButton ? 65 : 0,
        duration: 180,
        useNativeDriver: false,
      }),
    ]).start();
  }, [showBackButton]);

  return (
    <View style={styles.container}>
      {/* Back button that fades in/out */}
      <Animated.View
        style={[styles.backButtonContainer, { opacity: backButtonOpacity }]}
      >
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            onBackPress();
          }}
          activeOpacity={0.7}
          disabled={!showBackButton || isLoading}
        >
          <Ionicons name="arrow-back" size={24} color={"grey"} />
        </TouchableOpacity>
      </Animated.View>

      {/* Continue button with animated margin */}
      <Animated.View
        style={[
          styles.continueButtonContainer,
          { marginLeft: continueButtonMargin },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.button,
            disabled || isLoading
              ? styles.buttonDisabled
              : styles.buttonEnabled,
          ]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            onPress();
          }}
          disabled={disabled || isLoading}
          activeOpacity={0.7}
        >
          {isLoading ? (
            <ActivityIndicator color="white" size="small" />
          ) : (
            <Text
              style={[
                styles.buttonText,
                disabled ? styles.buttonTextDisabled : styles.buttonTextEnabled,
              ]}
            >
              {label}
            </Text>
          )}
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    marginBottom: 15,
    position: "relative",
    height: 48,
    backgroundColor: "transparent",
  },
  backButtonContainer: {
    width: 48,
    height: 48,
    position: "absolute",
    left: 20,
    zIndex: 2,
  },
  backButton: {
    width: 48,
    height: 48,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.03)",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    top: 0,
  },
  continueButtonContainer: {
    flex: 1,
    zIndex: 1,
  },
  button: {
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  buttonEnabled: {
    backgroundColor: red,
  },
  buttonDisabled: {
    backgroundColor: "#ccc",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  buttonTextEnabled: {
    color: "white",
  },
  buttonTextDisabled: {
    color: "rgba(255, 255, 255, 0.7)",
  },
});

export default NavigationButton;
