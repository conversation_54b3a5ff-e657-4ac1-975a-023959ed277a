// /components/notifications/FriendRequestNotification.js
import { memo } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import ProfileImage from "../../assets/ProfileImage";
import { red } from "../../screens/colors";
import { Ionicons } from "@expo/vector-icons";

export const FriendRequestNotification = memo(
  ({ notification, onAcceptRequest, onRemove, language }) => {
    const isAccepted = true;

    return (
      <View style={styles.container}>
        <ProfileImage
          profileImageRef={notification.sender.profileImageRef}
          profileColor={notification.sender.profileColor}
          name={notification.sender.name}
          size={85}
          style={styles.profileImage}
        />

        <View style={styles.contentContainer}>
          <Text style={styles.messageText}>
            <Text style={styles.senderName}>{notification.sender.name} </Text>
            <Text>{language.friendRequestMessage}</Text>
          </Text>

          {isAccepted ? (
            <View style={styles.statusIndicator}>
              <Text style={styles.statusText}>{language.accepted}</Text>
            </View>
          ) : (
            <View style={styles.buttonsContainer}>
              <TouchableOpacity
                onPress={() => onAcceptRequest(notification)}
                style={styles.acceptButton}
              >
                <Text style={styles.acceptButtonText}>{language.accept}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => onRemove(notification)}
                style={styles.removeButton}
              >
                <Text style={styles.removeButtonText}>{language.delete}</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    padding: 15,
    backgroundColor: "white",
    alignItems: "center",
  },
  profileImage: {
    marginRight: 15,
  },
  contentContainer: {
    flex: 1,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 5,
  },
  senderName: {
    fontWeight: "600",
  },
  buttonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  acceptButton: {
    backgroundColor: red,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    width: "48%",
    alignItems: "center",
  },
  acceptButtonText: {
    color: "white",
    fontWeight: "600",
  },
  removeButton: {
    backgroundColor: "#f1f1f1",
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    width: "48%",
    alignItems: "center",
  },
  removeButtonText: {
    color: "black",
    fontWeight: "500",
  },
  statusIndicator: {
    backgroundColor: "rgba(33, 150, 243, 0.1)", // Light blue background
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    alignSelf: "flex-start",
    marginTop: 5,
  },
  statusText: {
    fontSize: 14,
    color: "#1976D2", // Darker blue text
    fontWeight: "500",
  },
});
