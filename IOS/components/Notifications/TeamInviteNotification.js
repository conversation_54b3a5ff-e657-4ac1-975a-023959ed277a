// /components/notifications/TeamInviteNotification.js
import { memo } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import TeamProfileIcon from "../../assets/TeamProfileIcon";
import ProfileImage from "../../assets/ProfileImage";
import { red } from "../../screens/colors";
import { Ionicons } from "@expo/vector-icons";

export const TeamInviteNotification = memo(
  ({ notification, onAcceptRequest, onRemove, onDecline, language }) => {
    const isAccepted = notification.accepted;

    return (
      <View style={styles.container}>
        <View>
          <TeamProfileIcon team={notification.team} size={83} />
          <View style={styles.senderProfileContainer}>
            <ProfileImage
              profileImageRef={notification.sender.profileImageRef}
              profileColor={notification.sender.profileColor}
              name={notification.sender.name}
              size={35}
            />
          </View>
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.messageText}>
            <Text style={styles.boldText}>{notification.sender.name} </Text>
            <Text style={styles.normalText}>{language.from} </Text>
            <Text style={styles.boldText}>{notification.team.name} </Text>
            <Text>{language.teamInviteMessage}</Text>
          </Text>

          {isAccepted ? (
            <View style={styles.statusIndicator}>
              <Text style={styles.statusText}>{language.accepted}</Text>
            </View>
          ) : (
            <View style={styles.buttonsContainer}>
              <TouchableOpacity
                onPress={() => onAcceptRequest(notification)}
                style={styles.acceptButton}
              >
                <Text style={styles.acceptButtonText}>{language.join}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => onDecline(notification)}
                style={styles.declineButton}
              >
                <Text style={styles.declineButtonText}>{language.decline}</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    paddingLeft: 15,
    paddingVertical: 15,
    paddingRight: 10,
    backgroundColor: "white",
    alignItems: "center",
  },
  senderProfileContainer: {
    position: "absolute",
    backgroundColor: "white",
    width: 40,
    height: 40,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    left: -2,
    bottom: -2,
  },
  contentContainer: {
    flex: 1,
    marginLeft: 15,
    paddingRight: 5,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 5,
  },
  boldText: {
    fontWeight: "600",
  },
  teamNameText: {
    fontWeight: "600",
    color: red,
  },
  normalText: {
    fontWeight: "400",
  },
  buttonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 5,
  },
  acceptButton: {
    backgroundColor: red,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    width: "48%",
    alignItems: "center",
  },
  acceptButtonText: {
    color: "white",
    fontWeight: "600",
  },
  declineButton: {
    backgroundColor: "#f1f1f1",
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    width: "48%",
    alignItems: "center",
  },
  declineButtonText: {
    color: "black",
    fontWeight: "500",
  },
  statusIndicator: {
    backgroundColor: "rgba(33, 150, 243, 0.1)", // Light blue background
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 6,
    alignSelf: "flex-start",
    marginTop: 5,
  },
  statusText: {
    fontSize: 14,
    color: "#1976D2", // Darker blue text
    fontWeight: "500",
  },
});
