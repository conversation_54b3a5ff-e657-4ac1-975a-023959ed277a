// ErrorScreen.js
import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Dimensions,
  Animated,
  Pressable,
  ScrollView,
} from "react-native";
import { AntDesign, Feather } from "@expo/vector-icons";
import { red } from "../screens/colors";
import FastImage from "react-native-fast-image";
import * as Haptics from "expo-haptics";

// Get screen dimensions
const { width, height } = Dimensions.get("window");
const statusBarHeight = Platform.OS === "ios" ? 44 : 0;

const ErrorScreen = ({ error, resetError, language }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [detailsHeight] = useState(new Animated.Value(0));

  // Use provided language or fall back to English
  const errorLang = language || EN;

  const toggleDetails = () => {
    if (Platform.OS === "ios") {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    const toValue = showDetails ? 0 : 200;

    Animated.timing(detailsHeight, {
      toValue,
      duration: 250,
      useNativeDriver: false,
    }).start();

    setShowDetails(!showDetails);
  };

  return (
    <View style={styles.container}>
      {/* Background gradient elements */}
      {/* <View style={[styles.gradientCircle, styles.redCircle]} />
      <View style={[styles.gradientCircle, styles.orangeCircle]} /> */}

      <FastImage
        style={styles.hoopImage}
        source={require("../images/error-hoop9.png")}
      />

      <Text style={styles.title}>{errorLang.errorTitle}</Text>

      <Text style={styles.message}>{errorLang.errorMessage}</Text>

      {/* Technical details section */}
      <Pressable onPress={toggleDetails} style={styles.detailsButton}>
        <Text style={styles.detailsButtonText}>
          {showDetails ? errorLang.hideDetails : errorLang.showDetails}
        </Text>
        <Feather
          name={showDetails ? "chevron-up" : "chevron-down"}
          size={16}
          color="#999"
          style={{ marginLeft: 5 }}
        />
      </Pressable>

      <Animated.View
        style={[styles.errorDetailsContainer, { height: detailsHeight }]}
      >
        <ScrollView style={styles.errorScrollView}>
          <Text style={styles.errorDetailsText}>
            {error?.toString() || errorLang.unknownError}
          </Text>
        </ScrollView>
      </Animated.View>

      {/* Restart button */}
      <TouchableOpacity
        style={styles.restartButton}
        onPress={() => {
          if (Platform.OS === "ios") {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
          resetError();
        }}
        activeOpacity={0.8}
      >
        <AntDesign
          name="reload1"
          size={18}
          color="white"
          style={styles.buttonIcon}
        />
        <Text style={styles.restartButtonText}>{errorLang.restartApp}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgb(240,240,240)",
    padding: 25,
    paddingTop: statusBarHeight + 20,
    position: "relative",
    overflow: "hidden",
  },
  gradientCircle: {
    position: "absolute",
    borderRadius: 300,
    opacity: 0.15,
  },
  redCircle: {
    backgroundColor: red,
    width: width * 0.9,
    height: width * 0.9,
    top: -width * 0.25,
    right: -width * 0.25,
  },
  orangeCircle: {
    backgroundColor: "#FF9800",
    width: width * 0.7,
    height: width * 0.7,
    bottom: -width * 0.2,
    left: -width * 0.2,
  },
  hoopImage: {
    width: width * 0.7,
    aspectRatio: 1,
    // height: height * 0.3,
    resizeMode: "contain",
    marginBottom: 20,
  },
  title: {
    fontSize: 26,
    fontWeight: "bold",
    color: "#333",
    marginTop: 10,
    marginBottom: 15,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    color: "#555",
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 30,
    maxWidth: "90%",
  },
  detailsButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    borderRadius: 8,
    backgroundColor: "rgba(0,0,0,0.03)",
    marginBottom: 10,
  },
  detailsButtonText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  errorDetailsContainer: {
    overflow: "hidden",
    width: "100%",
    marginBottom: 20,
  },
  errorScrollView: {
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 10,
    padding: 15,
  },
  errorDetailsText: {
    fontSize: 13,
    color: "#666",
    fontFamily: Platform.OS === "ios" ? "Menlo" : "monospace",
  },
  restartButton: {
    backgroundColor: red,
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    minWidth: width * 0.6,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 5,
    marginTop: 15,
  },
  buttonIcon: {
    marginRight: 10,
  },
  restartButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ErrorScreen;
