// components/SetupHeader.js
import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useSetupProfile } from "../screens/SetupProfileContext";
import LanguageToggle from "./LanguageToggle";
import { EN, CZ } from "./../assets/strings";

const SetupHeader = () => {
  const insets = useSafeAreaInsets();
  const { languageCode, setLanguageCode, currentStep } = useSetupProfile();
  const languageStrings = languageCode === "CZ" ? CZ : EN;

  const getHeaderTitle = () => {
    switch (currentStep) {
      case 0:
        return languageStrings.setupProfile;
      case 1:
        return languageStrings.basketballExperienceTitle;
      case 2:
        return languageStrings.setupProfileLocation;
      default:
        return languageStrings.setupProfile;
    }
  };

  return (
    <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
      <Text style={styles.headerTitle}>{getHeaderTitle()}</Text>
      <LanguageToggle language={languageCode} setLanguage={setLanguageCode} />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingBottom: 10,
    backgroundColor: "white",
    // borderBottomWidth: 1,
    // borderBottomColor: "rgba(0,0,0,0.05)",
  },
  headerTitle: {
    fontSize: 21,
    fontWeight: "700",
    color: "#333",
  },
});

export default SetupHeader;
