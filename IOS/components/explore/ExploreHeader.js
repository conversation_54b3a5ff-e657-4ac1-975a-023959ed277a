// components/explore/ExploreHeader.js
import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const ExploreHeader = ({
  games,
  hasMoreGames,
  loading,
  displayRadius,
  language,
  onShowMapPreview,
  onFilterPress,
  accentColor,
}) => {
  return (
    <View style={styles.headerWrapper}>
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>
          {language?.findGamesNearby || "Discover Streetball Games Nearby"}
        </Text>

        <Text style={styles.gamesCountLabel}>
          {games.length > 0
            ? `${games.length}${hasMoreGames ? "+" : ""} ${
                games.length === 1 && !hasMoreGames
                  ? language?.gameFound || "game found"
                  : language?.gamesFound || "games found"
              }`
            : loading
            ? language?.searchingGames || "Searching for games..."
            : language?.noGamesAvailable || "No games available"}
        </Text>
      </View>

      <View style={styles.filterContainer}>
        <Pressable
          style={styles.radiusInfoContainer}
          onPress={onShowMapPreview}
        >
          <Ionicons
            name="location"
            size={30}
            color={accentColor}
            style={styles.radiusIcon}
          />
          <View style={styles.radiusTextContainer}>
            <Text style={styles.radiusValue}>
              {Math.round(displayRadius / 1000)}
              <Text style={styles.radiusUnit}> {language?.km || "km"}</Text>
            </Text>
            <Text style={styles.radiusLabel}>
              {language?.searchRadius || "Search radius"}
            </Text>
          </View>
        </Pressable>

        <Pressable
          style={({ pressed }) => [
            styles.filterButton,
            pressed && styles.filterButtonPressed,
            { borderColor: accentColor },
          ]}
          onPress={onFilterPress}
          accessibilityLabel={
            language?.adjustSearchRadius || "Adjust search radius"
          }
          accessibilityRole="button"
        >
          <Ionicons
            name="options-outline"
            size={20}
            color={accentColor}
            style={styles.filterIcon}
          />
          <Text style={[styles.filterButtonText, { color: accentColor }]}>
            {language?.filter || "Filter"}
          </Text>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerWrapper: {
    backgroundColor: "#FFFFFF",
    paddingTop: 12,
    paddingBottom: 8,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  headerContainer: {
    marginBottom: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#111827",
    textAlign: "left",
    marginBottom: 4,
  },
  gamesCountLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: "#6B7280",
    textAlign: "left",
    marginBottom: 6,
  },
  filterContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#f8f8f8",
    borderRadius: 14,
    paddingHorizontal: 18,
    paddingVertical: 14,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  radiusInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  radiusIcon: {
    marginRight: 12,
  },
  radiusTextContainer: {
    flexDirection: "column",
    flex: 1,
  },
  radiusValue: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333",
  },
  radiusUnit: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  radiusLabel: {
    fontSize: 12,
    color: "rgba(0,0,0,0.5)",
    marginTop: 2,
  },
  mapPreviewIcon: {
    marginLeft: "auto",
    opacity: 0.7,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ffffff",
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 16,
    paddingVertical: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  filterButtonPressed: {
    opacity: 0.7,
    backgroundColor: "rgba(197, 42, 71, 0.05)",
  },
  filterIcon: {
    marginRight: 6,
  },
  filterButtonText: {
    fontWeight: "600",
    fontSize: 14,
  },
});

export default ExploreHeader;
