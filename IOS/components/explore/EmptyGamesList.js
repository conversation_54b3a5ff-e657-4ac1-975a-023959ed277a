// components/explore/EmptyGamesList.js
import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";

const EmptyGamesList = ({ error, language }) => {
  return (
    <View style={styles.emptyContainer}>
      <Ionicons name="sad-outline" size={80} color="rgba(0,0,0,0.2)" />
      <Text style={styles.emptyText}>
        {error
          ? language?.errorLoadingGames || "Error loading games"
          : language?.noGamesFound || "No games found"}
      </Text>
      <Text style={styles.emptySubtext}>
        {error
          ? language?.pleaseTryAgainLater || "Please try again later"
          : language?.tryChangingFilters || "Try changing your filters"}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    marginTop: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "600",
    color: "rgba(0,0,0,0.7)",
    marginTop: 20,
    textAlign: "center",
  },
  emptySubtext: {
    fontSize: 14,
    color: "rgba(0,0,0,0.5)",
    marginTop: 10,
    textAlign: "center",
  },
});

export default EmptyGamesList;