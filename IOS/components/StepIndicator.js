// components/StepIndicator.js
import React from "react";
import { View, StyleSheet, Animated } from "react-native";
import { red } from "../screens/colors";

const StepIndicator = ({ currentStep, totalSteps }) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: totalSteps }).map((_, index) => (
        <View
          key={index}
          style={[
            styles.dot,
            currentStep === index ? styles.activeDot : styles.inactiveDot,
            index === 0 ? styles.firstDot : null,
            index === totalSteps - 1 ? styles.lastDot : null,
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    // marginVertical: 20,
    // marginTop: 10,
  },
  dot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginHorizontal: 5,
  },
  activeDot: {
    backgroundColor: red,
    width: 10,
    height: 10,
  },
  inactiveDot: {
    backgroundColor: "rgba(0,0,0,0.2)",
    width: 8,
    height: 8,
  },
  firstDot: {
    marginLeft: 0,
  },
  lastDot: {
    marginRight: 0,
  },
});

export default StepIndicator;
