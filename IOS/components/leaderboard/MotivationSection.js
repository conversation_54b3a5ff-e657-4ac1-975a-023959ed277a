// File: components/leaderboard/MotivationSection.js
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { red } from '../../screens/colors';

/**
 * Motivation section component for encouraging court contributions
 */
const MotivationSection = ({ onAddCourt, language }) => {
  return (
    <View style={styles.motivationContainer}>
      <Text style={styles.motivationText}>
        {language.addCourtMotivation || "Add courts to your area to climb the leaderboard!"}
      </Text>
      
      <TouchableOpacity 
        style={styles.addCourtButton}
        onPress={onAddCourt}
      >
        <Text style={styles.addCourtButtonText}>
          {language.addCourt || "Add Court"}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  motivationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  motivationText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    marginRight: 12,
  },
  addCourtButton: {
    backgroundColor: red,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addCourtButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default React.memo(MotivationSection);