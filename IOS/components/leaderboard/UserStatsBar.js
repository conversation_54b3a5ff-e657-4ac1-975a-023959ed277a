// File: components/leaderboard/UserStatsBar.js
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { red } from '../../screens/colors';

/**
 * Compact user stats bar for leaderboard
 * Shows user contributions and rank
 */
const UserStatsBar = ({ userContributions, userRank, language }) => {
  return (
    <View style={styles.userStatsContainer}>
      <View style={styles.statColumn}>
        <Text style={styles.statValue}>{userContributions}</Text>
        <Text style={styles.statLabel}>
          {language.myContributions || "My Contributions"}
        </Text>
      </View>
      
      <View style={styles.statDivider} />
      
      <View style={styles.statColumn}>
        <Text style={styles.statValue}>
          {userRank ? `#${userRank}` : '-'}
        </Text>
        <Text style={styles.statLabel}>
          {language.myRank || "My Rank"}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  userStatsContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    marginTop: 4,
    marginBottom: 4,
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statColumn: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 22,
    fontWeight: 'bold',
    color: red,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(0,0,0,0.1)',
  },
});

export default React.memo(UserStatsBar);