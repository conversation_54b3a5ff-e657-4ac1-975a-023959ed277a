// File: components/leaderboard/LeaderboardPodium.js
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { red } from '../../screens/colors';

/**
 * Podium component for displaying top 3 contributors
 * 
 * @param {Array} podiumUsers - Array of top 3 contributors
 * @param {Object} language - Language strings
 */
const LeaderboardPodium = ({ podiumUsers, language }) => {
  const renderPodiumPosition = (user, position) => {
    if (!user) return <View style={styles.emptyPodiumSpot} />;
    
    // Podium colors for positions
    const colors = {
      1: '#FFD700', // Gold
      2: '#C0C0C0', // Silver
      3: '#CD7F32'  // Bronze
    };
    
    // Position 1 is in the middle (tallest), position 2 is left, position 3 is right
    return (
      <View style={[
        styles.podiumSpot,
        { backgroundColor: colors[position] },
        position === 1 ? styles.firstPlace : null
      ]}>
        <Text style={styles.positionNumber}>{position}</Text>
        <Text style={styles.podiumUsername} numberOfLines={1}>
          {user.username || user.email || "Anonymous"}
        </Text>
        <View style={styles.countContainer}>
          <FontAwesome5 name="basketball-ball" size={12} color="#333" />
          <Text style={styles.countText}>{user.count}</Text>
        </View>
      </View>
    );
  };
  
  return (
    <View style={styles.podiumContainer}>
      <Text style={styles.podiumTitle}>
        {language.topContributors || "Top Contributors"}
      </Text>
      
      <View style={styles.podiumRow}>
        <View style={styles.podiumCol}>
          {podiumUsers[1] && renderPodiumPosition(podiumUsers[1], 2)}
        </View>
        <View style={styles.podiumCol}>
          {podiumUsers[0] && renderPodiumPosition(podiumUsers[0], 1)}
        </View>
        <View style={styles.podiumCol}>
          {podiumUsers[2] && renderPodiumPosition(podiumUsers[2], 3)}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  podiumContainer: {
    backgroundColor: 'white',
    padding: 12,
    paddingTop: 16,
    paddingBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  podiumTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333',
    marginTop: 4,
  },
  podiumRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginBottom: 8,
    marginTop: 8,
  },
  podiumCol: {
    width: '33%',
    alignItems: 'center',
  },
  podiumSpot: {
    width: 85,
    height: 85,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 6,
    padding: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  firstPlace: {
    height: 100,
    marginBottom: 10,
  },
  emptyPodiumSpot: {
    width: 90,
    height: 90,
    marginHorizontal: 8,
  },
  positionNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  podiumUsername: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginTop: 4,
    textAlign: 'center',
  },
  countContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  countText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});

export default React.memo(LeaderboardPodium);