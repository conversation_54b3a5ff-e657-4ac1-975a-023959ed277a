// File: components/leaderboard/LeaderboardContributorsList.js
import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  ActivityIndicator, 
  TouchableOpacity,
  Dimensions 
} from 'react-native';
import { FontAwesome5, AntDesign } from '@expo/vector-icons';
import { red } from '../../screens/colors';

const { height } = Dimensions.get('window');

/**
 * Scrollable list component for contributors below the podium
 * With pagination functionality
 */
const LeaderboardContributorsList = ({ 
  contributors, 
  isLoadingMore, 
  hasMore, 
  onLoadMore,
  language 
}) => {
  const listRef = useRef(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  
  // Calculate whether list is empty
  const isEmpty = contributors.length === 0;

  // Handle end reached (pagination)
  const handleEndReached = () => {
    console.log("End reached, hasMore:", hasMore, "isLoadingMore:", isLoadingMore);
    if (hasMore && !isLoadingMore) {
      onLoadMore();
    }
  };
  
  // Handle scroll events to show/hide scroll to top button
  const handleScroll = (event) => {
    const scrollPosition = event.nativeEvent.contentOffset.y;
    setShowScrollTop(scrollPosition > 100);
  };
  
  // Render a contributor in the list
  const renderContributor = ({ item, index }) => {
    // Position is index + 4 since this list starts after podium (top 3)
    const position = index + 4;
    
    return (
      <View style={styles.listItem}>
        <View style={styles.rankContainer}>
          <Text style={styles.rankText}>{position}</Text>
        </View>
        
        <Text style={styles.usernameText} numberOfLines={1}>
          {item.username || item.email || "Anonymous"}
        </Text>
        
        <View style={styles.courtsContainer}>
          <FontAwesome5 name="basketball-ball" size={12} color="#333" />
          <Text style={styles.courtsCount}>{item.count}</Text>
        </View>
      </View>
    );
  };
  
  // Render footer with loading indicator or end of list message
  const renderFooter = () => {
    if (isLoadingMore) {
      return (
        <View style={styles.loadMoreIndicator}>
          <ActivityIndicator size="small" color={red} />
          <Text style={styles.loadMoreText}>
            {language.loadingMore || "Loading more..."}
          </Text>
        </View>
      );
    }
    
    if (!hasMore && contributors.length > 0) {
      return (
        <Text style={styles.endOfListText}>
          {language.endOfList || "End of list"}
        </Text>
      );
    }
    
    return null;
  };
  
  // Render empty list placeholder
  const renderEmpty = () => {
    if (isEmpty && !isLoadingMore) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {language.noOtherContributors || "No other contributors yet"}
          </Text>
        </View>
      );
    }
    return null;
  };
  
  return (
    <View style={styles.listContainer}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>
          {language.otherTopContributors || "Other Top Contributors"}
        </Text>
        
        {isLoadingMore && (
          <ActivityIndicator size="small" color={red} style={styles.miniLoader} />
        )}
      </View>
      
      {/* FlatList for better performance and easier pagination */}
      <FlatList
        ref={listRef}
        data={contributors}
        keyExtractor={(item, index) => `contributor-${item.uid || index}`}
        renderItem={renderContributor}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.list}
        contentContainerStyle={[
          styles.listContentContainer,
          isEmpty && styles.emptyListContent
        ]}
        showsVerticalScrollIndicator={true}
        initialNumToRender={10}
      />
      
      {/* Scroll to top button */}
      {showScrollTop && (
        <TouchableOpacity 
          style={styles.scrollToTopButton}
          onPress={() => {
            if (listRef.current) {
              listRef.current.scrollToOffset({ offset: 0, animated: true });
            }
          }}
        >
          <AntDesign name="arrowup" size={20} color="white" />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    flex: 1,
    backgroundColor: 'white',
    margin: 16,
    marginTop: 8,
    marginBottom: 8,
    borderRadius: 12,
    padding: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  listTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  miniLoader: {
    marginRight: 8,
  },
  list: {
    flex: 1,
    maxHeight: 300, // Fixed height to ensure it's scrollable
  },
  listContentContainer: {
    paddingBottom: 8,
  },
  emptyListContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 120,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  rankContainer: {
    width: 30,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  usernameText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    color: '#333',
  },
  courtsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  courtsCount: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    color: '#333',
  },
  loadMoreIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
  },
  loadMoreText: {
    marginLeft: 8,
    color: '#666',
    fontSize: 14,
  },
  endOfListText: {
    textAlign: 'center',
    color: '#666',
    paddingVertical: 12,
    fontStyle: 'italic',
  },
  scrollToTopButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: red,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  }
});

export default React.memo(LeaderboardContributorsList);