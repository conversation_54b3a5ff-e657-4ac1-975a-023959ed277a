import React, { useState, useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';

/**
 * A customizable switch component for toggling between two options
 * 
 * @param {Object} props - Component properties
 * @param {Object} props.navigation - Navigation object
 * @param {number} props.selectionMode - Initial selection mode
 * @param {boolean} props.roundCorner - Whether corners should be rounded
 * @param {string} props.option1 - Text for first option
 * @param {string} props.option2 - Text for second option (option3 in original)
 * @param {string} props.selectionColor - Color for selected option
 */
const CustomSwitch = React.memo(({
  navigation,
  selectionMode,
  roundCorner = true,
  option1,
  option2,
  selectionColor = "#FF0000",
}) => {
  // State for tracking selected option
  const [selectedRestrictionType, setSelectedRestrictionType] = useState(selectionMode || 1);

  // Function to handle option selection
  const handleOptionSelect = (value) => {
    setSelectedRestrictionType(value);
    // If you need to add onSelectSwitch callback in future, you can add it here:
    // if (onSelectSwitch) onSelectSwitch(value);
  };

  return (
    <View style={styles.container}>
      <View style={[
        styles.switchContainer,
        {
          borderRadius: roundCorner ? 25 : 0,
          borderColor: selectionColor
        }
      ]}>
        {/* First Option */}
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => handleOptionSelect(1)}
          style={[
            styles.option,
            {
              backgroundColor: selectedRestrictionType === 1 ? selectionColor : "white",
              borderRadius: roundCorner ? 25 : 0,
            }
          ]}
        >
          <Text style={{
            color: selectedRestrictionType === 1 ? "white" : selectionColor,
          }}>
            {option1}
          </Text>
        </TouchableOpacity>

        {/* Second Option (was option3 in original code) */}
        <TouchableOpacity
          activeOpacity={0.7}
          onPress={() => handleOptionSelect(3)}
          style={[
            styles.option,
            {
              backgroundColor: selectedRestrictionType === 3 ? selectionColor : "white",
              borderRadius: roundCorner ? 25 : 0,
            }
          ]}
        >
          <Text style={{
            color: selectedRestrictionType === 3 ? "white" : selectionColor,
          }}>
            {option2}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  switchContainer: {
    height: 44,
    width: "100%",
    backgroundColor: "white",
    borderWidth: 0.5,
    flexDirection: "row",
    justifyContent: "center",
    padding: 2,
  },
  option: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  }
});

export default CustomSwitch;