// EnhancedCourtFilterModal.js
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { ChipFilterGroup } from "./CompactFilterPicker";
import { red } from "../screens/colors";
import { getFilterOptions, getFilterCategories } from "./filterOptions";

const EnhancedCourtFilterModal = ({ 
  visible = false, 
  onClose = () => {}, 
  onApplyFilter = () => {}, 
  appliedFilters = null, 
  language = {} 
}) => {
  // Accordion state
  const [expandedSection, setExpandedSection] = useState("basic");
  
  // Get filter definitions and organize them by category
  const filterOptions = getFilterOptions(language);
  const filterCategories = getFilterCategories();
  
  // Initialize filter state using useReducer pattern approach
  const [filterValues, setFilterValues] = useState({
    // Basic filters
    surfaceType: 1,
    indoorOutdoor: 1,
    publicPrivate: 1,
    
    // Court features
    fullCourt: 1,
    lighting: 1,
    lines: 1,
    baskets: 1,
    maintenance: 1,
    
    // Amenities
    parking: 1,
    seating: 1,
    wheelchairAccessible: 1,
    restrooms: 1,
    waterFountain: 1,
  });
  
  // Helper to update a single filter value
  const updateFilter = (key, value) => {
    setFilterValues(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  // Reset all filters to default
  const resetFilters = () => {
    // Create an object with all filter keys set to 1 ("All")
    const resetValues = Object.keys(filterValues).reduce((acc, key) => {
      acc[key] = 1;
      return acc;
    }, {});
    
    setFilterValues(resetValues);
    onApplyFilter(null);
  };

  // Sync with external filter changes
  useEffect(() => {
    if (!appliedFilters) {
      resetFilters();
      return;
    }
    
    // Helper to find value by label
    const findValueByLabel = (optionsArray, label) => {
      if (!label) return 1;
      const option = optionsArray.find(opt => 
        String(opt.label).toLowerCase() === String(label).toLowerCase()
      );
      return option ? option.value : 1;
    };
    
    // Create updated filter state
    const newFilterValues = { ...filterValues };
    
    // Update each filter if it exists in appliedFilters
    Object.entries(appliedFilters).forEach(([key, label]) => {
      const optionsForKey = filterOptions[key];
      if (optionsForKey) {
        newFilterValues[key] = findValueByLabel(optionsForKey, label);
      }
    });
    
    setFilterValues(newFilterValues);
  }, [appliedFilters]);

  // Apply filters and close modal
  const applyFilter = () => {
    // Convert values to labels
    const filterLabels = {};
    
    Object.entries(filterValues).forEach(([key, value]) => {
      if (value !== 1 && filterOptions[key]) { // Skip "All" values
        const option = filterOptions[key].find(opt => opt.value === value);
        if (option) filterLabels[key] = option.label;
      }
    });
    
    // Only apply if there are active filters
    const hasActiveFilters = Object.keys(filterLabels).length > 0;
    onApplyFilter(hasActiveFilters ? filterLabels : null);
    onClose();
  };
  
  // Count active filters
  const countActiveFilters = () => {
    return Object.values(filterValues).filter(value => value !== 1).length;
  };
  
  // Helper to get translated text
  const getText = (key, defaultText) => language[key] || defaultText;

  // Render a filter section
  const renderFilterSection = (categoryKey, options) => {
    const category = filterCategories[categoryKey];
    
    return (
      <>
        <View style={styles.sectionHeader}>
          <TouchableOpacity 
            style={styles.sectionHeaderButton}
            onPress={() => toggleSection(categoryKey)}
          >
            <Text style={styles.sectionTitle}>{getText(category.labelKey, category.defaultLabel)}</Text>
            <FontAwesome 
              name={expandedSection === categoryKey ? "chevron-up" : "chevron-down"} 
              size={16} 
              color="#666" 
            />
          </TouchableOpacity>
        </View>
        
        {expandedSection === categoryKey && (
          <View style={styles.sectionContent}>
            {category.filters.map(filter => (
              <ChipFilterGroup
                key={filter.key}
                label={getText(filter.labelKey, filter.defaultLabel)}
                options={filterOptions[filter.key]}
                selectedValue={filterValues[filter.key]}
                onValueChange={(value) => updateFilter(filter.key, value)}
              />
            ))}
          </View>
        )}
      </>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.title}>{getText("filterCourts", "Filter Courts")}</Text>
          
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Basic Filters */}
            {renderFilterSection("basic", filterOptions)}
            
            {/* Court Features */}
            {renderFilterSection("features", filterOptions)}
            
            {/* Amenities */}
            {renderFilterSection("amenities", filterOptions)}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={resetFilters}
              style={[styles.button, styles.resetButton]}
            >
              <Text style={styles.resetButtonText}>{getText("reset", "Reset")}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.button, styles.cancelButton]}
            >
              <Text style={styles.buttonText}>{getText("cancel", "Cancel")}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={applyFilter}
              style={[styles.button, styles.applyButton]}
            >
              <Text style={styles.buttonText}>
                {getText("apply", "Apply")} {countActiveFilters() > 0 ? `(${countActiveFilters()})` : ''}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContainer: {
    width: "90%",
    maxHeight: "80%",
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  scrollView: {
    maxHeight: 450,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 15,
    color: red,
    textAlign: "center",
  },
  sectionHeader: {
    marginVertical: 8,
    backgroundColor: "#f8f8f8",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: red,
    overflow: "hidden",
  },
  sectionHeaderButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#444",
  },
  sectionContent: {
    paddingHorizontal: 2,
    paddingVertical: 5,
    paddingBottom: 10,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    flex: 1,
    padding: 12,
    alignItems: "center",
    borderRadius: 8,
    marginHorizontal: 5,
  },
  resetButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#999",
  },
  resetButtonText: {
    color: "#666",
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#999",
  },
  applyButton: {
    backgroundColor: red,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
  },
});

export default EnhancedCourtFilterModal;