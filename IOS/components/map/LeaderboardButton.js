// File: components/map/LeaderboardButton.js
import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { FontAwesome5 } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { red } from '../../screens/colors';

/**
 * <PERSON><PERSON> that navigates to the courts leaderboard screen
 * 
 * @param {Object} props - Component props
 * @param {Object} props.style - Additional styles to apply
 * @param {Object} props.selectedMarker - Currently selected marker
 */
const LeaderboardButton = ({ style, selectedMarker }) => {
  const navigation = useNavigation();

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      activeOpacity={0.6}
      onPress={() => navigation.navigate('CourtsLeaderboard')}
    >
      <FontAwesome5 name="trophy" size={24} color={red} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    position: 'absolute',
    bottom: 5,
    right: 75, // Positioned to the left of the add court button
    backgroundColor: "white",
    width: 60,
    height: 60,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: "rgba(0,0,0,0.17)",
    borderWidth: 0.6,
    zIndex: 1,
  },
});

export default LeaderboardButton;