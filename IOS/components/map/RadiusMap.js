// components/map/RadiusMap.js
import React from "react";
import { StyleSheet } from "react-native";
import MapView, { Circle } from "react-native-maps";
import { red } from "../../screens/colors";

const RadiusMap = ({ 
  centerCoordinate, 
  radius, 
  mapRef, 
  mapDelta, 
  onRegionChange, 
  editable = true 
}) => {
  return (
    <MapView
      ref={mapRef}
      style={styles.map}
      onRegionChange={editable ? onRegionChange : undefined}
      initialRegion={{
        latitude: centerCoordinate.latitude,
        longitude: centerCoordinate.longitude,
        latitudeDelta: mapDelta.latitudeDelta,
        longitudeDelta: mapDelta.longitudeDelta,
      }}
      scrollEnabled={editable}
      zoomEnabled={true}
      pitchEnabled={false}
      rotateEnabled={editable}
    >
      {/* Radius Circle */}
      <Circle
        center={centerCoordinate}
        radius={radius}
        strokeColor={red}
        fillColor={`${red}20`}
        strokeWidth={1}
      />
      {/* Center Marker */}
      <Circle
        center={centerCoordinate}
        radius={200}
        strokeColor={red}
        fillColor={red}
        strokeWidth={1}
      />
    </MapView>
  );
};

const styles = StyleSheet.create({
  map: {
    flex: 1,
  },
});

export default RadiusMap;