// File: components/map/MapTabSwitch.js
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { red } from '../../screens/colors';

/**
 * Tab switch component specifically styled for the Map screen
 * 
 * @param {Object} props - Component props
 * @param {Array} props.options - Array of options objects with label and value properties
 * @param {number} props.selectedValue - Currently selected value
 * @param {Function} props.onValueChange - Callback when an option is selected
 * @param {string} props.selectionColor - Color for selected option
 */
const MapTabSwitch = ({ 
  options = [{ label: 'Option 1', value: 1 }, { label: 'Option 2', value: 2 }],
  selectedValue = 1,
  onValueChange = () => {},
  selectionColor = red,
}) => {
  return (
    <View style={styles.container}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          activeOpacity={0.7}
          onPress={() => onValueChange(option.value)}
          style={[
            styles.option,
            {
              backgroundColor: selectedValue === option.value ? selectionColor : "white",
            }
          ]}
        >
          <Text
            style={{
              color: selectedValue === option.value ? "white" : selectionColor,
              fontWeight: "600",
              fontSize: 14,
            }}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 3,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  option: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 16,
    marginHorizontal: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default MapTabSwitch;