// components/map/MapPreviewModal.js
import React from "react";
import { View, Text, Modal, TouchableOpacity, StyleSheet } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import MapView, { Circle } from "react-native-maps";

const MapPreviewModal = ({
  isVisible,
  onClose,
  onEditRadius,
  centerCoords,
  radius,
  mapDelta,
  language,
  accentColor,
}) => {
  if (!centerCoords) {
    return null;
  }

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {language?.currentSearchArea || "Current Search Area"}
            </Text>
            <TouchableOpacity 
              style={styles.closeButton}
              onPress={onClose}
            >
              <AntDesign name="close" size={24} color="#333" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.mapPreviewContainer}>
            <MapView
              style={styles.mapPreview}
              initialRegion={{
                latitude: centerCoords.latitude,
                longitude: centerCoords.longitude,
                latitudeDelta: mapDelta.latitudeDelta,
                longitudeDelta: mapDelta.longitudeDelta,
              }}
              scrollEnabled={true}
              zoomEnabled={true}
              pitchEnabled={false}
              rotateEnabled={false}
            >
              {/* Radius Circle */}
              <Circle
                center={centerCoords}
                radius={radius}
                strokeColor={accentColor}
                fillColor={`${accentColor}20`}
                strokeWidth={1}
              />
              {/* Center Marker */}
              <Circle
                center={centerCoords}
                radius={200}
                strokeColor={accentColor}
                fillColor={accentColor}
                strokeWidth={1}
              />
            </MapView>
          </View>
          
          <View style={styles.modalFooter}>
            <Text style={styles.radiusInfoText}>
              {language?.searchingWithinRadius || "Searching within"} {Math.round(radius / 1000)} {language?.km || "km"}
            </Text>
            <TouchableOpacity 
              style={[styles.editRadiusButton, { backgroundColor: accentColor }]}
              onPress={onEditRadius}
            >
              <Text style={styles.editRadiusButtonText}>
                {language?.editRadius || "Edit Radius"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    padding: 4,
  },
  mapPreviewContainer: {
    height: 300,
    width: '100%',
  },
  mapPreview: {
    ...StyleSheet.absoluteFillObject,
  },
  modalFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    alignItems: 'center',
  },
  radiusInfoText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 12,
  },
  editRadiusButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 10,
    width: '100%',
    alignItems: 'center',
  },
  editRadiusButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
});

export default MapPreviewModal;