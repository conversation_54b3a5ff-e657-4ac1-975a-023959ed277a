// components/Teams/LocationItem.js
// import React from "react";
import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ActivityIndicator,
} from "react-native";
import FastImage from "react-native-fast-image";
import { Feather, MaterialCommunityIcons } from "@expo/vector-icons";
import { red } from "../../screens/colors";

// components/Teams/LocationItem.js
export const LocationItem = ({
  item,
  onDelete,
  onPickImage,
  onDeleteImage,
  language,
  isMain,
  onSetMainLocation,
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.title}>{item.name}</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => onDelete(item)}
        >
          <Feather name="trash-2" size={22} color={red} />
        </TouchableOpacity>
      </View>

      {item.image ? (
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={() => onPickImage(item)}
          activeOpacity={0.8}
        >
          {imageLoading && (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size="small" color="grey" />
            </View>
          )}
          <FastImage
            source={{ uri: item.image }}
            style={styles.locationImage}
            resizeMode={FastImage.resizeMode.cover}
            onLoadStart={() => setImageLoading(true)}
            onLoadEnd={() => setImageLoading(false)}
          />
          <View style={styles.imageOverlay}>
            <MaterialCommunityIcons name="image-edit" size={24} color="white" />
            <Text style={styles.editText}>{language.updateImage}</Text>
          </View>
          {!imageLoading && (
            <TouchableOpacity
              style={styles.deleteImageButton}
              onPress={() => onDeleteImage(item)}
            >
              <Feather name="x" size={20} color={red} />
            </TouchableOpacity>
          )}
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={styles.addImageButton}
          onPress={() => onPickImage(item)}
        >
          <MaterialCommunityIcons
            name="image-plus"
            size={28}
            color={red}
            style={{ marginRight: 10 }}
          />
          <Text style={styles.addImageText}>{language.addLocationImage}</Text>
        </TouchableOpacity>
      )}

      {isMain ? (
        <View style={styles.mainLocationStatus}>
          <MaterialCommunityIcons name="star" size={20} color="#FFC107" />
          <Text style={styles.mainLocationText}>{language.mainLocation}</Text>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.setMainButton}
          onPress={() => onSetMainLocation(item.place_id)}
        >
          <MaterialCommunityIcons
            name="star-outline"
            size={20}
            color="#FFC107"
          />
          <Text style={styles.setMainText}>{language.setAsMainLocation}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  mainLocationStatus: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 193, 7, 0.1)",
    borderWidth: 0.7,
    borderColor: "#FFC107",
    padding: 10,
    borderRadius: 8,
    marginTop: 12,
    justifyContent: "center",
    gap: 8,
  },
  mainLocationText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#ebb000",
  },
  setMainButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.03)",
    padding: 10,
    borderRadius: 8,
    marginTop: 12,
    justifyContent: "center",
    gap: 8,
  },
  setMainText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  card: {
    backgroundColor: "white",
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 15,
    padding: 15,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 15,
  },
  title: {
    fontSize: 15,
    fontWeight: "500",
    color: "#333",
    flex: 1,
    marginRight: 10,
  },
  deleteButton: {
    padding: 5,
  },
  imageContainer: {
    width: "100%",
    aspectRatio: 16 / 10,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    ...Platform.select({
      android: {
        elevation: 3,
      },
      ios: {
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
      },
    }),
  },
  locationImage: {
    width: "100%",
    height: "100%",
  },
  imageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.3)",
    justifyContent: "center",
    alignItems: "center",
    opacity: 0,
  },
  loaderContainer: {
    position: "absolute",
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(251, 250, 250, 0.7)",
    zIndex: 1,
  },
  editText: {
    color: "white",
    marginTop: 8,
    fontSize: 14,
    fontWeight: "600",
  },
  deleteImageButton: {
    position: "absolute",
    top: 10,
    right: 10,
    backgroundColor: "white",
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  addImageButton: {
    width: "100%",
    height: 80,
    borderRadius: 10,
    backgroundColor: "rgba(0,0,0,0.03)",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    borderStyle: "dashed",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  addImageText: {
    color: red,
    fontSize: 16,
    fontWeight: "600",
  },
});
