//components/Teams/TrainingSessionItem.js

import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Pressable,
  Animated,
  ActivityIndicator,
  Easing,
} from "react-native";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { auth } from "../../config/firebase";
import ProfileImage from "../../assets/ProfileImage";
import { red } from "../../screens/colors";
import { StyleSheet } from "react-native";

const getDateCategory = (date) => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const sessionDate = new Date(date);

  if (sessionDate.toDateString() === today.toDateString()) {
    return "Today";
  } else if (sessionDate.toDateString() === tomorrow.toDateString()) {
    return "Tomorrow";
  }
  return null;
};

export const TrainingSessionItem = React.memo(
  ({
    item,
    language,
    team,
    previousDate,
    navigation,
    handleAttendance,
    isHistory,
  }) => {
    // Log each render with the session id
    // console.log("Rendering TrainingSessionItem:", item.id);

    // console.warn(item.date.seconds);

    const dateCategory = getDateCategory(item.date);
    const showDateHeader = previousDate
      ? new Date(item.date).toDateString() !==
        new Date(previousDate).toDateString()
      : true;

    const currentUserId = auth.currentUser.uid;
    const isGoing = item.goingUsers && item.goingUsers.includes(currentUserId);
    const isNotGoing =
      item.notGoingUsers && item.notGoingUsers.includes(currentUserId);

    // Calculate attendance stats
    const goingCount = item.goingUsers?.length || 0;
    const notGoingCount = item.notGoingUsers?.length || 0;
    const totalMembers = team.membersData?.length || 0;
    const pendingCount = totalMembers - goingCount - notGoingCount;

    // Animation values
    const [goingWidth] = useState(new Animated.Value(isGoing ? 55 : 50));
    const [notGoingWidth] = useState(new Animated.Value(isNotGoing ? 55 : 50));

    useEffect(() => {
      // Animate button widths
      Animated.parallel([
        Animated.timing(goingWidth, {
          toValue: isGoing ? 55 : isNotGoing ? 45 : 50,
          duration: 150,
          useNativeDriver: false,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }),
        Animated.timing(notGoingWidth, {
          toValue: isNotGoing ? 55 : isGoing ? 45 : 50,
          duration: 150,
          useNativeDriver: false,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }),
      ]).start();
    }, [isGoing, isNotGoing, goingWidth, notGoingWidth]);

    const goingPercentage = (goingCount / totalMembers) * 100;
    const notGoingPercentage = (notGoingCount / totalMembers) * 100;

    const AVATAR_SECTION_WIDTH = 95;
    const AVATAR_SIZE = 38;
    const AVATAR_OVERLAP = 14;

    if (!team) {
      return (
        <View style={{ flex: 1, backgroundColor: "white" }}>
          <ActivityIndicator size="small" style={{ marginTop: 20 }} />
        </View>
      );
    }

    return (
      <View style={{ marginHorizontal: 15, marginBottom: 20 }}>
        {showDateHeader && (
          <Text
            style={{
              fontSize: 18,
              fontWeight: "600",
              marginBottom: 10,
              color: "#333",
            }}
          >
            {dateCategory ||
              new Date(item.date).toLocaleDateString([], {
                weekday: "long",
                month: "long",
                day: "numeric",
              })}
          </Text>
        )}
        <Pressable
          style={({ pressed }) => ({
            transform: [{ scale: pressed ? 0.98 : 1 }],
          })}
          onPress={() => {
            if (isHistory) {
              console.warn("aaaa");
              navigation.navigate("TrainingSessionDetails", {
                historySession: item, // Pass the full session object
                team: team,
              });
            } else {
              navigation.navigate("TrainingSessionDetails", {
                sessionId: item.id,
                team: team,
              });
            }
          }}
        >
          <View
            style={{
              // marginHorizontal: 5,
              borderRadius: 15,
              backgroundColor: "white",
              borderWidth: item.isCancelled ? 1 : 0.5,
              borderColor: item.isCancelled ? "#FF8C00" : "rgba(200,200,200,1)",
              // borderWidth: 0.5,
              // borderColor: "rgba(200,200,200,1)",
              padding: 15,
            }}
          >
            {/* Date and Time */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 10,
              }}
            >
              {/* <Text style={{ fontSize: 16, fontWeight: "600", color: "#333" }}>
                {new Date(item.date).toLocaleDateString([], {
                  weekday: "short",
                  month: "short",
                  day: "numeric",
                })}
              </Text> */}
              <Text style={{ fontSize: 16, fontWeight: "600", color: "#333" }}>
                {item.name}
              </Text>
              <Text style={{ color: "#666", marginHorizontal: 8 }}>•</Text>
              <Text style={{ fontSize: 16, color: "#333" }}>
                {new Date(item.startTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </Text>
            </View>

            {/* Location */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 20,
              }}
            >
              <Ionicons
                name="location-outline"
                size={16}
                color="#666"
                style={{ marginRight: 5 }}
              />
              <Text
                style={{ fontSize: 14, color: "#666", flex: 1 }}
                numberOfLines={1}
              >
                {item.location}
              </Text>
            </View>

            {/* Attendance Section */}
            <View
              style={{ marginBottom: 20, opacity: item.isCancelled ? 0.5 : 1 }}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: 12,
                }}
              >
                {/* Going Users */}
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View style={{ width: 95, height: AVATAR_SIZE }}>
                    {[...Array(3)].map((_, index) => {
                      const user = item.goingUsers?.[index];
                      if (user) {
                        const userData = team.membersData.find(
                          (member) => member.uid === user
                        );
                        return (
                          <View
                            key={user}
                            style={{
                              position: "absolute",
                              left: index * (AVATAR_SIZE - 15),
                              zIndex: 3 - index,
                            }}
                          >
                            <ProfileImage
                              profileImageRef={userData?.profileImageRef}
                              profileColor={userData?.profileColor}
                              name={userData?.name}
                              size={AVATAR_SIZE}
                              style={{
                                borderWidth: 2,
                                borderColor: "white",
                              }}
                            />
                          </View>
                        );
                      }
                      return (
                        <View
                          key={`placeholder-${index}`}
                          style={{
                            position: "absolute",
                            left: index * (AVATAR_SIZE - 15),
                            zIndex: 3 - index,
                          }}
                        >
                          <View
                            style={{
                              width: AVATAR_SIZE - 5,
                              height: AVATAR_SIZE - 5,
                              borderRadius: AVATAR_SIZE / 2,
                              borderWidth: 1.5,
                              borderStyle: "dashed",
                              borderColor: "rgba(0,0,0,0.2)",
                              backgroundColor: "white",
                              marginTop: 2,
                            }}
                          />
                        </View>
                      );
                    })}
                  </View>
                  <View
                    style={{
                      width: 23,
                      height: 23,
                      backgroundColor: "rgba(74, 175, 80, 0.2)",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 50,
                      marginLeft: 2,
                      marginBottom: 3,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 13,
                        color: "#4CAF50",
                        fontWeight: "600",
                        opacity: isHistory ? 0.7 : 1,
                      }}
                    >
                      {goingCount}
                    </Text>
                  </View>
                </View>

                {/* Not Going Users */}
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View style={{ width: 95, height: AVATAR_SIZE }}>
                    {[...Array(3)].map((_, index) => {
                      const user = item.notGoingUsers?.[index];
                      if (user) {
                        const userData = team.membersData.find(
                          (member) => member.uid === user
                        );
                        return (
                          <View
                            key={user}
                            style={{
                              position: "absolute",
                              left: index * (AVATAR_SIZE - 15),
                              zIndex: 3 - index,
                            }}
                          >
                            <ProfileImage
                              profileImageRef={userData?.profileImageRef}
                              profileColor={userData?.profileColor}
                              name={userData?.name}
                              size={AVATAR_SIZE}
                              style={{
                                borderWidth: 2,
                                borderColor: "white",
                              }}
                            />
                          </View>
                        );
                      }
                      return (
                        <View
                          key={`placeholder-${index}`}
                          style={{
                            position: "absolute",
                            left: index * (AVATAR_SIZE - 15),
                            zIndex: 3 - index,
                          }}
                        >
                          <View
                            style={{
                              width: AVATAR_SIZE - 5,
                              height: AVATAR_SIZE - 5,
                              borderRadius: AVATAR_SIZE / 2,
                              borderWidth: 1.5,
                              borderStyle: "dashed",
                              borderColor: "rgba(0,0,0,0.2)",
                              backgroundColor: "white",
                              marginTop: 2,
                            }}
                          />
                        </View>
                      );
                    })}
                  </View>
                  <View
                    style={{
                      width: 23,
                      height: 23,
                      backgroundColor: "rgba(197, 42, 71, 0.2)",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 50,
                      marginLeft: 2,
                      marginBottom: 3,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 13,
                        color: red,
                        fontWeight: "600",
                        opacity: isHistory ? 0.7 : 1,
                      }}
                    >
                      {notGoingCount}
                    </Text>
                  </View>
                </View>

                <View style={{ padding: 4, marginLeft: 8 }}>
                  <Ionicons name="chevron-forward" size={20} color="#666" />
                </View>
              </View>

              {/* Progress Bar */}
              <View
                style={{
                  height: 6,
                  backgroundColor: "#f0f0f0",
                  borderRadius: 3,
                  flexDirection: "row",
                  overflow: "hidden",
                }}
              >
                <Animated.View
                  style={{
                    width: `${goingPercentage}%`,
                    backgroundColor: "#4CAF50",
                    opacity: isHistory ? 0.6 : 1,
                  }}
                />
                <Animated.View
                  style={{
                    width: `${(pendingCount / totalMembers) * 100}%`,
                    backgroundColor: "#E0E0E0",
                    opacity: isHistory ? 0.6 : 1,
                  }}
                />
                <Animated.View
                  style={{
                    width: `${notGoingPercentage}%`,
                    backgroundColor: red,
                    opacity: isHistory ? 0.6 : 1,
                  }}
                />
              </View>
            </View>

            {/* Action Buttons */}
            {item.isCancelled ? (
              // Cancelled session message
              <View style={styles.cancelledContainer}>
                <MaterialIcons
                  name="event-busy"
                  size={20}
                  color="#FF8C00"
                  style={styles.cancelledIcon}
                />
                <View style={styles.cancelledTextContainer}>
                  <Text style={styles.cancelledTitle}>
                    {language.sessionCancelled || "Session Cancelled"}
                  </Text>
                  {item.cancelReason && (
                    <Text style={styles.cancelledReason}>
                      {item.cancelReason}
                    </Text>
                  )}
                </View>
              </View>
            ) : !isHistory ? (
              // Active session (not cancelled, not history)
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 10,
                  marginTop: 5,
                }}
              >
                <Pressable
                  style={({ pressed }) => ({
                    width: "48%",
                    height: 40,
                    backgroundColor: isGoing
                      ? "#4CAF50"
                      : pressed
                      ? "rgba(74, 175, 80, 0.1)"
                      : "white",
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: "#4CAF50",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 6,
                  })}
                  onPress={() => {
                    handleAttendance(item.id, "going");
                  }}
                >
                  <Ionicons
                    name="checkmark"
                    size={18}
                    color={isGoing ? "white" : "#4CAF50"}
                  />
                  <Text
                    style={{
                      color: isGoing ? "white" : "#4CAF50",
                      fontWeight: "600",
                      fontSize: 15,
                    }}
                  >
                    {language.yesGoing}
                  </Text>
                </Pressable>

                <Pressable
                  style={({ pressed }) => ({
                    width: "48%",
                    height: 40,
                    backgroundColor: isNotGoing
                      ? red
                      : pressed
                      ? "rgba(197, 42, 71, 0.1)"
                      : "white",
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: red,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 6,
                  })}
                  onPress={() => handleAttendance(item.id, "notGoing")}
                >
                  <Ionicons
                    name="close"
                    size={18}
                    color={isNotGoing ? "white" : red}
                  />
                  <Text
                    style={{
                      color: isNotGoing ? "white" : red,
                      fontWeight: "600",
                      fontSize: 15,
                    }}
                  >
                    {language.notGoing}
                  </Text>
                </Pressable>
              </View>
            ) : (
              // History session (non-cancelled)
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 10,
                  marginTop: 5,
                  opacity: 0.6,
                }}
              >
                <View
                  style={{
                    width: "48%",
                    height: 40,
                    backgroundColor: isGoing ? "#4CAF50" : "white",
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: "#4CAF50",
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 6,
                  }}
                >
                  <Ionicons
                    name="checkmark"
                    size={18}
                    color={isGoing ? "white" : "#4CAF50"}
                  />
                  <Text
                    style={{
                      color: isGoing ? "white" : "#4CAF50",
                      fontWeight: "600",
                      fontSize: 15,
                    }}
                  >
                    {language.yesGoing}
                  </Text>
                </View>

                <View
                  style={{
                    width: "48%",
                    height: 40,
                    backgroundColor: isNotGoing ? red : "white",
                    borderRadius: 8,
                    borderWidth: 1,
                    borderColor: red,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: 6,
                  }}
                >
                  <Ionicons
                    name="close"
                    size={18}
                    color={isNotGoing ? "white" : red}
                  />
                  <Text
                    style={{
                      color: isNotGoing ? "white" : red,
                      fontWeight: "600",
                      fontSize: 15,
                    }}
                  >
                    {language.notGoing}
                  </Text>
                </View>
              </View>
            )}
          </View>
        </Pressable>
      </View>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.item.goingUsers?.length === nextProps.item.goingUsers?.length &&
      prevProps.item.notGoingUsers?.length ===
        nextProps.item.notGoingUsers?.length &&
      prevProps.language === nextProps.language &&
      prevProps.team.id === nextProps.team.id &&
      prevProps.previousDate === nextProps.previousDate &&
      prevProps.isHistory === nextProps.isHistory &&
      prevProps.item.name === nextProps.item.name &&
      prevProps.item.location === nextProps.item.location &&
      prevProps.item.description === nextProps.item.description &&
      prevProps.item.templateId === nextProps.item.templateId &&
      prevProps.language === nextProps.language &&
      prevProps.item.date?.seconds === nextProps.item.date?.seconds &&
      prevProps.item.startTime?.seconds === nextProps.item.startTime?.seconds &&
      prevProps.item.endTime?.seconds === nextProps.item.endTime?.seconds &&
      prevProps.item.isCancelled === nextProps.item.isCancelled &&
      prevProps.item.cancelReason === nextProps.item.cancelReason
    );
  }
);

// Add to StyleSheet
const styles = StyleSheet.create({
  // ...existing styles
  cancelledContainer: {
    flexDirection: "row",
    backgroundColor: "rgba(255, 140, 0, 0.1)",
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 140, 0, 0.3)",
    marginTop: 5,
  },
  cancelledIcon: {
    marginRight: 12,
  },
  cancelledTextContainer: {
    flex: 1,
  },
  cancelledTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#FF8C00",
  },
  cancelledReason: {
    fontSize: 13,
    color: "rgba(255, 140, 0, 0.8)",
    fontStyle: "italic",
    marginTop: 2,
  },
});
