// components/Teams/TeamMemberLeftNotif.js
import { memo } from "react";
import { View, Text, StyleSheet } from "react-native";
import ProfileImage from "../../assets/ProfileImage";
import { red } from "../../screens/colors";
import { FontAwesome5 } from "@expo/vector-icons";

export const TeamMemberLeftNotif = memo(({ notification, language }) => {
  return (
    <View style={styles.container}>
      <ProfileImage
        profileImageRef={notification.memberProfileImage}
        profileColor={notification.memberProfileColor}
        name={notification.memberName}
        size={60}
        style={styles.profileImage}
      />

      <View style={styles.contentContainer}>
        <Text style={styles.messageText}>
          <Text style={styles.memberName}>{notification.memberName} </Text>
          <Text>{language.leftTeam}</Text>
        </Text>

        <View style={styles.timestampContainer}>
          <FontAwesome5
            name="clock"
            size={12}
            color="#666"
            style={styles.clockIcon}
          />
          <Text style={styles.timestamp}>
            {new Date(notification.date).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    padding: 15,
    backgroundColor: "white",
    alignItems: "center",
  },
  profileImage: {
    marginRight: 15,
  },
  contentContainer: {
    flex: 1,
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  memberName: {
    fontWeight: "600",
  },
  timestampContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 5,
  },
  clockIcon: {
    marginRight: 5,
  },
  timestamp: {
    fontSize: 12,
    color: "#666",
  },
});
