// components/Teams/RecurringSessionItem.js
import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  TouchableOpacity,
} from "react-native";
import {
  MaterialIcons,
  MaterialCommunityIcons,
  Feather,
  FontAwesome6,
} from "@expo/vector-icons";
import { doc } from "@react-native-firebase/firestore";
import { red } from "../../screens/colors";
import FastImage from "react-native-fast-image";

const days = {
  EN: ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"],
  CZ: ["Po", "Út", "St", "<PERSON>t", "<PERSON><PERSON>", "So", "Ne"],
};

export const RecurringSessionItem = ({
  item,
  language,
  onDelete,
  onEdit,
  isDeleting,
}) => (
  <View style={[styles.sessionCard, isDeleting && styles.deletingSessionCard]}>
    {/* Header: Session name and days count */}
    <View style={styles.sessionHeader}>
      <Text style={styles.sessionTitle}>{item.name}</Text>
      <View style={styles.headerButtons}>
        <TouchableOpacity
          style={[styles.editButton, isDeleting && styles.disabledButton]}
          onPress={() => !isDeleting && onEdit(item)}
          disabled={isDeleting}
        >
          <FontAwesome6
            name="edit"
            size={22}
            color={isDeleting ? "rgba(0,0,0,0.3)" : "black"}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.deleteButton, isDeleting && styles.disabledButton]}
          onPress={() => !isDeleting && onDelete(item)}
          disabled={isDeleting}
        >
          <Feather
            name="trash-2"
            size={24}
            color={isDeleting ? "rgba(255,0,0,0.3)" : red}
          />
        </TouchableOpacity>
      </View>
    </View>

    {/* Time display */}
    <View style={styles.timeContainer}>
      <View style={styles.timeBox}>
        <Text style={styles.timeLabel}>{language.startTime}</Text>
        <View style={styles.timeRow}>
          <Feather name="clock" size={16} color={red} style={styles.timeIcon} />
          <Text style={styles.timeText}>
            {new Date(item.startTime).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </View>

      <View style={styles.timeBox}>
        <Text style={styles.timeLabel}>{language.endTime}</Text>
        <View style={styles.timeRow}>
          <Feather name="clock" size={16} color={red} style={styles.timeIcon} />
          <Text style={styles.timeText}>
            {new Date(item.endTime).toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </Text>
        </View>
      </View>
    </View>

    {/* Days selector */}
    <View style={styles.daysContainer}>
      {days[language.languageCode].map((day, index) => (
        <View
          key={index}
          style={[
            styles.dayIndicator,
            item.days.includes(index) && styles.activeDayIndicator,
          ]}
        >
          <Text
            style={[
              styles.dayText,
              item.days.includes(index) && styles.activeDayText,
            ]}
          >
            {day}
          </Text>
        </View>
      ))}
    </View>

    {/* Location */}
    <View style={styles.locationContainer}>
      {item.locationImage ? (
        <FastImage
          source={{ uri: item.locationImage }}
          style={styles.locationImage}
        />
      ) : (
        <View style={styles.locationImagePlaceholder}>
          <View style={styles.locationIconContainer}>
            <FastImage
              source={require("../../images/qq2.png")}
              style={styles.locationIcon}
            />
            <FastImage
              source={require("../../images/elipse.png")}
              style={styles.locationElipse}
            />
          </View>
        </View>
      )}
      <Text style={styles.locationText} numberOfLines={2}>
        {item.location}
      </Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  sessionCard: {
    backgroundColor: "white",
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 15,
    paddingHorizontal: 15,
    paddingVertical: 20,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sessionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  sessionTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
  },
  headerButtons: {
    flexDirection: "row",
    gap: 10,
    position: "absolute",
    right: -10,
    top: -13,
  },
  editButton: {
    borderRadius: 5,
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  deleteButton: {
    borderRadius: 5,
    paddingVertical: 8,
    paddingHorizontal: 12,
    justifyContent: "center",
    alignItems: "center",
  },

  timeContainer: {
    flexDirection: "row",
    marginBottom: 20,
    gap: 15,
  },
  timeBox: {
    flex: 1,
    backgroundColor: "white",
    borderRadius: 12,
    // padding: 8,
    paddingHorizontal: 12,
    paddingVertical: 7,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.15)",
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeIcon: {
    marginRight: 6,
  },
  timeText: {
    fontSize: 15,
    fontWeight: "600",
    color: "black",
  },
  timeLabel: {
    fontSize: 13,
    color: "black",
    marginBottom: 4,
    fontWeight: "500",
  },
  daysContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 15,
  },
  dayIndicator: {
    width: 35,
    aspectRatio: 1,
    borderRadius: 8,
    backgroundColor: `${red}15`,
    justifyContent: "center",
    alignItems: "center",
  },
  activeDayIndicator: {
    backgroundColor: red,
  },
  dayText: {
    color: red,
    fontSize: 13,
    fontWeight: "700",
  },
  activeDayText: {
    color: "white",
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 5,
  },
  locationImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10,
  },
  locationImagePlaceholder: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "rgba(0,0,0,0.4)",
    borderWidth: 0.5,
    borderRadius: 8,
    marginRight: 10,
  },
  locationIconContainer: {
    position: "relative",
    width: 30,
    height: 30,
  },
  locationIcon: {
    width: 30,
    height: 30,
    borderRadius: 15,
    position: "absolute",
    zIndex: 100,
    top: -2,
    left: 0,
  },
  locationElipse: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    position: "absolute",
    zIndex: 99,
    bottom: -27,
    left: 1,
  },
  locationText: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  deletingSessionCard: {
    opacity: 0.7,
  },
  disabledButton: {
    opacity: 0.5,
  },
});
