// components/Teams/InviteTeamModal.js
import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Pressable,
  TextInput,
  FlatList,
  Animated,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { BlurView } from "expo-blur";
import FastImage from "react-native-fast-image";
import { useNavigation } from "@react-navigation/native";
import { auth } from "../../config/firebase";
import { red } from "../../screens/colors";
import { AntDesign, Feather, MaterialCommunityIcons } from "@expo/vector-icons";
import { useTeamInvitations } from "../../hooks/Teams/useTeamInvitations";

const { width, height } = Dimensions.get("window");

function calculateAge(dob) {
  let diff_ms;
  if (dob?.seconds) {
    diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  } else {
    diff_ms = Date.now() - new Date(dob).getTime();
  }

  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const InviteTeamModal = ({ teamId, isVisible, onClose }) => {
  const {
    modalOpacity,
    inviteSearchQuery,
    setInviteSearchQuery,
    selectedTab,
    setSelectedTab,
    invitingUsers,
    invitedUsers,
    language,
    handleInviteUser,
    getFilteredUsers,
    handleLoadMore,
    isLoading,
    hasMoreUsers,
  } = useTeamInvitations(teamId);
  const navigation = useNavigation();

  // IMPORTANT: Simple animation control
  useEffect(() => {
    if (isVisible) {
      // When becoming visible, animate to 1
      Animated.timing(modalOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      // When closing, animate to 0
      Animated.timing(modalOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  const filteredUsers = getFilteredUsers();

  const renderListFooter = () => {
    if (!isLoading || selectedTab !== "players") return null;
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={red} />
      </View>
    );
  };

  return (
    <View style={styles.modalOverlay}>
      <BlurView style={styles.blurOverlay} intensity={25} tint="dark">
        <Pressable onPress={onClose} style={styles.pressableDismiss} />
      </BlurView>

      <Animated.View style={[styles.inviteModal, { opacity: modalOpacity }]}>
        <Text style={styles.inviteModalTitle}>{language.inviteToTeam}</Text>

        {/* Search Bar */}
        <View style={styles.searchBarContainer}>
          <View style={styles.searchIconContainer}>
            <Feather name="search" size={20} color="rgba(0,0,0,0.5)" />
          </View>
          <TextInput
            style={styles.searchInput}
            placeholder={language.searchPlayers}
            value={inviteSearchQuery}
            onChangeText={setInviteSearchQuery}
          />
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              selectedTab === "players" && styles.activeTabButton,
            ]}
            onPress={() => setSelectedTab("players")}
          >
            <Text
              style={[
                styles.tabButtonText,
                selectedTab === "players" && styles.activeTabButtonText,
              ]}
            >
              {language.players}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabButton,
              selectedTab === "friends" && styles.activeTabButton,
            ]}
            onPress={() => setSelectedTab("friends")}
          >
            <Text
              style={[
                styles.tabButtonText,
                selectedTab === "friends" && styles.activeTabButtonText,
              ]}
            >
              {language.friends}
            </Text>
          </TouchableOpacity>
        </View>

        {/* User List */}
        <FlatList
          style={styles.inviteUsersList}
          data={filteredUsers}
          renderItem={({ item }) => (
            // User item rendering remains unchanged
            <Pressable
              style={({ pressed }) => [
                styles.userItem,
                pressed && styles.userItemPressed,
              ]}
              onPress={() => {
                if (item.id === auth.currentUser.uid) {
                  navigation.navigate("Profile");
                } else {
                  navigation.navigate("User", {
                    user: item,
                    navigation: navigation,
                  });
                }
              }}
            >
              {item.profileImageRef ? (
                <FastImage
                  source={{ uri: item.profileImageRef }}
                  style={styles.userAvatar}
                />
              ) : (
                <View
                  style={[
                    styles.userAvatarPlaceholder,
                    { backgroundColor: item.profileColor || "#B5CCE7" },
                  ]}
                >
                  <Text style={styles.userAvatarInitial}>
                    {item.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
              <View style={styles.userInfo}>
                <Text style={styles.userName}>{item.name}</Text>
                <Text style={styles.userAge}>
                  {calculateAge(item.date)} {language.yearsOld}
                </Text>
              </View>
              <TouchableOpacity
                style={[
                  styles.inviteButton,
                  invitedUsers.has(item.uid) && styles.inviteButtonSuccess,
                  invitingUsers.has(item.uid) && styles.inviteButtonLoading,
                ]}
                onPress={() => handleInviteUser(item)}
                disabled={
                  invitingUsers.has(item.uid) || invitedUsers.has(item.uid)
                }
              >
                {invitingUsers.has(item.uid) ? (
                  <ActivityIndicator size="small" color="white" />
                ) : invitedUsers.has(item.uid) ? (
                  <AntDesign name="check" size={18} color="white" />
                ) : (
                  <Text style={styles.inviteButtonText}>{language.invite}</Text>
                )}
              </TouchableOpacity>
            </Pressable>
          )}
          keyExtractor={(item) => item.uid}
          contentContainerStyle={styles.inviteListContent}
          ListEmptyComponent={() => (
            <Text style={styles.emptyListText}>
              {selectedTab === "players"
                ? language.noPlayersFound
                : language.noFriendsFound}
            </Text>
          )}
          ItemSeparatorComponent={() => <View style={styles.listSeparator} />}
          onEndReached={() => {
            if (selectedTab === "players") {
              handleLoadMore();
            }
          }}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderListFooter}
        />

        <View style={styles.dividerContainer}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>{language.or}</Text>
          <View style={styles.dividerLine} />
        </View>

        <TouchableOpacity style={styles.shareButton}>
          <View style={styles.shareTextContainer}>
            <Feather
              name="share"
              size={20}
              color={red}
              style={styles.shareIcon}
            />
            <Text style={styles.shareButtonText}>{language.quickShare}</Text>
          </View>

          {/* Social Icons Preview */}
          <View style={styles.socialIconsContainer}>
            <View style={styles.socialDivider} />
            <View style={styles.socialIcons}>
              <MaterialCommunityIcons
                name="whatsapp"
                size={22}
                color="#25D366"
                style={styles.socialIcon}
              />
              <MaterialCommunityIcons
                name="instagram"
                size={22}
                color="#E4405F"
                style={styles.socialIcon}
              />
              <MaterialCommunityIcons
                name="facebook-messenger"
                size={22}
                color="#0084FF"
                style={styles.socialIcon}
              />
              <View style={styles.moreIconsContainer}>
                <Text style={styles.moreIconsText}>+5</Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>

        {/* Close Button */}
        <TouchableOpacity style={styles.modalCloseButton} onPress={onClose}>
          <AntDesign name="close" size={24} color="rgba(0,0,0,0.5)" />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  blurOverlay: {
    flex: 1,
  },
  pressableDismiss: {
    flex: 1,
  },
  inviteModal: {
    position: "absolute",
    top: "50%",
    left: "50%",
    width: "90%",
    height: "75%",
    transform: [{ translateX: -width * 0.45 }, { translateY: -height * 0.35 }],
    backgroundColor: "white",
    borderRadius: 20,
    padding: 20,
    zIndex: 1001,
  },
  inviteModalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
  },
  searchBarContainer: {
    marginBottom: 20,
  },
  searchIconContainer: {
    position: "absolute",
    left: 12,
    top: 12,
    zIndex: 1,
  },
  searchInput: {
    fontSize: 16,
    backgroundColor: "rgba(245,245,245,1)",
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 10,
    paddingLeft: 40,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
    height: 45,
  },
  tabsContainer: {
    flexDirection: "row",
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomColor: red,
  },
  tabButtonText: {
    textAlign: "center",
    fontWeight: "600",
    color: "rgba(0,0,0,0.6)",
  },
  activeTabButtonText: {
    color: red,
  },
  inviteUsersList: {
    height: "50%",
  },
  inviteListContent: {
    paddingBottom: 20,
  },
  userItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 10,
    backgroundColor: "white",
    borderRadius: 10,
  },
  userItemPressed: {
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  userAvatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 15,
  },
  userAvatarInitial: {
    color: "white",
    fontSize: 20,
    fontWeight: "bold",
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: "600",
  },
  userAge: {
    fontSize: 14,
    color: "rgba(0,0,0,0.5)",
  },
  inviteButton: {
    backgroundColor: red,
    padding: 8,
    borderRadius: 80,
    width: 70,
    justifyContent: "center",
    alignItems: "center",
    height: 33,
  },
  inviteButtonSuccess: {
    backgroundColor: "#4CAF50",
  },
  inviteButtonLoading: {
    opacity: 0.8,
  },
  inviteButtonText: {
    color: "white",
    textAlign: "center",
    fontWeight: "600",
    fontSize: 13,
  },
  emptyListText: {
    textAlign: "center",
    marginTop: 20,
    color: "rgba(0,0,0,0.5)",
  },
  listSeparator: {
    height: 1,
    backgroundColor: "rgba(0,0,0,0.05)",
    marginVertical: 5,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 15,
    marginBottom: 5,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(0,0,0,0.15)",
    marginHorizontal: 15,
  },
  dividerText: {
    color: "rgba(0,0,0,0.5)",
    fontSize: 12,
    fontWeight: "500",
    paddingHorizontal: 10,
  },
  shareButton: {
    marginTop: 10,
    backgroundColor: "white",
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: red,
  },
  shareTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  shareIcon: {
    marginRight: 10,
  },
  shareButtonText: {
    color: red,
    fontWeight: "600",
    fontSize: 13,
  },
  socialIconsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  socialDivider: {
    width: 1,
    height: 20,
    backgroundColor: "rgba(0,0,0,0.3)",
    marginRight: 15,
  },
  socialIcons: {
    flexDirection: "row",
    alignItems: "center",
  },
  socialIcon: {
    marginRight: 8,
  },
  moreIconsContainer: {
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 11,
    width: 22,
    height: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  moreIconsText: {
    fontSize: 10,
    color: "rgba(0,0,0,0.6)",
    fontWeight: "700",
  },
  modalCloseButton: {
    position: "absolute",
    top: 20,
    right: 20,
    padding: 5,
  },
});

export default InviteTeamModal;
