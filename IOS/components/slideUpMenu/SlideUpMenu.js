// components/common/SlideUpMenu.js
import React, { useRef, useEffect } from "react";
import {
  View,
  Text,
  Pressable,
  Animated,
  StyleSheet,
  Dimensions,
  Easing,
} from "react-native";
import { BlurView } from "expo-blur";
import * as Haptics from "expo-haptics";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const { height } = Dimensions.get("window");

export const SlideUpMenu = ({
  isVisible,
  onClose,
  title,
  options,
  // Optional props for customization
  slideDistance = height * 0.08,
  hapticFeedback = true,
}) => {
  const insets = useSafeAreaInsets();
  const slideAnim = useRef(new Animated.Value(slideDistance)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const menuOpacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isVisible) {
      if (hapticFeedback) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
      menuOpacity.setValue(1);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: slideDistance,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(menuOpacity, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start(() => {
        menuOpacity.setValue(1);
      });
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <View style={StyleSheet.absoluteFill}>
      <Animated.View style={[styles.overlay, { opacity: opacityAnim }]}>
        <BlurView style={styles.blur} intensity={25} tint="dark">
          <Pressable style={styles.blur} onPress={onClose} />
        </BlurView>
      </Animated.View>

      <Animated.View
        style={[
          styles.menuContainer,
          {
            paddingBottom: insets.bottom + 20,
            transform: [{ translateY: slideAnim }],
            opacity: menuOpacity,
          },
        ]}
      >
        {title && <Text style={styles.title}>{title}</Text>}

        {options.map((option, index) => (
          <Pressable
            key={index}
            style={({ pressed }) => [
              styles.option,
              {
                backgroundColor: pressed
                  ? option.pressedBgColor || "rgba(0, 0, 0, 0.1)"
                  : option.bgColor || "rgba(0, 0, 0, 0.05)",
                marginBottom: index === options.length - 1 ? 0 : 15,
                opacity: option.disabled ? 0.5 : 1,
              },
              option.style,
            ]}
            onPress={() => {
              onClose();
              option.onPress();
            }}
          >
            {option.icon}
            <Text
              style={[
                styles.optionText,
                { color: option.textColor || "rgba(0, 0, 0, 0.8)" },
                option.textStyle,
              ]}
            >
              {option.text}
            </Text>
          </Pressable>
        ))}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 9997,
  },
  blur: {
    flex: 1,
  },
  menuContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    zIndex: 9998,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 30,
    color: "#333",
  },
  option: {
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 13,
    height: 45,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  optionText: {
    fontWeight: "600",
    fontSize: 15,
    marginLeft: 10,
  },
});
