// CompactFilterPicker.js
import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Modal,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { red } from "./../screens/colors";

/**
 * A space-efficient filter picker component that uses chips/tags instead of switches
 *
 * @param {Object} props
 * @param {string} props.label - The label for the filter
 * @param {Array} props.options - Array of options [{ label, value }]
 * @param {any} props.selectedValue - Currently selected value
 * @param {Function} props.onValueChange - Callback when value changes
 */
const CompactFilterPicker = ({
  label,
  options,
  selectedValue,
  onValueChange,
  multiSelect = false,
}) => {
  const [modalVisible, setModalVisible] = useState(false);

  // For multi-select
  const [selectedValues, setSelectedValues] = useState([]);

  const selectedOption = options.find((opt) => opt.value === selectedValue);

  const toggleModal = () => {
    setModalVisible(!modalVisible);
  };

  const handleSelect = (value) => {
    if (multiSelect) {
      // For multi-select, toggle the selection
      const newValues = [...selectedValues];
      const index = newValues.indexOf(value);

      if (index > -1) {
        newValues.splice(index, 1);
      } else {
        newValues.push(value);
      }

      setSelectedValues(newValues);
    } else {
      // For single-select, just update the value
      onValueChange(value);
      toggleModal();
    }
  };

  const handleApplyMultiSelect = () => {
    // Here you'd handle the multi-select values
    // This is just a placeholder implementation
    console.log("Selected values:", selectedValues);
    toggleModal();
  };

  const isSelected = (value) => {
    if (multiSelect) {
      return selectedValues.includes(value);
    } else {
      return selectedValue === value;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>{label}</Text>

      <TouchableOpacity
        style={styles.selectedValueButton}
        onPress={toggleModal}
      >
        <Text style={styles.selectedValueText}>
          {selectedOption ? selectedOption.label : "Select..."}
        </Text>
        <MaterialIcons name="keyboard-arrow-down" size={24} color="#666" />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={toggleModal}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{label}</Text>

            <ScrollView style={styles.optionsList}>
              {options.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionItem,
                    isSelected(option.value) && styles.selectedOption,
                  ]}
                  onPress={() => handleSelect(option.value)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      isSelected(option.value) && styles.selectedOptionText,
                    ]}
                  >
                    {option.label}
                  </Text>

                  {isSelected(option.value) && (
                    <MaterialIcons name="check" size={20} color="white" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            {multiSelect ? (
              <View style={styles.multiSelectButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={toggleModal}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.applyButton}
                  onPress={handleApplyMultiSelect}
                >
                  <Text style={styles.applyButtonText}>Apply</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                style={styles.closeButton}
                onPress={toggleModal}
              >
                <Text style={styles.closeButtonText}>Close</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

// ChipFilterGroup - An alternative UI for showing filter options as horizontal scrollable chips
const ChipFilterGroup = ({ label, options, selectedValue, onValueChange }) => {
  return (
    <View style={styles.chipContainer}>
      <Text style={styles.label}>{label}</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.chipScrollView}
      >
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.chip,
              selectedValue === option.value && styles.selectedChip,
            ]}
            onPress={() => onValueChange(option.value)}
          >
            <Text
              style={[
                styles.chipText,
                selectedValue === option.value && styles.selectedChipText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    color: "#333",
  },
  selectedValueButton: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#f5f5f5",
    borderRadius: 6,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  selectedValueText: {
    fontSize: 15,
    color: "#444",
  },
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "85%",
    maxHeight: "70%",
    backgroundColor: "white",
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 16,
    color: red,
    textAlign: "center",
  },
  optionsList: {
    maxHeight: 300,
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  selectedOption: {
    backgroundColor: red,
  },
  optionText: {
    fontSize: 16,
    color: "#444",
  },
  selectedOptionText: {
    color: "white",
    fontWeight: "600",
  },
  closeButton: {
    marginTop: 20,
    padding: 14,
    backgroundColor: red,
    borderRadius: 8,
    alignItems: "center",
  },
  closeButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 16,
  },
  multiSelectButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    padding: 14,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    alignItems: "center",
    marginRight: 8,
  },
  cancelButtonText: {
    color: "#666",
    fontWeight: "600",
  },
  applyButton: {
    flex: 1,
    padding: 14,
    backgroundColor: red,
    borderRadius: 8,
    alignItems: "center",
    marginLeft: 8,
  },
  applyButtonText: {
    color: "white",
    fontWeight: "600",
  },

  // Chip styles
  chipContainer: {
    marginBottom: 16,
  },
  chipScrollView: {
    flexGrow: 0,
  },
  chip: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 4,
  },
  selectedChip: {
    backgroundColor: red,
  },
  chipText: {
    fontSize: 14,
    color: "#444",
  },
  selectedChipText: {
    color: "white",
    fontWeight: "500",
  },
});

export { CompactFilterPicker, ChipFilterGroup };