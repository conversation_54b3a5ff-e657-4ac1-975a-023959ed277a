import React, { useState, useRef } from "react";
import {
  Animated,
  Text,
  StyleSheet,
  View,
  ActivityIndicator,
  PanResponder,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { ToastContext } from "../../hooks/toast/useToast";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";

const TOAST_TYPES = {
  success: {
    icon: "checkmark-circle",
    color: "#4CAF50",
  },
  error: {
    icon: "close-circle",
    color: "#F44336",
  },
  info: {
    icon: "information-circle",
    color: "#2196F3",
  },
  loading: {
    color: "#2196F3", // Same blue as info
  },
};

export const ToastContainer = ({ children }) => {
  const [toast, setToast] = useState({ message: "", type: "info" });
  const [isLoading, setIsLoading] = useState(false);
  const slideAnim = useRef(new Animated.Value(-100)).current;
  const timeoutRef = useRef(null);
  const activeToastId = useRef(null);
  const insets = useSafeAreaInsets();

  // Function to hide toast with animation
  const hideToast = () => {
    Animated.timing(slideAnim, {
      toValue: -200,
      duration: 350,
      useNativeDriver: true,
    }).start(() => {
      setToast({ message: "", type: "info" });
      setIsLoading(false);
    });
  };

  // Create pan responder for swipe gesture
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !isLoading, // Disable when loading
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Only respond to upward movement and when not loading
        return gestureState.dy < 0 && !isLoading;
      },
      onPanResponderMove: (_, gestureState) => {
        // Only allow upward movement (negative values)
        if (gestureState.dy < 0) {
          slideAnim.setValue(-10 + gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        // If swiped up with enough velocity or distance, dismiss the toast
        if (gestureState.vy < -0.3 || gestureState.dy < -50) {
          hideToast();
        } else {
          // Otherwise, snap back to original position
          Animated.spring(slideAnim, {
            toValue: -10,
            useNativeDriver: true,
            friction: 6,
          }).start();
        }
      },
    })
  ).current;

  // Core toast display function
  const showToastBase = (message, type = "info", loading = false) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    const toastId = Date.now().toString();
    activeToastId.current = toastId;

    // If toast isn't visible yet, animate it in
    if (!toast.message) {
      slideAnim.setValue(-100);
      Animated.timing(slideAnim, {
        toValue: -10,
        duration: 180,
        useNativeDriver: true,
      }).start();
    }

    setToast({ message, type });
    setIsLoading(loading);

    // Only set auto-dismiss timeout if not in loading state
    if (!loading) {
      timeoutRef.current = setTimeout(() => {
        hideToast();
      }, 2500);
    }

    return toastId;
  };

  // Original showToast function (for backward compatibility)
  const showToast = (message, type = "info") => {
    return showToastBase(message, type, false);
  };

  // Show loading toast that persists until updated
  const showLoadingToast = (message) => {
    return showToastBase(message, "info", true);
  };

  // Update an existing toast
  const updateToast = (toastId, message, type = "info", hide = false) => {
    // Only update if this is the current toast
    if (toastId !== activeToastId.current) return;

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Update the toast content
    setToast({ message, type });
    setIsLoading(false);

    if (hide) {
      hideToast();
    } else {
      // Set regular timeout for auto-dismiss
      timeoutRef.current = setTimeout(() => {
        hideToast();
      }, 2500);
    }
  };

  // Create function with methods (allows both direct calls and method access)
  const toastFunction = (message, type) => showToast(message, type);
  toastFunction.loading = showLoadingToast;
  toastFunction.update = updateToast;

  return (
    <ToastContext.Provider value={toastFunction}>
      {children}
      <Animated.View
        style={[
          styles.container,
          {
            transform: [{ translateY: slideAnim }],
            top: insets.top + 10,
            opacity: toast.message ? 1 : 0,
          },
        ]}
        {...(isLoading ? {} : panResponder.panHandlers)}
        pointerEvents={toast.message ? "auto" : "none"}
      >
        <View style={styles.shadowWrapper}>
          <View style={styles.shadowContainer}>
            <BlurView intensity={50} tint="light" style={styles.blurContainer}>
              <View style={styles.contentContainer}>
                <View style={styles.textWrapper}>
                  <Text style={styles.text} numberOfLines={10}>
                    {toast.message}
                  </Text>
                </View>
                <View style={styles.iconContainer}>
                  {isLoading ? (
                    <ActivityIndicator
                      size="small"
                      color={TOAST_TYPES.loading.color}
                    />
                  ) : (
                    <Ionicons
                      name={TOAST_TYPES[toast.type].icon}
                      size={24}
                      color={TOAST_TYPES[toast.type].color}
                    />
                  )}
                </View>
              </View>
            </BlurView>
          </View>
        </View>
        <View style={styles.swipeView} />
      </Animated.View>
    </ToastContext.Provider>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    alignSelf: "center",
    width: "95%",
    zIndex: 99999,
  },
  shadowWrapper: {
    borderRadius: 12,
    shadowColor: "rgba(0, 0, 0, 0.9)",
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.4,
    shadowRadius: 5,
    elevation: 5,
  },
  shadowContainer: {
    borderRadius: 12,
    overflow: "hidden",
  },
  blurContainer: {
    paddingHorizontal: 13,
    paddingVertical: 22,
    borderRadius: 12,
    backgroundColor: "rgba(246, 246, 246, 0.38)",
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    position: "relative",
  },
  textWrapper: {
    flex: 1,
    alignItems: "center",
    paddingRight: 34,
    paddingLeft: 34, // Reserve space for the icon
  },
  iconContainer: {
    width: 24,
    height: 24,
    position: "absolute",
    right: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  text: {
    color: "rgba(0,0,0,0.8)",
    textAlign: "center",
    fontSize: 15,
    fontWeight: "500",
  },
  swipeView: {
    height: 4,
    width: 50,
    alignSelf: "center",
    backgroundColor: "rgba(0,0,0,0.1)",
    borderRadius: 5,
    position: "absolute",
    bottom: 7,
  },
});
