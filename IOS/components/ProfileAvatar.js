// components/ProfileAvatar.js
import React from "react";
import { View, Text, Image, StyleSheet, TouchableOpacity } from "react-native";
import { Feather, FontAwesome5, MaterialIcons } from "@expo/vector-icons";
import { red } from "../screens/colors";

const ProfileAvatar = ({
  name,
  profileImage,
  size,
  profileColor,
  onChangeColor,
  onAddImage,
  onDeleteImage,
}) => {
  return (
    <View style={styles.avatarContainer}>
      <View style={styles.avatarWrapper}>
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={onAddImage}
          style={[
            styles.avatar,
            {
              backgroundColor: profileImage ? "transparent" : profileColor,
              width: size,
              height: size,
              borderRadius: size / 2,
            },
          ]}
        >
          {profileImage ? (
            <Image
              source={{ uri: profileImage }}
              style={{ width: size, height: size, borderRadius: size / 2 }}
            />
          ) : (
            <>
              <Text style={[styles.avatarText, { fontSize: size / 2.5 }]}>
                {name ? name.charAt(0).toUpperCase() : ""}
              </Text>
              {!name && (
                <View style={styles.cameraIconOverlay}>
                  <Feather
                    name="camera"
                    size={size / 4}
                    color="rgba(255,255,255,0.9)"
                  />
                </View>
              )}
            </>
          )}
        </TouchableOpacity>

        {!profileImage ? (
          <TouchableOpacity
            style={[
              styles.refreshButton,
              {
                right: size * 0.02,
                bottom: size * 0.01,
                width: size * 0.3,
                height: size * 0.3,
                borderRadius: size * 0.3,
              },
            ]}
            onPress={onChangeColor}
            activeOpacity={0.8}
          >
            <FontAwesome5
              name="sync-alt"
              size={size * 0.12}
              color="rgb(105, 103, 103)"
            />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[
              styles.refreshButton,
              {
                right: size * 0.02,
                bottom: size * 0.01,
                width: size * 0.3,
                height: size * 0.3,
                borderRadius: size * 0.3,
              },
            ]}
            onPress={onDeleteImage}
            activeOpacity={0.8}
          >
            <MaterialIcons
              name="delete-outline"
              size={size * 0.18}
              color={red}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  avatarContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 25,
  },
  avatarWrapper: {
    position: "relative",
  },
  avatar: {
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    borderColor: "rgba(0,0,0,0.07)",
    borderWidth: 1,
  },
  avatarText: {
    color: "white",
    fontWeight: "bold",
  },
  cameraIconOverlay: {
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
  },
  refreshButton: {
    position: "absolute",

    justifyContent: "center",
    alignItems: "center",
    // borderWidth: 2,
    // borderColor: "white",
    backgroundColor: "rgb(245, 243, 243)",
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.06)",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
});

export default ProfileAvatar;
