// components/common/CreateGameFab.js
import React from 'react';
import { Pressable, StyleSheet, Alert } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";

const CreateGameFab = ({ navigation, insets, language, color }) => {
  const handlePress = () => {
    // Navigate to CreateGame or equivalent
    try {
      // Try different screens that might exist in your navigation stack
      let navigated = false;

      try {
        navigation.navigate("MyGamesStack");
        navigated = true;
      } catch (e) {
        console.log("Primary navigation failed:", e.message);
      }

      if (!navigated) {
        try {
          navigation.navigate("GameCreation");
          navigated = true;
        } catch (e) {
          console.log("Secondary navigation failed:", e.message);
        }
      }

      // If still not navigated, try the last option
      if (!navigated) {
        navigation.navigate("Home", { screen: "CreateGame" });
      }
    } catch (error) {
      console.error("All navigation attempts failed:", error);
      Alert.alert(
        "Navigation Error",
        "Could not navigate to game creation. Please check app configuration."
      );
    }
  };

  return (
    <Pressable
      style={({ pressed }) => [
        styles.fab,
        pressed && styles.fabPressed,
        { bottom: insets.bottom, backgroundColor: color },
      ]}
      onPress={handlePress}
      accessibilityLabel={language?.createGame || "Create a new game"}
      accessibilityRole="button"
    >
      <MaterialIcons name="add" size={28} color="white" />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  fab: {
    position: "absolute",
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  fabPressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
});

export default CreateGameFab;