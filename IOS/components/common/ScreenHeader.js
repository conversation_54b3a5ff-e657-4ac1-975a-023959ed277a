// components/common/ScreenHeader.js
import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { Feather, Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import ProfileImage from "../../assets/ProfileImage";

// import UpdateInfoBox from "./UpdateInfoBox";
import Constants from "expo-constants";

const ScreenHeader = ({ title }) => {
  const navigation = useNavigation();
  const { language } = useSelector((state) => state.language);
  const user = useSelector((state) => state.socials.userData);
  const notifications = useSelector((state) => state.socials.notifications);
  const chats = useSelector((state) => state.socials.chats);

  const currentVersion = Constants.expoConfig?.version;
  const versionInfo = useSelector((state) => state.appControl.versionInfo);

  const isBehind =
    versionInfo?.latestVersion &&
    currentVersion &&
    versionInfo.latestVersion !== currentVersion;

  // Calculate chat notifications count
  const chatNotifsCount = React.useMemo(() => {
    return chats.reduce((total, chat) => {
      if (!chat) return total;
      return total + (chat.unreadCount || 0);
    }, 0);
  }, [chats]);

  return (
    <>
      <View style={styles.container}>
        <View>
          <Text style={styles.title}>{title}</Text>
        </View>

        <View style={styles.iconsContainer}>
          <Pressable
            onPress={() => {
              navigation.navigate("NotificationsSc", { language: language });
            }}
            style={styles.notificationButton}
          >
            <Feather name="bell" size={27} color="black" />
            {notifications.length > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.badgeText}>{notifications.length}</Text>
              </View>
            )}
          </Pressable>
          <Pressable
            onPress={() => {
              navigation.navigate("ChatsList", { language: language });
            }}
            style={styles.chatButton}
          >
            <Ionicons
              name="chatbubble-ellipses-outline"
              size={28}
              color="black"
            />
            {chatNotifsCount > 0 && (
              <View style={styles.chatBadge}>
                <Text style={styles.badgeText}>{chatNotifsCount}</Text>
              </View>
            )}
          </Pressable>
          <Pressable
            onPress={() => {
              navigation.navigate("Profile");
            }}
            style={{ marginRight: 10, paddingBottom: 3, paddingHorizontal: 5 }}
          >
            <ProfileImage
              profileImageRef={user.profileImageRef}
              profileColor={user.profileColor}
              name={user.name}
              size={38}
            />
          </Pressable>
        </View>
      </View>
      {/* {isBehind && (
        <UpdateInfoBox
          message={versionInfo?.updateMessage || "Update available"}
        />
      )} */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 45,
    // backgroundColor: "yellow",
    alignSelf: "center",
    width: "97%",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontFamily: "Roboto",
    fontWeight: "700",
    fontSize: 25,
    color: "rgba(0,0,0,0.87)",
    paddingLeft: 15,
    paddingTop: 7,
  },
  iconsContainer: {
    flexDirection: "row",
    paddingTop: 4,
    paddingRight: 2,
    alignItems: "center",
  },
  notificationButton: {
    paddingHorizontal: 13,
    paddingTop: 7,
    paddingBottom: 5,
    alignItems: "center",
    justifyContent: "center",
    alignContent: "center",
  },
  notificationBadge: {
    backgroundColor: "red", // Using red from the original code
    width: 18,
    height: 18,
    borderRadius: 20,
    position: "absolute",
    top: 2,
    right: 6,
  },
  chatButton: {
    paddingHorizontal: 13,
    marginRight: 4,
    paddingTop: 5,
    paddingBottom: 5,
    alignItems: "center",
    justifyContent: "center",
  },
  chatBadge: {
    backgroundColor: "red", // Using red from the original code
    width: 18,
    height: 18,
    borderRadius: 20,
    position: "absolute",
    top: 3,
    right: 3,
  },
  badgeText: {
    color: "white",
    fontWeight: "600",
    alignSelf: "center",
    fontSize: 13,
    marginTop: 1,
    marginLeft: 0.5,
  },
});

export default ScreenHeader;
