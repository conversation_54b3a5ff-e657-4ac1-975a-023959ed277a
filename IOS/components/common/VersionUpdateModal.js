// VersionUpdateModal.js
import React, { useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Dimensions,
  Modal,
  Linking,
  StatusBar,
  SafeAreaView,
} from "react-native";
import { Ionicons, FontAwesome6 } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { LinearGradient } from "expo-linear-gradient";
import { red } from "../../screens/colors";
import Fontisto from "@expo/vector-icons/Fontisto";
import { useSelector } from "react-redux";
import { EN, CZ } from "../../assets/strings"; // Make sure to import your language files

// Get screen dimensions
const { width } = Dimensions.get("window");

// Helper function to properly compare version strings
const compareVersions = (version1, version2) => {
  try {
    if (!version1 || !version2) return false;

    // Split versions by periods and convert to numbers
    const v1Parts = version1.split(".").map(Number);
    const v2Parts = version2.split(".").map(Number);

    // Compare each part of the version numbers
    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      // If a part doesn't exist, treat it as 0
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      // If parts differ, we have our answer
      if (v1Part < v2Part) return true; // v1 is less than v2 (update needed)
      if (v1Part > v2Part) return false; // v1 is greater than v2 (no update needed)
    }

    // Versions are identical
    return false;
  } catch (error) {
    console.error("Version comparison error:", error);
    return false; // Default to non-mandatory if there's an error
  }
};

const VersionUpdateModal = ({ isVisible, onClose, versionInfo }) => {
  const user = useSelector((state) => state.socials.userData);
  const userLanguage = (user?.language || "EN").toUpperCase();

  const strings = userLanguage === "CZ" ? CZ : EN;

  const currentVersion = versionInfo?.currentVersion || "0.0.0";
  const minVersion = versionInfo?.minVersion || "0.0.0";
  const latestVersion = versionInfo?.latestVersion || "0.0.0";

  useEffect(() => {}, [versionInfo.latestVersion, versionInfo.minVersion]);

  const isMandatory = compareVersions(currentVersion, minVersion);

  const featuresKey = `features${userLanguage}`;
  const features = Array.isArray(versionInfo?.[featuresKey])
    ? versionInfo[featuresKey]
    : [];

  const updateMessageKey = `updateMessage${userLanguage}`;
  const updateMessage = versionInfo?.[updateMessageKey] || "";

  const mandatoryMessageKey = `mandatoryUpdateMessage${userLanguage}`;
  const mandatoryMessage =
    versionInfo?.[mandatoryMessageKey] || strings.mandatoryUpdateRequired;

  const handleUpdate = () => {
    try {
      // Use storeLink from Firestore based on platform
      const storeUrl =
        Platform.OS === "ios"
          ? (Array.isArray(versionInfo?.storeLink) &&
              versionInfo.storeLink[0]) ||
            "https://apps.apple.com/cz/app/lets-hoop/id6451425446?l=cs"
          : (Array.isArray(versionInfo?.storeLink) &&
              versionInfo.storeLink[1]) ||
            "https://play.google.com/store/apps/details?id=com.matyasnemec.letshoop";

      Linking.openURL(storeUrl);
    } catch (error) {
      console.error("Failed to open store link:", error);
    }
  };

  if (!isVisible) return null;

  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <StatusBar translucent backgroundColor="transparent" />

      {/* Full screen blur view */}
      <BlurView intensity={10} tint="light" style={styles.fullScreenBlur}>
        <View style={styles.container}>
          {/* Bottom sheet that will expand upward based on content */}
          <View style={styles.bottomSheetContainer}>
            {/* Gradient fade at top */}
            <LinearGradient
              colors={["rgba(255,255,255,0)", "rgba(255,255,255,1)"]}
              style={styles.fadeGradient}
            />

            {/* White card with content */}
            <SafeAreaView style={styles.whiteCard}>
              <View style={styles.contentContainer}>
                {/* Title */}
                <Text style={styles.title}>{strings.newVersionAvailable}</Text>

                {/* Version badge */}
                <View style={styles.versionBadge}>
                  <Text style={styles.versionNumber}>{latestVersion}</Text>
                </View>

                {/* Feature bullets */}
                <View style={styles.featureList}>
                  {features && features.length > 0 ? (
                    features.map((feature, index) => (
                      <View key={index} style={styles.featureItem}>
                        {/* <Fontisto
                          name="star"
                          size={18}
                          color={red}
                          style={styles.featureIcon}
                        /> */}
                        <FontAwesome6
                          name="circle-check"
                          size={16}
                          color={red}
                          style={styles.featureIcon}
                        />
                        {/* <Ionicons
                          name="checkmark-circle"
                          size={18}
                          color={red}
                          style={styles.featureIcon}
                        /> */}
                        <Text style={styles.featureText}>{feature}</Text>
                      </View>
                    ))
                  ) : (
                    // Default features if none provided
                    <>
                      <View style={styles.featureItem}>
                        <Ionicons
                          name="checkmark-circle"
                          size={18}
                          color={red}
                          style={styles.featureIcon}
                        />
                        <Text style={styles.featureText}>
                          {strings.bugFixes}
                        </Text>
                      </View>
                      <View style={styles.featureItem}>
                        <Ionicons
                          name="checkmark-circle"
                          size={18}
                          color={red}
                          style={styles.featureIcon}
                        />
                        <Text style={styles.featureText}>
                          {strings.enhancedUI}
                        </Text>
                      </View>
                    </>
                  )}
                </View>

                {/* Update Message - shown if provided */}
                {updateMessage && updateMessage.trim() !== "" && (
                  <Text style={styles.updateMessage}>{updateMessage}</Text>
                )}

                {/* Mandatory update notice - based on minVersion */}
                {isMandatory && (
                  <View style={styles.mandatoryContainer}>
                    <Ionicons
                      name="information-circle"
                      size={18}
                      color="#FF9800"
                      style={styles.mandatoryIcon}
                    />
                    <Text style={styles.mandatoryText}>{mandatoryMessage}</Text>
                  </View>
                )}

                {/* Buttons */}
                <View style={styles.buttonContainer}>
                  {/* Update button with gradient */}
                  <TouchableOpacity
                    style={styles.updateButtonWrapper}
                    onPress={handleUpdate}
                    activeOpacity={0.8}
                  >
                    <LinearGradient
                      colors={[red, red]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                      style={styles.updateButtonGradient}
                    >
                      <Text style={styles.updateButtonText}>
                        {strings.updateNow}
                      </Text>
                    </LinearGradient>
                  </TouchableOpacity>

                  {/* Later text button - only if update is not mandatory */}
                  {!isMandatory && (
                    <TouchableOpacity
                      onPress={onClose}
                      style={styles.laterButton}
                      activeOpacity={0.6}
                    >
                      <Text style={styles.laterButtonText}>
                        {strings.later}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            </SafeAreaView>
          </View>
        </View>
      </BlurView>
    </Modal>
  );

  return isVisible ? (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={[styles.container, { backgroundColor: "rgba(0,0,0,0.5)" }]}>
        <View style={[styles.whiteCard, { margin: 20, borderRadius: 10 }]}>
          <View style={styles.contentContainer}>
            <Text style={styles.title}>Update Available</Text>
            <TouchableOpacity
              style={[styles.updateButtonWrapper, { backgroundColor: red }]}
              onPress={onClose}
            >
              <Text style={styles.updateButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  ) : null;
};

const styles = StyleSheet.create({
  fullScreenBlur: {
    flex: 1,
  },
  container: {
    flex: 1,
    justifyContent: "flex-end",
  },
  bottomSheetContainer: {
    position: "relative",
  },
  fadeGradient: {
    height: 100,
    position: "absolute",
    left: 0,
    right: 0,
    top: -100,
    zIndex: 1,
  },
  whiteCard: {
    backgroundColor: "white",
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  contentContainer: {
    paddingTop: 30,
    paddingHorizontal: 24,
    // paddingBottom: Platform.OS === "ios" ? 34 : 24,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#333",
    marginBottom: 12,
    textAlign: "center",
    marginTop: 15,
  },
  versionBadge: {
    backgroundColor: `${red}15`,
    paddingHorizontal: 15,
    paddingVertical: 6,
    borderRadius: 20,
    marginBottom: 25,
  },
  versionNumber: {
    fontSize: 16,
    fontWeight: "600",
    color: red,
  },
  featureList: {
    alignSelf: "stretch",
    marginBottom: 15,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  featureIcon: {
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: "#444",
    flex: 1,
  },
  updateMessage: {
    color: "#777",
    fontSize: 15,
    textAlign: "center",
    marginBottom: 25,
    fontStyle: "italic",
    alignSelf: "stretch",
  },
  mandatoryContainer: {
    flexDirection: "row",
    backgroundColor: "rgba(255, 152, 0, 0.1)",
    padding: 12,
    borderRadius: 10,
    marginBottom: 25,
    width: "100%",
  },
  mandatoryIcon: {
    marginRight: 12,
  },
  mandatoryText: {
    fontSize: 15,
    color: "#FF9800",
    flex: 1,
  },
  buttonContainer: {
    width: "100%",
    alignItems: "center",
  },
  updateButtonWrapper: {
    width: "100%",
    borderRadius: 30,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
    marginBottom: 16,
  },
  updateButtonGradient: {
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  updateButtonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "700",
  },
  laterButton: {
    padding: 10,
  },
  laterButtonText: {
    color: "#666",
    fontSize: 15,
    fontWeight: "500",
    textAlign: "center",
  },
});

export default VersionUpdateModal;
