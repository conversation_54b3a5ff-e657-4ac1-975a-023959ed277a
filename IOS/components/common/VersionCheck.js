// VersionCheck.js
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Constants from "expo-constants";
import VersionUpdateModal from "./VersionUpdateModal";
import { EN, CZ } from "../../assets/strings";

const VersionCheck = ({ children }) => {
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [currentVersion, setCurrentVersion] = useState(null);
  const versionInfo = useSelector((state) => state.appControl.versionInfo);

  const { language } = useSelector((state) => state.language);

  // Get current app version on component mount
  useEffect(() => {
    setCurrentVersion(Constants.expoConfig?.version);
  }, []);

  // Check if there's a new version available whenever versionInfo changes
  useEffect(() => {
    if (versionInfo && currentVersion) {
      const shouldUpdate =
        versionInfo.latestVersion &&
        currentVersion &&
        versionInfo.latestVersion !== currentVersion;

      // Add a small delay to ensure the app is fully loaded
      if (shouldUpdate) {
        // const timer = setTimeout(() => {
        setShowUpdateModal(true);
        // }, 300);
        return () => clearTimeout(timer);
      }
    }
  }, [versionInfo, currentVersion]);

  const handleCloseModal = () => {
    setShowUpdateModal(false);
  };

  // Prepare enhanced version info with current version
  const enhancedVersionInfo = {
    ...versionInfo,
    currentVersion: currentVersion,
  };

  return (
    <>
      {children}
      <VersionUpdateModal
        isVisible={showUpdateModal}
        onClose={handleCloseModal}
        versionInfo={enhancedVersionInfo}
        language={language}
      />
    </>
  );
};

export default VersionCheck;
