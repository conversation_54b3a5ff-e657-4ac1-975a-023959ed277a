import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Linking,
  Platform,
} from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { red } from "../../screens/colors";
import { LinearGradient } from "expo-linear-gradient";

const UpdateInfoBox = ({ message, storeLink }) => {
  const handlePress = () => {
    const url = Platform.OS === "ios" ? storeLink?.ios : storeLink?.android;
    if (url) Linking.openURL(url);
  };

  return (
    <LinearGradient
      colors={["#fff5f5", "#ffeaea"]}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
    >
      <View style={styles.row}>
        <AntDesign
          name="infocirlceo"
          size={18}
          color={red}
          style={styles.icon}
        />
        <Text style={styles.text} numberOfLines={2}>
          {message || "New version available"}
        </Text>
        <TouchableOpacity onPress={handlePress} style={styles.iconButton}>
          <AntDesign name="arrowright" size={18} color="white" />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 15,
    marginTop: 10,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: `${red}20`,
    paddingHorizontal: 10,
    paddingVertical: 8,
    backgroundColor: `${red}08`,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  icon: {
    marginRight: 8,
  },
  text: {
    flex: 1,
    fontSize: 13,
    fontWeight: "500",
    color: red,
  },
  iconButton: {
    backgroundColor: red,
    padding: 6,
    borderRadius: 8,
    marginLeft: 8,
  },
});

export default UpdateInfoBox;
