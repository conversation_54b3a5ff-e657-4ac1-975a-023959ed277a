// File: components/common/LoadingState.js
import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { red } from '../../screens/colors';

/**
 * Reusable loading state component
 */
const LoadingState = ({ message }) => {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator color={red} size="large" />
      <Text style={styles.loadingText}>
        {message}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    marginTop: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
});

export default React.memo(LoadingState);