import React from "react";
import { View, Text, Button, StyleSheet } from "react-native";
import ErrorBoundary from "react-native-error-boundary";
import analytics from "@react-native-firebase/analytics";
import firestore from "@react-native-firebase/firestore";
// import DeviceInfo from "react-native-device-info";

const CustomFallback = ({ error, resetError }) => {
  // Log error to Firebase Analytics
  analytics().logEvent("app_error", {
    error_message: error.message,
    error_stack: error.stack,
  });

  // Collect device information
  const deviceInfo = {
    // brand: DeviceInfo.getBrand(),
    // model: DeviceInfo.getModel(),
    // systemName: DeviceInfo.getSystemName(),
    // systemVersion: DeviceInfo.getSystemVersion(),
    // appVersion: DeviceInfo.getVersion(),
  };

  // Save error details to Firestore
  firestore().collection("error_reports").add({
    error_message: error.message,
    error_stack: error.stack,
    device_info: deviceInfo,
    timestamp: firestore.FieldValue.serverTimestamp(),
  });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Oops! Something went wrong.</Text>
      <Text style={styles.errorMessage}>{error.toString()}</Text>
      <Button onPress={resetError} title="Try Again" />
    </View>
  );
};

const AppWithErrorBoundary = ({ children }) => (
  <ErrorBoundary FallbackComponent={CustomFallback}>{children}</ErrorBoundary>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
    backgroundColor: "#f8d7da",
    zIndex: 1000000,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#721c24",
    marginBottom: 16,
  },
  errorMessage: {
    fontSize: 16,
    color: "#721c24",
    marginBottom: 16,
    textAlign: "center",
  },
});

export default AppWithErrorBoundary;
