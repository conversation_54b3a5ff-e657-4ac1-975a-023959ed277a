// components/LanguageToggle.js
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import * as Haptics from "expo-haptics";
import { red } from "../screens/colors";

const LanguageToggle = ({ language, setLanguage }) => {
  return (
    <View style={styles.languageToggleContainer}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          if (language === "CZ") return;
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setLanguage("CZ");
        }}
        style={[
          styles.languageButton,
          language === "CZ" && styles.languageButtonActive,
        ]}
      >
        <Text
          style={[
            styles.languageText,
            language === "CZ" && styles.languageTextActive,
          ]}
        >
          CZ
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          if (language === "EN") return;
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setLanguage("EN");
        }}
        style={[
          styles.languageButton,
          language === "EN" && styles.languageButtonActive,
        ]}
      >
        <Text
          style={[
            styles.languageText,
            language === "EN" && styles.languageTextActive,
          ]}
        >
          EN
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  languageToggleContainer: {
    flexDirection: "row",
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 25,
    padding: 3,
    width: 100,
    height: 36,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  languageButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 22,
  },
  languageButtonActive: {
    backgroundColor: red,
    shadowColor: red,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  languageText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#888",
  },
  languageTextActive: {
    color: "white",
  },
});

export default LanguageToggle;
