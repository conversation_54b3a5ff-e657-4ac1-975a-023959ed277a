// File: components/CustomComponents.js
import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, TouchableHighlight, ActivityIndicator } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { red } from "../screens/colors";

/**
 * Custom header component for AddCourt screens
 */
export const ScreenHeader = ({ title, onBack, rightComponent }) => {
  return (
    <View style={styles.header}>
      <TouchableHighlight
        style={styles.backButton}
        onPress={onBack}
        underlayColor="rgba(0, 0, 0, 0.05)"
      >
        <AntDesign name="close" size={22} color={red} />
      </TouchableHighlight>
      
      <Text style={styles.headerTitle}>{title}</Text>
      
      <View style={styles.headerRight}>
        {rightComponent}
      </View>
    </View>
  );
};

/**
 * Map screen header component
 */
export const MapHeader = ({ 
  title, 
  onBack, 
  onSubmit, 
  uploading = false, 
  submitText = "Submit"
}) => {
  return (
    <View style={styles.mapHeader}>
      <TouchableHighlight
        style={styles.backButton}
        onPress={onBack}
        underlayColor="rgba(0, 0, 0, 0.1)"
        disabled={uploading}
      >
        <AntDesign name="arrowleft" size={24} color="white" />
      </TouchableHighlight>
      
      <Text style={styles.headerTitle}>{title}</Text>
      
      {uploading ? (
        <ActivityIndicator color="white" style={styles.uploadingIndicator} />
      ) : (
        <TouchableOpacity
          style={styles.submitButton}
          onPress={onSubmit}
          disabled={uploading}
        >
          <Text style={styles.submitButtonText}>{submitText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

/**
 * Section selector component for tabbed navigation
 */
export const SectionSelector = ({ activeSection, sections, onSectionChange }) => {
  return (
    <View style={styles.sectionSelector}>
      {sections.map((section) => (
        <TouchableOpacity 
          key={section.id}
          style={[
            styles.sectionButton, 
            activeSection === section.id && styles.activeSectionButton
          ]}
          onPress={() => onSectionChange(section.id)}
        >
          {/* Only show the icon, no text */}
          {section.icon}
          
          {/* Add a small indicator dot for active section */}
          {activeSection === section.id && (
            <View style={styles.activeDot} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );
};

/**
 * Custom switch component for multiple options
 */
export const CustomSwitch = React.memo(({
  roundCorner = true,
  options,
  selectedValue,
  onValueChange,
  selectionColor = red,
}) => {
  return (
    <View style={styles.switchContainer}>
      <View
        style={[
          styles.switchWrapper,
          { borderRadius: roundCorner ? 10 : 0, borderColor: selectionColor }
        ]}
      >
        {options.map((option, index) => (
          <TouchableOpacity
            key={index}
            activeOpacity={0.7}
            onPress={() => onValueChange(option.value)}
            style={[
              styles.switchOption,
              {
                backgroundColor: selectedValue === option.value ? selectionColor : "white",
                borderRadius: roundCorner ? 8 : 0,
              }
            ]}
          >
            <Text
              style={{
                color: selectedValue === option.value ? "white" : selectionColor,
                fontWeight: "500",
                fontSize: 13,
                textAlign: "center",
              }}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
});

/**
 * Feature item component for displaying court features
 */
export const FeatureItem = ({ icon, label, control }) => {
  return (
    <View style={styles.featureItem}>
      <View style={styles.featureIconContainer}>
        {icon}
      </View>
      <Text style={styles.featureLabel}>{label}</Text>
      <View style={styles.featureControl}>
        {control}
      </View>
    </View>
  );
};

/**
 * Feature group component for grouping court features
 */
export const FeatureGroup = ({ title, children }) => {
  return (
    <View style={styles.featureGroup}>
      <Text style={styles.featureGroupTitle}>{title}</Text>
      {children}
    </View>
  );
};

/**
 * Continue button component
 */
export const ContinueButton = ({ onPress, disabled, text = "Continue" }) => {
  return (
    <View style={styles.continueButtonContainer}>
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        style={[
          styles.continueButton,
          { backgroundColor: !disabled ? red : "rgba(200,200,200,1)" }
        ]}
        activeOpacity={disabled ? 1 : 0.7}
      >
        <Text style={styles.continueButtonText}>{text}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  // Header styles
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
    backgroundColor: 'white',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: red,
    textAlign: "center",
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerRight: {
    width: 40,
  },
  
  // Map header
  mapHeader: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: red,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 15,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    zIndex: 10,
  },
  submitButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 8,
  },
  submitButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 15,
  },
  uploadingIndicator: {
    marginRight: 8,
  },
  
  // Section selector styles
  sectionSelector: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  sectionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: red,
  },
  activeSectionButton: {
    backgroundColor: red,
  },
  sectionButtonText: {
    marginLeft: 5,
    color: red,
    fontWeight: "600",
    fontSize: 14,
  },
  activeSectionButtonText: {
    color: "white",
  },
  
  // Switch component styles  
  switchWrapper: {
    height: 40,
    width: "100%",
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "center",
    padding: 2,
  },
  switchOption: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
  },
  
  // Feature styles
  featureGroup: {
    marginBottom: 20,
    backgroundColor: "rgba(255,255,255,0.9)",
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  featureGroupTitle: {
    fontSize: 17,
    fontWeight: "700",
    marginBottom: 15,
    color: "#333",
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
  },
  featureIconContainer: {
    width: 30,
    alignItems: "center",
    marginRight: 10,
  },
  featureLabel: {
    flex: 1,
    fontSize: 15,
    color: "#333",
  },
  featureControl: {
    width: "40%",
  },
  
  // Continue button styles
  continueButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: "white",
  },
  continueButton: {
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 10,
  },
  continueButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    height: 48,
    backgroundColor: 'white',
    borderRadius: 24,
    padding: 4,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
    minWidth: 200,
    alignSelf: 'center',
  },
  mapSwitchOption: {
    paddingHorizontal: 24,
    paddingVertical: 10,
    borderRadius: 20,
    marginHorizontal: 2,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: red,
  },
  mapSwitchOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: red,
  },
  activeDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'white',
    position: 'absolute',
    bottom: -10,
  },
});