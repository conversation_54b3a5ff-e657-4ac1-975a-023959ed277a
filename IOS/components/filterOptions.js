// filterOptions.js
// This module contains the configuration for all filter options
// Following the Single Responsibility Principle - it only handles filter definitions

/**
 * Get all filter options with proper translations
 * @param {Object} language - Translation object
 * @returns {Object} Object containing all filter options
 */
export const getFilterOptions = (language = {}) => {
    // Helper for translations
    const getText = (key, defaultText) => language[key] || defaultText;
    
    return {
      // Basic filters
      surfaceType: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("artificial", "Artificial"), value: 2 },
        { label: getText("concrete", "Concrete"), value: 3 },
        { label: getText("other", "Other"), value: 4 }
      ],
      
      indoorOutdoor: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("outdoor", "Outdoor"), value: 2 },
        { label: getText("indoor", "Indoor"), value: 3 }
      ],
      
      publicPrivate: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("public", "Public"), value: 2 },
        { label: getText("private", "Private"), value: 3 }
      ],
      
      // Yes/No filters - reused for multiple properties
      yesNoAll: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      // Court feature filters
      fullCourt: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      lighting: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      lines: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      baskets: [
        { label: getText("all", "All"), value: 1 },
        { label: "1", value: 2 },
        { label: "2", value: 3 },
        { label: "4+", value: 4 }
      ],
      
      maintenance: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("good", "Good"), value: 2 },
        { label: getText("average", "Average"), value: 3 },
        { label: getText("poor", "Poor"), value: 4 }
      ],
      
      // Amenities filters
      parking: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      seating: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      wheelchairAccessible: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      restrooms: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ],
      
      waterFountain: [
        { label: getText("all", "All"), value: 1 },
        { label: getText("yes", "Yes"), value: 2 },
        { label: getText("no", "No"), value: 3 }
      ]
    };
  };
  
  /**
   * Get filter categories with their contained filters
   * @returns {Object} Object containing filter categories configuration
   */
  export const getFilterCategories = () => {
    return {
      // Basic filters category
      basic: {
        labelKey: "basicFilters",
        defaultLabel: "Basic Filters",
        filters: [
          { key: "surfaceType", labelKey: "surfaceType", defaultLabel: "Surface Type" },
          { key: "indoorOutdoor", labelKey: "type", defaultLabel: "Type" },
          { key: "publicPrivate", labelKey: "access", defaultLabel: "Access" }
        ]
      },
      
      // Court features category
      features: {
        labelKey: "courtFeatures",
        defaultLabel: "Court Features",
        filters: [
          { key: "fullCourt", labelKey: "fullCourt", defaultLabel: "Full Court" },
          { key: "lines", labelKey: "lines", defaultLabel: "Court Lines" },
          { key: "lighting", labelKey: "lighting", defaultLabel: "Lighting" },
          { key: "baskets", labelKey: "baskets", defaultLabel: "Baskets" },
          { key: "maintenance", labelKey: "maintenance", defaultLabel: "Court Condition" }
        ]
      },
      
      // Amenities category
      amenities: {
        labelKey: "amenities",
        defaultLabel: "Amenities",
        filters: [
          { key: "parking", labelKey: "parking", defaultLabel: "Parking" },
          { key: "seating", labelKey: "seating", defaultLabel: "Seating" },
          { key: "wheelchairAccessible", labelKey: "wheelchairAccessible", defaultLabel: "Wheelchair Accessible" },
          { key: "restrooms", labelKey: "restrooms", defaultLabel: "Nearby Restrooms" },
          { key: "waterFountain", labelKey: "waterFountain", defaultLabel: "Water Fountain" }
        ]
      }
    };
  };