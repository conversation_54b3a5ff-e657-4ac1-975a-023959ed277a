// components/controls/RadiusSliderControl.js
import React from "react";
import { View, Text, StyleSheet } from "react-native";
import Slider from "@react-native-community/slider";
import { Ionicons } from "@expo/vector-icons";
import { MIN_RADIUS, MAX_RADIUS } from "../../utils/mapUtils";

const RadiusSliderControl = ({ 
  radius, 
  onRadiusChange, 
  onSlidingComplete,
  language,
  accentColor 
}) => {
  return (
    <View style={styles.sliderContainer}>
      <View style={styles.sliderInfoContainer}>
        <Ionicons name="locate" size={20} color={accentColor} />
        <Text style={styles.radiusText}>
          {language?.radius || "Radius"}: {Math.round(radius / 1000)} km
        </Text>
      </View>
      <Slider
        style={styles.slider}
        minimumValue={MIN_RADIUS}
        maximumValue={MAX_RADIUS}
        minimumTrackTintColor={accentColor}
        maximumTrackTintColor={"rgba(0,0,0,0.2)"}
        thumbTintColor={accentColor}
        value={radius}
        onValueChange={onRadiusChange}
        onSlidingComplete={onSlidingComplete}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  sliderContainer: {
    position: "absolute",
    bottom: 40,
    left: 20,
    right: 20,
    backgroundColor: "white",
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1,
  },
  sliderInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  radiusText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  slider: {
    width: "100%",
    height: 40,
  },
});

export default RadiusSliderControl;