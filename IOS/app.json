{"expo": {"name": "Let's Hoopd", "slug": "lets-hoop", "version": "2.0.0", "scheme": "letshoop", "orientation": "portrait", "icon": "./images/righticon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/newlogo2.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"googleServicesFile": "./GoogleService-Info.plist", "supportsTablet": false, "buildNumber": "2.0.0", "bundleIdentifier": "com.matyasnemec.letshoop", "usesAppleSignIn": true, "infoPlist": {"NSCameraUsageDescription": "This app needs access to the camera to take photos.", "NSPhotoLibraryUsageDescription": "This app needs access to the library to choose photos.", "CFBundleURLTypes": [{"CFBundleURLSchemes": ["com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n"]}, {"CFBundleURLSchemes": ["com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n"]}, {"CFBundleURLSchemes": ["com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n"]}, {"CFBundleURLSchemes": ["com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n"]}], "ITSAppUsesNonExemptEncryption": false}}, "android": {"googleServicesFile": "./google-services.json", "package": "com.matyasnemec.letshoop", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["@react-native-firebase/app", "@react-native-firebase/crashlytics", "expo-font", "expo-asset", "expo-apple-authentication", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}]], "extra": {"eas": {"projectId": "601fa968-e69a-4e71-9c37-bd4de7bd5f96"}}, "owner": "lerty", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/601fa968-e69a-4e71-9c37-bd4de7bd5f96"}}}