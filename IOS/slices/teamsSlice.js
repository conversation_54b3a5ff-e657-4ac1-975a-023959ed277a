// teamsSlice.js
import { createSlice } from "@reduxjs/toolkit";
import {
  collection,
  query,
  where,
  onSnapshot,
  orderBy,
  doc,
  getDoc,
  Timestamp,
} from "@react-native-firebase/firestore";
import { db } from "../config/firebase";

const teamsSlice = createSlice({
  name: "teams",
  initialState: {
    teams: [],
    trainingSessions: {},
    teamNotifications: {},
    isLoading: true,
    error: null,
  },
  reducers: {
    setTeams: (state, action) => {
      try {
        // Process timestamps for recurring sessions
        action.payload.map((team) => {
          if (team.recurringTrainingSessions) {
            team.recurringTrainingSessions.map((session) => {
              if (session.endTime) {
                session.endTime = session.endTime.toMillis();
              }
              if (session.startTime) {
                session.startTime = session.startTime.toMillis();
              }
            });
          }
          return team;
        });
      } catch (error) {
        console.warn(error);
      }

      // Create a Map of teams by ID to eliminate duplicates
      const teamsMap = new Map();

      // First add existing teams to the map (unless they're being replaced)
      state.teams.forEach((team) => {
        teamsMap.set(team.id, team);
      });

      // Then add or update with new teams from the action
      action.payload.forEach((team) => {
        teamsMap.set(team.id, team);
      });

      // Convert map back to array
      state.teams = Array.from(teamsMap.values());
      state.isLoading = false;
    },
    setLoadingTeams: (state, action) => {
      state.isLoading = action.payload;
    },
    setTeamsError: (state, action) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    updateTeamMemberData: (state, action) => {
      const { teamId, userId, userData } = action.payload;
      const team = state.teams.find((t) => t.id === teamId);
      if (team) {
        if (!team.membersData) team.membersData = [];
        const memberIndex = team.membersData.findIndex((m) => m.uid === userId);
        if (memberIndex !== -1) {
          team.membersData[memberIndex] = { ...userData, uid: userId };
        } else {
          team.membersData.push({ ...userData, uid: userId });
        }
      }
    },
    setTrainingSessions: (state, action) => {
      const { teamId, sessions } = action.payload;

      sessions.map((session) => {
        if (session.endTime) {
          session.endTime = session.endTime.toMillis();
        }
        if (session.startTime) {
          session.startTime = session.startTime.toMillis();
        }
        if (session.createdAt) {
          session.createdAt = session.createdAt.toMillis();
        }
        if (session.date) {
          session.date = session.date.toMillis();
        }

        return session;
      });

      state.trainingSessions[teamId] = sessions;
    },
    sessionCancelled: (state, action) => {
      const { teamId, sessionId, reason, cancelledBy, cancelledAt } =
        action.payload;

      if (state.trainingSessions[teamId]) {
        const sessionIndex = state.trainingSessions[teamId].findIndex(
          (s) => s.id === sessionId
        );

        if (sessionIndex !== -1) {
          state.trainingSessions[teamId][sessionIndex] = {
            ...state.trainingSessions[teamId][sessionIndex],
            isCancelled: true,
            cancelReason: reason,
            cancelledBy: cancelledBy,
            cancelledAt: cancelledAt,
          };
        }
      }
    },
    revertSessionCancelled: (state, action) => {
      const { teamId, sessionId } = action.payload;

      if (state.trainingSessions[teamId]) {
        const sessionIndex = state.trainingSessions[teamId].findIndex(
          (s) => s.id === sessionId
        );

        if (sessionIndex !== -1) {
          state.trainingSessions[teamId][sessionIndex] = {
            ...state.trainingSessions[teamId][sessionIndex],
            isCancelled: false,
            cancelReason: "",
            cancelledBy: null,
            cancelledAt: null,
          };
        }
      }
    },
    sessionRestored: (state, action) => {
      const { teamId, sessionId, restoredBy, restoredAt } = action.payload;

      if (state.trainingSessions[teamId]) {
        const sessionIndex = state.trainingSessions[teamId].findIndex(
          (s) => s.id === sessionId
        );

        if (sessionIndex !== -1) {
          state.trainingSessions[teamId][sessionIndex] = {
            ...state.trainingSessions[teamId][sessionIndex],
            isCancelled: false,
            cancelReason: null,
            cancelledBy: null,
            cancelledAt: null,
            restoredBy: restoredBy,
            restoredAt: restoredAt,
          };
        }
      }
    },
    revertSessionRestored: (state, action) => {
      const { teamId, sessionId } = action.payload;

      if (state.trainingSessions[teamId]) {
        const sessionIndex = state.trainingSessions[teamId].findIndex(
          (s) => s.id === sessionId
        );

        if (sessionIndex !== -1) {
          // Reset to cancelled state
          const session = state.trainingSessions[teamId][sessionIndex];

          // We don't have the original values, so we'll just set isCancelled back to true
          // and remove the restoration info
          state.trainingSessions[teamId][sessionIndex] = {
            ...session,
            isCancelled: true,
            restoredBy: null,
            restoredAt: null,
          };
        }
      }
    },
    updateLocationImage: (state, action) => {
      const { teamId, locationId, imageUrl } = action.payload;

      // Find the team in state
      const team = state.teams.find((t) => t.id === teamId);
      if (!team) return;

      // Find the location in the team's locations array
      const location = team.locations?.find(
        (loc) => loc.place_id === locationId
      );
      if (!location) return;

      // Update the image URL
      location.image = imageUrl;
    },
    deleteLocationImage: (state, action) => {
      const { teamId, locationId } = action.payload;

      // Find the team in state
      const team = state.teams.find((t) => t.id === teamId);
      if (!team) return;

      // Find the location in the team's locations array
      const location = team.locations?.find(
        (loc) => loc.place_id === locationId
      );
      if (!location) return;

      // Set the image to null
      location.image = null;
    },
    setMainLocationRedux: (state, action) => {
      const { teamId, locationId } = action.payload;

      // Find the team in state
      const team = state.teams.find((t) => t.id === teamId);
      if (!team) return;

      // Update the main location ID
      team.mainLocationId = locationId;
    },
    removeRecurringSession: (state, action) => {
      const { teamId, templateId } = action.payload;

      // Find the team in state
      const team = state.teams.find((t) => t.id === teamId);
      if (!team || !team.recurringTrainingSessions) return;

      // Filter out the session with matching templateId
      team.recurringTrainingSessions = team.recurringTrainingSessions.filter(
        (session) =>
          session.createdAt.toDate?.().getTime().toString() !== templateId
      );
    },
    removeTeam: (state, action) => {
      const { teamId } = action.payload;

      // Remove the team entirely from state
      state.teams = state.teams.filter((team) => team.id !== teamId);

      // Also clean up any associated training sessions
      if (state.trainingSessions[teamId]) {
        delete state.trainingSessions[teamId];
      }
    },
    setTeamNotifications: (state, action) => {
      const { teamId, notifications } = action.payload;

      // Process timestamps for notifications
      const processedNotifications = notifications.map((notification) => {
        if (notification.date) {
          return {
            ...notification,
            date:
              notification.date instanceof Timestamp
                ? notification.date.toMillis()
                : notification.date,
          };
        }
        return notification;
      });

      state.teamNotifications[teamId] = processedNotifications;
    },
    addTeamNotification: (state, action) => {
      const { teamId, notification } = action.payload;

      if (!state.teamNotifications[teamId]) {
        state.teamNotifications[teamId] = [];
      }

      // Process date timestamp
      const processedNotification = {
        ...notification,
        date:
          notification.date instanceof Timestamp
            ? notification.date.toMillis()
            : notification.date,
      };

      // Add to front of array (newest first)
      state.teamNotifications[teamId].unshift(processedNotification);
    },
  },
});

export const {
  setTeams,
  setLoadingTeams,
  setTeamsError,
  updateTeamMemberData,
  setTrainingSessions,
  sessionCancelled,
  revertSessionCancelled,
  sessionRestored,
  revertSessionRestored,
  updateLocationImage,
  deleteLocationImage,
  setMainLocationRedux,
  removeRecurringSession,
  removeTeam,
  setTeamNotifications,
  addTeamNotification,
} = teamsSlice.actions;

// Helper function to fetch user public data
const fetchUserPublicData = async (userId) => {
  const userPublicDocRef = doc(
    db,
    "users",
    userId,
    "public",
    `${userId}public`
  );

  const userPublicDocSnap = await getDoc(userPublicDocRef);
  if (userPublicDocSnap.exists) {
    return userPublicDocSnap.data();
  }
  return null;
};

// Training sessions listener
const startTeamTrainingSessionsListener = (teamId) => (dispatch) => {
  const sessionsCollection = collection(
    db,
    "teams",
    teamId,
    "trainingSessions"
  );
  const sessionsQuery = query(sessionsCollection, orderBy("date"));

  const unsubscribe = onSnapshot(sessionsQuery, (snapshot) => {
    const sessions = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
    dispatch(setTrainingSessions({ teamId, sessions }));
  });

  return unsubscribe;
};

// Team notifications listener
export const startTeamNotificationsListener = (teamId) => (dispatch) => {
  const notificationsCollection = collection(
    db,
    "teams",
    teamId,
    "notifications"
  );
  const notificationsQuery = query(
    notificationsCollection,
    orderBy("date", "desc")
  );

  const unsubscribe = onSnapshot(notificationsQuery, (snapshot) => {
    const notifications = snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    dispatch(setTeamNotifications({ teamId, notifications }));
  });

  return unsubscribe;
};

// Main teams listener
export const startTeamsListener = (auth) => (dispatch) => {
  dispatch(setLoadingTeams(true));

  const teamsCollection = collection(db, "teams");
  const teamsQuery = query(
    teamsCollection,
    where("members", "array-contains", auth.currentUser.uid)
  );

  let trainingSessionListeners = {};
  let teamNotificationListeners = {};

  const unsubscribe = onSnapshot(
    teamsQuery,
    async (snapshot) => {
      const teamsData = await Promise.all(
        snapshot.docs.map(async (doc) => {
          const teamData = doc.data();
          const team = {
            id: doc.id,
            ...teamData,
            createdAt:
              teamData.createdAt &&
              typeof teamData.createdAt.toMillis === "function"
                ? teamData.createdAt.toMillis()
                : teamData.createdAt,
            membersData: [],
          };

          // Fetch user data for members that need updating
          const membersToUpdate = teamData.refetch || team.members;
          for (const userId of membersToUpdate) {
            const userData = await fetchUserPublicData(userId);
            if (userData) {
              team.membersData.push({
                ...userData,
                uid: userId,
                date: userData.date.toMillis(),
              });
            }
          }

          // Set up training session listener for this team
          if (!trainingSessionListeners[team.id]) {
            trainingSessionListeners[team.id] = dispatch(
              startTeamTrainingSessionsListener(team.id)
            );
          }

          // Set up team notifications listener for this team
          if (!teamNotificationListeners[team.id]) {
            teamNotificationListeners[team.id] = dispatch(
              startTeamNotificationsListener(team.id)
            );
          }

          return team;
        })
      );

      dispatch(setTeams(teamsData));
      dispatch(setLoadingTeams(false));

      // Clean up unused listeners
      const currentTeamIds = teamsData.map((team) => team.id);

      // Clean training session listeners
      Object.keys(trainingSessionListeners).forEach((teamId) => {
        if (!currentTeamIds.includes(teamId)) {
          trainingSessionListeners[teamId]();
          delete trainingSessionListeners[teamId];
        }
      });

      // Clean team notifications listeners
      Object.keys(teamNotificationListeners).forEach((teamId) => {
        if (!currentTeamIds.includes(teamId)) {
          teamNotificationListeners[teamId]();
          delete teamNotificationListeners[teamId];
        }
      });
    },
    (error) => {
      console.error("Error fetching teams:", error);
      dispatch(setTeamsError(error.message));
    }
  );

  return () => {
    unsubscribe();
    // Clean up all listeners
    Object.values(trainingSessionListeners).forEach((unsubscribe) =>
      unsubscribe()
    );
    Object.values(teamNotificationListeners).forEach((unsubscribe) =>
      unsubscribe()
    );
  };
};

export default teamsSlice.reducer;
