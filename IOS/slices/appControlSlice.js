// appControlSlice.js
import { createSlice } from "@reduxjs/toolkit";
import { doc, onSnapshot } from "@react-native-firebase/firestore";
import { db } from "../config/firebase";

const appControlSlice = createSlice({
  name: "appControl",
  initialState: {
    versionInfo: null,
    isLoading: true,
  },
  reducers: {
    setVersionInfo: (state, action) => {
      state.versionInfo = action.payload;
      state.isLoading = false;
    },
  },
});

export const { setVersionInfo } = appControlSlice.actions;

export const startAppControlListener = () => (dispatch) => {
  const docRef = doc(db, "app-control", "global-config");

  const unsubscribe = onSnapshot(docRef, (docSnap) => {
    if (docSnap.exists) {
      dispatch(setVersionInfo(docSnap.data()));
    }
  });

  return unsubscribe;
};

export default appControlSlice.reducer;
