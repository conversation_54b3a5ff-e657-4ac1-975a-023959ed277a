// socialsSlice.js
import { createSlice } from "@reduxjs/toolkit";
import {
  collection,
  query,
  where,
  onSnapshot,
  orderBy,
  doc,
  getDoc,
  limit,
  startAfter,
  getDocs,
} from "@react-native-firebase/firestore";
import { db } from "../config/firebase";

const MAX_ACTIVE_CHATS = 3;

const socialsSlice = createSlice({
  name: "socials",
  initialState: {
    isLoadingUserData: true,
    isLoadingChats: true,
    isLoadingFriends: true,
    chats: [],
    notifications: [],
    friends: [],
    userData: null,
    activeChats: [],
    messages: {},
    chatListeners: {},
  },
  reducers: {
    setLoadingUserData: (state, action) => {
      state.isLoadingUserData = action.payload;
    },
    setLoadingChats: (state, action) => {
      state.isLoadingChats = action.payload;
    },
    setLoadingFriends: (state, action) => {
      state.isLoadingFriends = action.payload;
    },
    setChats: (state, action) => {
      action.payload.map((chat) => {
        if (chat.createdAt) {
          chat.createdAt = chat.createdAt.toMillis();
        }
        if (chat.updatedAt) {
          chat.updatedAt = chat.updatedAt.toMillis();
        }
        return chat;
      });
      state.chats = action.payload;
    },
    setNotifications: (state, action) => {
      state.notifications = action.payload;
    },
    setUserData: (state, action) => {
      action.payload.date = action.payload.date.toMillis();
      state.userData = action.payload;
    },
    setFriends: (state, action) => {
      action.payload.map((friend) => {
        if (friend.date) {
          friend.date = friend.date.toMillis();
        }

        return friend;
      });

      state.friends = action.payload;
    },
    updateMessages: (state, action) => {
      const { chatId, messages } = action.payload;
      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }

      messages.forEach((message) => {
        const index = state.messages[chatId].findIndex(
          (m) => m._id === message._id
        );
        if (index !== -1) {
          // Update existing message
          state.messages[chatId][index] = message;
        } else {
          // Add new message
          state.messages[chatId].unshift(message);
        }
      });

      // Sort messages by createdAt in descending order (newest first)
      state.messages[chatId].sort((a, b) => b.createdAt - a.createdAt);
    },
    removeMessage: (state, action) => {
      const { chatId, messageId } = action.payload;
      if (state.messages[chatId]) {
        state.messages[chatId] = state.messages[chatId].filter(
          (m) => m._id !== messageId
        );
      }
    },
    addOlderMessages: (state, action) => {
      const { chatId, messages } = action.payload;
      if (!state.messages[chatId]) {
        state.messages[chatId] = [];
      }

      const uniqueMessages = new Map();

      // First, add all existing messages to the map
      state.messages[chatId].forEach((message) => {
        uniqueMessages.set(message._id, message);
      });

      // Then, add new messages, but only if they don't already exist
      messages.forEach((message) => {
        if (!uniqueMessages.has(message._id)) {
          uniqueMessages.set(message._id, message);
        }
      });

      // Convert map back to array and sort
      state.messages[chatId] = Array.from(uniqueMessages.values()).sort(
        (a, b) => b.createdAt - a.createdAt
      );
    },
    setMessagesLoading: (state, action) => {
      state.isMessagesLoading = action.payload;
    },
    updateActiveChats: (state, action) => {
      const { activeChats, chatListeners } = action.payload;
      state.activeChats = activeChats;
      state.chatListeners = chatListeners;
    },
    clearOlderMessages: (state, action) => {
      const { chatId, keepCount } = action.payload;
      if (state.messages[chatId] && state.messages[chatId].length > keepCount) {
        state.messages[chatId] = state.messages[chatId].slice(0, keepCount);
      }
    },
    removeChatById: (state, action) => {
      const chatId = action.payload;
      state.chats = state.chats.filter((chat) => chat.id !== chatId);

      // Also clean up any messages for this chat
      if (state.messages[chatId]) {
        delete state.messages[chatId];
      }

      // Clean up any active chat listeners
      if (state.chatListeners && state.chatListeners[chatId]) {
        delete state.chatListeners[chatId];
      }

      // Remove from active chats if present
      if (state.activeChats) {
        state.activeChats = state.activeChats.filter((id) => id !== chatId);
      }
    },

    addChat: (state, action) => {
      const chat = action.payload;
      if (!state.chats.some((c) => c.id === chat.id)) {
        state.chats.push(chat);
      }
    },
  },
});

export const {
  setChats,
  setNotifications,
  setUserData,
  setLoadingUserData,
  setLoadingChats,
  setLoadingFriends,
  setFriends,
  updateMessages,
  updateActiveChats,
  setMessagesLoading,
  removeMessage,
  addOlderMessages,
  clearOlderMessages,
  removeChatById,
  addChat,
} = socialsSlice.actions;

export const startUserDataListener = (auth) => (dispatch) => {
  const docRef = doc(db, "users", auth.currentUser.uid);

  const unsubscribe = onSnapshot(docRef, (docSnap) => {
    // aa;
    if (docSnap.exists) {
      const data = docSnap.data();
      dispatch(setUserData(data));
    } else {
      console.log("No such document!");
    }
    dispatch(setLoadingUserData(false));
  });

  return unsubscribe;
};

export const startFriendsListener = (auth) => (dispatch) => {
  dispatch(setLoadingFriends(true));

  const userDocRef = doc(db, "users", auth.currentUser.uid);

  const unsubscribe = onSnapshot(userDocRef, async (userDocSnap) => {
    if (userDocSnap.exists && userDocSnap.data().friends) {
      const friendsUids = userDocSnap.data().friends;

      const friendsPromises = friendsUids.map((friendUid) => {
        const friendPublicDocRef = doc(
          db,
          "users",
          friendUid,
          "public",
          `${friendUid}public`
        );
        return getDoc(friendPublicDocRef);
      });

      const friendsData = await Promise.all(friendsPromises);
      const friends = friendsData
        .map((docSnap) => {
          if (docSnap.exists) {
            return { uid: docSnap.id.replace("public", ""), ...docSnap.data() };
          } else {
            return null;
          }
        })
        .filter(Boolean);

      dispatch(setFriends(friends));
    } else {
      console.log("User document or friends field not found!");
    }
    dispatch(setLoadingFriends(false));
  });

  return unsubscribe;
};

export const startSocialsListener = (auth) => (dispatch) => {
  const chatsCollection = collection(db, "chats");
  const userChatsQuery = query(
    chatsCollection,
    where("users", "array-contains", auth.currentUser.uid),
    orderBy("lastUnreadMessageDate", "desc")
  );

  const chatsUnsubscribe = onSnapshot(userChatsQuery, async (querySnapshot) => {
    const chatsPromises = querySnapshot.docs.map(async (chatDoc) => {
      const data = chatDoc.data();

      if (data.type === "private") {
        const otherUserUid = data.users.find(
          (uid) => uid !== auth.currentUser.uid
        );

        const otherUserPublicDocRef = doc(
          db,
          "users",
          otherUserUid,
          "public",
          `${otherUserUid}public`
        );

        let otherUserPublicDocSnap;
        try {
          otherUserPublicDocSnap = await getDoc(otherUserPublicDocRef);
        } catch (error) {
          return null;
        }

        let otherUser = null;

        if (otherUserPublicDocSnap.exists) {
          const publicData = otherUserPublicDocSnap.data();
          otherUser = {
            ...publicData,
            date: publicData.date.toDate().toISOString(),
          };
        }

        serializeProps(data);

        const lastUnreadMessageDate = data.lastUnreadMessageDate
          ? data.lastUnreadMessageDate.toMillis()
          : null;

        return {
          id: chatDoc.id,
          ...data,
          lastUnreadMessageDate,
          otherUser,
          unreadCount: data.unreadMessages[auth.currentUser.uid] || 0,
        };
      } else {
        const lastUnreadMessageDate = data.lastUnreadMessageDate
          ? data.lastUnreadMessageDate.toMillis()
          : null;

        serializeProps(data);

        return {
          id: chatDoc.id,
          ...data,
          lastUnreadMessageDate,
          unreadCount: data.unreadMessages[auth.currentUser.uid] || 0,
        };
      }
    });

    const chats = await Promise.all(chatsPromises);
    dispatch(setChats(chats));
  });

  const notifCollection = collection(
    db,
    "users",
    auth.currentUser.uid,
    "notifications"
  );

  const notificationsQuery = query(notifCollection, orderBy("date", "desc"));

  const notificationsUnsubscribe = onSnapshot(
    notificationsQuery,
    async (querySnapshot) => {
      const notificationsPromises = querySnapshot.docs.map(async (document) => {
        const notificationData = document.data();
        const date = notificationData.date?.toDate().toISOString();

        // Base notification object
        const notification = {
          id: document.id,
          ...notificationData,
          date,
        };

        // Fetch sender info
        if (notificationData.from) {
          const senderDocRef = doc(
            db,
            "users",
            notificationData.from,
            "public",
            `${notificationData.from}public`
          );

          const senderDoc = await getDoc(senderDocRef);

          if (senderDoc.exists) {
            const senderData = senderDoc.data();
            notification.sender = {
              name: senderData.name,
              profileImageRef: senderData.profileImageRef,
              profileColor: senderData.profileColor,
              description: senderData.description,
              uid: senderData.uid,
              date: senderData.date?.toDate().toISOString(),
            };
          }
        }

        // Fetch team info for team invites
        if (notificationData.type === "teamInvite" && notificationData.teamId) {
          const teamDocRef = doc(db, "teams", notificationData.teamId);
          const teamDoc = await getDoc(teamDocRef);

          if (teamDoc.exists) {
            const teamData = teamDoc.data();
            notification.team = {
              id: teamDoc.id,
              name: teamData.name,
              color: teamData.color,
              imageUrl: teamData.imageUrl,
              gradientDirection: teamData.gradientDirection,
            };
          }
        }

        return notification;
      });

      try {
        const notifications = await Promise.all(notificationsPromises);
        dispatch(setNotifications(notifications));
      } catch (error) {
        console.warn("Fetching notifications error:", error);
      }
    }
  );

  dispatch(setLoadingChats(false));

  return () => {
    chatsUnsubscribe();
    notificationsUnsubscribe();
  };
};

export const setupMessageListener = (chatId) => (dispatch, getState) => {
  const state = getState().socials;
  let activeChats = [...state.activeChats];
  let chatListeners = { ...state.chatListeners };

  // Manage active chats
  if (!activeChats.includes(chatId)) {
    if (activeChats.length >= MAX_ACTIVE_CHATS) {
      const oldestChatId = activeChats.shift();
      if (chatListeners[oldestChatId]) {
        chatListeners[oldestChatId]();
        delete chatListeners[oldestChatId];
      }
    }
    activeChats.push(chatId);
  } else {
    activeChats = activeChats.filter((id) => id !== chatId);
    activeChats.push(chatId);
  }

  // If we already have a listener for this chat, just update the active chats order
  if (chatListeners[chatId]) {
    dispatch(updateActiveChats({ activeChats, chatListeners }));
    return;
  }

  dispatch(setMessagesLoading(true));

  const messagesCollection = collection(db, "chats", chatId, "messages");
  const messagesQuery = query(
    messagesCollection,
    orderBy("createdAt", "desc"),
    limit(20)
  );

  const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
    snapshot.docChanges().forEach((change) => {
      if (change.type === "removed") {
        dispatch(removeMessage({ chatId, messageId: change.doc.id }));
      } else {
        const messageData = change.doc.data();
        if (messageData.createdAt) {
          const createdAt = new Date(messageData.createdAt.seconds * 1000);
          const newMessage = {
            _id: change.doc.id,
            image: messageData.image,
            imageSize: messageData.imageSize,
            read: messageData.read,
            replyMessage: messageData.replyMessage,
            text: messageData.text,
            user: messageData.user,
            userId: messageData.userId,
            status: "sent",
            createdAt: createdAt,
          };
          dispatch(updateMessages({ chatId, messages: [newMessage] }));
        } else {
          console.warn(`Message ${change.doc.id} has null createdAt, skipping`);
        }
      }
    });

    dispatch(setMessagesLoading(false));
  });

  // Store the unsubscribe function
  chatListeners[chatId] = unsubscribe;

  // Update the Redux state with new activeChats and chatListeners
  dispatch(updateActiveChats({ activeChats, chatListeners }));

  // If there are no messages, dispatch an update with an empty array
  if (state.messages[chatId] === undefined) {
    dispatch(updateMessages({ chatId, messages: [] }));
  }
};

export const loadOlderMessages =
  (chatId, lastMessageTimestamp, pageSize = 20) =>
  async (dispatch) => {
    console.log("Loading older messages for chat", chatId);

    const messagesCollection = collection(db, "chats", chatId, "messages");
    const olderMessagesQuery = query(
      messagesCollection,
      orderBy("createdAt", "desc"),
      startAfter(lastMessageTimestamp),
      limit(pageSize)
    );

    try {
      const snapshot = await getDocs(olderMessagesQuery);
      const olderMessages = snapshot.docs.map((doc) => {
        const messageData = doc.data();
        return {
          _id: doc.id,
          image: messageData.image,
          imageSize: messageData.imageSize,
          read: messageData.read,
          replyMessage: messageData.replyMessage,
          text: messageData.text,
          user: messageData.user,
          userId: messageData.userId,
          status: "sent",
          createdAt: new Date(messageData.createdAt.seconds * 1000),
        };
      });

      dispatch(addOlderMessages({ chatId, messages: olderMessages }));
      return olderMessages.length < pageSize; // Return true if this is the last page
    } catch (error) {
      console.error("Error loading older messages:", error);
      return false;
    }
  };

export default socialsSlice.reducer;

// Helper function for serializing message properties
function serializeProps(data) {
  if (data.lastUnreadMessage) {
    data.lastUnreadMessage.createdAt =
      data.lastUnreadMessage.createdAt.toMillis();

    if (data.lastUnreadMessage.replyMessage) {
      data.lastUnreadMessage.replyMessage.createdAt =
        data.lastUnreadMessage.replyMessage.createdAt.toMillis();
    }
  }
}
