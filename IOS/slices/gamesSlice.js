// gamesSlice.js
import { createSlice } from "@reduxjs/toolkit";
import {
  collection,
  query,
  where,
  onSnapshot,
  doc,
  getDoc,
} from "@react-native-firebase/firestore";
import { db } from "../config/firebase";

const gamesSlice = createSlice({
  name: "games",
  initialState: {
    games: [],
    isLoadingGames: true,
  },
  reducers: {
    setGames: (state, action) => {
      action.payload.map((game) => {
        if (game.timeEnd) {
          game.timeEnd = game.timeEnd.toMillis();
        }
        if (game.timeStart) {
          game.timeStart = game.timeStart.toMillis();
        }
        if (game.date) {
          game.date = game.date.toMillis();
        }
        return game;
      });
      state.games = action.payload;
    },
    setLoadingGames: (state, action) => {
      state.isLoadingGames = action.payload;
    },
  },
});

export const { setGames, setLoadingGames } = gamesSlice.actions;

export const startGamesListener = (auth) => async (dispatch) => {
  dispatch(setLoadingGames(true));

  const gamesCollection = collection(db, "games");
  const userGamesQuery = query(
    gamesCollection,
    where("users", "array-contains", auth.currentUser.uid)
  );

  const fetchUserData = async (uid) => {
    const userDocRef = doc(db, "users", uid, "public", `${uid}public`);

    const userDocSnap = await getDoc(userDocRef);
    if (userDocSnap.exists) {
      return { uid, ...userDocSnap.data() };
    }
    return null;
  };

  let existingGames = [];

  const unsubscribe = onSnapshot(userGamesQuery, async (querySnapshot) => {
    const gamesPromises = querySnapshot.docs.map(async (doc) => {
      const gameData = doc.data();
      const existingGame = existingGames.find((game) => game.id === doc.id);

      let goingUsers = existingGame?.goingUsers || [];
      let interestedUsers = existingGame?.interestedUsers || [];

      const newGoingUids = gameData.going.filter(
        (uid) => !goingUsers.some((user) => user.uid === uid)
      );
      const newInterestedUids = gameData.interested.filter(
        (uid) => !interestedUsers.some((user) => user.uid === uid)
      );

      const [newGoingUsers, newInterestedUsers] = await Promise.all([
        Promise.all(newGoingUids.map(fetchUserData)),
        Promise.all(newInterestedUids.map(fetchUserData)),
      ]);

      goingUsers = [
        ...goingUsers.filter((user) => gameData.going.includes(user.uid)),
        ...newGoingUsers.filter(Boolean),
      ];

      interestedUsers = [
        ...interestedUsers.filter((user) =>
          gameData.interested.includes(user.uid)
        ),
        ...newInterestedUsers.filter(Boolean),
      ];

      return {
        id: doc.id,
        ...gameData,
        goingUsers,
        interestedUsers,
      };
    });

    const games = await Promise.all(gamesPromises);
    existingGames = games;
    dispatch(setGames(games));
    dispatch(setLoadingGames(false));
  });

  return unsubscribe;
};

export default gamesSlice.reducer;
