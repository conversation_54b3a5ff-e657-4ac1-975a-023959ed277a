// languageSlice.js
import { createSlice } from "@reduxjs/toolkit";
import { doc, getDoc } from "@react-native-firebase/firestore";
import { db } from "../config/firebase";
import { EN, CZ } from "../assets/strings";

const languageSlice = createSlice({
  name: "language",
  initialState: {
    language: CZ,
    isLoading: true,
  },
  reducers: {
    setLanguage: (state, action) => {
      state.language = action.payload === "CZ" ? CZ : EN;
    },
    setLoadingLanguage: (state, action) => {
      state.isLoading = action.payload;
    },
  },
});

export const { setLanguage, setLoadingLanguage } = languageSlice.actions;

export const fetchLanguagePreference = (auth) => async (dispatch) => {
  dispatch(setLoadingLanguage(true));
  if (auth.currentUser) {
    const docRef = doc(db, "users", auth.currentUser.uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists) {
      const userLanguage = docSnap.data().language;
      dispatch(setLanguage(userLanguage));
    }
  }
  dispatch(setLoadingLanguage(false));
};

export default languageSlice.reducer;
