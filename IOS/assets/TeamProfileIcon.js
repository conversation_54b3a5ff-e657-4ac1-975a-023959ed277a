import React from "react";
import { View, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import FastImage from "react-native-fast-image";

const lightenColor = (color, percent) => {
  const num = parseInt(color.replace("#", ""), 16),
    amt = Math.round(2.55 * percent),
    R = (num >> 16) + amt,
    B = ((num >> 8) & 0x00ff) + amt,
    G = (num & 0x0000ff) + amt;
  return (
    "#" +
    (
      0x1000000 +
      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
      (B < 255 ? (B < 1 ? 0 : B) : 255) * 0x100 +
      (G < 255 ? (G < 1 ? 0 : G) : 255)
    )
      .toString(16)
      .slice(1)
  );
};

const getGradientStartEnd = (direction) => {
  switch (direction) {
    case "topLeft":
      return { start: { x: 0, y: 0 }, end: { x: 1, y: 1 } };
    case "topRight":
      return { start: { x: 1, y: 0 }, end: { x: 0, y: 1 } };
    case "bottomLeft":
      return { start: { x: 0, y: 1 }, end: { x: 1, y: 0 } };
    case "bottomRight":
      return { start: { x: 1, y: 1 }, end: { x: 0, y: 0 } };
    default:
      return { start: { x: 0, y: 0 }, end: { x: 1, y: 1 } };
  }
};

const TeamProfileIcon = ({ team, size }) => {
  const { start, end } = getGradientStartEnd(team.gradientDirection);

  return (
    <>
      {team.imageUrl ? (
        <FastImage
          style={{ width: size, height: size, borderRadius: size / 4.2 }}
          source={{ uri: team.imageUrl }}
        ></FastImage>
      ) : (
        <LinearGradient
          colors={[
            lightenColor(team.color, -6),
            team.color,
            // team.color,
            lightenColor(team.color, 8),
          ]}
          start={start}
          end={end}
          style={{
            width: size,
            height: size,
            borderRadius: size / 4.2,
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text
            style={{
              fontSize: size * 0.4,
              color: "white",
              fontWeight: "bold",
            }}
          >
            {team.name.charAt(0).toUpperCase()}
          </Text>
        </LinearGradient>
      )}
    </>
  );
};

export default TeamProfileIcon;
