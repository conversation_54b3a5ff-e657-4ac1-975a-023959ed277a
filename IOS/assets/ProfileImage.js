// ProfileImage.js

import React from "react";
import { View, Text, StyleSheet } from "react-native";
import FastImage from "react-native-fast-image";

const ProfileImage = ({
  profileImageRef,
  profileColor,
  name,
  size = 45,
  style,
}) => {
  const borderRadius = size / 2; // To make the shape circular

  return profileImageRef ? (
    <FastImage
      source={{ uri: profileImageRef }}
      style={[
        {
          width: size,
          height: size,
          borderRadius: borderRadius,
          borderWidth: 0.15,
          borderColor: "rgba(0,0,0,0.8)",
        },
        style,
      ]}
    />
  ) : (
    <View
      style={[
        {
          width: size,
          height: size,
          borderRadius: borderRadius,
          backgroundColor: profileColor || "#B5CCE7",
          justifyContent: "center",
          alignItems: "center",
        },
        style,
      ]}
    >
      <Text style={[styles.text, { fontSize: size / 2.6 }]}>
        {name?.charAt(0).toUpperCase()}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  text: {
    color: "white",
    fontWeight: "bold",
  },
});

export default ProfileImage;
