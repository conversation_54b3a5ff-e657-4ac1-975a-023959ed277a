export const EN = {
  age: "age",
  communityTeaseTitle: "Community",

  communityTeaseBody:
    "Adding friends, creating teams and training sessions, joining tournaments and challanging other teams to matches comming this spring!",

  weekdays: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ],

  deletedEvent: "This event has been deleted",

  swipeToConfirm: "Swipe to confirm",

  noGames: "There are currently no games\n in your area",

  feedbackSentHome: "Message Sent",

  eventFull: "This game is already full",

  writeUsTut:
    "Have a question, idea, or something to share with us? You can express yourself here",

  writeUs: "Share your thoughts",

  start: "Start :)",

  welcome: "Welcome to our new app! 👋",

  welcomeString:
    "We're glad you've joined our basketball community. Since the app is new, we'll be grateful if you help us expand the community by sharing the app. It will help us create a community across the whole country in the future. \n\nWe are students from Prague, and so far, we've documented over 60 courts just in Prague. If you live outside of Prague or can't see your favorite court, you can easily add it to the map. \n\nIf you have a question, an idea for improvement, or anything else, be sure to tell us",

  areaUpdated: "Region updated",

  added<PERSON>ourt:
    "Thank you for adding a new court. We will verify it as soon as possible and let you know :)",

  usernameTaken: "This username is already taken :(",

  editEvent: "Edit game",

  noUsername: "Please choose a username",
  noDate: "Please choose your date of birth",
  lowAge: "You have to be at least 15 years old to sign up",

  save: "Save",

  areaInfo:
    "Please choose an area that is relevant for you. This will allow us to show you events that are near you",

  changeLater: "You will be able to change this later",

  dateLocale: "en-En",

  setNotif: "Set up notifications",

  //Setup profile
  userInfoGuide:
    "Here you can describe how long you have been playing basketball, how often you play, and what type of players you want to play with",
  continue: "Continue",

  passwordInput: "Password",
  confirmPassword: "Confirm Password",
  emailInput: "Email",
  login: "Login",
  signUp: "Sign up",
  haveAcc: "Already have an account?",
  noAcc: "Don't have an account yet?",

  hi: "Hi",
  changeRegion: "Change region",
  liveEvents: "Live Events",
  plannedEvents: "Planned Events",
  endsAt: "Ends at",
  yourGames: "Your games",
  favCourts: "Favourite courts",
  noFavCourts:
    "Here you can add your favourite courts and we will notify you whenever someone creates a new game on one of them",
  exploreCourts: "Explore courts",
  createGame: "Create a new game",
  moreCourtInfo: "More...",
  openingHours: "Open hours",
  artificial: "Artificial",
  concrete: "Concrete",
  otherSurface: "Other...",

  //Add a court
  describeCourt:
    "Describe the courts condition, especially the hoops and the surface. Also, if you see any opening hours, be sure to write them down here!",
  basketsCount: "Baskets count",
  surfaceType: "Surface type",
  access: "Access",
  type: "Type",
  addCourt: "Add a court",
  chooseImage: "Choose a photo",
  takeImage: "Take a photo",
  send: "Send",
  howTo: "What images of the court are required",
  howToText:
    "-  Hold the camera horizontally \n-  At least one photo for every hoop \n-  Take the photos of the court at daylight, ideally when it's nice outside\n-  Without people. In case you meet any, ask them to move for a moment \n-  If the court has basketball lines, include them in the photo",
  examplePhoto: "Example",
  understand: "I understand",
  images: "Images",
  chooseLocation: "Pinpoint the location",

  //CourtInfo
  games: "Games",

  //Creating a game
  nameOfEvent: "Name of your event",
  eventInfoInput:
    "Give some information about your event. Mainly the level of basketball at which you want to play. (beginner/advanced)",
  chooseCourt: "Choose a court",
  chooseThisCourt: "Choose court",
  date: "Date",
  from: "From",
  to: "To",
  public: "Public",
  ageRestricted: "Age restricted",
  usersLimit: "Limit of players",
  defaultUserLimit: "Default no limit",
  publishGame: "Slide to publish game",
  minAge: "Min age",
  maxAge: "Max age",
  notifExplanation:
    "We would like to send you important notfications, like when you recieve a new chat message or when someone creates an event on one of your favourite courts. \n\n You can easily change this later in the settings of this app",
  allow: "Allow",
  deny: "Deny",

  //Profile
  age: "Age",
  name: "Username",
  dateOfBirth: "Date of birth",
  userDescription: "Description",
  settings: "Settings",
  logOut: "Log out",
  editProfile: "Edit profile",
  saveChanges: "Save changes",
  password: "Password",
  description: "Description",

  save: "Save",

  //Settings
  notifications: "Notifications",
  favCourtNotif: "Someone has created a game on one of your favourite courts",
  newChatNotif: "New message in chat",
  eventChangeNotif: "The setting of an event that you are in has changed",
  usersChangeNotif: "Someone has joined/left your game",
  languageLabel: "Language",

  other: "Others",
  feedback: "Send us feedback",
  feedbackSent: "Feedback sent!",

  reportBug: "Report a bug",
  reportBugSent: "Information about the bug was sent, thank you!",
  reportGuide: "Please tell us what exactly you did that lead to the bug.",

  deleteAccount: "Delete account",
  deleteAccountConfirm: `Type "Delete" to confirm`,
  deleteAccountDone: "Account was deleted",
  deleteBtn: "DELETE",
  deleteInfo: "This action will permanently delte your account",
  accountNotDeleted: "Account was not deleted",
  signOutTitle:
    "Please sign out and sign in again in order to be able to delete your account",
  singOutText:
    "For security reasons, you need to sign out and sign in again before deleting your account.",

  signOutText2:
    "You migh have to sign out and sign in again in order to be able to delete your account",

  signOutTitleEmail: "Please sign out and sign in again",

  signOutTitleEmail2:
    "For security reasons, you need to sign out and sign in again before updating your email",

  cancel: "Cancel",

  information: "Information",
  deleteChat: "Delete",
  joinGame: "Join game",
  leaveGame: "Leave game",
  users: "Users",
  yearsOld: "years old",
  reportUser: "Report",
  reportText: "Tell us why do you want to report this user",
  //   send: "Send",
  deleteEvent: "Cancel event",

  editEvent: "Edit event",
  confirm: "Confirm",

  gamesLimit: "You can only have 4 games at once",

  removeUsers: "Remove a user",

  removeUserEnsure: "Are you sure you want to remove",
  yes: "Yes",
  no: "No",

  logOutEnsure: "Are you sure you want to log out?",

  games: "Games",

  interested: "I'm Interested",

  uninterested: "I'm not interested",

  done: "Done",

  doneRemovingUsers: "Done removing users",

  going: "Going",
  interested: "Interested",

  showOnMap: "Show on map",

  notes: "Notes",

  openChat: "Open chat",

  reply: "Reply",

  profile: "Profile",

  search: "Search",
  navigateGame: "Go to game",

  chatMenu: "Chat info",

  mute: "Mute",

  noImagesInChat: "No images in chat",

  users: "Users",

  user: "User",

  more: "More",

  chooseTeamOpenness: "Set Your Team's Openness",
  opennessDescription:
    "Choose how open your team will be to other players and teams. These settings help define your team's interaction with the community.",
  teamVisibility: "Team Visibility",
  teamVisibilityDescription:
    "Make your team visible to other players in the app. This allows others to find and learn about your team.",
  allowJoinRequests: "Allow Join Requests",
  joinRequestsDescription:
    "Let other players request to join your team. You'll have the option to review and accept or decline these requests.",
  allowChallenges: "Accept Challenges",
  challengesDescription:
    "Allow other teams to challenge yours for friendly matches. This is a great way to compete and improve your team's skills.",
  changeOpenessLater:
    "Don't worry, you can adjust these settings anytime in your team profile.",
  next: "Next",
  enabled: "Enabled",
  disabled: "Disabled",

  createTeam: "Create Your Team",
  addTeamLogo: "Add Team Logo",
  teamName: "Team Name",
  enterTeamName: "Enter team name",
  teamDescription: "Team Description",
  describeTeam: "Describe your team",
  location: "Location",
  chooseTeamLocation: "Choose team location",
  createTeamButton: "Create Team",

  confirm: "Confirm",

  recruitingMessage: "Message for players you'd like to recruit to your team",
  recruitingMessageExample:
    "E.g., We're looking for intermediate players to join us for games every Monday",

  optional: "Optional",

  addLocationImage: "Add location image",
  changeLocationImage: "Change location image",

  addAnotherLocation: "Add another location",
  location: "Location",
  chooseTeamLocation: "Choose team location",
  addLocationImage: "Add location image",
  changeLocationImage: "Change location image",

  skillLevel: "Skill Level",
  beginner: "Beginner",
  intermediate: "Intermediate",
  advanced: "Advanced",
  professional: "Professional",
  teamType: "Team Type",
  casual: "Casual",
  competitive: "Competitive",
  ageGroup: "Age Group",
  allAges: "All Ages",
  youth: "Youth",
  adult: "Adult",
  senior: "Senior",
  genderPolicy: "Team Composition",
  mixed: "Mixed",
  menOnly: "Men Only",
  womenOnly: "Women Only",

  locationInstructions:
    "Click on the map to pinpoint your team's exact location",
  gotIt: "Got it",

  multipleSelectionsAllowed: "Multiple selections allowed",

  locationInstructions: "Search for a location or address",
  pinpointLocationInstructions:
    "Now, tap on the map to pinpoint the exact location",

  searchLocation: "Search Location",
  searchPlaceholder: "Enter an address or place",

  pinpointLocation: "Pinpoint Location",

  satellite: "Satellite",
  standard: "Standard",

  tapMapToPinpoint: "Tap on the map to pinpoint the exact location",

  nextStepMap: "Next step: Map",
  continueToMap: "Continue to Map",

  team: "Team",
  trainingSessions: "Training Sessions",

  yesGoing: "I'm going",
  dontKnow: "I don't know",
  notGoing: "I'm not going",

  createTrainingSession: "Create Training Session",
  sessionName: "Session Name",
  enterSessionName: "Enter session name",
  description: "Description",
  describeSession: "Describe the training session",
  chooseLocation: "Choose location",
  date: "Date",
  startTime: "Start",
  endTime: "End",
  createSessionButton: "Create Session",
  trainingSession: "Training Session",

  languageCode: "EN",
  makeRecurring: "Make Recurring",
  repeatsOn: "Repeats on",
  MON: "Monday",
  TUE: "Tuesday",
  WED: "Wednesday",
  THU: "Thursday",
  FRI: "Friday",
  SAT: "Saturday",
  SUN: "Sunday",

  addAnotherTime: "Add a different time",

  time: "Time",

  selectLocation: "Select location",
  close: "Close",

  inviteToTeam: "Invite to the team",
  inviteToTeam: "Invite to Team",
  searchPlayers: "Search players",
  players: "Players",
  friends: "Friends",
  invite: "Invite",
  yearsOld: "years",
  noPlayersFound: "No players found",
  noFriendsFound: "No friends found",

  or: "OR",
  shareInviteLink: "Share Invite Link",
  inviteLinkCopied: "Invite link copied to clipboard",
  shareInviteLinkMessage: "Join my team on Let's Hoop!",

  quickShare: "Share invite",
  copyLink: "Copy Invite Link",
  linkCopied: "Link copied to clipboard",
  shareTeamInvite: "Join our team in Let's Hoop!",
  moreShareOptions: "More options",

  inviteSent: "Invitation sent successfully",
  inviteFailed: "Failed to send invitation",
  userAlreadyMember: "This user is already a team member",
  noPermissionToInvite: "You don't have permission to send invites",
  success: "Success",
  error: "Error",

  notifications: "Notifications",
  noNotifications: "No notifications",
  friendRequestMessage: "sent you a friend request",
  teamInviteMessage: "invited you to join their team",
  accept: "Accept",
  delete: "Delete",
  join: "Join",
  decline: "Decline",
  from: "from",

  openChat: "Open chat",

  openChatTeam: "Tap to start chatting",
  players: "Players",

  today: "Today",
  tomorrow: "Tomorrow",
  upcoming: "Upcoming",
  newTrainingSession: "New Session",
  history: "History",
  noTrainingSessions: "No training sessions scheduled",
  yesGoing: "Going",
  notGoing: "Not Going",

  noOneIsGoing: "No one is going yet",
  noOneHasDeclinedYet: "No one has declined yet",
  everyOneHasResponded: "Everyone has responded",

  areGoing: "Going",
  areNotGoing: "Not Going",
  haveNotResponded: "Haven't responded yet",

  trainingHistory: "Training History",
  went: "Went",
  didntGo: "Didn't go",
  haventResponded: "Haven't responded",

  noOneWent: "No one went",
  noOneDeclined: "No one declined",
  everyOneResponded: "Everyone responded",

  schedule: "Schedule",
  sessionsSchedule: "Training Sessions Schedule",

  sureYouWantToDeleteSession: "Are you sure you want to delete this session?",

  sessionDeletedSuccessfully: "Session deleted successfully",
  sessionCreatedSuccessfully: "Session created successfully",
  sessionUpdatedSuccessfully: "Session updated successfully",
  editTrainingSession: "Edit training session",
  edit: "Edit",

  listOfLocations: "List of training ocations",
  locations: "Locations",
  sureYouWantToDeleteLocation: "Are you sure you want to delete this location?",
  locationDeletedSuccessfully: "Location deleted successfully",
  locationCreatedSuccessfully: "Location created successfully",
  minimumOneLocationRequired: "Teams must have at least one location",
  mainLocation: "Main Location",
  setAsMainLocation: "Set as Main Location",
  errorUpdatingMainLocation: "Error updating main location",
  locationUsedInSessions: "This location is used in training sessions",

  sessionNameRequired: "Please enter a session name",
  sessionNameAlreadyExists: "A session with this name already exists",
  locationRequired: "Please select a location",
  selectAtLeastOneDay: "Please select at least one day",
  invalidTimeRange: "Invalid time range",

  noImageAvailable: "No image available",

  cancelSession: "Cancel Session",
  enterCancelReason: "Reason: (optional)",
  cancelReasonPlaceholder: "e.g., Venue unavailable",
  // pleaseEnterCancelReason: "Please enter a reason for cancellation",
  sessionCancelledSuccessfully: "Session cancelled successfully",
  errorCancellingSession: "Error cancelling session",
  confirm: "Confirm",
  cancel: "Cancel",
  sessionOptions: "Session Options",

  sessionCancelled: "Session Cancelled",
  cancelSession: "Cancel Session",
  cancelReasonPlaceholder: "e.g., Venue unavailable",
  enterCancelReason: "Please enter a reason for cancellation:",
  sessionCancelledSuccessfully: "Session cancelled successfully",
  errorCancellingSession: "Error cancelling session",
  restoreSession: "Restore Session",
  sessionRestoredSuccessfully: "Session restored successfully",
  errorRestoringSession: "Error restoring session",

  events: "Events",
  streetball: "Streetball",

  deleting: "Deleting",
  deletingSession: "Deleting session...",

  autoSchedulingTitle: "Automatic Scheduling",
  autoSchedulingDescription:
    "Sessions are automatically created based on your schedule for easy attendance tracking",
  schedulingSettings: "Scheduling Settings",
  weeksAhead: "Weeks ahead",
  sendReminders: "Send reminders",
  sendRemindersDescription: "Notify team members a few hours before sessions",
  weeksAheadDescription: "How many weeks ahead should sessions be created",

  selectWeeksAhead: "Select Weeks Ahead",
  week: "week",
  weeks: "weeks",

  noSchedulesYet: "No Training Schedules Yet",
  createFirstSchedule:
    "Create your first recurring training schedule to automate team practices.",
  getStarted: "Get Started",

  trainingInfoDescription:
    "Manage your team's training effortlessly. Create a recurring schedule, and the app automatically generates training sessions, helping you track attendance and team participation.",
  createSchedule: "Create Schedule",
  streamlineYourTraining: "Streamline Your Training",

  // Notification related strings
  errorRemovingNotification: "Failed to remove notification",
  friendRequestAccepted: "Friend request accepted successfully",
  errorAcceptingRequest: "Failed to accept friend request",
  joiningTeam: "Joining team...",
  teamJoinedSuccessfully: "Successfully joined the team",
  errorJoiningTeam: "Failed to join team",
  inviteDeclined: "Invite declined",
  errorDecliningInvite: "Failed to decline invite",
  responseError: "Error responding to request",

  andYouAreFriendsNow: "and you are friends now",
  friendRequestAccepted: "Friend request accepted",
  teamJoinedSuccessfully: "Team joined successfully",
  youJoinedTeam: "You joined team",
  errorRemovingNotification: "Failed to remove notification",
  errorAcceptingRequest: "Failed to accept friend request",
  joinedTeamSuccessfully: "Successfully joined the team",
  errorJoiningTeam: "Failed to join team",
  errorDecliningInvite: "Failed to decline invite",
  joiningTeam: "Joining team...",
  today: "Today",
  yesterday: "Yesterday",
  teamJoinedSuccessfully: "You have joined the team",
  accepted: "Accepted",
  declined: "Declined",

  // Date related strings (if not already defined)
  weekdays: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ],

  deleteTeam: "Delete team",
  leaveTeam: "Leave team",
  deleteTeamConfirm: "Delete this team?",
  leaveTeamConfirm: "Leave this team?",
  deleteTeamWarning:
    "This will permanently delete the team and remove all members. This action cannot be undone.",
  leaveTeamWarning:
    "You will be removed from this team and will need an invitation to rejoin.",
  leave: "Leave",
  delete: "Delete",
  leftTeamSuccessfully: "You have left the team",
  errorLeavingTeam: "Error leaving team",

  inbox: "Inbox",
  leftTeam: "has left the team",
  noTeamNotifications: "No notifications for this team yet",
  teamMemberLeft: "Team member left",

  chooseYourArea: "Choose Your Area",
  areaInstructions: "Area Selection",
  areaInfo2:
    "Move the map to center your search area and adjust the radius with the slider below. This helps us find relevant courts and games near you.",
  changeLater: "You can always change this later in settings.",
  searchRadius: "Search Radius",
  saveAndContinue: "Save and Continue",
  error: "Error",

  setupProfile: "Setup Profile",
  name: "Username",
  enterYourUsername: "Enter your username",
  chooseImage: "Choose Image",
  takeImage: "Take Photo",
  dateOfBirth: "Date of Birth",
  aboutYou: "About You",
  userInfoGuide:
    "Share a bit about yourself, your favorite sport, or anything you'd like other players to know",
  descriptionPlaceholder: "Tell us about yourself...",
  continue: "Continue",
  error: "Error",
  usernameTaken: "This username is already taken",
  noUsername: "Please enter a username",
  noDate: "Please select your date of birth",
  lowAge: "You must be at least 15 years old to use this app",
  nameTooLong: "Username must be 18 characters or less",
  permissionRequired: "Permission Required",
  mediaPermissionMessage:
    "We need access to your media library to select a profile picture",
  cameraPermissionMessage: "We need camera access to take a profile picture",

  // Add these to your EN object
  setupProfile: "Setup Profile",
  welcomeTitle: "Let's get started",
  welcomeText:
    "Create your profile to start finding basketball games and teams in your area.",
  personalInfoTitle: "Personal Details",
  username: "Username",
  enterYourName: "Enter your username",

  personalInfoText:
    "Tell us a bit more about yourself so we can help you connect with the right players.",
  continue: "Continue",
  saveAndContinue: "Save & Continue",
  name: "Name",
  gender: "Gender",
  male: "Male",
  female: "Female",
  selectGender: "Please select your gender",
  dateOfBirth: "Date of Birth",
  change: "Change",
  youAre: "You are",
  yearsOld: "years old",
  aboutYou: "About You",
  userInfoGuide:
    "Share a bit about yourself, your favorite sport, or anything you'd like other players to know",
  descriptionPlaceholder: "Tell us about yourself...",
  chooseYourArea: "Choose Your Area",
  searchRadius: "Search Radius",
  areaInstructions: "Area Selection",
  areaInfo2:
    "Move the map to center your search area and adjust the radius with the slider below. This helps us find relevant courts and games near you.",
  changeLater: "You can always change this later in settings.",
  error: "Error",
  usernameTaken: "This username is already taken",
  noUsername: "Please enter a username",
  noDate: "Please select your date of birth",
  lowAge: "You must be at least 15 years old to use this app",
  ageRestriction: "You must be at least 15 years old to use this app.",
  nameTooLong: "Username must be 18 characters or less",
  permissionRequired: "Permission Required",
  mediaPermissionMessage:
    "We need access to your media library to select a profile picture",
  cameraPermissionMessage: "We need camera access to take a profile picture",

  basketballExperienceTitle: "More About You",

  basketballExperienceText:
    "Want to share your basketball skills? (Optional) This helps us find the right teammates for you.",
  startPlayingDate: "When did you start playing basketball?",
  skillLevel: "Your skill level",
  playingFor: "You've been playing for",
  justStarted: "Just started playing",
  year: "year",
  years: "years",

  playingDetailsLabel:
    "How often do you play basketball? Who do you want to connect with?",
  basketballDescriptionPlaceholder:
    "I play 2-3 times a week. I'd like to play with someone at a intermediate level.",

  skip: "Skip",

  setupProfileLocation: "Choose Your Area",
  areaInstructions: "Area Selection",
  areaInfo2:
    "Move the map to center your search area and adjust the radius with the slider below. This helps us find relevant courts and games near you.",
  changeLater: "You can always change this later in settings.",
  searchRadius: "Search Radius",
  saveAndContinue: "Save and Continue",
  creatingProfile: "Creating your profile...",
  profileCreated: "Profile created successfully!",
  errorCreatingProfile:
    "There was an error creating your profile. Please try again.",

  finish: "Finish",

  height: "Height",
  weight: "Weight",

  usernameTooShort: "Username must be at least 3 characters long",
  usernameTooLong: "Username cannot exceed 18 characters",
  usernameInvalidChars:
    "Username can only contain letters, numbers, and the characters: . - _",
  usernameHelper: "Use letters, numbers, underscores, periods, or hyphens",

  errorTitle: "Something went wrong",
  errorMessage:
    "We've encountered an unexpected error in the app. This doesn't happen often, but when it does, a restart usually fixes it.",
  technicalInfo: "Technical information:",
  unknownError: "Unknown error",
  restartApp: "Restart App",

  showDetails: "Show technical details",
  hideDetails: "Hide technical details",

  newVersionAvailable: "New Version Available!",
  updateNow: "Update Now",
  later: "Later",
  mandatoryUpdateRequired: "This update is required to continue using the app",
  bugFixes: "Bug fixes and stability improvements",
  enhancedUI: "Enhanced user interface",
  newFeatures: "New features available",
};

////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
////////////////////////////////////////////////////////

export const CZ = {
  age: "věk",
  communityTeaseTitle: "Komunita",

  communityTeaseBody:
    "Přidávání přátel, vytváření týmů a opakujících se tréninků, otevřené turnaje a vyzývání ostatních týmu na zápas. Tento update přidáme na jaře!",

  weekdays: [
    "neděle",
    "pondělí",
    "úterý",
    "středa",
    "čtvrtek",
    "pátek",
    "sobota",
  ],
  deletedEvent: "Tato hra byla smazána",

  eventFull: "Tato hra je plná",
  noGames: "V současnosti se v tvé oblasti\n nekonají žádné hry",

  feedbackSentHome: "Zpráva odeslána",

  writeUsTut:
    "Máš dotaz, nápad nebo nám chceš něco sdělit? Tady se můžeš vyjádřit",

  writeUs: "Napiš nám",

  start: "Začít :)",

  welcome: "Vítej v naší nové aplikaci! 👋",

  welcomeString:
    "Jsme rádi, že ses přidal do naší komunity basketbalistů. Jelikož je tato aplikace nová, budeme vděčni, když nám pomůžes rozšířit komunitu a aplikaci sdílíš. Pomůže nám to  do budoucna vytvořit komunitu po celé republice. \n\nJsme studenti z Prahy a zatím jsme zdokumentovali přes 60 hřišť pouze v Praze. Pokud ale bydlíš mimo Prahu, nebo nevidíš své oblíbené hřiště, můžeš ho na mapě jednoduše přidat. \n\nKdybys měl dotaz, nápad na vylepšení, nebo cokoliv jiného, určitě nám napiš",

  areaUpdated: "Oblast aktualizována",

  addedCourt:
    "Děkujeme za přidání nového hřiště! Co nejrychleji hřiště ověříme a dáme ti vědět :)",

  usernameTaken: "Toto uživatelské jméno už je zabrané :(",

  editEvent: "Upravit událost",

  noUsername: "Zadejte uživatelské jméno",
  noDate: "Zadejte datum narození",
  lowAge: "Musí vám být aspoň 15 let, abyste mohli používat aplikaci",

  save: "Uložit",

  areaInfo:
    "Vyberte prosím pro vás relevantní oblast. Toto nám umožní zobrazovat hry ve vašem okolí",

  changeLater: "Toto nastavení budete moci později změnit",

  dateLocale: "cs-Cz",

  setNotif: "Nastavení upozornění",

  //Setup Profile
  userInfoGuide:
    "Zde můžete stručně uvést, odkdy a jak často hrajete basketbal, případně s jakými hráči byste chtěli hrát",
  continue: "Pokračovat",

  passwordInput: "Heslo",
  confirmPassword: "Potvrdit heslo",
  emailInput: "Email",
  login: "Přihlásit se",
  signUp: "Registrovat",
  haveAcc: "Už máte účet?",
  noAcc: "Jěště nemáte účet?",

  hi: "Ahoj",
  changeRegion: "Změnit oblast",
  liveEvents: "Živé hry",
  plannedEvents: "Naplánované hry",
  endsAt: "Končí v",
  yourGames: "Tvé hry",
  favCourts: "Oblíbené hřiště",
  noFavCourts:
    "Zde si můžete přidat oblbené hřiště ve vašem okolí a my vás upozorníme, kdykoliv na nich někdo vytvoří novou hru.",
  exploreCourts: "Prozkoumat hřiště",
  createGame: "Vytvořit hru",
  moreCourtInfo: "Více...",
  openingHours: "Otevírací doba",
  artificial: "Umělý",
  concrete: "Beton",
  otherSurface: "Jiný",

  //Add a court
  describeCourt:
    "Popište stav hřiště, zejména povrch a koše. Pokud má hřiště otevírací dobu, zapište ji sem nebo ji vyfoťte.",
  basketsCount: "Počet košů",
  type: "Typ",
  access: "Přístup",
  surfaceType: "Typ povrchu", 
  addCourt: "Přidat hřiště",
  chooseImage: "Vybrat obrázek",
  takeImage: "Vyfotit obrázek",
  send: "Odeslat",
  howTo: "Jak vyfotit hřiště:",
  howToText:
    "-  Foťte hřiště horizontálně \n-  Alespoň jednu fotku pro každý koš \n-  Foťte hřiště za světla, ideálně, když je venku hezky\n-  Bez lidí. Kdyžtak je poproste, aby na šli na chvilku na stranu \n-  Pokud má hřiště basektbalové čáry, zachyťte je na fotce",
  examplePhoto: "Příklad",
  understand: "Rozumím",
  images: "Fotky",
  chooseLocation: "Zadejte lokaci hřiště",
  all: "Vše",
  indoor: "Vnitřní",
  outdoor: "Venkovní",
  good: "Dobrý",
  average: "Průměrný",
  poor: "Špatný",

  //CourtInfo
  games: "Hry",

  //Streetball
  streetball: "Streetball",

  //Creating a game
  nameOfEvent: "Název vaší hry",
  eventInfoInput:
    "Uveďte informace o hře. Je dobré napsat, na jaké úrovni basketbalu si chcete zahrát",
  chooseCourt: "Vyber hřiště",
  chooseThisCourt: "Vybrat hřiště",
  date: "Datum",
  from: "Od",
  to: "Do",
  public: "Otevřený",
  private: "Soukromé",
  ageRestricted: "Věkově omezený",
  usersLimit: "Limit hřáčů",
  defaultUserLimit: "Žádný limit",
  publishGame: "Přejeďte pro zveřejnění hry",
  minAge: "Min věk",
  maxAge: "Max věk",
  notifExplanation:
    "Rádi bychom ti posílali důležitá oznámení, jako když někdo napíše novou zprávu do chatu nebo založí hru na jednom z tvých oblíbených hřišť.\n\n Jestli a jaké oznámení ti budou chodit si můžeš později jednoduše změnit v nastavení",
  allow: "Povolit",
  deny: "Zakázat",

  //Profile
  age: "Věk",
  name: "Uživatelské jméno",
  dateOfBirth: "Datum narození",
  userDescription: "Popis",
  settings: "Nastavení",
  logOut: "Odhlásit se",
  editProfile: "Upravit profil",
  saveChanges: "Uložit změny",
  password: "Heslo",
  description: "Popis",

  save: "Uložit",

  //Settings
  notifications: "Oznámení",
  favCourtNotif: "Někdo vytvořil hru na jednom z tvých oblíbených hřišť",
  newChatNotif: "Nová zpráva v chatu",
  eventChangeNotif: "Nastavení hry, ke které jsi připojen, se změnilo",
  usersChangeNotif: "Někdo se právě připojil k tvé hře / odešel z tvé hry",
  languageLabel: "Jazyk",

  other: "Další",
  feedback: "Pošli nám feedback",
  feedbackSent: "Feedback odeslán!",

  reportBug: "Nahlásit chybu",
  reportBugSent: "Informace o chybě poslány, děkujeme!",
  reportGuide: "Prosím přesně popiště, co jste udělali, aby k chybě došlo.",

  deleteAccount: "Trvale smazat profil",
  deleteAccountConfirm: `Napište "Delete" pro potvrzení`,
  deleteAccountDone: "Účet byl smazán",
  deleteBtn: "SMAZAT",
  deleteInfo: "Váš účet bude nevratně smazán",
  accountNotDeleted: "Účet nebyl smazán",

  signOutTitle:
    "Prosím odhlaste se a znovu se přihlašte, abyste mohli smazat svůj účet",
  singOutText:
    "Z bezpečnostních důvodů se musíte odhlásit a znovu přihlásit, aby jste mohli vymazat svůj účet",

  signOutText2:
    "Zkuste se prosím odhlásit a znovu přihlásit, a pak zkuste svůj účet znovu smazat.",

  cancel: "Zrušit",

  information: "Informace",
  deleteChat: "Smazat",
  joinGame: "Přidat se",
  leaveGame: "Opustit hru",
  users: "Hřáči",
  yearsOld: "let",
  reportUser: "Nahlásit uživatele",
  reportText: "Uveďte důvod nahlášení uživatele",
  //   send: "Send",
  deleteEvent: "Zrušit hru",
  swipeToConfirm: "Potvrdit přejetím",
  editEvent: "Upravit událost",
  confirm: "Potvrdit",

  gamesLimit: "Můžete mít najednou jen 4 hry",

  removeUsers: "Odebrat uživatele",

  removeUserEnsure: "Opravdu chcete odebrat uživatele",
  yes: "Ano",
  no: "Ne",

  logOutEnsure: "Opravdu se chcete odhlásit?",

  games: "Hry",
  //Streetball
  streetball: "Streetball",

  interested: "Mám zájem",

  uninterested: "Nemám zájem",

  done: "Hotovo",

  doneRemovingUsers: "Hotovo",

  going: "Jdou",
  interested: "Mají zájem",

  showOnMap: "Zobrazit na mapě",

  notes: "Poznámky",
  openChat: "Otevřít chat",

  reply: "Odpovědět",

  profile: "Profil",
  search: "Hledat",
  navigateGame: "Přejít na hru",

  chatMenu: "Chat info",

  mute: "Ztlumit",

  noImagesInChat: "Žádné obrázky v chatu",

  users: "Users",

  user: "User",

  more: "Více",

  chooseTeamOpenness: "Nastavte otevřenost vašeho týmu",
  opennessDescription:
    "Zvolte, jak otevřený bude váš tým vůči ostatním hráčům a týmům. Nebojte se, tato nastavení můžete kdykoliv změnit.",
  teamVisibility: "Viditelnost týmu",
  teamVisibilityDescription: "Zviditelněte svůj tým ostatním hráčům v aplikaci",
  allowJoinRequests: "Povolit žádosti o připojení",
  joinRequestsDescription:
    "Umožněte ostatním hráčům požádat o připojení k vašemu týmu",
  allowChallenges: "Přijímat výzvy",
  challengesDescription:
    "Umožněte ostatním týmům vyzvat váš tým k přátelským zápasům",
  changeOpenessLater:
    "Tato nastavení můžete kdykoliv upravit v profilu vašeho týmu",
  next: "Další",
  enabled: "Povoleno",
  disabled: "Zakázáno",

  createTeam: "Vytvořte svůj tým",
  addTeamLogo: "Přidat logo týmu",
  teamName: "Název týmu",
  enterTeamName: "Zadejte název týmu",
  teamDescription: "Popis týmu",
  describeTeam: "Popište svůj tým",
  location: "Lokalita",
  chooseTeamLocation: "Vyberte lokalitu týmu",
  createTeamButton: "Vytvořit tým",

  recruitingMessage: "Zpráva pro hráče, které byste rádi nabrali do týmu",
  recruitingMessageExample:
    "Např. Hledáme středně pokročilé hráče, kteří s námi budou hrát každé pondělí",

  confirm: "Potvrdit",
  optional: "Nepovinné",

  addLocationImage: "Přidat obrázek lokace",
  changeLocationImage: "Změnit obrázek lokace",

  addAnotherLocation: "Přidat další lokaci",
  location: "Lokace",
  chooseTeamLocation: "Vybrat lokaci týmu",
  addLocationImage: "Přidat obrázek lokace",
  changeLocationImage: "Změnit obrázek místa",

  skillLevel: "Úroveň hry",
  beginner: "Začátečník",
  intermediate: "Středně Pokročilý",
  advanced: "Pokročilý",
  professional: "Profesionální",
  teamType: "Typ týmu",
  casual: "Rekreační",
  competitive: "Soutěžní",
  ageGroup: "Věková skupina",
  allAges: "Všechny věky",
  youth: "Mládež",
  adult: "Dospělí",
  senior: "Senioři",
  genderPolicy: "Složení týmu",
  mixed: "Smíšené",
  menOnly: "Pouze muži",
  womenOnly: "Pouze ženy",

  locationInstructions: "Klikněte na mapu pro určení přesné lokace vašeho týmu",
  gotIt: "Rozumím",

  multipleSelectionsAllowed: "Lze vybrat více možností",

  locationInstructions: "Vyhledejte místo nebo adresu",
  pinpointLocationInstructions: "Nyní klepnutím na mapu určete přesné umístění",

  searchLocation: "Vyhledat místo",
  searchPlaceholder: "Zadejte adresu nebo místo",

  pinpointLocation: "Upřesnit polohu",

  satellite: "Satelit",
  standard: "Standardní",
  tapMapToPinpoint: "Klepnutím na mapu určete přesné umístění",

  nextStepMap: "Další krok: Mapa",
  continueToMap: "Pokračovat na mapu",

  team: "Tým",
  trainingSessions: "Tréninky",

  yesGoing: "Jdu",
  dontKnow: "Nevím",
  notGoing: "Nejdu",

  createTrainingSession: "Vytvořit trénink",
  sessionName: "Název tréninku",
  enterSessionName: "Zadejte název tréninku",
  description: "Popis",
  describeSession: "Popište trénink",
  chooseLocation: "Vyberte místo",
  date: "Datum",
  startTime: "Začátek",
  endTime: "Konec",
  createSessionButton: "Vytvořit trénink",

  trainingSession: "Trénink",
  time: "Čas",

  languageCode: "CZ",
  makeRecurring: "Opakovat",
  repeatsOn: "Opakuje se v",
  PON: "Pondělí",
  UTE: "Úterý",
  STR: "Středa",
  CTV: "Čtvrtek",
  PAT: "Pátek",
  SOB: "Sobota",
  NED: "Neděle",

  addAnotherTime: "Přidat jiný čas",
  selectLocation: "Vyberte lokaci",

  close: "Zavřít",

  inviteToTeam: "Pozvat hráče do týmu",
  inviteToTeam: "Pozvat do týmu",
  searchPlayers: "Hledat hráče",
  players: "Hráči",
  friends: "Přátelé",
  invite: "Pozvat",
  yearsOld: "let",
  noPlayersFound: "Žádní hráči nenalezeni",
  noFriendsFound: "Žádní přátelé nenalezeni",

  or: "OR",
  shareInviteLink: "Sdílet pozvánku",
  inviteLinkCopied: "Odkaz na pozvánku zkopírován",
  shareInviteLinkMessage: "Připoj se k mému týmu v Let's Hoop!",

  quickShare: "Sdílet pozvánku",
  copyLink: "Kopírovat odkaz pozvánky",
  linkCopied: "Odkaz zkopírován",
  shareTeamInvite: "Připoj se k našemu týmu v Let's Hoop!",
  moreShareOptions: "Další možnosti",

  inviteSent: "Pozvánka úspěšně odeslána",
  inviteFailed: "Nepodařilo se odeslat pozvánku",
  userAlreadyMember: "Tento uživatel je již členem týmu",
  noPermissionToInvite: "Nemáte oprávnění posílat pozvánky",
  success: "Úspěch",
  error: "Chyba",

  notifications: "Oznámení",
  noNotifications: "Žádná oznámení",
  friendRequestMessage: "ti poslal/a žádost o přátelství",
  from: "z",
  teamInviteMessage: "tě pozval/a do týmu",
  accept: "Přijmout",
  delete: "Smazat",
  join: "Připojit se",
  decline: "Odmítnout",
  openChat: "Otevřít chat",
  openChatTeam: "Klikněte pro začátek chatu",

  players: "Hráči",

  today: "Dnes",
  tomorrow: "Zítra",
  upcoming: "Nadcházející",
  newTrainingSession: "Nový trénink",
  history: "Historie",
  noTrainingSessions: "Žádné naplánované tréninky",
  yesGoing: "Půjdu",
  notGoing: "Nepůjdu",

  noOneIsGoing: "Nikdo zatím nepůjde",
  noOneHasDeclinedYet: "Nikdo zatím neodmítl",
  everyOneHasResponded: "Všichni odpověděli",

  areGoing: "Jdou",
  areNotGoing: "Nejdou",
  haveNotResponded: "Ještě neodpověděli",

  trainingHistory: "Historie tréninků",
  went: "Šli",
  didntGo: "Nešli",
  haventResponded: "Neopdověděli",

  noOneWent: "Nikdo nešel",
  noOneDeclined: "Nikdo neodmítl",
  everyOneResponded: "Všichni odpověděli",

  schedule: "Rozvrh",
  sessionsSchedule: "Rozvrh tréninků",

  sureYouWantToDeleteSession: "Opravdu chcete smazat tento trénink?",

  sessionDeletedSuccessfully: "Trénink úspěšně smazán",
  sessionCreatedSuccessfully: "Trénink úspěšně vytvořen",
  sessionUpdatedSuccessfully: "Trénink úspěšně upraven",
  editTrainingSession: "Upravit trénink",
  edit: "Upravit",

  listOfLocations: "Přehled tréninkových míst",

  locations: "Tréninková místa",
  sureYouWantToDeleteLocation: "Opravdu chcete smazat toto místo?",
  locationDeletedSuccessfully: "Místo úspěšně smazáno",
  locationCreatedSuccessfully: "Místo úspěšně vytvořeno",
  minimumOneLocationRequired: "Týmy musí mít alespoň jednu lokaci",
  mainLocation: "Hlavní lokace",
  setAsMainLocation: "Natavit jako hlavní lokaci",
  errorUpdatingMainLocation: "Nepodařilo se nastavit hlavní lokaci",
  locationUsedInSessions: "Toto místo je používáno v trénincích",

  sessionNameRequired: "Zadejte název tréninku",
  sessionNameAlreadyExists: "Trénink s tímto názvem již existuje",
  locationRequired: "Vyberte místo konání",
  selectAtLeastOneDay: "Vyberte alespoň jeden den",
  invalidTimeRange: "Neplatný časový rozsah",

  noImageAvailable: "Není k dispozici žádný obrázek",

  cancelSession: "Zrušit trénink",
  enterCancelReason: "Důvod: (nepovinný)",
  cancelReasonPlaceholder: "např. Sportoviště není k dispozici",
  pleaseEnterCancelReason: "Prosím zadejte důvod zrušení",
  sessionCancelledSuccessfully: "Trénink byl úspěšně zrušen",
  errorCancellingSession: "Chyba při rušení tréninku",
  confirm: "Potvrdit",
  cancel: "Zrušit",
  sessionOptions: "Nastavení tréninku",

  sessionCancelled: "Trénink zrušen",
  cancelSession: "Zrušit trénink",
  cancelReasonPlaceholder: "např. Místo není dostupné",
  enterCancelReason: "Prosím zadejte důvod zrušení:",
  sessionCancelledSuccessfully: "Trénink byl úspěšně zrušen",
  errorCancellingSession: "Chyba při rušení tréninku",
  restoreSession: "Obnovit trénink",
  sessionRestoredSuccessfully: "Trénink byl úspěšně obnoven",
  errorRestoringSession: "Chyba při obnovení tréninku",

  events: "Události",

  deleting: "Mazání",
  deletingSession: "Mazání tréninku...",

  autoSchedulingTitle: "Automatické vytváření tréninků",
  autoSchedulingDescription:
    "Tréninky jsou automaticky vytvářeny podle vašeho rozvrhu pro snadné sledování účasti",
  schedulingSettings: "Nastavení plánování",
  weeksAhead: "Týdny dopředu",
  sendReminders: "Posílat připomenutí",
  sendRemindersDescription: "Upozornit členy týmu několik hodin před tréninkem",
  weeksAheadDescription: "Na kolik týdnů dopředu chcete tréninky vytvářet",

  selectWeeksAhead: "Vyberte týdny dopředu",
  week: "týden",
  weeks: "týdny",

  noSchedulesYet: "Zatím žádný rozvrh tréninků",
  createFirstSchedule:
    "Vytvořte svůj první tréninkový rozvrh pro snadné sledování účasti",
  getStarted: "Začít",

  trainingInfoDescription:
    "Spravujte trénink vašeho týmu bez námahy. Zadejte do aplikace váš rozvrh tréninků a aplikace automaticky vytvoří tréninky, které vám pomohou sledovat účast týmu",
  createSchedule: "Vytvořit rozvrh",

  streamlineYourTraining: "Zefektivněte Trénink Týmu",

  // Notification related strings
  errorRemovingNotification: "Nepodařilo se odstranit oznámení",
  friendRequestAccepted: "Žádost o přátelství úspěšně přijata",
  errorAcceptingRequest: "Nepodařilo se přijmout žádost o přátelství",
  joiningTeam: "Připojování k týmu...",
  teamJoinedSuccessfully: "Úspěšně jste se připojili k týmu",
  errorJoiningTeam: "Nepodařilo se připojit k týmu",
  inviteDeclined: "Pozvánka odmítnuta",
  errorDecliningInvite: "Nepodařilo se odmítnout pozvánku",
  responseError: "Chyba při odpovědi na požadavek",

  // New notification strings
  andYouAreFriendsNow: " - jste přátelé",
  friendRequestAccepted: "Žádost o přátelství přijata",
  teamJoinedSuccessfully: "Úspěšně jste se připojili k týmu",
  youJoinedTeam: "Připojili jste se k týmu",
  errorRemovingNotification: "Nepodařilo se odstranit oznámení",
  errorAcceptingRequest: "Nepodařilo se přijmout žádost o přátelství",
  joinedTeamSuccessfully: "Úspěšně jste se připojili k týmu",
  errorJoiningTeam: "Nepodařilo se připojit k týmu",
  errorDecliningInvite: "Nepodařilo se odmítnout pozvánku",
  joiningTeam: "Připojování k týmu...",
  today: "Dnes",
  yesterday: "Včera",

  teamJoinedSuccessfully: "Připojili jste se k týmu",
  accepted: "Přijato",
  declined: "Zamítnuto",

  // Date related strings (if not already defined)
  weekdays: [
    "Neděle",
    "Pondělí",
    "Úterý",
    "Středa",
    "Čtvrtek",
    "Pátek",
    "Sobota",
  ],

  // Add to CZ language object
  deleteTeam: "Smazat tým",
  leaveTeam: "Opustit tým",
  deleteTeamConfirm: "Smazat tento tým?",
  leaveTeamConfirm: "Opustit tento tým?",
  deleteTeamWarning:
    "Tím trvale smažete tým a odstraníte všechny členy. Tuto akci nelze vrátit zpět.",
  leaveTeamWarning:
    "Budete odstraněni z tohoto týmu a pro opětovné připojení budete potřebovat pozvánku.",
  leave: "Opustit",
  delete: "Smazat",
  leftTeamSuccessfully: "Opustili jste tým",
  errorLeavingTeam: "Chyba při opouštění týmu",

  inbox: "Schránka",
  leftTeam: "opustil tým",
  noTeamNotifications: "Zatím žádná oznámení pro tento tým",
  teamMemberLeft: "Člen opustil tým",

  chooseYourArea: "Vyberte svou oblast",
  areaInstructions: "Výběr oblasti",
  areaInfo:
    "Posuňte mapu pro zaměření vaší oblasti a upravte poloměr pomocí posuvníku níže. To nám pomůže najít relevantní hřiště a hry ve vašem okolí.",
  changeLater: "Toto nastavení můžete kdykoliv změnit v nastavení.",
  searchRadius: "Vyhledávací poloměr",
  saveAndContinue: "Uložit a pokračovat",
  error: "Chyba",

  setupProfile: "Vytvořte si profil",
  username: "Uživatelské jméno",
  enterYourName: "Zadejte své uživatelské jméno",
  chooseImage: "Vybrat obrázek",
  takeImage: "Vyfotit",
  dateOfBirth: "Datum narození",
  aboutYou: "O vás",
  userInfoGuide:
    "Podělte se o informace o sobě, vašem oblíbeném sportu nebo o čemkoli, co byste chtěli, aby ostatní hráči věděli",
  descriptionPlaceholder: "Řekněte nám o sobě...",
  continue: "Pokračovat",
  error: "Chyba",
  usernameTaken: "Toto uživatelské jméno je již obsazené",
  noUsername: "Zadejte prosím uživatelské jméno",
  noDate: "Vyberte prosím datum narození",
  lowAge: "Pro používání této aplikace musíte být starší 15 let",
  ageRestriction: "Pro používání této aplikace musíte být starší 15 let.",
  nameTooLong: "Uživatelské jméno musí mít maximálně 18 znaků",
  permissionRequired: "Vyžadováno oprávnění",
  mediaPermissionMessage:
    "Potřebujeme přístup k vaší galerii pro výběr profilového obrázku",
  cameraPermissionMessage:
    "Potřebujeme přístup k fotoaparátu pro pořízení profilového obrázku",

  // Add these to your CZ object

  welcomeTitle: "Začněme",
  welcomeText:
    "Vytvořte si profil, abyste mohli najít basketbalové hry a týmy ve vašem okolí.",
  personalInfoTitle: "Osobní údaje",
  personalInfoText:
    "Řekněte nám něco o sobě, abychom vám mohli pomoci spojit se se správnými hráči.",
  continue: "Pokračovat",
  saveAndContinue: "Uložit a pokračovat",
  name: "Jméno",
  enterYourUsername: "Zadejte své uživatelské jméno",
  gender: "Pohlaví",
  male: "Muž",
  female: "Žena",
  selectGender: "Vyberte své pohlaví",
  dateOfBirth: "Datum narození",
  change: "Změnit",
  youAre: "Je vám",
  yearsOld: "let",
  aboutYou: "O vás",
  userInfoGuide:
    "Podělte se o informace o sobě, vašem oblíbeném sportu nebo o čemkoli, co byste chtěli, aby ostatní hráči věděli",
  descriptionPlaceholder: "Řekněte nám o sobě...",
  chooseYourArea: "Vyberte svou oblast",
  searchRadius: "Vyhledávací poloměr",
  areaInstructions: "Výběr oblasti",
  areaInfo2:
    "Posuňte mapu pro zaměření vaší oblasti a upravte poloměr pomocí posuvníku níže. To nám pomůže najít relevantní hřiště a hry ve vašem okolí.",
  changeLater: "Toto nastavení můžete kdykoliv změnit v nastavení.",
  error: "Chyba",
  usernameTaken: "Toto uživatelské jméno je již obsazené",
  noUsername: "Zadejte prosím uživatelské jméno",
  noDate: "Vyberte prosím datum narození",
  lowAge: "Pro používání této aplikace musíte být starší 15 let",
  nameTooLong: "Uživatelské jméno musí mít maximálně 18 znaků",
  permissionRequired: "Vyžadováno oprávnění",
  mediaPermissionMessage:
    "Potřebujeme přístup k vaší galerii pro výběr profilového obrázku",
  cameraPermissionMessage:
    "Potřebujeme přístup k fotoaparátu pro pořízení profilového obrázku",

  basketballExperienceTitle: "Více o vás",
  basketballExperienceText:
    "Zde můžete uvést své basketbalové dovednosti. (Dobrovolné) Usnadní vám to najít ideální spoluhráče.",
  startPlayingDate: "Kdy jste začali hrát basketbal?",
  skillLevel: "Vaše úroveň dovedností",
  playingFor: "Hrajete již",
  justStarted: "Právě začínáte hrát",
  year: "rok",
  years: "let",

  playingDetailsLabel: "Jak často hrajete? Jaké spoluhráče hledáte?",
  basketballDescriptionPlaceholder:
    "Hraji 2-3 týdně. Rád bych si zahrál s někým na pokočilejší úrovni.",

  skip: "Přeskočit",

  setupProfileLocation: "Vyberte svou oblast",
  areaInstructions: "Výběr oblasti",
  areaInfo2:
    "Posuňte mapu na střed vaší oblasti a upravte poloměr pomocí posuvníku níže. To nám pomůže najít relevantní hřiště a hry ve vašem okolí.",
  changeLater: "Toto nastavení můžete později kdykoliv změnit.",
  searchRadius: "Poloměr vyhledávání",
  saveAndContinue: "Uložit a pokračovat",
  creatingProfile: "Vytváření profilu...",
  profileCreated: "Profil byl úspěšně vytvořen!",
  errorCreatingProfile:
    "Při vytváření profilu došlo k chybě. Zkuste to prosím znovu.",

  finish: "Dokončit",

  height: "Výška",
  weight: "Váha",

  usernameTooShort: "Uživatelské jméno musí mít alespoň 3 znaky",
  usernameTooLong: "Uživatelské jméno nesmí překročit 18 znaků",
  usernameInvalidChars:
    "Uživatelské jméno může obsahovat pouze písmena, čísla a znaky: . - _",
  usernameHelper: "Použijte písmena, čísla, podtržítka, tečky nebo pomlčky",

  errorTitle: "Něco se pokazilo",
  errorMessage:
    "Narazili jsme na chybu v aplikaci. Zkuste restartovat aplikaci nebo kontaktujte podporu.",
  technicalInfo: "Technické informace:",
  unknownError: "Neznámá chyba",
  restartApp: "Znovu načíst",

  showDetails: "Zobrazit detaily",
  hideDetails: "Skrýt detaily",

  newVersionAvailable: "Nová verze k dispozici!",
  updateNow: "Aktualizovat nyní",
  later: "Později",
  mandatoryUpdateRequired:
    "Tato aktualizace je nutná pro další používání aplikace",
  bugFixes: "Opravy chyb a zlepšení stability",
  enhancedUI: "Vylepšené uživatelské rozhraní",
  newFeatures: "Nové funkce k dispozici",
};
