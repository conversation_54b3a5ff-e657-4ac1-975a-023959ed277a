{"c90fb4585dd852a3d67af39baf923f67": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets", "httpServerLocation": "/assets/node_modules/@react-navigation/elements/src/assets", "width": 12, "height": 21, "scales": [1, 1.5, 2, 3, 4], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>"], "hash": "c90fb4585dd852a3d67af39baf923f67", "name": "back-icon", "type": "png", "fileHashes": ["7d40544b395c5949f4646f5e150fe020", "cdd04e13d4ec83ff0cd13ec8dabdc341", "a132ecc4ba5c1517ff83c0fb321bc7fc", "0ea69b5077e7c4696db85dbcba75b0e1", "f5b790e2ac193b3d41015edb3551f9b8"]}, "5223c8d9b0d08b82a5670fb5f71faf78": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets", "httpServerLocation": "/assets/node_modules/@react-navigation/elements/src/assets", "width": 50, "height": 85, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/back-icon-mask.png"], "hash": "5223c8d9b0d08b82a5670fb5f71faf78", "name": "back-icon-mask", "type": "png", "fileHashes": ["5223c8d9b0d08b82a5670fb5f71faf78"]}, "3a2ba31570920eeb9b1d217cabe58315": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/AntDesign.ttf"], "hash": "3a2ba31570920eeb9b1d217cabe58315", "name": "AntDesign", "type": "ttf", "fileHashes": ["3a2ba31570920eeb9b1d217cabe58315"]}, "31b5ffea3daddc69dd01a1f3d6cf63c5": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Entypo.ttf"], "hash": "31b5ffea3daddc69dd01a1f3d6cf63c5", "name": "<PERSON><PERSON><PERSON>", "type": "ttf", "fileHashes": ["31b5ffea3daddc69dd01a1f3d6cf63c5"]}, "140c53a7643ea949007aa9a282153849": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/EvilIcons.ttf"], "hash": "140c53a7643ea949007aa9a282153849", "name": "EvilIcons", "type": "ttf", "fileHashes": ["140c53a7643ea949007aa9a282153849"]}, "a76d309774d33d9856f650bed4292a23": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Feather.ttf"], "hash": "a76d309774d33d9856f650bed4292a23", "name": "<PERSON><PERSON>", "type": "ttf", "fileHashes": ["a76d309774d33d9856f650bed4292a23"]}, "b49ae8ab2dbccb02c4d11caaacf09eab": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Fontisto.ttf"], "hash": "b49ae8ab2dbccb02c4d11caaacf09eab", "name": "Fontisto", "type": "ttf", "fileHashes": ["b49ae8ab2dbccb02c4d11caaacf09eab"]}, "b06871f281fee6b241d60582ae9369b9": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome.ttf"], "hash": "b06871f281fee6b241d60582ae9369b9", "name": "FontAwesome", "type": "ttf", "fileHashes": ["b06871f281fee6b241d60582ae9369b9"]}, "1f77739ca9ff2188b539c36f30ffa2be": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"], "hash": "1f77739ca9ff2188b539c36f30ffa2be", "name": "FontAwesome5_Regular", "type": "ttf", "fileHashes": ["1f77739ca9ff2188b539c36f30ffa2be"]}, "605ed7926cf39a2ad5ec2d1f9d391d3d": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"], "hash": "605ed7926cf39a2ad5ec2d1f9d391d3d", "name": "FontAwesome5_Solid", "type": "ttf", "fileHashes": ["605ed7926cf39a2ad5ec2d1f9d391d3d"]}, "3b89dd103490708d19a95adcae52210e": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"], "hash": "3b89dd103490708d19a95adcae52210e", "name": "FontAwesome5_Brands", "type": "ttf", "fileHashes": ["3b89dd103490708d19a95adcae52210e"]}, "370dd5af19f8364907b6e2c41f45dbbf": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Regular.ttf"], "hash": "370dd5af19f8364907b6e2c41f45dbbf", "name": "FontAwesome6_Regular", "type": "ttf", "fileHashes": ["370dd5af19f8364907b6e2c41f45dbbf"]}, "adec7d6f310bc577f05e8fe06a5daccf": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Solid.ttf"], "hash": "adec7d6f310bc577f05e8fe06a5daccf", "name": "FontAwesome6_Solid", "type": "ttf", "fileHashes": ["adec7d6f310bc577f05e8fe06a5daccf"]}, "56c8d80832e37783f12c05db7c8849e2": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/FontAwesome6_Brands.ttf"], "hash": "56c8d80832e37783f12c05db7c8849e2", "name": "FontAwesome6_Brands", "type": "ttf", "fileHashes": ["56c8d80832e37783f12c05db7c8849e2"]}, "e20945d7c929279ef7a6f1db184a4470": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Foundation.ttf"], "hash": "e20945d7c929279ef7a6f1db184a4470", "name": "Foundation", "type": "ttf", "fileHashes": ["e20945d7c929279ef7a6f1db184a4470"]}, "6148e7019854f3bde85b633cb88f3c25": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Ionicons.ttf"], "hash": "6148e7019854f3bde85b633cb88f3c25", "name": "Ionicons", "type": "ttf", "fileHashes": ["6148e7019854f3bde85b633cb88f3c25"]}, "b62641afc9ab487008e996a5c5865e56": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"], "hash": "b62641afc9ab487008e996a5c5865e56", "name": "MaterialCommunityIcons", "type": "ttf", "fileHashes": ["b62641afc9ab487008e996a5c5865e56"]}, "4e85bc9ebe07e0340c9c4fc2f6c38908": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/MaterialIcons.ttf"], "hash": "4e85bc9ebe07e0340c9c4fc2f6c38908", "name": "MaterialIcons", "type": "ttf", "fileHashes": ["4e85bc9ebe07e0340c9c4fc2f6c38908"]}, "f7c53c47a66934504fcbc7cc164895a7": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Octicons.ttf"], "hash": "f7c53c47a66934504fcbc7cc164895a7", "name": "Octicons", "type": "ttf", "fileHashes": ["f7c53c47a66934504fcbc7cc164895a7"]}, "d2285965fe34b05465047401b8595dd0": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/SimpleLineIcons.ttf"], "hash": "d2285965fe34b05465047401b8595dd0", "name": "SimpleLineIcons", "type": "ttf", "fileHashes": ["d2285965fe34b05465047401b8595dd0"]}, "1681f34aaca71b8dfb70756bca331eb2": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "httpServerLocation": "/assets/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@expo/vector-icons/build/vendor/react-native-vector-icons/Fonts/Zocial.ttf"], "hash": "1681f34aaca71b8dfb70756bca331eb2", "name": "Zocial", "type": "ttf", "fileHashes": ["1681f34aaca71b8dfb70756bca331eb2"]}, "33d5303221efcdaf98df4c5a1f1915f2": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1794, "height": 1640, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/streetball2e.jpg"], "hash": "33d5303221efcdaf98df4c5a1f1915f2", "name": "streetball2e", "type": "jpg", "fileHashes": ["33d5303221efcdaf98df4c5a1f1915f2"]}, "e9612850a6cb55eb547266043e1eef86": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 512, "height": 512, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/google.png"], "hash": "e9612850a6cb55eb547266043e1eef86", "name": "google", "type": "png", "fileHashes": ["e9612850a6cb55eb547266043e1eef86"]}, "8a36205bd9b83e03af0591a004bc97f4": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts", "httpServerLocation": "/assets/assets/fonts", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Roboto-Regular.ttf"], "hash": "8a36205bd9b83e03af0591a004bc97f4", "name": "Roboto-Regular", "type": "ttf", "fileHashes": ["8a36205bd9b83e03af0591a004bc97f4"]}, "ff42a47086b4ec01c30ce3e62f090ab1": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 7000, "height": 7000, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/newlogo.png"], "hash": "ff42a47086b4ec01c30ce3e62f090ab1", "name": "newlogo", "type": "png", "fileHashes": ["ff42a47086b4ec01c30ce3e62f090ab1"]}, "4602ff49c8f9169fab07a45e002a4dad": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 400, "height": 400, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/white-marker.png"], "hash": "4602ff49c8f9169fab07a45e002a4dad", "name": "white-marker", "type": "png", "fileHashes": ["4602ff49c8f9169fab07a45e002a4dad"]}, "00c71596ef30a843f83d25e79a1c11cf": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1587, "height": 2245, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/11.png"], "hash": "00c71596ef30a843f83d25e79a1c11cf", "name": "11", "type": "png", "fileHashes": ["00c71596ef30a843f83d25e79a1c11cf"]}, "34ea680df01b6ce2d84b8e392b278380": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 700, "height": 700, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq2.png"], "hash": "34ea680df01b6ce2d84b8e392b278380", "name": "qq2", "type": "png", "fileHashes": ["34ea680df01b6ce2d84b8e392b278380"]}, "d2f9a9ae5b000c5afd1d1ff5277d0a6e": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 700, "height": 700, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq-star2.png"], "hash": "d2f9a9ae5b000c5afd1d1ff5277d0a6e", "name": "qq-star2", "type": "png", "fileHashes": ["d2f9a9ae5b000c5afd1d1ff5277d0a6e"]}, "17056879e5a2f425ae2a1080ac94507f": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 200, "height": 200, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq-star5.png"], "hash": "17056879e5a2f425ae2a1080ac94507f", "name": "qq-star5", "type": "png", "fileHashes": ["17056879e5a2f425ae2a1080ac94507f"]}, "d0572d9a6dc9c83c2c3c6192093b19c8": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 200, "height": 200, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq5.png"], "hash": "d0572d9a6dc9c83c2c3c6192093b19c8", "name": "qq5", "type": "png", "fileHashes": ["d0572d9a6dc9c83c2c3c6192093b19c8"]}, "e8c5b6b444278cd65f930a822b4bdfb2": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 200, "height": 200, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq-empty-star5.png"], "hash": "e8c5b6b444278cd65f930a822b4bdfb2", "name": "qq-empty-star5", "type": "png", "fileHashes": ["e8c5b6b444278cd65f930a822b4bdfb2"]}, "cef52d032ce32c8aae4b7e2f3eb08878": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 200, "height": 200, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq-empty5.png"], "hash": "cef52d032ce32c8aae4b7e2f3eb08878", "name": "qq-empty5", "type": "png", "fileHashes": ["cef52d032ce32c8aae4b7e2f3eb08878"]}, "fa88ab0a8b42a2b2ac0e600403b62e26": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1848, "height": 2104, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/elipse.png"], "hash": "fa88ab0a8b42a2b2ac0e600403b62e26", "name": "elipse", "type": "png", "fileHashes": ["fa88ab0a8b42a2b2ac0e600403b62e26"]}, "f2084c80c5137c0f29283fde45f7d755": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1300, "height": 975, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/court-image-example.jpg"], "hash": "f2084c80c5137c0f29283fde45f7d755", "name": "court-image-example", "type": "jpg", "fileHashes": ["f2084c80c5137c0f29283fde45f7d755"]}, "324ab7df48a97a42ebb9746982d901df": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 800, "height": 659, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/streetball.jpg"], "hash": "324ab7df48a97a42ebb9746982d901df", "name": "streetball", "type": "jpg", "fileHashes": ["324ab7df48a97a42ebb9746982d901df"]}, "0c87a71d9f13708bc2afb77a8b544a0b": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/react-native-google-places-autocomplete/images", "httpServerLocation": "/assets/node_modules/react-native-google-places-autocomplete/images", "width": 144, "height": 18, "scales": [1, 2, 3], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/react-native-google-places-autocomplete/images/powered_by_google_on_white.png", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/react-native-google-places-autocomplete/images/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/react-native-google-places-autocomplete/images/<EMAIL>"], "hash": "0c87a71d9f13708bc2afb77a8b544a0b", "name": "powered_by_google_on_white", "type": "png", "fileHashes": ["5e1218b10c1558a03d57e2aa7746552b", "3894e74f02d02e84351781c358026d5a", "cd72971a8a0413f5519e6827d89763cf"]}, "527d9f6820299ad84869cbb265ef4e45": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1920, "height": 1920, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/qq-empty.png"], "hash": "527d9f6820299ad84869cbb265ef4e45", "name": "qq-empty", "type": "png", "fileHashes": ["527d9f6820299ad84869cbb265ef4e45"]}, "1d138f74cac23b4744d6538500d5cf77": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 144, "height": 138, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/standard.jpg"], "hash": "1d138f74cac23b4744d6538500d5cf77", "name": "standard", "type": "jpg", "fileHashes": ["1d138f74cac23b4744d6538500d5cf77"]}, "338adce82d3a8055ffa6243b4923eebb": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 138, "height": 134, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/satellite.jpg"], "hash": "338adce82d3a8055ffa6243b4923eebb", "name": "satellite", "type": "jpg", "fileHashes": ["338adce82d3a8055ffa6243b4923eebb"]}, "6f7637dbee4eb00d36ae562ac762fc47": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images", "httpServerLocation": "/assets/images", "width": 1024, "height": 1024, "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/images/error-hoop9.png"], "hash": "6f7637dbee4eb00d36ae562ac762fc47", "name": "error-hoop9", "type": "png", "fileHashes": ["6f7637dbee4eb00d36ae562ac762fc47"]}, "3d63ba021db2d824d5d64173666a2921": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Bungee_Inline", "httpServerLocation": "/assets/assets/fonts/Bungee_Inline", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Bungee_Inline/BungeeInline-Regular.ttf"], "hash": "3d63ba021db2d824d5d64173666a2921", "name": "BungeeInline-Regular", "type": "ttf", "fileHashes": ["3d63ba021db2d824d5d64173666a2921"]}, "91019ffb3b1df640e444b34e5a73dfc3": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Nunito/static", "httpServerLocation": "/assets/assets/fonts/Nunito/static", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Nunito/static/Nunito-Bold.ttf"], "hash": "91019ffb3b1df640e444b34e5a73dfc3", "name": "Nunito-Bold", "type": "ttf", "fileHashes": ["91019ffb3b1df640e444b34e5a73dfc3"]}, "055c4df4e2f8c7a4d4675cdd8fa68da0": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Anton", "httpServerLocation": "/assets/assets/fonts/Anton", "scales": [1], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/assets/fonts/Anton/Anton-Regular.ttf"], "hash": "055c4df4e2f8c7a4d4675cdd8fa68da0", "name": "Anton-Regular", "type": "ttf", "fileHashes": ["055c4df4e2f8c7a4d4675cdd8fa68da0"]}, "a364dc7a784101f7c8f6791c7b4514ce": {"__packager_asset": true, "fileSystemLocation": "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets", "httpServerLocation": "/assets/node_modules/@react-navigation/elements/src/assets", "width": 24, "height": 24, "scales": [1, 1.5, 2, 3, 4], "files": ["/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>", "/home/<USER>/Documents/Git-LH/Lets-Hoop/IOS/node_modules/@react-navigation/elements/src/assets/<EMAIL>"], "hash": "a364dc7a784101f7c8f6791c7b4514ce", "name": "back-icon", "type": "png", "fileHashes": ["778ffc9fe8773a878e9c30a6304784de", "376d6a4c7f622917c39feb23671ef71d", "c79c3606a1cf168006ad3979763c7e0c", "02bc1fa7c0313217bde2d65ccbff40c9", "35ba0eaec5a4f5ed12ca16fabeae451d"]}}