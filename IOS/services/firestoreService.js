import {
  collection,
  query,
  where,
  startAfter,
  limit,
  getDocs,
} from "@react-native-firebase/firestore";
import { db } from "../config/firebase";
import { normalizeTimestamp } from "../hooks/normalizeTimestamp";

export class FirestoreService {
  constructor(collectionName) {
    this.collectionRef = collection(db, collectionName);
  }

  buildQuery(baseQuery, filters) {
    const arrayContainsFilters = [];
    const equalityFilters = [];

    // console.log("Incoming Filters:", filters);

    Object.entries(filters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          // Add non-empty array filters
          // console.log(`Adding array filter for key "${key}":`, value);
          arrayContainsFilters.push({ key, value });
        } else {
          // Skip empty arrays
          // console.log(`Skipping empty array filter: "${key}"`);
        }
      } else if (value) {
        // Add non-empty string/number filters
        // console.log(`Adding equality filter for key "${key}":`, value);
        equalityFilters.push({ key, value });
      } else {
        // Skip other empty values (e.g., empty strings or null)
        // console.log(`Skipping empty filter: "${key}"`);
      }
    });

    // Apply equality filters to the base query
    equalityFilters.forEach(({ key, value }) => {
      // console.log(`Applying equality filter: ${key} == ${value}`);
      baseQuery = query(baseQuery, where(key, "==", value));
    });

    // console.log("Constructed Query:", { baseQuery, arrayContainsFilters });
    return { baseQuery, arrayContainsFilters };
  }

  async executeQuery({ baseQuery, arrayContainsFilters }) {
    // console.log("Executing Query:");
    // console.log("Base Query:", baseQuery);
    // console.log("Array Filters:", arrayContainsFilters);

    if (arrayContainsFilters.length === 0) {
      const snapshot = await getDocs(baseQuery);
      // console.log("Snapshot size (base query):", snapshot.size);
      // console.log(
      //   "Documents:",
      //   snapshot.docs.map((doc) => doc.data())
      // );
      return snapshot.docs.map((doc) => ({
        id: doc.id,
        ...normalizeTimestamp(doc.data()),
      }));
    }

    const queryPromises = arrayContainsFilters.map(({ key, value }) => {
      // console.log(`Querying key "${key}" with values:`, value);
      return getDocs(query(baseQuery, where(key, "array-contains-any", value)));
    });

    const snapshots = await Promise.all(queryPromises);

    // snapshots.forEach((snapshot, index) => {
    //   console.log(
    //     `Snapshot size for key "${arrayContainsFilters[index].key}":`,
    //     snapshot.size
    //   );
    //   console.log(
    //     "Documents:",
    //     snapshot.docs.map((doc) => doc.data())
    //   );
    // });

    const results = new Map();
    snapshots.forEach((snapshot) => {
      snapshot.docs.forEach((doc) => {
        results.set(doc.id, { id: doc.id, ...normalizeTimestamp(doc.data()) });
      });
    });

    // console.log("Final Results:", Array.from(results.values()));
    return Array.from(results.values());
  }
}
