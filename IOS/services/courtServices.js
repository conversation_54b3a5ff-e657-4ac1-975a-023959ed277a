// File: services/CourtServices.js
import { auth, db, storage } from "../config/firebase";
import { collection, addDoc, doc, getDoc } from "firebase/firestore";
import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import * as ImageManipulator from "expo-image-manipulator";
import { MediaLibrary } from "expo";

/**
 * Service for fetching user location preferences from Firestore
 */
export const UserLocationService = {
  /**
   * Fetches user's saved location and radius preferences
   * @returns {Promise<{centerCoordinate: Object, radius: number}>}
   */
  async getUserLocationPreferences() {
    try {
      if (!auth.currentUser) {
        console.log("No authenticated user found");
        return { centerCoordinate: null, radius: 1000 };
      }

      const docRef = doc(db, "users", auth.currentUser.uid);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists) {
        const userData = docSnap.data();
        return {
          centerCoordinate: userData.centerCoord || null,
          radius: userData.radius || 1000,
        };
      }

      return { centerCoordinate: null, radius: 1000 };
    } catch (error) {
      console.error("Error fetching user location preferences:", error);
      throw error;
    }
  },
};

/**
 * Service for handling image operations
 */
export const ImageService = {
  /**
   * Process and compress an image for upload
   * @param {string} imageUri - The image URI to process
   * @returns {Promise<{uri: string, blob: Blob}>}
   */
  async processImage(imageUri) {
    try {
      // Handle different URI formats
      let uri = imageUri;
      if (
        imageUri.startsWith("content://") ||
        imageUri.startsWith("assets-library://")
      ) {
        const asset = await MediaLibrary.getAssetInfoAsync(imageUri);
        uri = asset.localUri;
      }

      // Compress and resize the image
      const compressedImage = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 1200 } }],
        { format: "jpeg", compress: 0.7 }
      );

      // Convert to blob
      const response = await fetch(compressedImage.uri);
      const blob = await response.blob();

      return { uri: compressedImage.uri, blob };
    } catch (error) {
      console.error("Error processing image:", error);
      throw error;
    }
  },

  /**
   * Uploads an image to Firebase Storage
   * @param {Blob} blob - The image blob to upload
   * @param {string} path - The storage path
   * @returns {Promise<string>} - The download URL
   */
  async uploadImage(blob, path) {
    try {
      const storageRef = ref(storage, path);
      const snapshot = await uploadBytesResumable(storageRef, blob);
      return await getDownloadURL(snapshot.ref);
    } catch (error) {
      console.error("Error uploading image:", error);
      throw error;
    }
  },
};

/**
 * Service for court operations
 */
export const CourtService = {
  /**
   * Uploads a new court to Firestore with images
   * @param {Object} courtData - Complete court data
   * @returns {Promise<string>} - The document ID of the created court
   */
  async uploadCourt(courtData) {
    try {
      // Create a timestamp ID for the court
      const cuid = new Date().getTime();
      const imagesRefs = [];

      // Upload all images in parallel
      const uploadPromises = courtData.images.map(async (image, index) => {
        const storagePath = `/new-courts-images/${auth.currentUser.uid}/${cuid}-${index}`;

        // Process the image
        const processedImage = await ImageService.processImage(image);

        // Upload to Firebase Storage
        const downloadUrl = await ImageService.uploadImage(
          processedImage.blob,
          storagePath
        );

        // Add to image references array
        imagesRefs.push(downloadUrl);
      });

      // Wait for all images to be uploaded
      await Promise.all(uploadPromises);

      // Create court document in Firestore
      const courtDoc = {
        date: cuid,
        created: new Date(),
        userUid: auth.currentUser.uid,
        userEmail:
          auth.currentUser.email || auth.currentUser.providerData[0]?.email,
        imagesRefs: imagesRefs,
        courtName: courtData.courtName,
        surface: courtData.surface,
        differentSurface: courtData.differentSurface,
        basketsCount: courtData.basketsCount,
        courtDescription: courtData.courtDescription,
        coords: courtData.coordinates,
        boardSize: courtData.boardSize === 1 ? "regular" : "small",
        lines: courtData.lines === 1 ? true : false,
        lighting: courtData.lighting === 1 ? true : false,
        fullCourt: courtData.fullCourt === 1 ? true : false,
        indoorOutdoor: courtData.indoorOutdoor === 1 ? "outdoor" : "indoor",
        publicPrivate: courtData.publicPrivate === 1 ? "public" : "private",
        waterFountain: courtData.waterFountain === 1 ? true : false,
        seating: courtData.seating === 1 ? true : false,
        fencing: courtData.fencing === 1 ? true : false,
        accessible: courtData.accessible === 1 ? true : false,
        status: "pending", // New courts are pending until approved
      };

      // Add court to Firestore
      const docRef = await addDoc(collection(db, "new_courts"), courtDoc);
      return docRef.id;
    } catch (error) {
      console.error("Error uploading court:", error);
      throw error;
    }
  },
};
