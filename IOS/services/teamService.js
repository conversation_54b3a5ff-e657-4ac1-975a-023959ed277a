export class TeamService {
  constructor(teamRepository) {
    this.repository = teamRepository;
  }

  async fetchTeams(filters = {}, loadMore = false, lastVisible = null) {
    try {
      const paginatedFilters = {
        ...filters,
        ...(loadMore && lastVisible ? { startAfter: lastVisible } : {}),
      };

      const queryConfig = this.repository.buildQuery(paginatedFilters);
      return await this.repository.executeQuery(queryConfig);
    } catch (error) {
      console.error("Error fetching teams:", error);
      throw error;
    }
  }
}
