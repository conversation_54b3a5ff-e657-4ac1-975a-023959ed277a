import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  Image,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
  Animated,
  Easing,
} from "react-native";
import { red } from "./screens/colors";

const { height, width } = Dimensions.get("window");

const LoadingScreen = ({ message = "Preparing the app for you..." }) => {
  // Animation for subtle pulsing effect
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();

    // Create a pulsing animation for the logo
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.05,
          duration: 1500,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 1500,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [scaleAnim, opacityAnim]);

  return (
    <Animated.View style={[styles.container, { opacity: opacityAnim }]}>
      <View style={styles.logoContainer}>
        <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
          <Image
            source={require("./images/streetball2e.jpg")}
            style={styles.logo}
            resizeMode="contain"
          />
        </Animated.View>
      </View>

      <View style={styles.bottomContent}>
        <Text style={styles.message}>{message}</Text>
        <ActivityIndicator size="small" color={red} />
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  logoContainer: {
    position: "absolute",
    top: height * 0.2, // Position at 20% from the top of the screen
    alignSelf: "center",
    zIndex: 100,
  },
  logo: {
    width: width * 0.6, // 60% of screen width
    height: width * 0.6, // Maintain aspect ratio (assuming logo is about half as tall as wide)
  },
  bottomContent: {
    position: "absolute",
    bottom: 100,
    alignItems: "center",
  },
  message: {
    fontSize: 18,
    color: "#555",
    textAlign: "center",
    marginBottom: 20,
    fontWeight: "500",
  },
});

export default LoadingScreen;
