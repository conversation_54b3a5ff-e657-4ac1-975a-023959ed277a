// A utility file to manage Firestore rate limiting and connection issues
// Save this as: utils/firestoreHelper.js

import { collection, query, getDocs, where, orderBy, limit, getCountFromServer } from "firebase/firestore";

/**
 * Helper class to manage Firestore rate limiting and batching
 */
export class FirestoreHelper {
  /**
   * Performs batched aggregation queries to avoid rate limiting
   * @param {object} db - Firestore database instance 
   * @param {string} collectionName - Collection to query
   * @param {array} items - Array of items to query by (e.g. court IDs)
   * @param {string} fieldName - Field name to filter on
   * @param {number} batchSize - Size of each batch (default: 5)
   * @param {number} delayMs - Delay between batches in ms (default: 500)
   * @returns {object} - Object with results mapped by item ID
   */
  static async batchedAggregationQueries(db, collectionName, items, fieldName, batchSize = 5, delayMs = 500) {
    const results = {};
    const batches = [];
    
    // Split items into batches
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    console.log(`Processing ${items.length} items in ${batches.length} batches of size ${batchSize}`);
    
    // Process each batch with delay between batches
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchPromises = batch.map(async (item) => {
        try {
          const coll = collection(db, collectionName);
          const q = query(coll, where(fieldName, "==", item));
          const snapshot = await getCountFromServer(q);
          const count = snapshot.data().count;
          results[item] = count;
          return { item, count };
        } catch (error) {
          console.error(`Error querying for ${item}:`, error);
          results[item] = 0; // Default to 0 on error
          return { item, error };
        }
      });
      
      // Execute current batch
      await Promise.all(batchPromises);
      
      // Delay before next batch (except for the last batch)
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }
    
    return results;
  }
  
  /**
   * Throttles Firestore queries to prevent rate limiting
   * @param {function} queryFn - Async function that performs the query
   * @param {number} maxConcurrent - Maximum concurrent queries
   * @param {number} delayMs - Delay between queries in ms
   * @returns {array} - Query results
   */
  static async throttledQueries(queries, maxConcurrent = 5, delayMs = 500) {
    const results = [];
    const pending = [];
    
    // Create a queue of queries
    const queue = [...queries];
    
    // Process queries with throttling
    while (queue.length > 0 || pending.length > 0) {
      // Fill pending array up to maxConcurrent
      while (pending.length < maxConcurrent && queue.length > 0) {
        const query = queue.shift();
        const promise = query()
          .then(result => {
            results.push(result);
            const index = pending.indexOf(promise);
            if (index !== -1) {
              pending.splice(index, 1);
            }
            return result;
          })
          .catch(error => {
            console.error("Query error:", error);
            const index = pending.indexOf(promise);
            if (index !== -1) {
              pending.splice(index, 1);
            }
            throw error;
          });
        
        pending.push(promise);
        
        // Small delay between starting queries
        if (queue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, delayMs / maxConcurrent));
        }
      }
      
      // Wait for at least one query to complete if pending is full
      if (pending.length >= maxConcurrent || (pending.length > 0 && queue.length === 0)) {
        await Promise.race(pending);
      }
    }
    
    return results;
  }
  
  /**
   * Performs paginated queries to avoid loading too much data at once
   * @param {object} db - Firestore database instance
   * @param {object} baseQuery - Base query to paginate
   * @param {number} pageSize - Size of each page
   * @param {function} processPage - Function to process each page of results
   * @param {number} maxPages - Maximum number of pages to fetch (optional)
   */
  static async paginatedQuery(db, baseQuery, pageSize, processPage, maxPages = null) {
    let lastDoc = null;
    let pageCount = 0;
    let hasMore = true;
    
    while (hasMore && (maxPages === null || pageCount < maxPages)) {
      // Create paginated query
      let paginatedQuery = baseQuery;
      
      // Add limit and startAfter if needed
      paginatedQuery = query(paginatedQuery, limit(pageSize));
      if (lastDoc) {
        paginatedQuery = query(paginatedQuery, startAfter(lastDoc));
      }
      
      // Execute query
      const querySnapshot = await getDocs(paginatedQuery);
      
      // Process results
      const pageResults = [];
      querySnapshot.forEach(doc => {
        pageResults.push({
          id: doc.id,
          ...doc.data()
        });
      });
      
      // Call process function with results
      await processPage(pageResults, pageCount);
      
      // Update pagination variables
      pageCount++;
      hasMore = !querySnapshot.empty;
      lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      
      // Add delay between pages
      if (hasMore && (maxPages === null || pageCount < maxPages)) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    return pageCount;
  }
}