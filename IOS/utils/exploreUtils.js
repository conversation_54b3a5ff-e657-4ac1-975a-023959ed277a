// utils/exploreUtils.js
import { getDistance } from "geolib";
import { standardizeCoordinates } from "./mapUtils";

// Function to calculate if a game is within the filter radius
export const isWithinRadius = (gameCoords, centerPoint, radius) => {
  // If we don't have coordinates to filter with, include all games
  if (!centerPoint || !gameCoords) return true;
  
  try {
    const gamePoint = standardizeCoordinates(gameCoords);
    
    if (!gamePoint) return true;
    
    const distance = getDistance(
      {
        latitude: centerPoint.latitude,
        longitude: centerPoint.longitude,
      },
      {
        latitude: gamePoint.latitude,
        longitude: gamePoint.longitude,
      }
    );
    
    return distance <= radius;
  } catch (err) {
    console.error("Error calculating distance:", err);
    // On error, include the game (better to show too many than too few)
    return true;
  }
};

// Process game documents from Firestore
export const processGameDocs = (docs) => {
  const now = new Date();
  let processedGames = [];
  
  docs.forEach((doc) => {
    try {
      const gameData = doc.data();
      
      // Convert timestamps to JavaScript dates
      let timeStart, timeEnd, date;
      
      try {
        timeStart = gameData.timeStart?.toDate?.() || new Date(gameData.timeStart);
      } catch (e) {
        console.error(`Error parsing timeStart: ${e.message}`);
        timeStart = new Date();
      }
      
      try {
        timeEnd = gameData.timeEnd?.toDate?.() || new Date(gameData.timeEnd);
      } catch (e) {
        console.error(`Error parsing timeEnd: ${e.message}`);
        timeEnd = new Date(Date.now() + 3600000); // Default 1 hour from now
      }
      
      try {
        date = gameData.date?.toDate?.() || new Date(gameData.date);
      } catch (e) {
        console.error(`Error parsing date: ${e.message}`);
        date = new Date();
      }
      
      // Extract coordinates safely and ensure they're serializable objects
      const gameCoords = standardizeCoordinates(gameData.coords);
      
      // Add document ID to the game data
      const game = {
        ...gameData,
        eventID: doc.id,
        timeStart,
        timeEnd,
        date,
        isLive: timeStart <= now,
        usersCount: gameData.users?.length || 0,
        // Ensure coords is a plain object for Redux (not a GeoPoint)
        coords: gameCoords
      };
      
      processedGames.push(game);
    } catch (err) {
      console.error(`Error processing game ${doc.id}:`, err);
    }
  });
  
  return processedGames;
};