// utils/mapUtils.js
import { GeoPoint } from "firebase/firestore";

// Convert coordinates to a standard format
export const standardizeCoordinates = (coords) => {
  if (!coords) return null;
  
  // Handle different possible structures of coordinates
  if (coords instanceof GeoPoint) {
    return {
      latitude: Number(coords.latitude),
      longitude: Number(coords.longitude)
    };
  } else if (coords._lat !== undefined && coords._long !== undefined) {
    return {
      latitude: Number(coords._lat),
      longitude: Number(coords._long)
    };
  } else if (coords.latitude !== undefined && coords.longitude !== undefined) {
    return {
      latitude: Number(coords.latitude),
      longitude: Number(coords.longitude)
    };
  }
  
  return null;
};

// Calculate map zoom level based on radius
export const calculateMapDeltaForRadius = (radiusInMeters, centerLatitude = 0) => {
  // Convert radius from meters to approximate latitude degrees
  // This is a simplified calculation that works reasonably well
  // 111,000 meters is roughly 1 degree of latitude
  const latDelta = (radiusInMeters * 2.5) / 111000;
  
  // Adjust longitude delta based on latitude (gets wider near poles)
  // This formula accounts for the fact that longitude degrees get narrower at higher latitudes
  const cosLat = Math.cos((Math.abs(centerLatitude) * Math.PI) / 180);
  const lonDelta = latDelta / Math.max(0.25, cosLat);
  
  return {
    latitudeDelta: latDelta,
    longitudeDelta: lonDelta
  };
};

// Default initial coordinates (fallback)
export const DEFAULT_COORDINATES = {
  latitude: 50.0755,
  longitude: 14.4378
};

// Default radius in meters
export const DEFAULT_RADIUS = 50000;

// Min/max allowed radius values
export const MIN_RADIUS = 1000;
export const MAX_RADIUS = 50000;