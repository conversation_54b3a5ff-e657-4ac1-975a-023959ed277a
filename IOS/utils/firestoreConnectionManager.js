// Firestore Connection Manager
// Save as: utils/firestoreConnectionManager.js

import { enableNetwork, disableNetwork, waitForPendingWrites } from "firebase/firestore";

/**
 * Class to manage Firestore connection states and handle reconnection strategies
 */
export class FirestoreConnectionManager {
  constructor(db) {
    this.db = db;
    this.isOnline = true;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 2000; // Start with 2 seconds
    this.pendingOperations = new Set();
    this.setupNetworkListeners();
  }

  /**
   * Setup network state change listeners
   */
  setupNetworkListeners() {
    // Listen for online/offline events
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    // Clean up on unmount
    return () => {
      window.removeEventListener('online', this.handleOnline);
      window.removeEventListener('offline', this.handleOffline);
    };
  }

  /**
   * Handle device coming online
   */
  handleOnline = async () => {
    this.isOnline = true;
    this.reconnectAttempts = 0;
    console.log('Device is online, reconnecting to Firestore');
    try {
      await enableNetwork(this.db);
      console.log('Firestore network enabled');
      this.processPendingOperations();
    } catch (error) {
      console.error('Error enabling Firestore network:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Handle device going offline
   */
  handleOffline = async () => {
    this.isOnline = false;
    console.log('Device is offline, disabling Firestore network');
    try {
      await disableNetwork(this.db);
      console.log('Firestore network disabled');
    } catch (error) {
      console.error('Error disabling Firestore network:', error);
    }
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  scheduleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts - 1);
      console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
      
      setTimeout(() => {
        if (this.isOnline) {
          this.handleOnline();
        }
      }, delay);
    } else {
      console.warn('Max reconnection attempts reached');
    }
  }

  /**
   * Register a pending operation to be processed when connection is restored
   * @param {function} operation - Async function to execute
   * @returns {string} - Operation ID
   */
  registerPendingOperation(operation) {
    const id = Date.now().toString() + Math.random().toString(36).substring(2, 9);
    this.pendingOperations.add({ id, operation });
    return id;
  }

  /**
   * Process all pending operations
   */
  async processPendingOperations() {
    if (this.pendingOperations.size === 0) return;
    
    console.log(`Processing ${this.pendingOperations.size} pending operations`);
    
    // Wait for any pending writes to complete first
    try {
      await waitForPendingWrites(this.db);
      
      // Create a copy to avoid issues if new operations are added during processing
      const operations = [...this.pendingOperations];
      this.pendingOperations.clear();
      
      // Process operations sequentially to avoid overwhelming Firestore
      for (const { id, operation } of operations) {
        try {
          await operation();
          console.log(`Completed pending operation ${id}`);
        } catch (error) {
          console.error(`Error processing pending operation ${id}:`, error);
          // Re-add failed operations if they should be retried
          if (error.code !== 'permission-denied' && error.code !== 'not-found') {
            this.pendingOperations.add({ id, operation });
          }
        }
      }
    } catch (error) {
      console.error('Error processing pending operations:', error);
    }
  }

  /**
   * Execute a Firestore operation with automatic retry on connection issues
   * @param {function} operation - Async function that performs Firestore operation
   * @param {Object} options - Options for the operation
   * @returns {Promise} - Result of the operation
   */
  async executeOperation(operation, options = {}) {
    const { retries = 3, retryDelayMs = 1000, critical = false } = options;
    
    if (!this.isOnline && critical) {
      // For critical operations, register them as pending if offline
      const opId = this.registerPendingOperation(operation);
      throw new Error(`Device is offline. Operation ${opId} will be executed when connection is restored.`);
    }
    
    let lastError = null;
    let attempt = 0;
    
    while (attempt <= retries) {
      try {
        const result = await operation();
        return result;
      } catch (error) {
        lastError = error;
        attempt++;
        
        // Check if the error is related to network connectivity
        if (error.code === 'unavailable' || error.code === 'deadline-exceeded') {
          if (attempt <= retries) {
            const delay = retryDelayMs * Math.pow(2, attempt - 1);
            console.warn(`Firestore operation failed (attempt ${attempt}/${retries}). Retrying in ${delay}ms...`, error);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        } else {
          // Non-connectivity errors should not be retried
          break;
        }
      }
    }
    
    // If we reached here, all attempts failed
    if (!this.isOnline && options.queueIfOffline) {
      const opId = this.registerPendingOperation(operation);
      console.log(`Operation ${opId} queued for when connectivity is restored`);
      throw new Error(`Operation failed after ${attempt} attempts and was queued (${opId})`);
    }
    
    throw lastError || new Error(`Operation failed after ${attempt} attempts`);
  }
  
  /**
   * Clean up resources when the manager is no longer needed
   */
  dispose() {
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
  }
}

// Create and export a singleton instance
let connectionManager = null;

export const getConnectionManager = (db) => {
  if (!connectionManager) {
    connectionManager = new FirestoreConnectionManager(db);
  }
  return connectionManager;
};