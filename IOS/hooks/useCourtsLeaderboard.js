// File: hooks/useCourtsLeaderboard.js
import { useState, useEffect, useCallback } from "react";
import {
  collection,
  query,
  getDocs,
  getDoc,
  doc,
  where,
} from "firebase/firestore";
import { db, auth } from "../config/firebase";
import { useSelector } from "react-redux";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Cache keys
const LEADERBOARD_CACHE_KEY = "courts_leaderboard_cache";
const CACHE_TIMESTAMP_KEY = "courts_leaderboard_timestamp";
const CACHE_EXPIRY_TIME = 15 * 60 * 1000; // 15 minutes in milliseconds

/**
 * Custom hook to fetch courts leaderboard data with local storage caching
 *
 * @returns {Object} Leaderboard data including top contributors and user stats
 */
const useCourtsLeaderboard = () => {
  const [leaderboardData, setLeaderboardData] = useState({
    topContributors: [],
    userRank: null,
    userContributions: 0,
    isLoading: true,
    error: null,
  });

  // Try to get courts from Redux if available
  const reduxCourts = useSelector((state) => state.socials.courts);

  // Load cached data first
  useEffect(() => {
    const loadCachedData = async () => {
      try {
        const cachedTimestampStr = await AsyncStorage.getItem(
          CACHE_TIMESTAMP_KEY
        );
        const cachedTimestamp = cachedTimestampStr
          ? parseInt(cachedTimestampStr)
          : 0;
        const now = Date.now();

        // Check if cache is still valid
        if (now - cachedTimestamp < CACHE_EXPIRY_TIME) {
          const cachedDataStr = await AsyncStorage.getItem(
            LEADERBOARD_CACHE_KEY
          );
          if (cachedDataStr) {
            const cachedData = JSON.parse(cachedDataStr);
            console.log("Using cached leaderboard data");

            // Update state with cached data
            setLeaderboardData({
              ...cachedData,
              isLoading: false,
              error: null,
            });

            // Refresh in background if cached data is older than 5 minutes
            if (now - cachedTimestamp > 5 * 60 * 1000) {
              refreshLeaderboardInBackground();
            }

            return true; // Cache was used
          }
        }
        return false; // Cache was not used
      } catch (error) {
        console.error("Error loading cache:", error);
        return false;
      }
    };

    loadCachedData().then((cacheUsed) => {
      if (!cacheUsed) {
        // If cache wasn't used, load fresh data
        refreshLeaderboard();
      }
    });
  }, []);

  // Function to refresh leaderboard data
  const refreshLeaderboard = useCallback(() => {
    setLeaderboardData((prev) => ({
      ...prev,
      isLoading: true,
    }));

    fetchLeaderboardData(false);
  }, []);

  // Function to refresh leaderboard in background without showing loading state
  const refreshLeaderboardInBackground = () => {
    fetchLeaderboardData(true);
  };

  // Function to fetch user contribution and rank
  const fetchUserStats = async (contributorsMap = null) => {
    if (!auth.currentUser) return { userRank: null, userContributions: 0 };

    const currentUid = auth.currentUser.uid;
    let userContributions = 0;
    let userRank = null;

    // If we have a contributors map, use it to find user rank
    if (contributorsMap) {
      const contributorsArray = Object.values(contributorsMap);
      contributorsArray.sort((a, b) => b.count - a.count);
      const userIndex = contributorsArray.findIndex(
        (c) => c.uid === currentUid
      );

      if (userIndex >= 0) {
        userRank = userIndex + 1;
        userContributions = contributorsArray[userIndex].count;
        return { userRank, userContributions };
      }
    }

    // Otherwise, get user contributions directly
    try {
      if (reduxCourts && reduxCourts.length > 0) {
        userContributions = reduxCourts.filter(
          (court) => court.user === currentUid
        ).length;
      } else {
        const userCourtsQuery = query(
          collection(db, "courts-cz1"),
          where("user", "==", currentUid)
        );
        const userCourtsSnapshot = await getDocs(userCourtsQuery);
        userContributions = userCourtsSnapshot.size;
      }
    } catch (error) {
      console.error("Error fetching user contributions:", error);
    }

    return { userRank, userContributions };
  };

  // Main function to fetch and process leaderboard data
  const fetchLeaderboardData = async (isBackgroundRefresh = false) => {
    if (!isBackgroundRefresh) {
      setLeaderboardData((prev) => ({
        ...prev,
        isLoading: true,
        error: null,
      }));
    }

    try {
      // Step 1: Get contributors map
      let contributorsMap = {};

      if (reduxCourts && reduxCourts.length > 0) {
        console.log("Using courts from Redux:", reduxCourts.length);

        reduxCourts.forEach((court) => {
          const userUid = court.user;
          if (userUid) {
            if (!contributorsMap[userUid]) {
              contributorsMap[userUid] = {
                uid: userUid,
                count: 0,
                username: "Anonymous",
              };
            }
            contributorsMap[userUid].count += 1;
          }
        });
      } else {
        const courtsCollection = collection(db, "courts-cz1");
        const courtsSnapshot = await getDocs(courtsCollection);

        console.log("Fetched courts from Firestore:", courtsSnapshot.size);

        courtsSnapshot.forEach((doc) => {
          const courtData = doc.data();
          const userUid = courtData.user;

          if (userUid) {
            if (!contributorsMap[userUid]) {
              contributorsMap[userUid] = {
                uid: userUid,
                count: 0,
                username: "Anonymous",
              };
            }
            contributorsMap[userUid].count += 1;
          }
        });
      }

      // Step 2: Sort contributors and get top 10
      let contributorsArray = Object.values(contributorsMap);
      contributorsArray.sort((a, b) => b.count - a.count);
      let topContributors = contributorsArray.slice(0, 10);

      // Step 3: Get user stats
      const { userRank, userContributions } = await fetchUserStats(
        contributorsMap
      );

      // Step 4: Fetch usernames for top 10 in parallel
      const fetchPromises = topContributors.map(async (contributor) => {
        try {
          const publicDocRef = doc(
            db,
            `users/${contributor.uid}/public`,
            `${contributor.uid}public`
          );
          const publicDocSnap = await getDoc(publicDocRef);

          if (publicDocSnap.exists) {
            const userData = publicDocSnap.data();
            if (userData.name) {
              contributor.username = userData.name;
            }
          }
          return contributor;
        } catch (error) {
          console.log(
            `Error fetching user data for ${contributor.uid}:`,
            error
          );
          return contributor;
        }
      });

      // Wait for all username fetches to complete
      topContributors = await Promise.all(fetchPromises);

      // Step 5: Create result object
      const resultData = {
        topContributors,
        userRank,
        userContributions,
        isLoading: false,
        error: null,
      };

      // Step 6: Cache the result
      try {
        await AsyncStorage.setItem(
          LEADERBOARD_CACHE_KEY,
          JSON.stringify(resultData)
        );
        await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
      } catch (cacheError) {
        console.error("Error caching leaderboard data:", cacheError);
      }

      // Step 7: Update state
      setLeaderboardData(resultData);
    } catch (error) {
      console.error("Error fetching leaderboard data:", error);
      
      if (!isBackgroundRefresh) {
        setLeaderboardData((prev) => ({
          ...prev,
          isLoading: false,
          error: error.message,
        }));
      }
    }
  };

  return {
    ...leaderboardData,
    refreshLeaderboard,
  };
};

export default useCourtsLeaderboard;