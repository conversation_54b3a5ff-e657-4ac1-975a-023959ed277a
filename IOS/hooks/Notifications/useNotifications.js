// /hooks/Notifications/useNotifications.js
import { useCallback } from "react";
import { Alert } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { auth, db, functions } from "../../config/firebase";
import { doc, updateDoc } from "@react-native-firebase/firestore";
import { httpsCallable } from "@react-native-firebase/functions";
import { useToast } from "../toast/useToast";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

// Cloud functions
const acceptFriendRequestCloud = httpsCallable(
  functions,
  "acceptFriendRequestV2"
);
const respondToTeamInviteV2 = httpsCallable(functions, "respondToTeamInviteV2");
const deleteNotificationCloud = httpsCallable(functions, "deleteNotification");

export const useNotifications = () => {
  const dispatch = useDispatch();
  const { notifications } = useSelector((state) => state.socials);
  const { language } = useSelector((state) => state.language);
  const showToast = useToast();

  // Helper function to update notifications in Redux
  const updateNotifications = useCallback(
    (updatedNotifications) => {
      dispatch({
        type: "socials/setNotifications",
        payload: updatedNotifications,
      });
    },
    [dispatch]
  );

  // Remove notification completely
  const removeNotification = useCallback(
    async (notification) => {
      // Store original notifications for potential rollback
      const originalNotifications = notifications;
      const notificationId =
        typeof notification === "string" ? notification : notification.id;

      // Optimistic update - remove from Redux immediately
      const updatedNotifications = notifications.filter(
        (n) => n.id !== notificationId
      );
      updateNotifications(updatedNotifications);

      try {
        // Call cloud function to delete notification
        await deleteNotificationCloud({ notificationId });
      } catch (error) {
        recordCrashlyticsError(error, "useNotifications-removeNotification");
        console.error("Error removing notification:", error);

        // Rollback the optimistic update
        updateNotifications(originalNotifications);

        // Show error toast
        showToast(
          language.errorRemovingNotification || "Failed to remove notification",
          "error"
        );
      }
    },
    [notifications, updateNotifications, language, showToast]
  );

  // Accept friend request
  const acceptFriendRequest = useCallback(
    async (notification) => {
      // Store original notifications for potential rollback
      const originalNotifications = notifications;

      // Optimistic update - update notification status
      const updatedNotifications = notifications.map((n) =>
        n.id === notification.id ? { ...n, accepted: true } : n
      );
      updateNotifications(updatedNotifications);

      try {
        // Call cloud function
        await acceptFriendRequestCloud({
          notificationId: notification.id,
          to: auth.currentUser.uid,
          from: notification.sender.uid,
        });

        // Show success toast
        // showToast(
        //   language.friendRequestAccepted ||
        //     "Friend request accepted successfully",
        //   "success"
        // );
      } catch (error) {
        recordCrashlyticsError(error, "useNotifications-acceptFriendRequest");
        console.error("Error accepting friend request:", error);

        // Rollback the optimistic update
        updateNotifications(originalNotifications);

        // Show error toast
        showToast(
          language.errorAcceptingRequest || "Failed to accept friend request",
          "error"
        );
      }
    },
    [notifications, updateNotifications, language, showToast]
  );

  // Accept team invite
  const acceptTeamInvite = useCallback(
    async (notification) => {
      // Store original notifications for potential rollback
      const originalNotifications = notifications;

      // Optimistic update - update notification status
      const updatedNotifications = notifications.map((n) =>
        n.id === notification.id ? { ...n, accepted: true } : n
      );
      updateNotifications(updatedNotifications);

      // Show loading toast and get its ID for updating later
      const toastId = showToast.loading(
        language.joiningTeam || "Joining team..."
      );

      try {
        // Call cloud function
        await respondToTeamInviteV2({
          accept: true,
          teamId: notification.teamId,
          notificationId: notification.id,
        });

        // Update toast to success
        setTimeout(() => {
          showToast.update(
            toastId,
            language.joinedTeamSuccessfully || "Successfully joined the team",
            "success"
          );
        }, 200);
      } catch (error) {
        console.error("Error accepting team invite:", error);

        // Rollback the optimistic update
        updateNotifications(originalNotifications);

        // Update toast to error
        showToast.update(
          toastId,
          language.errorJoiningTeam || "Failed to join team",
          "error"
        );
      }
    },
    [notifications, updateNotifications, language, showToast]
  );

  // Decline team invite - notify the sender
  const declineTeamInvite = useCallback(
    async (notification) => {
      // Store original notifications for potential rollback
      const originalNotifications = notifications;

      // Optimistic update
      const updatedNotifications = notifications.filter(
        (n) => n.id !== notification.id
      );
      updateNotifications(updatedNotifications);

      try {
        // Call cloud function to decline (notifies the sender)
        await respondToTeamInviteV2({
          accept: false,
          teamId: notification.teamId,
          notificationId: notification.id,
        });
      } catch (error) {
        recordCrashlyticsError(error, "useNotifications-declineTeamInvite");
        console.error("Error declining team invite:", error);

        // Rollback the optimistic update
        updateNotifications(originalNotifications);

        // Show error toast
        showToast(
          language.errorDecliningInvite || "Failed to decline invite",
          "error"
        );
      }
    },
    [notifications, updateNotifications, language, showToast]
  );

  return {
    notifications,
    removeNotification,
    acceptFriendRequest,
    acceptTeamInvite,
    declineTeamInvite,
  };
};
