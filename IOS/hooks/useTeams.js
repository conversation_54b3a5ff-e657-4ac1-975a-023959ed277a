import { useState } from "react";
import { FirestoreService } from "../services/firestoreService";
import { TeamRepository } from "../repositories/teamRepository";
import { TeamService } from "../services/teamService";
import { recordCrashlyticsError } from "../functions/recordCrashlyticsError";

export const useTeams = (appliedFilters) => {
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [lastVisible, setLastVisible] = useState(null);
  const [hasMore, setHasMore] = useState(true);

  // Initialize services
  const firestoreService = new FirestoreService("teams");
  const teamRepository = new TeamRepository(firestoreService);
  const teamService = new TeamService(teamRepository);

  const fetchTeams = async (loadMore = false, filters = {}) => {
    if ((loadMore && loadingMore) || (!loadMore && loading)) return;

    loadMore ? setLoadingMore(true) : setLoading(true);

    try {
      const teams = await teamService.fetchTeams(
        filters,
        loadMore,
        lastVisible
      );
      // console.log("fetchTeamssss", teams);

      if (loadMore) {
        setTeams((prev) => [...prev, ...teams]);
      } else {
        setTeams(teams);
      }

      if (teams.length > 0) {
        setLastVisible(teams[teams.length - 1]);
      }
      if (teams.length < 10) setHasMore(false);
    } catch (error) {
      recordCrashlyticsError(error, "useTeams-fetchTeams");
      console.error("Error fetching teams:", error);
    } finally {
      loadMore ? setLoadingMore(false) : setLoading(false);
    }
  };

  const filteredTeams = teams.filter((team) => {
    if (!appliedFilters) return true;

    const { skillLevel, type, ageGroup } = appliedFilters;

    const result =
      (skillLevel ? team.skillLevel === skillLevel : true) &&
      (type ? team.type === type : true) &&
      (ageGroup ? team.ageGroup === ageGroup : true);

    // console.log("filteredTeams result", result);

    return result;
  });

  return {
    teams,
    filteredTeams,
    fetchTeams,
    loading,
    loadingMore,
    hasMore,
  };
};

export default useTeams;
