export const normalizeTimestamp = (docData) => {
  const normalizedData = { ...docData };
  Object.keys(normalizedData).forEach((key) => {
    if (
      normalizedData[key] &&
      typeof normalizedData[key] === "object" &&
      normalizedData[key].seconds !== undefined &&
      normalizedData[key].nanoseconds !== undefined
    ) {
      // Convert Firestore Timestamp to ISO string
      normalizedData[key] = new Date(
        normalizedData[key].seconds * 1000
      ).toISOString();
    }
  });
  return normalizedData;
};

export default normalizeTimestamp;
