// hooks/useExplore.js
import { useState, useEffect, useCallback, useRef } from "react";
import { auth, db } from "../config/firebase";
import { getDistance } from "geolib";
import { standardizeCoordinates, DEFAULT_COORDINATES, MIN_RADIUS, MAX_RADIUS } from "../utils/mapUtils";

export const useExplore = () => {
  // Core state variables
  const [games, setGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [filterRadius, setFilterRadius] = useState(MAX_RADIUS); // Default 50km in meters
  const [centerCoords, setCenterCoords] = useState(null);
  
  // Pagination state
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreGames, setHasMoreGames] = useState(true);
  const [lastVisibleDoc, setLastVisibleDoc] = useState(null);
  const PAGE_SIZE = 10; // Number of games to fetch per page
  
  // Use refs to track state between renders and avoid race conditions
  const radiusRef = useRef(filterRadius);
  const centerCoordsRef = useRef(centerCoords);
  const pendingFetchRef = useRef(false);
  const fetchTimerRef = useRef(null);

  // Update refs when state changes
  useEffect(() => {
    radiusRef.current = filterRadius;
  }, [filterRadius]);

  useEffect(() => {
    centerCoordsRef.current = centerCoords;
  }, [centerCoords]);

  // Load user's location preferences on mount
  useEffect(() => {
    const loadUserPreferences = async () => {
      try {
        console.log("Loading user preferences for useExplore");
        if (!auth.currentUser) {
          console.log("No authenticated user found");
          // Set default coordinates
          setCenterCoords(DEFAULT_COORDINATES);
          centerCoordsRef.current = DEFAULT_COORDINATES;
          return;
        }
        
        // React Native Firebase namespaced API syntax
        const userDoc = await db.collection("users").doc(auth.currentUser.uid).get();
        
        if (userDoc.exists) {  // Note: no parentheses here, it's a property not a method
          const userData = userDoc.data();
          
          if (userData.radius) {
            // Ensure radius is a number and within valid range
            const numericRadius = Number(userData.radius);
            const validRadius = Math.min(Math.max(numericRadius, MIN_RADIUS), MAX_RADIUS);
            console.log("Setting filterRadius to:", validRadius);
            setFilterRadius(validRadius);
            radiusRef.current = validRadius;
          }
          
          if (userData.centerCoord) {
            console.log("Setting centerCoords from user data");
            // Convert GeoPoint to plain object to avoid serialization issues
            const plainCoords = standardizeCoordinates(userData.centerCoord);
            
            if (plainCoords) {
              setCenterCoords(plainCoords);
              centerCoordsRef.current = plainCoords;
            } else {
              // If coordinates couldn't be standardized, use defaults
              setCenterCoords(DEFAULT_COORDINATES);
              centerCoordsRef.current = DEFAULT_COORDINATES;
            }
          } else {
            // If no coordinates, use defaults
            console.log("No centerCoord in user data, using defaults");
            setCenterCoords(DEFAULT_COORDINATES);
            centerCoordsRef.current = DEFAULT_COORDINATES;
          }
        } else {
          console.log("No user document found for radius/location settings");
          setCenterCoords(DEFAULT_COORDINATES);
          centerCoordsRef.current = DEFAULT_COORDINATES;
        }
      } catch (error) {
        console.error("Error loading user preferences:", error);
        // Set defaults even on error
        setCenterCoords(DEFAULT_COORDINATES);
        centerCoordsRef.current = DEFAULT_COORDINATES;
      }
    };

    loadUserPreferences();
  }, []);

  // Trigger an initial fetch when preferences are loaded
  useEffect(() => {
    if (centerCoords && !games.length && !pendingFetchRef.current) {
      console.log("Initial fetch after preferences loaded");
      fetchGames();
    }
  }, [centerCoords]);

  // Function to calculate if a game is within the filter radius
  const isWithinRadius = useCallback((gameCoords, centerPoint, radius) => {
    // If we don't have coordinates to filter with, include all games
    if (!centerPoint || !gameCoords) return true;
    
    try {
      const gamePoint = standardizeCoordinates(gameCoords);
      
      if (!gamePoint) return true;
      
      const distance = getDistance(
        {
          latitude: centerPoint.latitude,
          longitude: centerPoint.longitude,
        },
        {
          latitude: gamePoint.latitude,
          longitude: gamePoint.longitude,
        }
      );
      
      return distance <= radius;
    } catch (err) {
      console.error("Error calculating distance:", err);
      // On error, include the game (better to show too many than too few)
      return true;
    }
  }, []);

  // Process game documents from Firestore
  const processGameDocs = useCallback((docs) => {
    const now = new Date();
    let processedGames = [];
    
    docs.forEach((doc) => {
      try {
        const gameData = doc.data();
        
        // Convert timestamps to JavaScript dates
        let timeStart, timeEnd, date;
        
        try {
          timeStart = gameData.timeStart?.toDate?.() || new Date(gameData.timeStart);
        } catch (e) {
          console.error(`Error parsing timeStart: ${e.message}`);
          timeStart = new Date();
        }
        
        try {
          timeEnd = gameData.timeEnd?.toDate?.() || new Date(gameData.timeEnd);
        } catch (e) {
          console.error(`Error parsing timeEnd: ${e.message}`);
          timeEnd = new Date(Date.now() + 3600000); // Default 1 hour from now
        }
        
        try {
          date = gameData.date?.toDate?.() || new Date(gameData.date);
        } catch (e) {
          console.error(`Error parsing date: ${e.message}`);
          date = new Date();
        }
        
        // Extract coordinates safely and ensure they're serializable objects
        const gameCoords = standardizeCoordinates(gameData.coords);
        
        // Add document ID to the game data
        const game = {
          ...gameData,
          eventID: doc.id,
          timeStart,
          timeEnd,
          date,
          isLive: timeStart <= now,
          usersCount: gameData.users?.length || 0,
          // Ensure coords is a plain object for Redux (not a GeoPoint)
          coords: gameCoords || null
        };
        
        processedGames.push(game);
      } catch (err) {
        console.error(`Error processing game ${doc.id}:`, err);
      }
    });
    
    return processedGames;
  }, []);

  // Fetch games from Firestore - defined outside of dependency chain
  const fetchGames = useCallback(async (resetList = true) => {
    // Prevent concurrent fetches
    if (pendingFetchRef.current) {
      console.log("Fetch already in progress, skipping");
      return;
    }
    
    pendingFetchRef.current = true;
    
    if (resetList) {
      setLoading(true);
      setError(null);
      setHasMoreGames(true);
      setLastVisibleDoc(null);
    }

    // Use current values from refs to avoid closure issues
    const currentRadius = radiusRef.current;
    const currentCenterCoords = centerCoordsRef.current;

    console.log(`Fetching games with radius ${currentRadius}m, resetList=${resetList}`);

    try {
      // Current time - Fixed: Define 'now' here
      const now = new Date();
      
      // Use React Native Firebase namespaced API syntax
      let gamesQuery = db.collection("games")
        .where("timeEnd", ">", now)
        .orderBy("timeEnd", "asc")
        .limit(PAGE_SIZE);

      // Add startAfter if we're paginating
      if (!resetList && lastVisibleDoc) {
        gamesQuery = gamesQuery.startAfter(lastVisibleDoc);
      }

      console.log("Querying Firestore...");
      const querySnapshot = await gamesQuery.get();
      console.log(`Retrieved ${querySnapshot.docs.length} games from Firestore`);
      
      // Save the last document for pagination
      const lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
      setLastVisibleDoc(lastDoc || null);
      
      // Update hasMoreGames flag
      setHasMoreGames(querySnapshot.docs.length >= PAGE_SIZE);
      
      // Process the games
      const fetchedGames = processGameDocs(querySnapshot.docs);
      
      console.log(`Processed ${fetchedGames.length} games, now filtering by radius ${currentRadius}m`);
      
      // Now filter the games by radius if needed
      let filteredGames = fetchedGames;
      if (currentCenterCoords && currentRadius > 0) {
        filteredGames = fetchedGames.filter(game => {
          if (!game.coords) {
            return true; // Include games without coordinates
          }
          
          const isWithin = isWithinRadius(game.coords, currentCenterCoords, currentRadius);
          return isWithin;
        });
        
        console.log(`After filtering: ${filteredGames.length} games remain`);
      }
      
      // Update games list - either replace or append
      setGames(prevGames => {
        if (resetList) {
          return filteredGames;
        } else {
          // Make sure we don't duplicate games
          const existingIds = new Set(prevGames.map(g => g.eventID));
          const uniqueNewGames = filteredGames.filter(g => !existingIds.has(g.eventID));
          return [...prevGames, ...uniqueNewGames];
        }
      });
    } catch (err) {
      console.error("Error fetching games:", err);
      setError(err.message);
      setHasMoreGames(false);
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
      pendingFetchRef.current = false;
    }
  }, [isWithinRadius, processGameDocs, lastVisibleDoc]); 

  // Function to fetch more games (pagination)
  const fetchMoreGames = useCallback(() => {
    if (loadingMore || !hasMoreGames || loading) return;
    
    console.log("Fetching more games...");
    setLoadingMore(true);
    fetchGames(false); // false means don't reset the list
  }, [fetchGames, loadingMore, hasMoreGames, loading]);

  // Custom setter for center coordinates that ensures proper serialization
  const setCenterCoordsSafe = useCallback((newCoords) => {
    if (!newCoords) return;
    
    // Clear any existing timer
    if (fetchTimerRef.current) {
      clearTimeout(fetchTimerRef.current);
      fetchTimerRef.current = null;
    }
    
    // Ensure coordinates are numbers
    const serializedCoords = standardizeCoordinates(newCoords);
    
    if (!serializedCoords) {
      console.error("Invalid coordinates format:", newCoords);
      return;
    }
    
    console.log("Setting centerCoords to:", serializedCoords);
    
    // Update both state and ref
    setCenterCoords(serializedCoords);
    centerCoordsRef.current = serializedCoords;
    
    // Schedule a fetch with delay to ensure state is updated
    fetchTimerRef.current = setTimeout(() => {
      console.log("Executing delayed fetch after coordinates change");
      fetchGames(true); // Reset the list when filter changes
    }, 500);
  }, [fetchGames]);

  // Custom setter for filterRadius that ensures proper type and range
  const setFilterRadiusSafe = useCallback((newRadius) => {
    // Clear any existing timer
    if (fetchTimerRef.current) {
      clearTimeout(fetchTimerRef.current);
      fetchTimerRef.current = null;
    }
    
    // Ensure we're working with a number
    const numericRadius = Number(newRadius);
    
    if (isNaN(numericRadius)) {
      console.error("Invalid radius value:", newRadius);
      return;
    }
    
    // Ensure radius is within bounds
    const validRadius = Math.min(Math.max(numericRadius, MIN_RADIUS), MAX_RADIUS);
    console.log("Setting filterRadius to:", validRadius);
    
    // Update both state and ref
    setFilterRadius(validRadius);
    radiusRef.current = validRadius;
    
    // Schedule a fetch with delay to ensure state is updated
    fetchTimerRef.current = setTimeout(() => {
      console.log("Executing delayed fetch after radius change to:", validRadius);
      fetchGames(true); // Reset the list when filter changes
    }, 500);
  }, [fetchGames]);

  // Pull-to-refresh function with debounce
  const refreshGames = useCallback(() => {
    if (refreshing) return; // Prevent multiple refreshes
    
    console.log("Manual refresh triggered");
    setRefreshing(true);
    
    // Clear any existing timer
    if (fetchTimerRef.current) {
      clearTimeout(fetchTimerRef.current);
      fetchTimerRef.current = null;
    }
    
    fetchGames(true); // Reset the list on manual refresh
  }, [fetchGames, refreshing]);

  return {
    games,
    loading,
    error,
    refreshing,
    fetchGames,
    refreshGames,
    filterRadius,
    setFilterRadius: setFilterRadiusSafe,
    centerCoords,
    setCenterCoords: setCenterCoordsSafe,
    // Pagination properties
    fetchMoreGames,
    loadingMore,
    hasMoreGames
  };
};