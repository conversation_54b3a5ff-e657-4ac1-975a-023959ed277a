// hooks/Teams/useLocations.js
import { useState } from "react";
import { useSelector } from "react-redux";
import { useToast } from "../toast/useToast";
import {
  doc,
  updateDoc,
  getDoc,
  GeoPoint,
} from "@react-native-firebase/firestore";
import {
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
} from "@react-native-firebase/storage";
import { db, storage, functions } from "../../config/firebase";
import * as ImagePicker from "expo-image-picker";
import * as ImageManipulator from "expo-image-manipulator";
import { useDispatch } from "react-redux";
import { httpsCallable } from "@react-native-firebase/functions";
import {
  updateLocationImage,
  deleteLocationImage as deleteLocationImageAction,
  setMainLocationRedux,
} from "../../slices/socialsSlice";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

export const useLocations = (teamId) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [location, setLocation] = useState({
    name: "",
    image: null,
    place_id: null,
    latitude: null,
    longitude: null,
  });
  const showToast = useToast();
  const { language } = useSelector((state) => state.language);
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );

  const dispatch = useDispatch();

  const updateLocation = (locationData) => {
    setLocation((prev) => ({ ...prev, ...locationData }));
  };

  const setMainLocation = async (locationId) => {
    // Store previous state before any changes
    const previousLocationId = team.mainLocationId;

    try {
      // Immediate Redux update
      dispatch(setMainLocationRedux({ teamId, locationId: locationId }));

      // Background Firestore update
      const teamRef = doc(db, "teams", teamId);
      const newMainLocation = team.locations.find(
        (loc) => loc.place_id === locationId
      );

      await updateDoc(teamRef, {
        mainLocationId: locationId,
        coordinates: new GeoPoint(
          newMainLocation.originalLatitude,
          newMainLocation.originalLongitude
        ),
      });

      showToast(language.mainLocationUpdated, "success");
    } catch (error) {
      recordCrashlyticsError(error, "useLocations-setMainLocation");
      alert("Error updating main location:", error);
      // Rollback Redux state using stored previous value
      dispatch(
        setMainLocationRedux({
          teamId,
          locationId: previousLocationId,
        })
      );

      showToast(language.errorUpdatingMainLocation, "error");
    }
  };

  const handleDelete = (location) => {
    // Check if this is the last location
    if (team.locations.length <= 1) {
      showToast(language.minimumOneLocationRequired, "info");
      return;
    }

    // Check if the location is used in any recurring training sessions
    if (
      team.recurringTrainingSessions &&
      team.recurringTrainingSessions.length > 0
    ) {
      const isLocationUsed = team.recurringTrainingSessions.some(
        (session) => session.location === location.name
      );

      if (isLocationUsed) {
        showToast(
          language.locationUsedInSessions ||
            "This location is used in training sessions and cannot be deleted",
          "error"
        );

        return;
      }
    }

    setSelectedLocation(location);
    setMenuVisible(true);
  };

  const confirmDelete = async () => {
    if (!selectedLocation) {
      setMenuVisible(false);
      return;
    }

    // Extract storage path from image URL if it exists
    let storagePath = null;
    if (selectedLocation.image) {
      const urlParts = selectedLocation.image.split("/o/");
      if (urlParts.length > 1) {
        storagePath = decodeURIComponent(urlParts[1].split("?")[0]);
      }
    }

    const locationId = selectedLocation.place_id;
    const deleteTeamLocation = httpsCallable(functions, "deleteTeamLocation");
    const toastId = showToast.loading(
      language.deletingLocation || "Deleting location..."
    );

    try {
      const result = await deleteTeamLocation({
        teamId,
        locationId,
        storagePath,
      });

      if (result.data.success) {
        showToast.update(
          toastId,
          language.locationDeletedSuccessfully,
          "success"
        );
      } else {
        throw new Error(result.data.message || "Failed to delete location");
      }
    } catch (error) {
      recordCrashlyticsError(error, "useLocations-deleteLocation");
      alert("Error deleting location:", error);
      showToast.update(toastId, language.errorDeletingLocation, "error");
    } finally {
      setMenuVisible(false);
    }
  };

  const pickLocationImage = async (location) => {
    try {
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 1,
      });

      if (!result.canceled) {
        // Optimistically update UI with local image
        dispatch(
          updateLocationImage({
            teamId,
            locationId: location.place_id,
            imageUrl: result.assets[0].uri, // Use local URI temporarily
          })
        );

        try {
          // Handle actual upload in background
          const manipResult = await ImageManipulator.manipulateAsync(
            result.assets[0].uri,
            [{ resize: { width: 1200 } }],
            { format: ImageManipulator.SaveFormat.JPEG, compress: 0.8 }
          );

          const imageRef = ref(
            storage,
            `team_location_images/${teamId}/${
              location.place_id
            }_${Date.now()}.jpg`
          );
          const response = await fetch(manipResult.uri);
          const blob = await response.blob();
          await uploadBytesResumable(imageRef, blob);
          const imageUrl = await getDownloadURL(imageRef);

          // Update Redux with final URL
          dispatch(
            updateLocationImage({
              teamId,
              locationId: location.place_id,
              imageUrl,
            })
          );

          // Update Firestore
          const teamRef = doc(db, "teams", teamId);
          const teamDoc = await getDoc(teamRef);
          const currentLocations = teamDoc.data().locations || [];
          const locationIndex = currentLocations.findIndex(
            (loc) => loc.place_id === location.place_id
          );

          if (locationIndex !== -1) {
            const updatedLocations = [...currentLocations];
            updatedLocations[locationIndex] = {
              ...updatedLocations[locationIndex],
              image: imageUrl,
            };
            await updateDoc(teamRef, { locations: updatedLocations });
            showToast(language.imageUpdatedSuccessfully, "success");
          }
        } catch (error) {
          recordCrashlyticsError(error, "useLocations-pickLocationImage");
          console.error("Error picking/uploading image:", error);
          dispatch(
            updateLocationImage({
              teamId,
              locationId: location.place_id,
              imageUrl: location.image,
            })
          );
          throw error;
        }
      }
    } catch (error) {
      console.error("Error updating location image:", error);
      showToast(language.errorUpdatingImage, "error");
    }
  };

  const deleteLocationImage = async (location) => {
    // Extract storage path from image URL if it exists
    let storagePath = null;
    if (location.image) {
      const urlParts = location.image.split("/o/");
      if (urlParts.length > 1) {
        storagePath = decodeURIComponent(urlParts[1].split("?")[0]);
      }
    }

    // Optimistically update UI
    dispatch(
      deleteLocationImageAction({
        teamId,
        locationId: location.place_id,
      })
    );

    const deleteTeamLocationImage = httpsCallable(
      functions,
      "deleteTeamLocationImage"
    );

    try {
      const result = await deleteTeamLocationImage({
        teamId,
        locationId: location.place_id,
        imageUrl: location.image,
        storagePath,
      });

      if (result.data.success) {
        showToast(language.imageDeletedSuccessfully, "success");
      } else {
        throw new Error(result.data.message || "Failed to delete image");
      }
    } catch (error) {
      // If the update fails, revert the optimistic update
      dispatch(
        updateLocationImage({
          teamId,
          locationId: location.place_id,
          imageUrl: location.image,
        })
      );
      console.error("Error deleting location image:", error);
      showToast(language.errorDeletingImage, "error");
    }
  };

  const onLocationSelected = (locationData) => {
    updateLocation(locationData);
  };

  const addLocation = async (newLocation) => {
    try {
      // Get current team data first
      const teamRef = doc(db, "teams", teamId);
      const teamDoc = await getDoc(teamRef);
      const currentLocations = teamDoc.data().locations || [];

      // Add the new location
      const updatedLocations = [...currentLocations, newLocation];

      // Update Firestore
      await updateDoc(teamRef, {
        locations: updatedLocations,
      });

      showToast(language.locationCreatedSuccessfully, "success");
    } catch (error) {
      console.error("Error adding location:", error);
      showToast(language.errorAddingLocation, "error");
    }
  };

  return {
    location,
    updateLocation,
    menuVisible,
    setMenuVisible,
    selectedLocation,
    handleDelete,
    confirmDelete,
    pickLocationImage,
    deleteLocationImage,
    onLocationSelected,
    addLocation,
    setMainLocation,
  };
};
