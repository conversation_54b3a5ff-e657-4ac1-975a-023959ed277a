// hooks/Teams/useTrainingSessions.js
import { useSelector, useDispatch } from "react-redux";
import { useCallback, useState } from "react";
import {
  doc,
  updateDoc,
  arrayUnion,
  arrayRemove,
} from "@react-native-firebase/firestore";
import { db, auth, functions } from "../../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";
import * as Haptics from "expo-haptics";
import { Alert } from "react-native";
import {
  sessionCancelled,
  revertSessionCancelled,
  revertSessionRestored,
  sessionRestored,
} from "../../slices/socialsSlice";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

export const useTrainingSessions = (teamId) => {
  const dispatch = useDispatch();
  const [menuVisible, setMenuVisible] = useState(false);

  const trainingSessions = useSelector(
    (state) => state.teams.trainingSessions[teamId] || []
  );

  const getSession = useCallback(
    (sessionId) => {
      return trainingSessions.find((s) => s.id === sessionId);
    },
    [trainingSessions]
  );

  const handleAttendance = useCallback(
    async (sessionId, status) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const sessionRef = doc(
        db,
        "teams",
        teamId,
        "trainingSessions",
        sessionId
      );
      const currentUserId = auth.currentUser.uid;
      const session = getSession(sessionId);

      try {
        if (status === "going" && session.goingUsers?.includes(currentUserId)) {
          await updateDoc(sessionRef, {
            goingUsers: arrayRemove(currentUserId),
          });
        } else if (
          status === "notGoing" &&
          session.notGoingUsers?.includes(currentUserId)
        ) {
          await updateDoc(sessionRef, {
            notGoingUsers: arrayRemove(currentUserId),
          });
        } else {
          await updateDoc(sessionRef, {
            [`${status}Users`]: arrayUnion(currentUserId),
            [`${status === "going" ? "notGoingUsers" : "goingUsers"}`]:
              arrayRemove(currentUserId),
          });
        }
      } catch (error) {
        console.error("Error updating attendance:", error);
        Alert.alert("Error", "Failed to update attendance. Please try again.");
      }
    },
    [teamId, getSession]
  );

  const cancelSession = useCallback(
    async (sessionId, reason = "", onOptimisticUpdate) => {
      // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      try {
        // Get current timestamp for optimistic update
        const cancelledAt = new Date().toISOString();

        // 1. Optimistic update
        dispatch(
          sessionCancelled({
            teamId,
            sessionId,
            reason,
            cancelledBy: auth.currentUser.uid,
            cancelledAt,
          })
        );

        // 2. Execute callback immediately after optimistic update
        if (onOptimisticUpdate) {
          onOptimisticUpdate();
        }

        // 3. Call cloud function
        const cancelTrainingSession = httpsCallable(
          functions,
          "cancelTrainingSession"
        );
        const result = await cancelTrainingSession({
          teamId,
          sessionId,
          reason,
        });

        // 4. Check result
        if (result.data && result.data.success) {
          return { success: true };
        } else {
          // If cloud function returns an error
          dispatch(revertSessionCancelled({ teamId, sessionId }));
          throw new Error(
            (result.data && result.data.error) || "Failed to cancel session"
          );
        }
      } catch (error) {
        recordCrashlyticsError(error, "useTrainingSessions-cancelSession");
        dispatch(revertSessionCancelled({ teamId, sessionId }));
        console.error("Error cancelling session:", error);
        return {
          success: false,
          error: error.message,
        };
      }
    },
    [teamId, dispatch]
  );

  const restoreSession = useCallback(
    async (sessionId, onOptimisticUpdate) => {
      // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      try {
        // 1. Optimistic update
        dispatch(
          sessionRestored({
            teamId,
            sessionId,
            restoredBy: auth.currentUser.uid,
            restoredAt: new Date().toISOString(),
          })
        );

        // 2. Execute callback immediately after optimistic update
        if (onOptimisticUpdate) {
          onOptimisticUpdate();
        }

        // 3. Call cloud function
        const restoreTrainingSession = httpsCallable(
          functions,
          "restoreTrainingSession"
        );
        const result = await restoreTrainingSession({
          teamId,
          sessionId,
        });

        // 4. Check result
        if (result.data && result.data.success) {
          return { success: true };
        } else {
          // If cloud function returns an error
          dispatch(revertSessionRestored({ teamId, sessionId }));
          throw new Error(
            (result.data && result.data.error) || "Failed to restore session"
          );
        }
      } catch (error) {
        recordCrashlyticsError(error, "useTrainingSessions-restoreSession");
        dispatch(revertSessionRestored({ teamId, sessionId }));
        console.error("Error restoring session:", error);
        return {
          success: false,
          error: error.message,
        };
      }
    },
    [teamId, dispatch]
  );

  return {
    trainingSessions,
    getSession,
    handleAttendance,
    menuVisible,
    setMenuVisible,
    cancelSession,
    restoreSession,
  };
};
