// hooks/Teams/useSchedule.js
import { useState } from "react";
import { useToast } from "../toast/useToast";
import { useSelector, useDispatch } from "react-redux";
import { httpsCallable } from "@react-native-firebase/functions";
import { functions, db } from "../../config/firebase";
import { doc, updateDoc } from "@react-native-firebase/firestore";
import { removeRecurringSession } from "../../slices/socialsSlice";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

// Reference to the cloud function
const deleteTrainingTemplateV2 = httpsCallable(
  functions,
  "deleteTrainingTemplateV2"
);

export const useSchedule = (teamId) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [deletingSession, setDeletingSession] = useState(null);
  const [weeksAhead, setWeeksAhead] = useState(2); // Default value
  const [sendNotifications, setSendNotifications] = useState(true); // Default value
  const showToast = useToast();
  const { language } = useSelector((state) => state.language);
  const dispatch = useDispatch();

  const handleDelete = (session) => {
    setSelectedSession(session);
    setMenuVisible(true);
  };

  const confirmDelete = async () => {
    const session = selectedSession;

    if (!session || !session.createdAt) {
      showToast(language.sessionNotFound, "error");
      setMenuVisible(false);
      return;
    }

    // Extract the template ID
    const templateId = session.createdAt.toDate().getTime().toString();

    // Set this session as currently deleting
    setDeletingSession(true);

    // Show loading toast and get the toast ID
    const toastId = showToast.loading(
      language.deletingSession || "Deleting session..."
    );

    try {
      // Call the cloud function
      const result = await deleteTrainingTemplateV2({
        teamId,
        templateId,
      });

      console.log("Deleted template:", result);

      dispatch(removeRecurringSession({ teamId, templateId }));

      // Update loading toast to success
      showToast.update(toastId, language.sessionDeletedSuccessfully, "success");
      setMenuVisible(false);
    } catch (error) {
      recordCrashlyticsError(error, "useSchedule-confirmDelete");
      console.error("Error deleting template:", error);

      // Extract error message from cloud function if available
      const errorMessage =
        error.message ||
        (error.details ? error.details.message : "Failed to delete session");

      // Update loading toast to error
      showToast.update(toastId, `Error: ${errorMessage}`, "error");
    } finally {
      // Clear the deleting session id
      setTimeout(() => setDeletingSession(null), 1000);
    }
  };

  // Function to update weeks ahead setting
  const updateWeeksAhead = async (weeks) => {
    setWeeksAhead(weeks);

    try {
      const teamRef = doc(db, "teams", teamId);

      // Update the team document with the new weeks ahead value
      await updateDoc(teamRef, {
        "schedulingSettings.weeksAhead": weeks,
      });

      console.log(`Updated team ${teamId} with weeks ahead: ${weeks}`);
    } catch (error) {
      recordCrashlyticsError(error, "useSchedule-updateWeeksAhead");
      console.error("Error updating weeks ahead setting:", error);
      showToast(
        language.errorUpdatingSettings || "Error updating settings",
        "error"
      );
    }
  };

  // Function to update notifications setting
  const updateSendNotifications = async (enabled) => {
    setSendNotifications(enabled);

    try {
      const teamRef = doc(db, "teams", teamId);

      // Update the team document with the new notifications setting
      await updateDoc(teamRef, {
        "schedulingSettings.sessionReminderNotifications": enabled,
      });

      console.log(`Updated team ${teamId} with notifications: ${enabled}`);
    } catch (error) {
      recordCrashlyticsError(error, "useSchedule-updateSendNotifications");
      console.error("Error updating notifications setting:", error);
      showToast(
        language.errorUpdatingSettings || "Error updating settings",
        "error"
      );
    }
  };

  return {
    menuVisible,
    setMenuVisible,
    selectedSession,
    handleDelete,
    confirmDelete,
    deletingSession,
    weeksAhead,
    updateWeeksAhead,
    sendNotifications,
    updateSendNotifications,
  };
};
