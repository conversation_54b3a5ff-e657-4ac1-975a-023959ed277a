// hooks/Teams/useTeamDetails.js
import { useState } from "react";
import { useSelector, shallowEqual, useDispatch } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import * as Haptics from "expo-haptics";
import { doc, updateDoc, arrayRemove } from "@react-native-firebase/firestore";
import { db, auth, functions } from "../../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";
import { useToast } from "../toast/useToast";
import { removeTeam } from "../../slices/socialsSlice";
import { removeChatById, addChat } from "../../slices/socialsSlice";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

export const useTeamDetails = (teamId) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const showToast = useToast();
  const currentUser = auth.currentUser;

  // Redux selectors
  const { language } = useSelector((state) => state.language);
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );

  const teams = useSelector((state) => state.teams.teams);
  const friends = useSelector((state) => state.socials.friends);
  const chats = useSelector((state) => state.socials.chats);
  const chat = useSelector((state) => {
    const teamChat = state.socials.chats.find(
      (chat) => chat?.id === team?.chatId
    );
    return teamChat ? [teamChat] : [];
  }, shallowEqual);

  // UI states
  const [menuVisible, setMenuVisible] = useState(false);
  const [inviteModalVisible, setInviteModalVisible] = useState(false);
  const [leaveModalVisible, setLeaveModalVisible] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Check if current user is the team creator
  const isTeamCreator = team?.createdBy === currentUser?.uid;

  // Compute main location
  const mainLocation = team?.locations?.find(
    (loc) => loc.place_id === team.mainLocationId
  );

  const showMenu = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setMenuVisible(true);
  };

  const hideMenu = () => {
    setMenuVisible(false);
  };

  const showInviteModal = () => {
    hideMenu();
    setInviteModalVisible(true);
  };

  const hideInviteModal = () => {
    setInviteModalVisible(false);
  };

  const showLeaveModal = () => {
    hideMenu();
    setLeaveModalVisible(true);
  };

  const hideLeaveModal = () => {
    setLeaveModalVisible(false);
  };

  const leaveTeam = async () => {
    if (isProcessing) return;
    setIsProcessing(true);

    const teamToRemove = team;
    const chatToRemove = chat[0];

    try {
      // Perform optimistic UI updates first

      // Remove the team from Redux store
      dispatch({
        type: "teams/removeTeam",
        payload: { teamId },
      });

      // Remove the team chat from Redux store if it exists
      if (chatToRemove) {
        dispatch(removeChatById(chatToRemove.id));
      }

      // Navigate back immediately for better UX
      navigation.goBack();

      // Show success message
      showToast(language.leftTeamSuccessfully, "success");

      // Call cloud function to handle the leave operation
      const leaveTeamFunction = httpsCallable(functions, "leaveTeam");
      const result = await leaveTeamFunction({
        teamId,
        teamName: team.name,
      });

      // Cloud function completed successfully, no need to revert
    } catch (error) {
      recordCrashlyticsError(error, "useTeamDetails-leaveTeam");
      console.error("Error leaving team:", error);
      showToast(error.message || language.errorLeavingTeam, "error");

      // Revert optimistic updates
      if (teamToRemove) {
        dispatch({
          type: "teams/setTeams",
          payload: [...teams, teamToRemove],
        });
      }
      if (chatToRemove) {
        dispatch(addChat(chatToRemove));
      }
    } finally {
      setIsProcessing(false);
      hideLeaveModal();
    }
  };

  const deleteTeam = () => {
    // Placeholder function for team deletion
    console.log("Delete team button pressed");
    hideLeaveModal();
    // Would implement actual team deletion logic here
  };

  const navigateToLocations = () => {
    navigation.navigate("LocationsList", { teamId: team.id });
    hideMenu();
  };

  const navigateToChat = () => {
    if (chat[0]) {
      navigation.navigate("ChatScreen", {
        user: "game",
        chat: chat[0],
      });
    }
  };

  return {
    team,
    mainLocation,
    chat,
    language,
    menuVisible,
    inviteModalVisible,
    leaveModalVisible,
    isTeamCreator,
    isProcessing,
    showMenu,
    hideMenu,
    showInviteModal,
    hideInviteModal,
    showLeaveModal,
    hideLeaveModal,
    leaveTeam,
    deleteTeam,
    navigateToLocations,
    navigateToChat,
  };
};
