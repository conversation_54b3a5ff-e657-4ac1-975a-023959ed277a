// hooks/Teams/useTeamInvitations.js
import { useState, useRef, useEffect, useCallback } from "react";
import { Animated, Alert } from "react-native";
import { useSelector } from "react-redux";
import { auth, db, functions } from "../../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";
import {
  collection,
  query,
  where,
  getDocs,
  collectionGroup,
  startAfter,
  orderBy,
  limit,
  startAt,
  endAt,
} from "@react-native-firebase/firestore";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

const sendTeamInvite = httpsCallable(functions, "sendTeamInvite");

// <PERSON>-Yates shuffle algorithm for randomizing arrays
const shuffleArray = (array) => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const useTeamInvitations = (teamId) => {
  // UI states
  const [inviteSearchQuery, setInviteSearchQuery] = useState("");
  const [selectedTab, setSelectedTab] = useState("players");
  const [invitingUsers, setInvitingUsers] = useState(new Set());
  const [invitedUsers, setInvitedUsers] = useState(new Set());

  // Users data
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMoreUsers, setHasMoreUsers] = useState(true);
  const [lastDoc, setLastDoc] = useState(null);

  // Refs for stable arrays
  const initialUsersRef = useRef(null);

  // Pagination
  const PAGE_SIZE = 15;

  // Animation
  const modalOpacity = useRef(new Animated.Value(0)).current;

  // Selectors from Redux
  const { language } = useSelector((state) => state.language);
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );
  const friends = useSelector((state) => state.socials.friends);
  const user = useSelector((state) => state.socials.userData);

  // Fetch users when needed
  const fetchUsers = useCallback(
    async (loadMore = false) => {
      if (selectedTab !== "players" || (isLoading && !loadMore)) return;

      try {
        setIsLoading(true);

        // Create base query
        let baseQuery = query(
          collectionGroup(db, "public"),
          where("uid", "!=", auth.currentUser.uid)
        );

        // Add search filtering if provided
        if (inviteSearchQuery.trim()) {
          baseQuery = query(
            baseQuery,
            orderBy("name"),
            startAt(inviteSearchQuery.trim()),
            endAt(inviteSearchQuery.trim() + "\uf8ff")
          );
        } else {
          // When not searching, use a basic order
          baseQuery = query(baseQuery, orderBy("uid"));
        }

        // Add pagination
        let paginatedQuery;
        if (loadMore && lastDoc) {
          paginatedQuery = query(
            baseQuery,
            startAfter(lastDoc),
            limit(PAGE_SIZE)
          );
        } else {
          paginatedQuery = query(baseQuery, limit(PAGE_SIZE));
        }

        const querySnapshot = await getDocs(paginatedQuery);

        // Set the last document for pagination
        if (querySnapshot.docs.length > 0) {
          setLastDoc(querySnapshot.docs[querySnapshot.docs.length - 1]);
        }

        // Check if there are more users to load
        setHasMoreUsers(querySnapshot.docs.length === PAGE_SIZE);

        const docs = querySnapshot.docs;

        // Process the user data
        let newUsers = docs
          .map((doc) => doc.data())
          .filter((userData) => !team.members.includes(userData.uid))
          .map((userData) => ({
            ...userData,
            friendRequestSent:
              user.friendRequestsSent?.includes(userData.uid) || false,
          }));

        // Apply client-side shuffling only on initial load without search
        let processedUsers = newUsers;
        if (!inviteSearchQuery.trim() && !loadMore) {
          processedUsers = shuffleArray(newUsers);
          // Store the initial array to maintain stability
          if (!initialUsersRef.current) {
            initialUsersRef.current = [...processedUsers];
          }
        }

        // If loading more, append to existing list, otherwise use the reference or replace
        if (loadMore) {
          setUsers((prev) => [...prev, ...processedUsers]);
        } else if (!inviteSearchQuery.trim() && initialUsersRef.current) {
          // For initial load with no search, use stable reference if available
          setUsers(initialUsersRef.current);
        } else {
          // Otherwise use the fetched users
          setUsers(processedUsers);
        }
      } catch (error) {
        recordCrashlyticsError(error, "useTeamInvitations-fetchUsers");
        console.error("Error fetching users::", error);
      } finally {
        setIsLoading(false);
      }
    },
    [
      selectedTab,
      inviteSearchQuery,
      team.members,
      user.friendRequestsSent,
      lastDoc,
    ]
  );

  // Reset refs when tab changes
  useEffect(() => {
    initialUsersRef.current = null;
  }, [selectedTab]);

  // Handle load more when user scrolls to the bottom
  const handleLoadMore = useCallback(() => {
    if (!isLoading && hasMoreUsers && selectedTab === "players") {
      fetchUsers(true);
    }
  }, [fetchUsers, isLoading, hasMoreUsers, selectedTab]);

  // Reset pagination when search query or tab changes
  useEffect(() => {
    if (selectedTab === "players") {
      // Only reset and fetch if search changes or initial load
      if (inviteSearchQuery.trim() || !initialUsersRef.current) {
        setLastDoc(null);
        setHasMoreUsers(true);
        initialUsersRef.current = null; // Reset stable ref for new search
        fetchUsers(false);
      }
    }
  }, [inviteSearchQuery, selectedTab]);

  const handleInviteUser = async (selectedUser) => {
    if (invitingUsers.has(selectedUser.uid)) return;

    setInvitingUsers((prev) => new Set([...prev, selectedUser.uid]));

    try {
      const result = await sendTeamInvite({
        teamId,
        invitedUserId: selectedUser.uid,
      });

      if (result.data.success) {
        setInvitedUsers((prev) => new Set([...prev, selectedUser.uid]));
        // No re-filtering or reshuffling here to preserve array order
      }
    } catch (error) {
      recordCrashlyticsError(error, "useTeamInvitations-handleInviteUser");
      let errorMessage;
      switch (error.code) {
        case "already-exists":
          errorMessage = language.userAlreadyMember;
          break;
        case "permission-denied":
          errorMessage = language.noPermissionToInvite;
          break;
        default:
          errorMessage = language.inviteFailed || "Failed to send invitation";
      }
      Alert.alert(language.error || "Error", errorMessage);
    } finally {
      setInvitingUsers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(selectedUser.uid);
        return newSet;
      });
    }
  };

  const getFilteredUsers = useCallback(() => {
    if (selectedTab === "players") {
      // Return users without re-filtering or reshuffling
      return users;
    } else {
      // For friends tab, just filter by search query and team membership
      // Don't shuffle at all to maintain stability
      return friends.filter(
        (friend) =>
          friend.name.toLowerCase().includes(inviteSearchQuery.toLowerCase()) &&
          !team.members.includes(friend.uid)
      );
    }
  }, [selectedTab, users, friends, inviteSearchQuery, team.members]);

  return {
    modalOpacity,
    inviteSearchQuery,
    setInviteSearchQuery,
    selectedTab,
    setSelectedTab,
    invitingUsers,
    invitedUsers,
    language,
    team,
    isLoading,
    handleInviteUser,
    getFilteredUsers,
    handleLoadMore,
    hasMoreUsers,
  };
};
