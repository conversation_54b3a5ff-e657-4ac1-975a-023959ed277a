// hooks/Teams/useTeamInvites.js
import { useCallback } from "react";
import { Alert } from "react-native";
import { useNotifications } from "../Notifications/useNotifications";
import { auth, functions } from "../../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";

const respondToTeamInviteCloud = httpsCallable(
  functions,
  "respondToTeamInvite"
);

export const useTeamInvites = () => {
  const { notifications, updateNotifications } = useNotifications();

  const acceptTeamInvite = useCallback(
    async (notification, accept) => {
      console.log("acceptTeamInvite");

      // Optimistic update
      const originalNotifications = notifications;
      const updatedNotifications = notifications.filter(
        (n) => n.id !== notification.id
      );
      updateNotifications(updatedNotifications);

      try {
        await respondToTeamInviteCloud({
          accept,
          teamId: notification.teamId,
          notificationId: notification.id,
        });
      } catch (error) {
        console.error("Team invite response failed", error);
        updateNotifications(originalNotifications); // Rollback on failure
        Alert.alert("Error", "Failed to respond to team invite.");
      }
    },
    [notifications, updateNotifications]
  );

  return { acceptTeamInvite };
};
