import { useState, useEffect } from "react";
import {
  doc,
  updateDoc,
  getDoc,
  Timestamp,
  writeBatch,
  serverTimestamp,
  arrayUnion,
  arrayRemove,
} from "@react-native-firebase/firestore";
import { auth, db, functions } from "../../config/firebase";
import { useToast } from "../toast/useToast";
import { httpsCallable } from "@react-native-firebase/functions";
import { recordCrashlyticsError } from "../../functions/recordCrashlyticsError";

const createTrainingTemplateV2 = httpsCallable(
  functions,
  "createTrainingTemplateV2"
);
const updateTrainingTemplateV2 = httpsCallable(
  functions,
  "updateTrainingTemplateV2"
);

export const useSessionCreation = (team, session, language, navigation) => {
  const isEditMode = !!session;
  const showToast = useToast();

  // Form states
  const [sessionName, setSessionName] = useState(language.trainingSession);
  const [description, setDescription] = useState("");
  const [location, setLocation] = useState(null);
  const [isCreating, setIsCreating] = useState(false);
  const [nameAlreadyUsed, setNameAlreadyUsed] = useState(false);
  const [normalizedName, setNormalizedName] = useState("");
  const [selectedDays, setSelectedDays] = useState([]);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize time states with proper handling for midnight crossing
  const currentTime = new Date();
  const [startTime, setStartTimeInternal] = useState(currentTime);
  const [endTime, setEndTimeInternal] = useState(() => {
    // For initial state, calculate end time based on current time
    const oneHourLater = new Date(currentTime);
    oneHourLater.setHours(oneHourLater.getHours() + 1);

    // If adding one hour crosses to the next day, set it to 23:59 instead
    if (oneHourLater.getDate() !== currentTime.getDate()) {
      const endOfDay = new Date(currentTime);
      endOfDay.setHours(23);
      endOfDay.setMinutes(59);
      return endOfDay;
    }

    return oneHourLater;
  });

  // Custom function to ensure date components stay in sync
  const syncDateComponents = (startDate, endDate) => {
    const syncedEnd = new Date(endDate);
    syncedEnd.setFullYear(startDate.getFullYear());
    syncedEnd.setMonth(startDate.getMonth());
    syncedEnd.setDate(startDate.getDate());
    return syncedEnd;
  };

  // Custom start time setter
  const setStartTime = (newStartTime) => {
    // Set the start time
    setStartTimeInternal(newStartTime);

    // Ensure end time has same date components as start time
    const syncedEndTime = syncDateComponents(newStartTime, endTime);

    // Check if end time is still valid (after start time)
    if (syncedEndTime.getTime() <= newStartTime.getTime()) {
      // If not valid, set end time to 1 hour after start or 23:59
      const oneHourLater = new Date(newStartTime);
      oneHourLater.setHours(oneHourLater.getHours() + 1);

      // Check if adding 1 hour crosses midnight
      if (oneHourLater.getDate() !== newStartTime.getDate()) {
        // Set to end of the same day
        syncedEndTime.setHours(23);
        syncedEndTime.setMinutes(59);
      } else {
        // Use the one hour later time
        syncedEndTime.setHours(oneHourLater.getHours());
        syncedEndTime.setMinutes(oneHourLater.getMinutes());
      }

      setEndTimeInternal(syncedEndTime);
    } else {
      setEndTimeInternal(syncedEndTime);
    }
  };

  // Custom end time setter
  const setEndTime = (newEndTime) => {
    // Ensure end time has same date components as start time
    const syncedEndTime = syncDateComponents(startTime, newEndTime);

    // Check if end time is after start time
    if (syncedEndTime.getTime() <= startTime.getTime()) {
      // If not valid, show error but don't change the time
      showToast(
        language.endTimeMustBeAfterStart || "End time must be after start time",
        "error"
      );
      return;
    }

    setEndTimeInternal(syncedEndTime);
  };

  // Load initial data for edit mode
  useEffect(() => {
    if (isEditMode && session) {
      try {
        setSessionName(session.name || language.trainingSession);
        setDescription(session.description || "");
        setLocation(
          session.location
            ? {
                name: session.location,
                image: session.locationImage,
              }
            : null
        );
        setSelectedDays(session.days || []);

        const startTimeDate =
          typeof session.startTime === "number"
            ? new Date(session.startTime)
            : session.startTime?.toDate?.()
            ? session.startTime.toDate()
            : new Date();

        let endTimeDate =
          typeof session.endTime === "number"
            ? new Date(session.endTime)
            : session.endTime?.toDate?.()
            ? session.endTime.toDate()
            : new Date(Date.now() + 60 * 60 * 1000);

        // Ensure date components are the same
        endTimeDate = syncDateComponents(startTimeDate, endTimeDate);

        // If end time is before start time after synchronization, adjust to 23:59
        if (endTimeDate.getTime() <= startTimeDate.getTime()) {
          endTimeDate.setHours(23);
          endTimeDate.setMinutes(59);
        }

        setStartTimeInternal(startTimeDate);
        setEndTimeInternal(endTimeDate);
      } catch (error) {
        console.error("Error setting session data:", error);
        // Set defaults
        setSessionName(language.trainingSession);
        setDescription("");
        setLocation(null);
        setSelectedDays([]);
        setStartTimeInternal(new Date());
        setEndTimeInternal(new Date(Date.now() + 60 * 60 * 1000));
      }
    }
  }, [session, isEditMode, language]);

  // Name validation
  const normalizeSessionName = (name) => {
    return name.trim().toLowerCase().replace(/\s+/g, " ");
  };

  const checkSessionName = (name) => {
    const normalized = normalizeSessionName(name);
    setNormalizedName(normalized);

    if (isEditMode && normalized === normalizeSessionName(session.name)) {
      setNameAlreadyUsed(false);
      return;
    }

    const exists = team.recurringTrainingSessions?.some(
      (s) => normalizeSessionName(s.name) === normalized
    );
    setNameAlreadyUsed(exists);
  };

  // Change detection
  useEffect(() => {
    if (!isEditMode) return;

    const hasNameChanged = sessionName !== session.name;
    const hasDescriptionChanged = description !== session.description;
    const hasLocationChanged = location?.name !== session.location;
    const areDaysDifferent = () => {
      if (selectedDays.length !== session.days.length) return true;
      return (
        selectedDays.some((day) => !session.days.includes(day)) ||
        session.days.some((day) => !selectedDays.includes(day))
      );
    };

    const originalStartTime = new Date(session.startTime).getTime();
    const originalEndTime = new Date(session.endTime).getTime();
    const hasTimeChanged =
      startTime.getTime() !== originalStartTime ||
      endTime.getTime() !== originalEndTime;

    setHasChanges(
      hasNameChanged ||
        hasDescriptionChanged ||
        hasLocationChanged ||
        areDaysDifferent() ||
        hasTimeChanged
    );
  }, [sessionName, description, location, selectedDays, startTime, endTime]);

  const handleSubmit = async () => {
    if (isCreating) return;
    setIsCreating(true);

    try {
      // Convert Date objects to timestamp format expected by react-native-firebase
      // We just need to send milliseconds since epoch
      const startTimeMillis = startTime.getTime();
      const endTimeMillis = endTime.getTime();

      const sessionData = {
        name: sessionName,
        description: description ? description : "",
        location: location.name,
        locationImage: location.image ? location.image : null,
        days: selectedDays,
        startTime: startTimeMillis, // Send milliseconds instead of Date object
        endTime: endTimeMillis, // Send milliseconds instead of Date object
      };

      if (isEditMode) {
        // Update existing template
        const result = await updateTrainingTemplateV2({
          teamId: team.id,
          templateId: session.createdAt.toDate().getTime().toString(),
          updatedTemplate: sessionData,
        });

        console.log("Template update result:", result.data);
        showToast(language.sessionUpdatedSuccessfully, "success");
      } else {
        // Create new template
        const result = await createTrainingTemplateV2({
          teamId: team.id,
          template: sessionData,
        });

        console.log("Template creation result:", result.data);
        showToast(language.sessionCreatedSuccessfully, "success");
      }

      setTimeout(() => {
        navigation.goBack();
      }, 800);
    } catch (error) {
      recordCrashlyticsError(error, "useSessionCreation-templateOperation");
      console.error("Error in template operation:", error);

      const errorMessage =
        error.message ||
        (error.details
          ? error.details.message
          : `Failed to ${isEditMode ? "update" : "create"} training session`);

      showToast(errorMessage, "error");
    } finally {
      setTimeout(() => {
        setIsCreating(false);
      }, 700);
    }
  };

  const validateAndSubmit = () => {
    if (sessionName.trim() === "") {
      showToast(language.sessionNameRequired, "error");
      return;
    }

    if (location === null) {
      showToast(language.locationRequired, "error");
      return;
    }

    if (selectedDays.length === 0) {
      showToast(language.selectAtLeastOneDay, "error");
      return;
    }

    // Simple time comparison - dates should be synchronized
    if (endTime.getTime() <= startTime.getTime()) {
      showToast(language.invalidTimeRange, "error");
      return;
    }

    handleSubmit();
  };

  return {
    isEditMode,
    sessionName,
    setSessionName,
    description,
    setDescription,
    location,
    setLocation,
    isCreating,
    nameAlreadyUsed,
    startTime,
    setStartTime,
    endTime,
    setEndTime,
    selectedDays,
    setSelectedDays,
    hasChanges,
    checkSessionName,
    validateAndSubmit,
  };
};
