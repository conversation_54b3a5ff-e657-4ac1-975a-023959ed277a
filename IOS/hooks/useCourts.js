// Enhanced version of hooks/useCourts.js to handle new filter properties
import { useState, useEffect } from "react";
import { db } from "../config/firebase";
import { recordCrashlyticsError } from "../functions/recordCrashlyticsError";

export const useCourts = (user, filterType, appliedFilters) => {
  const [courts, setCourts] = useState([]);
  const [filteredCount, setFilteredCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCourts = async () => {
      setIsLoading(true);
      const q = db.collection("courts-cz1").where("address", "!=", "");
      const unsubscribe = q.onSnapshot(q, async (querySnapshot) => {
        const courtsData = await Promise.all(
          querySnapshot.docs.map(async (docSnapshot) => {
            const courtData = docSnapshot.data();
            
            if (courtData?.storage_id) {
              let eventsCount = 0;
              
              try {
                // Get events count
                const gamesQuery = db.collection("games").where("courtID", "==", docSnapshot.id);
                
                const gamesSnapshot = await gamesQuery.get();
                eventsCount = gamesSnapshot.size;
              } catch (error) {
                console.error("Error counting games for court:", docSnapshot.id, error);
              }
              
              return {
                id: docSnapshot.id,
                eventsCount: eventsCount,
                coords: [courtData.coords[1], courtData.coords[0]],
                imageRefs: courtData.imageRefs,
                address: courtData.address,
                isFavourite: user.favouriteCourts?.includes(docSnapshot.id) || false,
                storage_id: courtData.storage_id,
                description: courtData.description,
                baskets: courtData.baskets || 2,
                surface: courtData.surface || "concrete",
                fullCourt: courtData.fullCourt || false,
                lighting: courtData.lighting || false,
                lines: courtData.lines || false,
                indoorOutdoor: courtData.indoorOutdoor || "outdoor",
                publicPrivate: courtData.publicPrivate || "public",
                open: courtData.open || null,
                // New court characteristics with defaults
                maintenance: courtData.maintenance || "good",
                parkingAvailable: courtData.parkingAvailable || false,
                seating: courtData.seating || false,
                wheelchairAccessible: courtData.wheelchairAccessible || false,
                restrooms: courtData.restrooms || false,
                waterFountain: courtData.waterFountain || false,
              };
            }
            return null;
          })
        );
        setCourts(courtsData.filter(Boolean));
        setIsLoading(false);
      });

      return () => unsubscribe();
    };

    fetchCourts();
  }, [user.favouriteCourts]);

  // Helper function to safely check string values with case insensitivity
  const matchesStringFilter = (courtValue, filterValue) => {
    if (!filterValue || filterValue === "All") return true;
    
    const courtStr = String(courtValue || "").toLowerCase();
    const filterStr = String(filterValue).toLowerCase();
    
    return courtStr === filterStr || 
           courtStr.includes(filterStr) || 
           filterStr.includes(courtStr);
  };

  // Helper function to check boolean values
  const matchesBooleanFilter = (courtValue, filterValue) => {
    if (!filterValue || filterValue === "All") return true;
    
    const courtBool = Boolean(courtValue);
    return (filterValue === "Yes" && courtBool) || 
           (filterValue === "No" && !courtBool);
  };

  // Apply filtering logic for all filters
  const filteredCourts = courts.filter((court) => {
    // Special filter for games
    if (filterType === "games") {
      return court.eventsCount > 0;
    }

    if (!appliedFilters) return true;

    // Apply all filter checks
    return (
      // Basic Filters
      matchesStringFilter(court.surface, appliedFilters.surfaceType) &&
      matchesStringFilter(court.indoorOutdoor, appliedFilters.indoorOutdoor) &&
      matchesStringFilter(court.publicPrivate, appliedFilters.publicPrivate) &&
      
      // Court Features
      matchesBooleanFilter(court.fullCourt, appliedFilters.fullCourt) &&
      matchesBooleanFilter(court.lines, appliedFilters.lines) &&
      matchesBooleanFilter(court.lighting, appliedFilters.lighting) &&
      
      // New filters
      // Baskets (special case to handle numeric comparison)
      (appliedFilters.baskets ? 
        (appliedFilters.baskets === "All" || 
         Number(court.baskets) >= Number(appliedFilters.baskets)) : true) &&
      
      // Maintenance condition
      matchesStringFilter(court.maintenance, appliedFilters.maintenance) &&
      
      // Amenities
      matchesBooleanFilter(court.parkingAvailable, appliedFilters.parking) &&
      matchesBooleanFilter(court.seating, appliedFilters.seating) &&
      matchesBooleanFilter(court.wheelchairAccessible, appliedFilters.wheelchairAccessible) &&
      matchesBooleanFilter(court.restrooms, appliedFilters.restrooms) &&
      matchesBooleanFilter(court.waterFountain, appliedFilters.waterFountain)
    );
  });

  // Update filtered count any time filtered courts change
  useEffect(() => {
    setFilteredCount(filteredCourts.length);
  }, [filteredCourts]);

  return { 
    courts, 
    filteredCourts, 
    filteredCount,
    isLoading 
  };
};