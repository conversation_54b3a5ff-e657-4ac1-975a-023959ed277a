// hooks/toast/useToast.js
import { createContext, useContext } from "react";

// Create default context value with same shape as our implementation
const defaultToastFunction = (message, type) => {};
defaultToastFunction.loading = () => {};
defaultToastFunction.update = () => {};

const ToastContext = createContext(defaultToastFunction);

export const useToast = () => useContext(ToastContext);

export { ToastContext };
