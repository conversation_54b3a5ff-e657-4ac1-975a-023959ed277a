import {
  collection,
  query,
  where,
  startAfter,
  limit,
  getDocs,
} from "@react-native-firebase/firestore";

export class TeamRepository {
  constructor(firestoreService) {
    this.firestoreService = firestoreService;
  }

  buildQuery(filters) {
    const baseQuery = query(this.firestoreService.collectionRef, limit(10));
    return this.firestoreService.buildQuery(baseQuery, filters);
  }

  async executeQuery(queryConfig) {
    return this.firestoreService.executeQuery(queryConfig);
  }
}
