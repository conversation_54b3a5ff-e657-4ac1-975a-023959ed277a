import { configureStore, combineReducers } from "@reduxjs/toolkit";
import socialsReducer from "./slices/socialsSlice";
import languageReducer from "./slices/languageSlice";
import gamesReducer from "./slices/gamesSlice";
import teamsReducer from "./slices/teamsSlice";
import appControlReducer from "./slices/appControlSlice";

const rootReducer = combineReducers({
  socials: socialsReducer,
  language: languageReducer,
  games: gamesReducer,
  teams: teamsReducer,
  appControl: appControlReducer,
});

const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
      // serializableCheck: {
      //   // Ignore these specific action types
      //   ignoredActions: [
      //     // "games/setGames",
      //     // "games/setLoadingGames",
      //     // "socials/setFriends",
      //     // "socials/setLoadingFriends",
      //     // "teams/setTeams",
      //     // "teams/setLoadingTeams",
      //     // "teams/setTrainingSessions",
      //   ],
      //   // Ignore these field paths in all actions and state
      //   ignoredPaths: [
      //     "payload.*.coords",
      //     "games.games.*.coords",
      //     "games.games.*.date",
      //     // "payload.*.date",
      //     "payload.*.courtObj",
      //     // "payload.sessions.*.date",
      //     // "payload.sessions.*.startTime",
      //     // "teams.trainingSessions.*.date",
      //     // "teams.trainingSessions.*.startTime",
      //   ],
      // },
    }),
});

export default store;
