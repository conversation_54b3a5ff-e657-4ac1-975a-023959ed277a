// screens/Teams/TeamInbox.js
import { useCallback, useMemo } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  SectionList,
  StyleSheet,
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { AntDesign } from "@expo/vector-icons";
import { TeamMemberLeftNotif } from "../../components/Teams/TeamMemberLeftNotif";

// Helper function to group notifications by date
const getDateLabel = (date, language) => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // Reset hours to compare just the date
  today.setHours(0, 0, 0, 0);
  yesterday.setHours(0, 0, 0, 0);
  const compareDate = new Date(date);
  compareDate.setHours(0, 0, 0, 0);

  // Check if date is today or yesterday
  if (compareDate.getTime() === today.getTime()) {
    return language.today || "Today";
  } else if (compareDate.getTime() === yesterday.getTime()) {
    return language.yesterday || "Yesterday";
  }

  // Check if date is within this week
  const daysDiff = Math.floor((today - compareDate) / (1000 * 60 * 60 * 24));
  if (daysDiff < 7) {
    // Return day of week
    return (
      language.weekdays?.[compareDate.getDay()] ||
      compareDate.toLocaleDateString(language.dateLocale || "en-US", {
        weekday: "long",
      })
    );
  }

  // Return formatted date for older notifications
  return compareDate.toLocaleDateString(language.dateLocale || "en-US", {
    month: "long",
    day: "numeric",
    year:
      compareDate.getFullYear() !== today.getFullYear() ? "numeric" : undefined,
  });
};

export default function TeamInbox() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const route = useRoute();
  const { teamId } = route.params;
  const { language } = useSelector((state) => state.language);

  // Get team notifications from Redux
  const teamNotifications = useSelector(
    (state) => state.teams.teamNotifications[teamId] || []
  );

  // Group notifications by date
  const groupedNotifications = useMemo(() => {
    if (!teamNotifications.length) return [];

    // Sort notifications by date (newest first)
    const sortedNotifications = [...teamNotifications].sort((a, b) => {
      const dateA = a.date ? new Date(a.date) : new Date();
      const dateB = b.date ? new Date(b.date) : new Date();
      return dateB - dateA;
    });

    // Group by date
    const groups = [];
    let currentDate = null;
    let currentGroup = null;

    sortedNotifications.forEach((notification) => {
      const notifDate = notification.date
        ? new Date(notification.date)
        : new Date();
      const dateString = notifDate.toDateString();

      if (dateString !== currentDate) {
        currentDate = dateString;
        currentGroup = {
          date: notifDate,
          title: getDateLabel(notifDate, language),
          data: [notification],
        };
        groups.push(currentGroup);
      } else {
        currentGroup.data.push(notification);
      }
    });

    return groups;
  }, [teamNotifications, language]);

  const renderNotificationItem = useCallback(
    ({ item }) => {
      switch (item.type) {
        case "teamMemberLeft":
          return (
            <TeamMemberLeftNotif notification={item} language={language} />
          );
        // Add other notification types here as needed
        default:
          return null;
      }
    },
    [language]
  );

  const renderSectionHeader = ({ section }) => {
    // Find the index of this section in the grouped notifications
    const index = groupedNotifications.findIndex(
      (group) => group.title === section.title
    );

    return (
      <View
        style={[styles.dateHeaderContainer, { paddingTop: index > 0 ? 15 : 0 }]}
      >
        <View style={styles.dateHeaderInner}>
          <View style={styles.dateHeaderLine} />
          <Text style={styles.dateHeaderText}>{section.title}</Text>
          <View style={styles.dateHeaderLine} />
        </View>
      </View>
    );
  };

  // Get team name from Redux
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );

  console.warn("notifs", teamNotifications);

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <AntDesign name="arrowleft" size={24} color="black" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {`${team?.name || ""} ${language.inbox}`}
        </Text>
      </View>

      {teamNotifications.length > 0 ? (
        <SectionList
          sections={groupedNotifications}
          renderItem={renderNotificationItem}
          renderSectionHeader={renderSectionHeader}
          keyExtractor={(item) => item.id}
          stickySectionHeadersEnabled={false}
          SectionSeparatorComponent={() => (
            <View style={styles.sectionSeparator} />
          )}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          contentContainerStyle={styles.listContent}
        />
      ) : (
        <Text style={styles.emptyMessage}>{language.noTeamNotifications}</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    fontSize: 18,
    fontWeight: "600",
    marginRight: 24,
  },
  listContent: {
    paddingTop: 15,
    paddingBottom: 20,
  },
  dateHeaderContainer: {
    paddingHorizontal: 20,
    backgroundColor: "white",
    marginVertical: 5,
  },
  dateHeaderInner: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "80%",
    alignSelf: "center",
  },
  dateHeaderLine: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(0,0,0,0.15)",
  },
  dateHeaderText: {
    fontSize: 15,
    fontWeight: "600",
    color: "#828282",
    marginHorizontal: 12,
  },
  separator: {
    height: 1,
    width: "90%",
    alignSelf: "center",
    backgroundColor: "rgba(0,0,0,0.08)",
  },
  sectionSeparator: {
    // height: 15,
  },
  emptyMessage: {
    textAlign: "center",
    marginTop: 40,
    color: "rgba(0,0,0,0.5)",
  },
});
