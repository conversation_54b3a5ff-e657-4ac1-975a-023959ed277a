//screens/Teams/TrainingSessionDetails.js
// React & Hooks
import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";

// React Native Components
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

// Navigation
import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

// Firebase
import { auth, db, functions } from "../../config/firebase";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  arrayRemove,
  query,
  where,
  onSnapshot,
  writeBatch,
  deleteDoc,
  orderBy,
} from "@react-native-firebase/firestore";
import { httpsCallable } from "@react-native-firebase/functions";

// Expo Modules
import { LinearGradient } from "expo-linear-gradient";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import * as Haptics from "expo-haptics";
import { BlurView } from "expo-blur";
import Constants from "expo-constants";

// Redux
import { useSelector, shallowEqual } from "react-redux";

// UI Libraries & Assets
import FastImage from "react-native-fast-image";
import { red } from "../colors";
import { EN, CZ } from "../../assets/strings";
import TeamProfileIcon from "../../assets/TeamProfileIcon";
import ProfileImage from "../../assets/ProfileImage";

// Icons
import {
  AntDesign,
  Ionicons,
  Feather,
  FontAwesome,
  MaterialIcons,
  MaterialCommunityIcons,
  FontAwesome5,
  FontAwesome6,
  Octicons,
  Entypo,
} from "@expo/vector-icons";

// Custom Hooks & Components
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useToast } from "../../hooks/toast/useToast";
import { useTrainingSessions } from "../../hooks/Teams/useTrainingSessions";
import { SlideUpMenu } from "../../components/slideUpMenu/SlideUpMenu";

const UserList = ({ title, users, teamMembers, emptyText, color }) => (
  <View style={styles.section}>
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={[styles.countBadge, { backgroundColor: `${color}20` }]}>
        <Text style={[styles.countText, { color }]}>{users?.length || 0}</Text>
      </View>
    </View>
    {users?.length > 0 ? (
      <FlatList
        data={users}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(userId) => userId}
        renderItem={({ item: userId }) => {
          const user = teamMembers.find((member) => member.uid === userId);
          return (
            <View key={userId} style={styles.userCard}>
              <ProfileImage
                profileImageRef={user?.profileImageRef}
                profileColor={user?.profileColor}
                name={user?.name}
                size={60}
              />
              <Text style={styles.userName} numberOfLines={1}>
                {user?.name}
              </Text>
            </View>
          );
        }}
      />
    ) : (
      <Text style={styles.emptyText}>{emptyText}</Text>
    )}
  </View>
);

const TrainingSessionDetails = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { language } = useSelector((state) => state.language);
  const toast = useToast();

  const { team, sessionId, historySession } = route.params;
  const {
    getSession,
    handleAttendance,
    menuVisible,
    setMenuVisible,
    cancelSession,
    restoreSession,
  } = useTrainingSessions(team.id);
  const session = historySession || getSession(sessionId);

  // State for cancellation modal
  const [cancelModalVisible, setCancelModalVisible] = useState(false);
  const [cancelReason, setCancelReason] = useState("");

  const showMenu = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setMenuVisible(true);
  };

  const handleCancelSession = async () => {
    try {
      // Pass a callback function that runs immediately after optimistic update
      const result = await cancelSession(session.id, cancelReason, () => {
        // These UI updates happen immediately
        setCancelModalVisible(false);
        setMenuVisible(false);

        toast(
          language.sessionCancelledSuccessfully ||
            "Session cancelled successfully",
          "success"
        );

        // navigation.goBack();
      });

      // This code only runs after the cloud function completes
      if (!result.success) {
        // Handle any errors from the cloud function
        throw new Error(result.error || "Unknown error");
      }
    } catch (error) {
      console.error("Error cancelling session:", error);
      toast(
        language.errorCancellingSession || "Error cancelling session",
        "error"
      );
    }
  };

  const handleRestoreSession = async () => {
    try {
      // Pass a callback function that runs immediately after optimistic update
      const result = await restoreSession(session.id, () => {
        // These UI updates happen immediately
        setMenuVisible(false);

        toast(
          language.sessionRestoredSuccessfully ||
            "Session restored successfully",
          "success"
        );
      });

      // This code only runs after the cloud function completes
      if (!result.success) {
        // Handle any errors from the cloud function
        throw new Error(result.error || "Unknown error");
      }
    } catch (error) {
      console.error("Error restoring session:", error);
      toast(
        language.errorRestoringSession || "Error restoring session",
        "error"
      );
    }
  };

  if (!session) {
    return (
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <ActivityIndicator style={styles.loadingIndicator} />
      </View>
    );
  }

  const undecidedUsers = team.membersData
    .filter(
      (member) =>
        !session.goingUsers?.includes(member.uid) &&
        !session.notGoingUsers?.includes(member.uid)
    )
    .map((member) => member.uid);

  const currentUserId = auth.currentUser.uid;
  const isGoing =
    session.goingUsers && session.goingUsers.includes(currentUserId);
  const isNotGoing =
    session.notGoingUsers && session.notGoingUsers.includes(currentUserId);

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Pressable
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <AntDesign
            name="arrowleft"
            size={24}
            color="black"
            style={styles.backIcon}
          />
        </Pressable>

        <Text style={styles.headerTitle}>{session.name}</Text>

        <Pressable style={styles.menuButton} onPress={showMenu}>
          <Entypo
            name="dots-three-horizontal"
            size={20}
            color="black"
            style={styles.menuIcon}
          />
        </Pressable>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.dateSection}>
          <View style={styles.dateHeader}>
            <View>
              <Text style={styles.monthDay}>
                {new Date(session.date).toLocaleDateString(
                  language.dateLocale,
                  {
                    day: "numeric",
                    month: "long",
                  }
                )}
              </Text>
              <Text style={styles.dayText}>
                {new Date(session.date).toLocaleDateString(
                  language.dateLocale,
                  {
                    weekday: "long",
                  }
                )}
              </Text>
            </View>
            <View style={styles.timeRow}>
              <Ionicons name="time-outline" size={20} color={red} />
              <Text style={styles.startTimeText}>
                {new Date(session.startTime).toLocaleTimeString(
                  language.dateLocale,
                  {
                    hour: "2-digit",
                    minute: "2-digit",
                  }
                )}
              </Text>
              <Text style={styles.timeSeparator}>-</Text>
              <Text style={styles.endTimeText}>
                {new Date(session.endTime).toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </Text>
            </View>
          </View>

          <View style={styles.locationWrapper}>
            <View style={styles.locationContentWrapper}>
              {session.locationImage ? (
                <FastImage
                  source={{ uri: session.locationImage }}
                  style={styles.locationImage}
                  resizeMode={FastImage.resizeMode.cover}
                />
              ) : (
                <View style={styles.locationPlaceholderContainer}>
                  <View style={styles.locationPlaceholderInner}>
                    <FastImage
                      source={require("./../../images/qq2.png")}
                      style={styles.locationPlaceholderIcon1}
                    />
                    <FastImage
                      source={require("./../../images/elipse.png")}
                      style={styles.locationPlaceholderIcon2}
                    />
                  </View>
                </View>
              )}
              <View style={styles.locationTextContainer}>
                <Text style={styles.locationText}>{session.location}</Text>
              </View>
            </View>
          </View>

          {/* Description - only show if not empty */}
          {session.description && session.description.trim() !== "" && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionText}>{session.description}</Text>
            </View>
          )}

          {session.isCancelled ? (
            // Cancelled session message (whether history or current)
            <View style={styles.cancelledContainer}>
              <MaterialIcons
                name="event-busy"
                size={22}
                color="#FF8C00"
                style={styles.cancelledIcon}
              />
              <View style={styles.cancelledTextContainer}>
                <Text style={styles.cancelledTitle}>
                  {language.sessionCancelled || "Session Cancelled"}
                </Text>
                {session.cancelReason && (
                  <Text style={styles.cancelledReason}>
                    {session.cancelReason}
                  </Text>
                )}
              </View>
            </View>
          ) : historySession ? (
            // History session (non-cancelled) - disabled buttons
            <View style={styles.buttonContainer}>
              <View
                style={[
                  styles.actionButton,
                  styles.goingButton,
                  isGoing && styles.activeGoingButton,
                  styles.disabledButtons,
                ]}
              >
                <Ionicons
                  name="checkmark"
                  size={18}
                  color={isGoing ? "white" : "#4CAF50"}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    styles.goingButtonText,
                    isGoing && styles.activeButtonText,
                  ]}
                >
                  {language.yesGoing}
                </Text>
              </View>

              <View
                style={[
                  styles.actionButton,
                  styles.notGoingButton,
                  isNotGoing && styles.activeNotGoingButton,
                  styles.disabledButtons,
                ]}
              >
                <Ionicons
                  name="close"
                  size={18}
                  color={isNotGoing ? "white" : red}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    styles.notGoingButtonText,
                    isNotGoing && styles.activeButtonText,
                  ]}
                >
                  {language.notGoing}
                </Text>
              </View>
            </View>
          ) : (
            // Active session (not cancelled, not history)
            <View style={styles.buttonContainer}>
              <Pressable
                style={({ pressed }) => [
                  styles.actionButton,
                  styles.goingButton,
                  isGoing && styles.activeGoingButton,
                  pressed && styles.pressedGoingButton,
                ]}
                onPress={() => handleAttendance(session.id, "going")}
              >
                <Ionicons
                  name="checkmark"
                  size={18}
                  color={isGoing ? "white" : "#4CAF50"}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    styles.goingButtonText,
                    isGoing && styles.activeButtonText,
                  ]}
                >
                  {language.yesGoing}
                </Text>
              </Pressable>

              <Pressable
                style={({ pressed }) => [
                  styles.actionButton,
                  styles.notGoingButton,
                  isNotGoing && styles.activeNotGoingButton,
                  pressed && styles.pressedNotGoingButton,
                ]}
                onPress={() => handleAttendance(session.id, "notGoing")}
              >
                <Ionicons
                  name="close"
                  size={18}
                  color={isNotGoing ? "white" : red}
                />
                <Text
                  style={[
                    styles.actionButtonText,
                    styles.notGoingButtonText,
                    isNotGoing && styles.activeButtonText,
                  ]}
                >
                  {language.notGoing}
                </Text>
              </Pressable>
            </View>
          )}
        </View>

        <View style={{ opacity: session.isCancelled ? 0.8 : 1 }}>
          <UserList
            title={!historySession ? language.areGoing : language.went}
            users={session.goingUsers}
            teamMembers={team.membersData}
            emptyText={
              !historySession ? language.noOneIsGoing : language.noOneWent
            }
            color="#4CAF50"
          />

          <UserList
            title={!historySession ? language.areNotGoing : language.didntGo}
            users={session.notGoingUsers}
            teamMembers={team.membersData}
            emptyText={
              !historySession
                ? language.noOneHasDeclinedYet
                : language.noOneDeclined
            }
            color={red}
          />

          <UserList
            title={
              !historySession
                ? language.haveNotResponded
                : language.haventResponded
            }
            users={undecidedUsers}
            teamMembers={team.membersData}
            emptyText={
              !historySession
                ? language.everyOneHasResponded
                : language.everyOneResponded
            }
            color="#666"
          />
        </View>
      </ScrollView>

      <SlideUpMenu
        isVisible={menuVisible}
        onClose={() => setMenuVisible(false)}
        title={language.sessionOptions || "Session Options"}
        options={[
          // Only show cancel option if session is not cancelled and not a history session
          !session.isCancelled && !historySession
            ? {
                text: language.cancelSession || "Cancel Session",
                textColor: "rgba(253, 141, 4, 0.89)",
                bgColor: "rgba(255, 140, 0, 0.12)",
                pressedBgColor: "rgba(249, 207, 156, 0.8)",
                onPress: () => {
                  setCancelReason("");
                  setCancelModalVisible(true);
                },
                icon: (
                  <MaterialIcons
                    name="event-busy"
                    size={20}
                    color="rgba(255, 140, 0, 0.8)"
                    style={{ marginRight: 8, position: "absolute", left: 15 }}
                  />
                ),
              }
            : null,
          // Only show restore option if session is cancelled
          session.isCancelled
            ? {
                text: language.restoreSession || "Restore Session",
                textColor: "#4CAF50",
                bgColor: "rgba(60, 197, 65, 0.14)",
                pressedBgColor: "rgba(39, 163, 43, 0.24)",
                onPress: () => {
                  // Handle session restoration
                  handleRestoreSession();
                },
                icon: (
                  <MaterialIcons
                    name="restore"
                    size={20}
                    color="#4CAF50"
                    style={{ marginRight: 8, position: "absolute", left: 15 }}
                  />
                ),
              }
            : null,
          {
            text: language.close || "Close",
            onPress: () => setMenuVisible(false),
          },
        ].filter(Boolean)} // Filter out any null values
      />

      {/* The new cancellation reason modal */}
      <Modal
        visible={cancelModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setCancelModalVisible(false)}
      >
        <TouchableWithoutFeedback onPress={() => setCancelModalVisible(false)}>
          <View style={styles.modalOverlay}>
            <TouchableWithoutFeedback>
              <View style={styles.modalContainer}>
                <Text style={styles.modalTitle}>
                  {language.cancelSession || "Cancel Session"}
                </Text>

                <Text style={styles.modalText}>
                  {language.enterCancelReason ||
                    "Please enter a reason for cancellation:"}
                </Text>

                <TextInput
                  style={styles.modalInput}
                  value={cancelReason}
                  onChangeText={setCancelReason}
                  placeholder={
                    language.cancelReasonPlaceholder ||
                    "e.g., Venue unavailable"
                  }
                  multiline={true}
                  numberOfLines={3}
                  textAlignVertical="top"
                  autoFocus={true}
                />

                <View style={styles.modalButtonsContainer}>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.modalCancelButton]}
                    onPress={() => setCancelModalVisible(false)}
                  >
                    <Text style={styles.modalCancelButtonText}>
                      {language.cancel || "Cancel"}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalButton, styles.modalConfirmButton]}
                    onPress={handleCancelSession}
                  >
                    <Text style={styles.modalConfirmButtonText}>
                      {language.confirm || "Confirm"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  loadingIndicator: {
    marginTop: 100,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    height: 55,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    justifyContent: "space-between", // Add this to properly space elements
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    padding: 8,
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    flex: 1, // Allow title to take up available space
    textAlign: "center", // Center the title
    marginLeft: 10, // Adjust margin to balance with the right button
    marginRight: 10,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  menuIcon: {
    alignSelf: "center",
  },
  content: {
    flex: 1,
  },
  dateSection: {
    paddingVertical: 25,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  dateHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: 25,
  },
  monthDay: {
    fontSize: 22,
    fontWeight: "700",
    color: "#333",
  },
  dayText: {
    fontSize: 15,
    fontWeight: "500",
    color: "#666",
    marginBottom: 4,
    paddingLeft: 2,
  },
  timeRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(238, 238, 238, 0.75)",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  startTimeText: {
    fontSize: 15,
    fontWeight: "600",
    color: "rgba(0, 0, 0,0.8)",
  },
  timeSeparator: {
    color: "rgba(0, 0, 0,0.8)",
    marginHorizontal: 4,
    fontWeight: "500",
  },
  endTimeText: {
    fontSize: 15,
    fontWeight: "600",
    color: "rgba(0, 0, 0,0.8)",
  },
  locationWrapper: {
    flexDirection: "row",
    width: "102%",
    alignSelf: "center",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 8,
    marginBottom: 25,
    borderRadius: 12,
    backgroundColor: "white",
    borderColor: "rgba(0,0,0,0.2)",
    borderWidth: 0.5,
    justifyContent: "center",
  },
  locationContentWrapper: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  locationImage: {
    width: 60,
    height: 60,
    borderRadius: 10,
    marginRight: 12,
  },
  locationPlaceholderContainer: {
    width: 60,
    height: 60,
    marginRight: 15,
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: 10,
    borderColor: "rgba(0,0,0,0.4)",
    borderWidth: 0.5,
    borderRadius: 10,
  },
  locationPlaceholderInner: {
    position: "relative",
    width: 40,
    height: 40,
  },
  locationPlaceholderIcon1: {
    width: 40,
    height: 40,
    borderRadius: 100,
    position: "absolute",
    zIndex: 100,
    top: -3,
    left: -5,
  },
  locationPlaceholderIcon2: {
    width: 60,
    height: 60,
    borderRadius: 100,
    position: "absolute",
    zIndex: 99,
    bottom: -36,
    left: -4,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationText: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 5,
    paddingRight: 10,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 12,
    marginBottom: 25,
    backgroundColor: "rgba(238, 238, 238, 0.75)",
    padding: 15,
    borderRadius: 10,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
    marginTop: 5,
    width: "102%",
    alignSelf: "center",
  },
  disabledButtons: {
    opacity: 0.6,
  },
  actionButton: {
    flex: 1,
    height: 45,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  goingButton: {
    backgroundColor: "white",
    borderColor: "#4CAF50",
  },
  notGoingButton: {
    backgroundColor: "white",
    borderColor: red,
  },
  activeGoingButton: {
    backgroundColor: "#4CAF50",
  },
  activeNotGoingButton: {
    backgroundColor: red,
  },
  pressedGoingButton: {
    backgroundColor: "rgba(74, 175, 80, 0.1)",
  },
  pressedNotGoingButton: {
    backgroundColor: "rgba(197, 42, 71, 0.1)",
  },
  actionButtonText: {
    fontWeight: "600",
    fontSize: 15,
  },
  goingButtonText: {
    color: "#4CAF50",
  },
  notGoingButtonText: {
    color: red,
  },
  activeButtonText: {
    color: "white",
  },
  section: {
    marginTop: 20,
    marginBottom: 0,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  countBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 10,
  },
  countText: {
    fontSize: 14,
    fontWeight: "600",
  },
  userCard: {
    alignItems: "center",
    marginRight: 15,
  },
  userName: {
    fontSize: 13,
    color: "#333",
    textAlign: "center",
    fontWeight: "500",
    width: 70,
    marginTop: 5,
  },
  emptyText: {
    fontSize: 15,
    color: "#666",
    fontStyle: "italic",
    textAlign: "center",
    marginTop: 10,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    // justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    marginTop: "50%",
    width: "85%",
    backgroundColor: "white",
    borderRadius: 15,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 15,
    textAlign: "center",
  },
  modalText: {
    fontSize: 15,
    color: "#666",
    marginBottom: 15,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.2)",
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    marginBottom: 20,
    minHeight: 80,
  },
  modalButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  modalButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
  },
  modalCancelButton: {
    backgroundColor: "#f5f5f5",
    marginRight: 8,
  },
  modalConfirmButton: {
    backgroundColor: red,
    marginLeft: 8,
  },
  modalCancelButtonText: {
    color: "#666",
    fontWeight: "600",
    fontSize: 15,
  },
  modalConfirmButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 15,
  },
  cancelledContainer: {
    flex: 1,
    flexDirection: "row",
    backgroundColor: "rgba(255, 140, 0, 0.1)",
    borderRadius: 8,
    padding: 12,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 140, 0, 0.3)",
  },
  cancelledIcon: {
    marginRight: 12,
  },
  cancelledTextContainer: {
    flex: 1,
  },
  cancelledTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#FF8C00",
    marginBottom: 2,
  },
  cancelledReason: {
    fontSize: 14,
    color: "rgba(255, 140, 0, 0.8)",
    fontStyle: "italic",
  },
  descriptionContainer: {
    marginBottom: 15,
    marginTop: 5,
  },
  descriptionText: {
    fontSize: 14,
    color: "#666",
  },
});

export default TrainingSessionDetails;
