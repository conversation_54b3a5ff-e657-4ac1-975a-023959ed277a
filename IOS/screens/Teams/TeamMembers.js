import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  arrayRemove,
  query,
  where,
  onSnapshot,
  writeBatch,
  deleteDoc,
  orderBy,
} from "@react-native-firebase/firestore";

import { red } from "../colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { FontAwesome5 } from "@expo/vector-icons";
import { Octicons } from "@expo/vector-icons";
import { Entypo } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "../../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector, shallowEqual } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

import TeamProfileIcon from "../../assets/TeamProfileIcon";
import { useRoute } from "@react-navigation/native";
import * as Haptics from "expo-haptics";
import { BlurView } from "expo-blur";
import ProfileImage from "../../assets/ProfileImage";
import { FontAwesome6 } from "@expo/vector-icons";
import { useToast } from "../../hooks/toast/useToast";

export const TeamMembers = ({ members, navigation }) => {
  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      <FlatList
        data={members}
        renderItem={({ item }) => (
          <Pressable
            style={{ flexDirection: "row", alignItems: "center", padding: 15 }}
            onPress={() => {
              item.uid === auth.currentUser.uid
                ? navigation.navigate("Profile")
                : navigation.navigate("User", { user: item });
            }}
          >
            {item.profileImageRef ? (
              <FastImage
                source={{ uri: item.profileImageRef }}
                style={{ width: 50, height: 50, borderRadius: 25 }}
              />
            ) : (
              <View
                style={{
                  width: 50,
                  height: 50,
                  borderRadius: 25,
                  backgroundColor: item.profileColor || "#B5CCE7",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{ color: "white", fontSize: 21, fontWeight: "bold" }}
                >
                  {item.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
            <Text style={{ marginLeft: 15, fontSize: 16 }}>{item.name}</Text>
            <MaterialIcons
              name="keyboard-arrow-right"
              size={24}
              color="rgba(0,0,0,0.7)"
              style={{ position: "absolute", right: 15 }}
            />
          </Pressable>
        )}
        keyExtractor={(item) => item.uid}
        ItemSeparatorComponent={() => (
          <View
            style={{
              height: 0.5,
              backgroundColor: "rgba(0,0,0,0.1)",
              marginLeft: 80,
            }}
          />
        )}
      />
    </View>
  );
};
