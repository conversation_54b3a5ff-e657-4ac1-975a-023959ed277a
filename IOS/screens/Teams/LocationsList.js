// screens/Teams/LocationsList.js
import React from "react";
import { useState } from "react";
import { View, Text, StyleSheet, Pressable, FlatList } from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { red } from "../colors";
import { AntDesign, MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LocationItem } from "../../components/Teams/LocationItem";
import { SlideUpMenu } from "../../components/slideUpMenu/SlideUpMenu";
import { useLocations } from "../../hooks/Teams/useLocations";
import * as ImagePicker from "expo-image-picker";

const LocationsList = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const teamId = route.params.teamId;
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );
  const { language } = useSelector((state) => state.language);
  const {
    location,
    menuVisible,
    setMenuVisible,
    handleDelete,
    confirmDelete,
    pickLocationImage,
    deleteLocationImage,
    onLocationSelected,
    addLocation,
    setMainLocation,
  } = useLocations(teamId);

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Pressable
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <AntDesign
              name="arrowleft"
              size={24}
              color="black"
              style={styles.backIcon}
            />
          </Pressable>
          <Text style={styles.headerTitle}>{language.locations}</Text>
        </View>
        <Pressable
          style={styles.addButton}
          onPress={() =>
            navigation.navigate("SearchLocation", {
              onLocationSelected,
              teamId,
              isFromLocationList: true,
              addLocation,
            })
          }
        >
          <MaterialIcons name="add" size={27} color="black" />
        </Pressable>
      </View>

      <FlatList
        data={team.locations}
        renderItem={({ item }) => (
          <LocationItem
            item={item}
            onDelete={handleDelete}
            onPickImage={pickLocationImage}
            onDeleteImage={deleteLocationImage}
            language={language}
            isMain={team.mainLocationId === item.place_id}
            onSetMainLocation={setMainLocation}
          />
        )}
        keyExtractor={(item, index) => item.place_id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />

      <SlideUpMenu
        isVisible={menuVisible}
        onClose={() => setMenuVisible(false)}
        title={language.sureYouWantToDeleteLocation}
        options={[
          {
            text: language.delete,
            textColor: "red",
            bgColor: "rgba(255, 0, 0, 0.05)",
            pressedBgColor: "rgba(255, 0, 0, 0.1)",
            onPress: confirmDelete,
            icon: (
              <AntDesign
                name="delete"
                size={20}
                color="red"
                style={{ marginRight: 8, position: "absolute", left: 15 }}
              />
            ),
          },
          {
            text: language.cancel,
            onPress: () => setMenuVisible(false),
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    paddingBottom: 5,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 15,
    color: "#333",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  listContainer: {
    paddingVertical: 15,
  },
});

export default LocationsList;
