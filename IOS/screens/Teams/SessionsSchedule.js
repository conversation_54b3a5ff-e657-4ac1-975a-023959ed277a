//screens/Teams/SessionsSchedule.js

// React & Hooks
import React, { useState, useEffect } from "react";

// React Native Components
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Pressable,
  FlatList,
  Easing,
  Switch,
  TouchableOpacity,
  Platform,
  Modal,
} from "react-native";

// Navigation
import { useNavigation, useRoute } from "@react-navigation/native";

// Firebase
import { auth, db } from "../../config/firebase";

// Redux
import { useSelector } from "react-redux";

// Assets
import { red } from "../colors";

// Icons
import {
  AntDesign,
  MaterialIcons,
  MaterialCommunityIcons,
  Ionicons,
  Feather,
  FontAwesome,
} from "@expo/vector-icons";

// Components
import { RecurringSessionItem } from "../../components/Teams/RecurringSessionItem";
import { SlideUpMenu } from "../../components/slideUpMenu/SlideUpMenu";
import { Picker } from "@react-native-picker/picker";

// Safe Area
import { useSafeAreaInsets } from "react-native-safe-area-context";
import * as Haptics from "expo-haptics";

// Hooks
import { useSchedule } from "../../hooks/Teams/useSchedule";
import { LinearGradient } from "expo-linear-gradient";

const SessionsSchedule = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const teamId = route.params.team.id;
  const team = useSelector((state) =>
    state.teams.teams.find((t) => t.id === teamId)
  );
  const { language } = useSelector((state) => state.language);
  const [showSettings, setShowSettings] = useState(false);

  const [pickerModalVisible, setPickerModalVisible] = useState(false);
  const [weeksPickerVisible, setWeeksPickerVisible] = useState(false);

  const {
    menuVisible,
    setMenuVisible,
    handleDelete,
    confirmDelete,
    deletingSession,
    weeksAhead,
    updateWeeksAhead,
    sendNotifications,
    updateSendNotifications,
  } = useSchedule(teamId);

  const handleEdit = (session) => {
    navigation.navigate("CreateTrainingSession", { team, session });
  };

  const toggleSettings = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowSettings(!showSettings);
  };

  const handleWeeksChange = (value) => {
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    updateWeeksAhead(value);
  };

  const handleNotificationsChange = (value) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    updateSendNotifications(value);
  };

  console.log(team.recurringTrainingSessions);

  const EmptyScheduleState = () => (
    <LinearGradient
      colors={["#fff0f0", "#fff5f5"]}
      style={styles.emptyScheduleBox}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
    >
      <View style={styles.emptyScheduleContent}>
        {/* <MaterialIcons
          name="add-to-queue"
          size={50}
          color="#ff4d4d"
          style={styles.emptyScheduleIcon}
        /> */}
        <FontAwesome
          name="calendar-plus-o"
          size={50}
          color={red}
          style={styles.emptyScheduleIcon}
        />
        <Text style={styles.emptyScheduleTitle}>{language.noSchedulesYet}</Text>
        <Text style={styles.emptyScheduleDescription}>
          {language.createFirstSchedule}
        </Text>
        <TouchableOpacity
          style={styles.emptyScheduleButton}
          onPress={() => navigation.navigate("CreateTrainingSession", { team })}
        >
          <Text style={styles.emptyScheduleButtonText}>
            {language.createSchedule}
          </Text>
          <MaterialIcons
            name="arrow-forward"
            size={20}
            color="white"
            style={{ marginLeft: 8 }}
          />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  const renderInfoBox = () => (
    <LinearGradient
      colors={["#d9f3ff", "#f0f9ff"]}
      style={styles.infoBox}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
    >
      <View style={styles.infoBoxHeader}>
        <View style={styles.infoTitleContainer}>
          <FontAwesome
            name="star"
            size={20}
            color="#14aff5"
            style={styles.infoTitleIcon}
          />
          <Text style={styles.infoTitle}>{language.autoSchedulingTitle}</Text>
        </View>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={toggleSettings}
        >
          <Ionicons name="settings-outline" size={22} color="#0277bd" />
        </TouchableOpacity>
      </View>

      <Text style={styles.infoDescription}>
        {language.autoSchedulingDescription}
      </Text>

      {showSettings && (
        <View style={styles.settingsContainer}>
          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingLabel}>{language.weeksAhead}</Text>
              <Text
                style={styles.settingDescription}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {language.weeksAheadDescription}
              </Text>
            </View>

            <TouchableOpacity
              style={styles.pickerContainer}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setWeeksPickerVisible(true);
              }}
            >
              <Text style={styles.pickerValue}>{weeksAhead}</Text>
              <Ionicons name="chevron-down" size={16} color="#4fc3f7" />
            </TouchableOpacity>
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingTextContainer}>
              <Text style={styles.settingLabel}>{language.sendReminders}</Text>
              <Text
                style={styles.settingDescription}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {language.sendRemindersDescription}
              </Text>
            </View>
            <Switch
              trackColor={{
                false: "#767577",
                true: "rgba(79, 195, 247, 0.4)",
              }}
              thumbColor={sendNotifications ? "#4fc3f7" : "#f4f3f4"}
              ios_backgroundColor="#3e3e3e"
              onValueChange={handleNotificationsChange}
              value={sendNotifications}
            />
          </View>
        </View>
      )}
    </LinearGradient>
  );

  return (
    <>
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Pressable
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <AntDesign
                name="arrowleft"
                size={24}
                color="black"
                style={styles.backIcon}
              />
            </Pressable>
            <Text style={styles.headerTitle}>{language.sessionsSchedule}</Text>
          </View>

          <Pressable
            style={styles.addButton}
            onPress={() =>
              navigation.navigate("CreateTrainingSession", { team })
            }
          >
            <MaterialIcons name="add" size={27} color="black" />
          </Pressable>
        </View>

        {team.recurringTrainingSessions &&
        team.recurringTrainingSessions.length === 0 ? (
          <EmptyScheduleState />
        ) : (
          <FlatList
            data={team.recurringTrainingSessions}
            ListHeaderComponent={renderInfoBox}
            renderItem={({ item }) => (
              <RecurringSessionItem
                item={item}
                language={language}
                onDelete={handleDelete}
                onEdit={handleEdit}
                isDeleting={deletingSession}
              />
            )}
            keyExtractor={(item, index) =>
              item.createdAt.toDate().getTime().toString()
            }
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}

        <SlideUpMenu
          isVisible={menuVisible}
          onClose={() => setMenuVisible(false)}
          title={language.sureYouWantToDeleteSession}
          options={[
            {
              text: language.delete,
              textColor: "red",
              bgColor: "rgba(255, 0, 0, 0.05)",
              pressedBgColor: "rgba(255, 0, 0, 0.1)",
              onPress: confirmDelete,
              icon: (
                <Feather
                  name="trash-2"
                  size={20}
                  color="red"
                  style={{ marginRight: 8, position: "absolute", left: 15 }}
                />
              ),
            },
            {
              text: language.cancel,
              onPress: () => setMenuVisible(false),
            },
          ]}
        />
      </View>
      <SlideUpMenu
        isVisible={weeksPickerVisible}
        onClose={() => setWeeksPickerVisible(false)}
        title={language.selectWeeksAhead}
        options={[
          {
            text: `1 ${language.week}`,
            textColor: weeksAhead === 1 ? "#4fc3f7" : "rgba(0, 0, 0, 0.8)",
            bgColor:
              weeksAhead === 1
                ? "rgba(79, 195, 247, 0.1)"
                : "rgba(0, 0, 0, 0.05)",
            pressedBgColor: "rgba(79, 195, 247, 0.2)",
            onPress: () => handleWeeksChange(1),
            icon:
              weeksAhead === 1 ? (
                <Ionicons
                  name="checkmark"
                  size={20}
                  color="#4fc3f7"
                  style={{ marginRight: 8, position: "absolute", left: 15 }}
                />
              ) : null,
          },
          {
            text: `2 ${language.weeks}`,
            textColor: weeksAhead === 2 ? "#4fc3f7" : "rgba(0, 0, 0, 0.8)",
            bgColor:
              weeksAhead === 2
                ? "rgba(79, 195, 247, 0.1)"
                : "rgba(0, 0, 0, 0.05)",
            pressedBgColor: "rgba(79, 195, 247, 0.2)",
            onPress: () => handleWeeksChange(2),
            icon:
              weeksAhead === 2 ? (
                <Ionicons
                  name="checkmark"
                  size={20}
                  color="#4fc3f7"
                  style={{ marginRight: 8, position: "absolute", left: 15 }}
                />
              ) : null,
          },
          {
            text: `3 ${language.weeks}`,
            textColor: weeksAhead === 3 ? "#4fc3f7" : "rgba(0, 0, 0, 0.8)",
            bgColor:
              weeksAhead === 3
                ? "rgba(79, 195, 247, 0.1)"
                : "rgba(0, 0, 0, 0.05)",
            pressedBgColor: "rgba(79, 195, 247, 0.2)",
            onPress: () => handleWeeksChange(3),
            icon:
              weeksAhead === 3 ? (
                <Ionicons
                  name="checkmark"
                  size={20}
                  color="#4fc3f7"
                  style={{ marginRight: 8, position: "absolute", left: 15 }}
                />
              ) : null,
          },
        ]}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
    paddingBottom: 5,
  },
  headerLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 15,
    color: "#333",
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  infoBox: {
    // marginTop: 15,
    marginHorizontal: 15,
    marginBottom: 20,
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "rgba(79, 195, 247, 0.3)",
  },
  infoBoxHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingTop: 15,
  },
  infoTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoTitleIcon: {
    marginRight: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0277bd",
  },
  settingsButton: {
    padding: 5,
    width: 35,
    height: 35,
    borderRadius: 30,
    backgroundColor: "rgba(79, 194, 247, 0.18)",
    justifyContent: "center",
    alignItems: "center",
  },
  infoDescription: {
    fontSize: 14,
    color: "#333",
    lineHeight: 20,
    paddingHorizontal: 15,
    paddingBottom: 15,
    paddingTop: 10,
  },
  settingsContainer: {
    marginTop: 5,
    paddingHorizontal: 15,
    paddingBottom: 15,
    backgroundColor: "rgba(255, 255, 255, 0.6)",
    borderTopWidth: 1,
    borderTopColor: "rgba(79, 195, 247, 0.2)",
  },
  settingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 15,
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 10,
    justifyContent: "center",
  },
  settingLabel: {
    fontSize: 15,
    fontWeight: "500",
    color: "#333",
  },
  settingDescription: {
    fontSize: 13,
    color: "#666",
    marginTop: 4,
    flexWrap: "wrap",
  },
  listContainer: {
    paddingVertical: 15,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  pickerModalContainer: {
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: Platform.OS === "ios" ? 40 : 20,
  },
  pickerModalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  pickerModalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  pickerOption: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 8,
    marginBottom: 10,
  },
  pickerOptionSelected: {
    backgroundColor: "rgba(79, 195, 247, 0.1)",
  },
  pickerOptionText: {
    fontSize: 16,
    color: "#333",
  },
  pickerOptionTextSelected: {
    fontWeight: "600",
    color: "#4fc3f7",
  },
  pickerContainer: {
    backgroundColor: "white",
    borderRadius: 8,
    width: 70,
    height: 40,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(79, 195, 247, 0.3)",
    overflow: "hidden",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
  },
  pickerValue: {
    fontSize: 16,
    color: "#333",
    fontWeight: "500",
    flex: 1,
    textAlign: "center",
  },
  emptyScheduleBox: {
    marginTop: 25,
    marginHorizontal: 15,
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: `${red}30`,
  },
  emptyScheduleContent: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  emptyScheduleIcon: {
    marginBottom: 20,
  },
  emptyScheduleTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: red,
    marginBottom: 15,
    textAlign: "center",
  },
  emptyScheduleDescription: {
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.7)",
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 22,
  },
  emptyScheduleButton: {
    backgroundColor: red,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  emptyScheduleButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default SessionsSchedule;
