// React & Hooks
import React, { useState, useEffect } from "react";

// React Native Components
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  Pressable,
  FlatList,
  Animated,
  Dimensions,
  Easing,
} from "react-native";

// Navigation
import { useNavigation } from "@react-navigation/native";

// Firebase
import { auth } from "../../config/firebase";

// Redux
import { useSelector } from "react-redux";

import { LinearGradient } from "expo-linear-gradient";
import { FontAwesome } from "@expo/vector-icons";

// Assets
import { red } from "../colors";
import ProfileImage from "../../assets/ProfileImage";

// Icons
import { AntDesign, Ionicons } from "@expo/vector-icons";

// Custom Hooks
import { useTrainingSessions } from "../../hooks/Teams/useTrainingSessions";

// Components
import { TrainingSessionItem } from "../../components/Teams/TrainingSessionItem";

const { width } = Dimensions.get("window");

const TrainingSessions = ({ team }) => {
  const navigation = useNavigation();
  const { language } = useSelector((state) => state.language);

  const { trainingSessions, handleAttendance } = useTrainingSessions(team.id);

  const SessionsActions = () => (
    <View style={styles.sessionsActions}>
      {/* <TouchableOpacity
        style={styles.actionButton}
        onPress={() => navigation.navigate("CreateTrainingSession", { team })}
      >
        <AntDesign name="plus" size={22} color="white" />
      </TouchableOpacity> */}

      <TouchableOpacity
        style={styles.scheduleButton}
        onPress={() => navigation.navigate("SessionsSchedule", { team })}
      >
        {/* <Ionicons name="time-outline" size={22} color={red} /> */}
        <AntDesign name="calendar" size={22} color={red} />
        <Text style={styles.scheduleButtonText}>{language.schedule}</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.actionButton, styles.secondaryButton]}
        onPress={() => navigation.navigate("TrainingSessionHistory", { team })}
      >
        <Ionicons name="time-outline" size={22} color={red} />
        <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
          {language.history}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const InfoBox = () => (
    <LinearGradient
      // colors={["#fcf2f2", "#ffeded"]}
      colors={["#fff5f5", "#fff5f5"]}
      style={styles.infoBox}
      start={{ x: 0, y: 1 }}
      end={{ x: 0, y: 0 }}
    >
      <View style={styles.infoBoxContent}>
        <FontAwesome
          name="magic"
          size={30}
          color={red}
          style={styles.infoBoxIcon}
        />
        <Text style={styles.infoBoxTitle}>
          {language.streamlineYourTraining}
        </Text>
        <Text style={styles.infoBoxDescription}>
          {language.trainingInfoDescription}
        </Text>
        <TouchableOpacity
          style={styles.infoBoxButton}
          onPress={() => navigation.navigate("SessionsSchedule", { team })}
        >
          <Text style={styles.infoBoxButtonText}>{language.getStarted}</Text>
          <AntDesign
            name="arrowright"
            size={20}
            color="white"
            style={{ marginLeft: 8 }}
          />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      {trainingSessions.length > 0 ? (
        <FlatList
          data={trainingSessions}
          ListHeaderComponent={SessionsActions}
          renderItem={({ item, index }) => (
            <TrainingSessionItem
              item={item}
              language={language}
              team={team}
              previousDate={index > 0 ? trainingSessions[index - 1].date : null}
              navigation={navigation}
              handleAttendance={handleAttendance}
            />
          )}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <SessionsActions />
          <InfoBox />
          {/* <View style={styles.emptyStateContent}>
            <Text style={styles.emptyStateText}>
              {language.noTrainingSessions}
            </Text>
          </View> */}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  sessionsActions: {
    flexDirection: "row",
    padding: 15,
    gap: 10,
    // justifyContent: "flex-end",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  scheduleButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: red,
    justifyContent: "center",
    flexDirection: "row",
    paddingHorizontal: 12,
    alignItems: "center",
    borderRadius: 8,
  },
  scheduleButtonText: {
    color: red,
    fontWeight: "600",
    fontSize: 15,
    marginLeft: 6,
  },
  actionButton: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: red,
    borderRadius: 8,
  },
  secondaryButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: red,
    width: "auto",
    paddingHorizontal: 12,
    flexDirection: "row",
    gap: 6,
  },
  actionButtonText: {
    color: red,
    fontWeight: "600",
    fontSize: 15,
  },
  secondaryButtonText: {
    color: red,
  },
  listContainer: {
    paddingBottom: 20,
  },
  sessionGroup: {
    marginTop: 20,
    paddingHorizontal: 15,
  },
  groupTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 10,
    color: "#333",
  },
  emptyState: {
    flex: 1,
  },
  emptyStateContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
  },

  infoBox: {
    marginHorizontal: 15,
    marginTop: 10,
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: `${red}30`,
  },
  infoBoxContent: {
    paddingVertical: 25,
    paddingHorizontal: 20,
    alignItems: "center",
  },
  infoBoxIcon: {
    marginBottom: 15,
  },
  infoBoxTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: red,
    marginBottom: 15,
    textAlign: "center",
  },
  infoBoxDescription: {
    fontSize: 16,
    color: "rgba(0, 0, 0, 0.7)",
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 22,
  },
  infoBoxButton: {
    backgroundColor: red,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    flexDirection: "row",
    alignItems: "center",
  },
  infoBoxButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default TrainingSessions;
