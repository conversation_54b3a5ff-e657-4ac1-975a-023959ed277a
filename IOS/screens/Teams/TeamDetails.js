// screens/Teams/TeamDetails.js
import React from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  FlatList,
  Animated,
  StyleSheet,
  Dimensions,
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import { auth } from "../../config/firebase";
import { BlurView } from "expo-blur";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";
import { red } from "../colors";
import TeamProfileIcon from "../../assets/TeamProfileIcon";
import ProfileImage from "../../assets/ProfileImage";
import { TeamMembers } from "./TeamMembers";
import TrainingSessions from "./TrainingSessions";
import { useTeamDetails } from "../../hooks/Teams/useTeamDetails";
import {
  AntDesign,
  Ionicons,
  Feather,
  MaterialCommunityIcons,
  Entypo,
  MaterialIcons,
} from "@expo/vector-icons";
import { SlideUpMenu } from "../../components/slideUpMenu/SlideUpMenu";
import InviteTeamModal from "../../components/Teams/InviteTeamModal";

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const width = Dimensions.get("window").width;
const height = Dimensions.get("window").height;

const Tab = createMaterialTopTabNavigator();

const TeamDetails = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { teamId } = route.params;

  const {
    team,
    mainLocation,
    chat,
    language,
    menuVisible,
    inviteModalVisible,
    leaveModalVisible,
    isTeamCreator,
    isProcessing,
    showMenu,
    hideMenu,
    showInviteModal,
    hideInviteModal,
    showLeaveModal,
    hideLeaveModal,
    leaveTeam,
    deleteTeam,
    navigateToLocations,
    navigateToChat,
  } = useTeamDetails(teamId);

  if (!team) {
    return (
      <View style={styles.teamNotFoundContainer}>
        <Text style={styles.teamNotFoundText}>{language.teamNotFound}</Text>
      </View>
    );
  }

  return (
    <View style={{ paddingTop: insets.top, ...styles.container }}>
      <View style={styles.leftShadow}></View>
      <View style={styles.headerContainer}>
        {/* Top Row - Back and Menu buttons */}
        <View style={styles.topRowContainer}>
          <Pressable
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <AntDesign
              name="arrowleft"
              size={24}
              color="black"
              style={styles.backIcon}
            />
          </Pressable>

          <View style={styles.headerButtonsContainer}>
            <Pressable
              style={styles.inboxButton}
              onPress={() =>
                navigation.navigate("TeamInbox", { teamId: team.id })
              }
            >
              <Feather
                name="inbox"
                color="black"
                size={24}
                style={styles.inboxIcon}
              />
            </Pressable>
            <Pressable style={styles.menuButton} onPress={showMenu}>
              <Entypo
                name="dots-three-horizontal"
                size={24}
                color="black"
                style={styles.menuIcon}
              />
            </Pressable>
          </View>
        </View>

        {/* Main Content Row */}
        <View style={styles.teamInfoContainer}>
          {/* Left: Large Team Icon */}
          <TeamProfileIcon team={team} size={97} />

          {/* Right: Team Info and Chat */}
          <View style={styles.teamDetailsContainer}>
            {/* Team Info */}
            <View>
              <Text style={styles.teamName} numberOfLines={1}>
                {team.name}
              </Text>

              {team.locations && mainLocation && (
                <Text style={styles.locationText} numberOfLines={1}>
                  {mainLocation.name}
                </Text>
              )}
            </View>

            {/* Chat Preview */}
            <Pressable
              style={({ pressed }) => [
                styles.chatButton,
                pressed && styles.chatButtonPressed,
              ]}
              onPress={navigateToChat}
            >
              <View style={styles.chatTextContainer}>
                {chat[0]?.lastUnreadMessage ? (
                  <Text numberOfLines={1} style={styles.chatMessageContainer}>
                    <Text
                      style={[
                        styles.chatSenderName,
                        chat[0]?.unreadMessages[auth.currentUser.uid] > 0 &&
                          styles.chatSenderNameUnread,
                      ]}
                    >
                      {chat[0].lastUnreadMessage.user.name}:
                    </Text>
                    <Text
                      style={[
                        styles.chatMessageText,
                        chat[0]?.unreadMessages[auth.currentUser.uid] > 0 &&
                          styles.chatMessageTextUnread,
                      ]}
                    >
                      {" " + chat[0].lastUnreadMessage.text}
                    </Text>
                  </Text>
                ) : (
                  <Text style={styles.openChatText}>
                    {language.openChatTeam}
                  </Text>
                )}
              </View>

              <View style={styles.chatIconContainer}>
                {chat[0]?.unreadMessages[auth.currentUser.uid] > 0 ? (
                  <View style={styles.unreadBadge}>
                    <Text style={styles.unreadBadgeText}>
                      {chat[0].unreadMessages[auth.currentUser.uid] > 5
                        ? "5+"
                        : chat[0].unreadMessages[auth.currentUser.uid]}
                    </Text>
                  </View>
                ) : (
                  <Ionicons
                    name="chatbubble-ellipses-outline"
                    size={19}
                    color={red}
                  />
                )}
              </View>
            </Pressable>
          </View>
        </View>
      </View>

      {/* Tab Navigator */}
      <Tab.Navigator
        screenOptions={{
          tabBarStyle: styles.tabBar,
          tabBarIndicatorStyle: styles.tabIndicator,
          tabBarLabelStyle: styles.tabLabel,
          tabBarActiveTintColor: red,
          tabBarInactiveTintColor: "rgba(0,0,0,0.6)",
        }}
      >
        <Tab.Screen
          name="TrainingSessions"
          options={{ title: language.events }}
          children={() => <TrainingSessions team={team} />}
        />
        <Tab.Screen
          name="Members"
          options={{ title: language.players }}
          children={() => (
            <TeamMembers members={team.membersData} navigation={navigation} />
          )}
        />
      </Tab.Navigator>

      {/* SlideUpMenu component */}
      <SlideUpMenu
        isVisible={menuVisible}
        onClose={hideMenu}
        title="Menu"
        options={[
          {
            text: language.inviteToTeam,
            onPress: showInviteModal,
            icon: (
              <Feather
                name="share"
                size={21}
                color="rgb(50,50,50)"
                style={{ marginRight: 8, position: "absolute", left: 15 }}
              />
            ),
          },
          {
            text: language.listOfLocations,
            onPress: navigateToLocations,
            icon: (
              <Ionicons
                name="location-outline"
                size={24}
                color="rgb(50,50,50)"
                style={{ marginRight: 8, position: "absolute", left: 15 }}
              />
            ),
          },
          {
            text: isTeamCreator ? language.deleteTeam : language.leaveTeam,
            textColor: "red",
            bgColor: "rgba(255, 0, 0, 0.05)",
            pressedBgColor: "rgba(255, 0, 0, 0.1)",
            onPress: showLeaveModal,
            icon: (
              <>
                {isTeamCreator ? (
                  <MaterialIcons
                    name="delete-forever"
                    size={21}
                    color="red"
                    style={{ marginRight: 8, position: "absolute", left: 15 }}
                  />
                ) : (
                  <Feather
                    name="log-out"
                    size={21}
                    color="red"
                    style={{ marginRight: 8, position: "absolute", left: 15 }}
                  />
                )}
              </>
            ),
          },
        ]}
      />

      {/* Leave/Delete confirmation modal */}
      {leaveModalVisible && (
        <View style={styles.modalOverlay}>
          <BlurView style={styles.blurOverlay} intensity={25} tint="dark">
            <Pressable
              style={styles.pressableDismiss}
              onPress={hideLeaveModal}
            />
          </BlurView>
          <View style={styles.confirmModal}>
            <Text style={styles.confirmTitle}>
              {isTeamCreator
                ? language.deleteTeamConfirm
                : language.leaveTeamConfirm}
            </Text>
            <Text style={styles.confirmText}>
              {isTeamCreator
                ? language.deleteTeamWarning
                : language.leaveTeamWarning}
            </Text>
            <View style={styles.confirmButtonContainer}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={hideLeaveModal}
              >
                <Text style={styles.cancelButtonText}>{language.cancel}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.confirmButton,
                  isProcessing && styles.confirmButtonDisabled,
                ]}
                onPress={isTeamCreator ? deleteTeam : leaveTeam}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Text style={styles.confirmButtonText}>
                    {isTeamCreator ? language.delete : language.leave}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Invite Team Modal component */}
      <InviteTeamModal
        teamId={teamId}
        isVisible={inviteModalVisible}
        onClose={hideInviteModal}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  leftShadow: {
    position: "absolute",
    height: "100%",
    width: 15,
    backgroundColor: "transparent",
    left: 0,
    zIndex: 1000,
  },
  headerContainer: {
    paddingHorizontal: 15,
    backgroundColor: "white",
  },
  topRowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    height: 35,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerButtonsContainer: {
    flexDirection: "row",
  },
  inboxButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    marginRight: 10,
  },
  inboxIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  menuButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
  },
  menuIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  teamInfoContainer: {
    flexDirection: "row",
    marginTop: 15,
    marginBottom: 5,
  },
  teamDetailsContainer: {
    flex: 1,
    marginLeft: 15,
    justifyContent: "space-between",
  },
  teamName: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
  },
  locationText: {
    fontSize: 13,
    color: "#666",
    marginTop: 4,
  },
  chatButton: {
    marginTop: 10,
    backgroundColor: "white",
    borderRadius: 50,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.2)",
    shadowColor: "rgba(0,0,0,0.1)",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  chatButtonPressed: {
    transform: [{ scale: 0.98 }],
  },
  chatTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  chatMessageContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  chatSenderName: {
    fontWeight: "600",
    color: "rgba(0,0,0,0.9)",
    fontSize: 13,
  },
  chatSenderNameUnread: {
    fontWeight: "700",
  },
  chatMessageText: {
    color: "rgba(0,0,0,0.8)",
    fontWeight: "500",
    fontSize: 13,
  },
  chatMessageTextUnread: {
    fontWeight: "600",
  },
  openChatText: {
    fontSize: 13,
    color: "#666",
  },
  chatIconContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 19,
  },
  unreadBadge: {
    backgroundColor: red,
    borderRadius: 12,
    minWidth: 24,
    height: 19,
    justifyContent: "center",
    alignItems: "center",
  },
  unreadBadgeText: {
    color: "white",
    fontSize: 11,
    fontWeight: "600",
  },
  tabBar: {
    backgroundColor: "white",
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  tabIndicator: {
    backgroundColor: red,
  },
  tabLabel: {
    textTransform: "none",
    fontWeight: "600",
    fontSize: 14,
  },
  teamNotFoundContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F0F4F8",
  },
  teamNotFoundText: {
    fontSize: 18,
    color: red,
    fontWeight: "bold",
  },

  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  blurOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pressableDismiss: {
    flex: 1,
  },
  confirmModal: {
    width: "85%",
    backgroundColor: "white",
    borderRadius: 16,
    padding: 24,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  confirmTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
    marginBottom: 12,
    textAlign: "center",
  },
  confirmText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 24,
    textAlign: "center",
    lineHeight: 22,
  },
  confirmButtonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f2f2f2",
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 8,
    alignItems: "center",
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#666",
  },
  confirmButton: {
    flex: 1,
    backgroundColor: red,
    paddingVertical: 12,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: "center",
  },
  confirmButtonDisabled: {
    opacity: 0.7,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
});

export default TeamDetails;
