// React & Hooks
import React, { useState, useEffect } from "react";

// React Native Components
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Pressable,
  FlatList,
} from "react-native";

// Navigation
import { useNavigation, useRoute } from "@react-navigation/native";

// Firebase
import { auth, db } from "../../config/firebase";
import {
  collection,
  getDocs,
  orderBy,
  query,
} from "@react-native-firebase/firestore";

// Redux
import { useSelector } from "react-redux";

// Assets
import { red } from "../colors";

// Icons
import { AntDesign } from "@expo/vector-icons";

// Components
import { TrainingSessionItem } from "../../components/Teams/TrainingSessionItem";

// Safe Area
import { useSafeAreaInsets } from "react-native-safe-area-context";

const TrainingSessionHistory = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const { team } = route.params;
  const { language } = useSelector((state) => state.language);
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSessionHistory();
  }, []);

  const fetchSessionHistory = async () => {
    try {
      const sessionsRef = collection(
        db,
        "teams",
        team.id,
        "trainingSessionsHistory"
      );
      const q = query(sessionsRef, orderBy("date", "desc"));
      const querySnapshot = await getDocs(q);

      const sessionsData = querySnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          endTime: data.endTime ? data.endTime.toMillis() : null,
          startTime: data.startTime ? data.startTime.toMillis() : null,
          createdAt: data.createdAt ? data.createdAt.toMillis() : null,
          date: data.date ? data.date.toMillis() : null,
        };
      });

      setSessions(sessionsData);
    } catch (error) {
      console.error("Error fetching session history:", error);
    } finally {
      setLoading(false);
    }
  };

  //   if (loading) {
  //     return (
  //       <View style={[styles.loadingContainer, { paddingTop: insets.top }]}>
  //         <ActivityIndicator
  //           size="small"
  //           color={"grey"}
  //           style={{ marginTop: -100 }}
  //         />
  //       </View>
  //     );
  //   }

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Pressable
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <AntDesign
            name="arrowleft"
            size={24}
            color="black"
            style={styles.backIcon}
          />
        </Pressable>
        <Text style={styles.headerTitle}>{language.trainingHistory}</Text>
      </View>
      {!loading ? (
        <FlatList
          data={sessions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TrainingSessionItem
              item={item}
              language={language}
              team={team}
              isHistory={true}
              navigation={navigation}
            />
          )}
          contentContainerStyle={styles.listContainer}
          // showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <Text style={styles.emptyText}>{language.noTrainingHistory}</Text>
          }
        />
      ) : (
        <ActivityIndicator
          size="small"
          color={"grey"}
          style={{ marginTop: 150 }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    height: 55,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 15,
    color: "#333",
  },
  listContainer: {
    paddingVertical: 15,
  },
  emptyText: {
    textAlign: "center",
    fontSize: 16,
    color: "#666",
    marginTop: 30,
    fontStyle: "italic",
  },
});

export default TrainingSessionHistory;
