import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
  limit,
  startAfter,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { FontAwesome5 } from "@expo/vector-icons";
import { Octicons } from "@expo/vector-icons";
import ScreenHeader from "../components/common/ScreenHeader";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

import ProfileImage from "./../assets/ProfileImage";
import { Entypo } from "@expo/vector-icons";

// import ProfileIcon from "./../assets/comps/ProfileIcon";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

const Tab = createMaterialTopTabNavigator();

const acceptFriendRequest = httpsCallable(functions, "acceptFriendRequestV2");

const sentFriendRequestNotificationV2 = httpsCallable(
  functions,
  "sentFriendRequestNotificationV2"
);

const createChatRoom = httpsCallable(functions, "createChatRoom");

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

// ProfileIcon component removed as we're now using ScreenHeader with ProfileImage

const Coaches = ({ navigation }) => {
  return (
    <View style={{ backgroundColor: "white", flex: 1 }}>
      <Text>Comunities</Text>
    </View>
  );
};

const Friends = ({ navigation }) => {
  const friends = useSelector((state) => state.socials.friends);
  // console.log("Friends:", friends);

  const renderItem = ({ item, index }) => {
    return (
      <View>
        <TouchableHighlight
          onPress={() => {
            console.log("Friend pressed:", item);
            navigation.navigate("FriendDetails", { friend: item });
          }}
          underlayColor="rgba(0, 0, 0, 0.04)"
          style={{
            width: "95%",
            alignSelf: "center",
            borderRadius: 15,
            marginVertical: 10,
            paddingVertical: 20,
            paddingHorizontal: 5,
          }}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            {item.profileImageRef ? (
              <Image
                source={{ uri: item.profileImageRef }}
                style={{
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  marginRight: 15,
                  borderWidth: 0.15,
                  borderColor: "rgba(0,0,0,0.8)",
                }}
              />
            ) : (
              <View
                style={{
                  width: 100,
                  height: 100,
                  borderRadius: 50,
                  backgroundColor: item.profileColor || "#B5CCE7",
                  justifyContent: "center",
                  alignItems: "center",
                  marginRight: 15,
                }}
              >
                <Text
                  style={{ color: "white", fontSize: 35, fontWeight: "bold" }}
                >
                  {item.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}

            <View style={{ justifyContent: "center", flex: 1 }}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 10,
                }}
              >
                <Text style={{ fontSize: 18, fontWeight: "600" }}>
                  {item.name}
                </Text>
                <Text
                  style={{ fontSize: 14, fontWeight: "500", marginLeft: 10 }}
                >
                  {calculateAge(item.date)} years
                </Text>
              </View>

              <View
                style={{
                  flexDirection: "row",
                  marginTop: 23,
                  justifyContent: "space-between",
                  paddingRight: 10,
                }}
              >
                <TouchableOpacity
                  style={{
                    backgroundColor: "white",
                    borderRadius: 5,
                    borderColor: "red",
                    borderWidth: 1,
                    flexDirection: "row",
                    justifyContent: "center",
                    height: 33,
                    alignItems: "center",
                    width: "44%",
                    paddingTop: 1,
                  }}
                  onPress={() => {
                    console.log(item);
                    // sendFriendRequest(item);
                  }}
                >
                  {/* <FontAwesome name="remove" size={15} color={red} /> */}
                  {/* <Ionicons name="person-remove-sharp" size={15} color={red} /> */}
                  {/* <MaterialIcons name="done" size={18} color={red} /> */}
                  {/* <FontAwesome name="check" size={18} color={red} /> */}
                  {/* <AntDesign name="checkcircle" size={15} color={red} /> */}
                  {/* <MaterialCommunityIcons
                    name="checkbox-marked-circle-outline"
                    size={15}
                    color={red}
                  /> */}
                  <Octicons name="check-circle" size={14} color={red} sty />
                  <Text
                    style={{
                      color: "red",
                      fontWeight: "600",
                      fontSize: 13,
                      marginLeft: 8,
                      // marginTop: 1,
                    }}
                  >
                    Friends
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={{
                    backgroundColor: "rgba(0,0,0,0.0)",
                    borderRadius: 5,
                    borderColor: "rgba(0, 0, 0, 0.2)",
                    borderWidth: 0.5,
                    height: 33,
                    justifyContent: "center",
                    width: "51%",
                    alignItems: "center",
                  }}
                  onPress={() => console.log("Message")}
                >
                  <Text
                    style={{
                      color: "rgba(0,0,0,0.8)",
                      fontWeight: "500",
                      fontSize: 13,
                    }}
                  >
                    Message
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </TouchableHighlight>
        {index !== friends.length - 1 && (
          <View
            style={{
              height: 0.8,
              backgroundColor: "rgba(0,0,0,0.1)",
              width: "90%",
              alignSelf: "center",
            }}
          />
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <FlatList
        data={friends}
        renderItem={renderItem}
        keyExtractor={(item) => item.uid}
        ListEmptyComponent={<Text>No friends to show</Text>}
      />
    </SafeAreaView>
  );
};

const SearchHeader = React.memo(
  ({ searchQuery, setSearchQuery, totalUsers }) => (
    <View>
      <View
        style={{
          backgroundColor: "rgba(0, 0, 0, 0.01)",
          borderColor: "#d0d0d0",
          borderWidth: 1,
          borderRadius: 25,
          margin: 10,
          flexDirection: "row",
          alignItems: "center",
          paddingHorizontal: 15,
          marginTop: 20,
          width: "90%",
          alignSelf: "center",
        }}
      >
        <Ionicons
          name="search"
          size={24}
          color="gray"
          style={{ marginRight: 10 }}
        />
        <TextInput
          style={{
            flex: 1,
            height: 40,
            fontSize: 16,
          }}
          placeholder="Find players"
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCorrect={false}
          returnKeyType="done"
        />
        {searchQuery.length > 0 && (
          <Pressable
            onPress={() => setSearchQuery("")}
            style={{
              padding: 8,
              marginRight: -10,
              // backgroundColor: "red",
            }}
          >
            <View
              style={{
                backgroundColor: "rgba(0,0,0,0.2)",
                borderRadius: 12,
                width: 20,
                height: 20,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {/* <Ionicons name="close" size={16} color="rgba(0,0,0,0.5)" /> */}
              <Entypo
                name="cross"
                size={14}
                color="rgba(255,255,255,1)"
                style={{ paddingLeft: 0.5, paddingTop: 0.5 }}
              />
            </View>
          </Pressable>
        )}
      </View>

      <View
        style={{
          height: 0.5,
          width: "90%",
          backgroundColor: "rgba(0,0,0,0.15)",
          justifyContent: "center",
          alignSelf: "center",
          marginTop: 7,
          marginBottom: 7,
        }}
      />
      {totalUsers > 0 && (
        <View
          style={{
            alignItems: "center",
            marginVertical: 10,
            backgroundColor: "rgba(0, 255, 0, 0.15)",
            borderRadius: 15,
            padding: 5,
            alignSelf: "center",
            width: 150,
          }}
        >
          <Text style={{ fontSize: 14, fontWeight: "bold", color: "green" }}>
            {totalUsers} Players
          </Text>
        </View>
      )}
    </View>
  )
);

const UserItem = ({ item, index, navigation, onSendRequest }) => {
  if (index === 0) {
    return;
  }

  return (
    <View>
      <TouchableHighlight
        onPress={() => {
          navigation.navigate("User", {
            user: item,
            navigation: navigation,
          });
        }}
        underlayColor="rgba(0, 0, 0, 0.04)"
        style={{
          width: "95%",
          alignSelf: "center",
          borderRadius: 15,
          marginVertical: 10,
          paddingVertical: 20,
          paddingHorizontal: 5,
        }}
      >
        <View
          style={{
            alignSelf: "center",
            width: "100%",
          }}
        >
          <View style={{ flexDirection: "row" }}>
            <ProfileImage
              profileImageRef={item.profileImageRef}
              profileColor={item.profileColor}
              name={item.name}
              size={100}
              style={{ marginRight: 15 }}
            />

            <View
              style={{
                justifyContent: "center",
                flex: 1,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 10,
                }}
              >
                <Text style={{ fontSize: 18, fontWeight: "600" }}>
                  {item.name}
                </Text>
                <Text
                  style={{ fontSize: 14, fontWeight: "500", marginLeft: 10 }}
                >
                  {calculateAge(item.date)} let
                </Text>
              </View>

              <View
                style={{
                  flexDirection: "row",
                  marginTop: 23,
                  justifyContent: "space-between",
                  paddingRight: 10,
                }}
              >
                {item.friendRequestSent ? (
                  <View
                    style={{
                      backgroundColor: "white",
                      borderRadius: 5,
                      borderColor: red,
                      borderWidth: 1,
                      flexDirection: "row",
                      justifyContent: "center",
                      height: 33,
                      alignItems: "center",
                      width: "52%",
                    }}
                  >
                    <Octicons
                      name="check-circle"
                      size={12}
                      color={red}
                      style={{ paddingTop: 1 }}
                    />
                    <Text
                      style={{
                        color: red,
                        fontWeight: "600",
                        fontSize: 12,
                        marginLeft: 5,
                        paddingTop: 1,
                      }}
                    >
                      Request sent
                    </Text>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={{
                      backgroundColor: "white",
                      borderRadius: 5,
                      borderColor: red,
                      borderWidth: 1,
                      flexDirection: "row",
                      justifyContent: "center",
                      height: 33,
                      alignItems: "center",
                      width: "52%",
                    }}
                    onPress={() => onSendRequest(item)}
                  >
                    <MaterialIcons name="person-add" size={18} color={red} />
                    <Text
                      style={{
                        color: red,
                        fontWeight: "600",
                        fontSize: 13,
                        marginLeft: 7,
                        paddingTop: 1,
                      }}
                    >
                      Add friend
                    </Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={{
                    backgroundColor: "rgba(0,0,0,0.0)",
                    borderRadius: 5,
                    borderColor: "rgba(0, 0, 0, 0.2)",
                    borderWidth: 0.5,
                    height: 33,
                    justifyContent: "center",
                    width: "45%",
                    alignItems: "center",
                  }}
                  onPress={() => {
                    messageUser(navigation, item);
                  }}
                >
                  <Text
                    style={{
                      color: "rgba(0,0,0,0.8)",
                      fontWeight: "500",
                      paddingTop: 0.5,
                      fontSize: 13,
                    }}
                  >
                    Message
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </TouchableHighlight>
      <View
        style={{
          height: 0.8,
          backgroundColor: "rgba(0,0,0,0.1)",
          width: "90%",
          alignSelf: "center",
        }}
      />
    </View>
  );
};

const Players = ({ navigation }) => {
  const [users, setUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [lastDoc, setLastDoc] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [totalUsers, setTotalUsers] = useState(0);
  const PAGE_SIZE = 10;

  const appControl = useSelector((state) => state.appControl);
  // console.warn(appControl);

  const chats = useSelector((state) => state.socials.chats);
  const user = useSelector((state) => state.socials.userData);

  const fetchUsers = async (isNewSearch = false) => {
    if (isLoading || (!hasMore && !isNewSearch)) return;

    try {
      setIsLoading(true);
      let q = query(
        collectionGroup(db, "public"),
        where("uid", "!=", auth.currentUser.uid)
      );

      if (searchQuery.trim()) {
        q = query(
          q,
          where("name", ">=", searchQuery.trim()),
          where("name", "<=", searchQuery.trim() + "\uf8ff")
        );
      }

      q = query(q, limit(PAGE_SIZE));
      if (lastDoc && !isNewSearch) {
        q = query(q, startAfter(lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const docs = querySnapshot.docs;

      setLastDoc(docs[docs.length - 1] || null);
      setHasMore(docs.length === PAGE_SIZE);

      const newUsers = docs
        .map((doc) => doc.data())
        .filter(
          (userData) =>
            !user.friends?.includes(userData.uid) &&
            !["fulup", "Bbbb", "tripledaja", "Tobias"].includes(userData.name)
        )
        .map((userData) => ({
          ...userData,
          friendRequestSent:
            user.friendRequestsSent?.includes(userData.uid) || false,
        }));

      setUsers(isNewSearch ? newUsers : [...users, ...newUsers]);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching users:", error);
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    setUsers([]);
    setLastDoc(null);
    setHasMore(true);
    fetchUsers(true);
  }, [searchQuery]);

  // Refetch when friendRequestsSent changes
  useEffect(() => {
    if (user.friendRequestsSent) {
      // Instead of refetching, we can just update the existing users
      setUsers((currentUsers) =>
        currentUsers.map((u) => ({
          ...u,
          friendRequestSent: user.friendRequestsSent.includes(u.uid),
        }))
      );
    }
  }, [user.friendRequestsSent]);

  const handleLoadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchUsers();
    }
  }, [isLoading, hasMore]);

  const getTotalUsersCount = async () => {
    try {
      const q = query(
        collectionGroup(db, "public"),
        where("uid", "!=", auth.currentUser.uid)
      );
      const snapshot = await getCountFromServer(q);
      setTotalUsers(snapshot.data().count);
    } catch (error) {
      console.error("Error getting users count:", error);
    }
  };
  useEffect(() => {
    getTotalUsersCount();
  }, []);

  // const memoizedFetchUsers = useCallback(fetchUsers, [
  //   searchQuery,
  //   lastDoc,
  //   users,
  //   user.friends,
  //   isLoading,
  //   hasMore,
  // ]);

  const sendFriendRequest = async (userToAdd) => {
    // Optimistically update our users list
    try {
      setUsers((currentUsers) =>
        currentUsers.map((u) =>
          u.uid === userToAdd.uid ? { ...u, friendRequestSent: true } : u
        )
      );

      console.log("Sending friend request to:", userToAdd);
      try {
        await sentFriendRequestNotificationV2({
          from: auth.currentUser.uid,
          to: userToAdd.uid,
        });
        // Success case - no need to do anything as the useEffect will handle it
      } catch (error) {
        // Revert the optimistic update
        setUsers((currentUsers) =>
          currentUsers.map((u) =>
            u.uid === userToAdd.uid ? { ...u, friendRequestSent: false } : u
          )
        );

        let errorMessage = "Failed to send friend request";
        if (error.code === "already-exists") {
          errorMessage = "Friend request already sent";
        } else if (error.code === "not-found") {
          errorMessage = "User not found";
        }
        Alert.alert("Error", errorMessage);
      }
    } catch (error) {
      console.error("Error sending friend request:", error);
    }
  };

  const ListFooterComponent = () => {
    if (!isLoading) return null;
    return (
      <View style={{ paddingVertical: 20 }}>
        <ActivityIndicator size="small" color={"grey"} />
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <View style={{ width: "100%", height: "100%", backgroundColor: "white" }}>
        <FlatList
          data={[...users]}
          renderItem={({ item, index }) => {
            return (
              <UserItem
                item={item}
                index={index}
                navigation={navigation}
                onSendRequest={sendFriendRequest}
              />
            );
          }}
          keyExtractor={(item, index) => item.uid}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={ListFooterComponent}
          ListHeaderComponent={
            <SearchHeader
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              totalUsers={totalUsers}
            />
          }
        />
      </View>
    </SafeAreaView>
  );
};

export default function Socials() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const chats = useSelector((state) => state.socials.chats);
  const user = useSelector((state) => state.socials.userData);

  // const [language2, setLanguage2] = useState(user.language);
  const [language, setLanguage] = useState(null);

  const [profileImageRef, setProfileImageRef] = useState(null);
  const [profileColor, setProfileColor] = useState(null);
  const [userName, setUserName] = useState(null);

  const [chatNotifsCount, setChatNotifsCount] = useState(null);

  // useEffect(() => {
  //   // setNotificationsCount(chats.length);

  //   const count = chats.filter((doc) => {
  //     return doc;
  //   });
  // }, [chats]);

  const notifications = useSelector((state) => state.socials.notifications);

  useEffect(() => {
    const count = chats.reduce((total, chat) => {
      // console.warn(chat);
      if (!chat) return;
      return total + (chat.unreadCount || 0);
    }, 0);
    setChatNotifsCount(count);
  }, [chats]);

  // useEffect(() => {
  //   const docRef = doc(db, "users", auth.currentUser.uid);

  //   const unsubscribe = onSnapshot(docRef, (docSnap) => {
  //     if (docSnap.exists) {
  //       const doc = docSnap.data();

  //       if (doc.language !== language2) {
  //         setLanguage2(doc.language);
  //         // setFriends(doc.friends);
  //
  //       }
  //     }
  //   });

  //   return () => unsubscribe();
  // }, []);

  useEffect(() => {
    if (user?.language === "CZ") {
      setLanguage(CZ);
    } else if (user?.language === "EN") {
      setLanguage(EN);
    }
  }, [user?.language, language]);

  // useEffect(() => {
  //   const notifCollection = collection(
  //     db,
  //     `users/${auth.currentUser.uid}/notifications`
  //   );

  //   const unsubscribe = onSnapshot(notifCollection, (snapshot) => {
  //     let count = 0;
  //     snapshot.forEach((doc) => {
  //       count++;
  //     });
  //
  //     setNotificationsCount(count);
  //   });

  //   // Cleanup function to unsubscribe from the listener when the component unmounts.
  //   return () => {
  //
  //     unsubscribe();
  //   };
  // }, []);

  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleButtonClick = async () => {
    try {
      const q = query(
        collectionGroup(db, "public"),
        where("name", "==", inputValue)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const document = querySnapshot.docs[0]; // Assume the name field is unique

        acceptFriendRequest({
          to: auth.currentUser.uid,
          from: querySnapshot.docs[0].data().uid,
        });
      } else {
      }
    } catch (error) {
      console.error("Error fetching document:", error);
    }
  };

  if (
    !language ||
    notifications == null ||
    chats == null ||
    chatNotifsCount == null ||
    !user
  ) {
    return;
  }

  return (
    <>
      <View
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "white",
          paddingTop: insets.top,
        }}
        // onStartShouldSetResponder={() => true}
      >
        <ScreenHeader title="Socials" />

        <Tab.Navigator
          screenOptions={{
            // swipeEnabled: false,
            tabBarLabelStyle: {
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              // backgroundColor: "red",
              fontSize: 14,
              fontWeight: "600",
              textTransform: "none",
            },
            tabBarIndicatorStyle: {
              backgroundColor: red,
            },
            tabBarStyle: {
              marginTop: 5,
              height: 46,
              borderBottomColor: "rgba(0,0,0,0.2)",
              borderBottomWidth: 0.5,
              // backgroundColor: "red",
            },

            lazy: false,
            // tabBarActiveTintColor: red,
            // tabBarInactiveTintColor: "rgba(0,0,0,0.6)",
          }}
        >
          <Tab.Screen
            name={"All Players"}
            component={Players}
            initialParams={{ navigation: navigation }}
          />
          <Tab.Screen
            name={"Friends"}
            component={Friends}
            initialParams={{ navigation: navigation }}
          />
          <Tab.Screen
            name={"Coaches"}
            component={Coaches}
            initialParams={{ navigation: navigation }}
          />
        </Tab.Navigator>

        {/* <View
          style={{
            height: 50,
            width: "90%",
            marginTop: 200,
            alignSelf: "center",
          }}
        >
          <TextInput
            value={inputValue}
            onChangeText={setInputValue}
            placeholder="Enter name"
            style={{
              height: 40,
              borderColor: "gray",
              borderWidth: 1,
              marginBottom: 10,
            }}
          />
          <Button title="Fetch Document" onPress={handleButtonClick} />
        </View> */}
      </View>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      ></View>
    </>
  );
}
