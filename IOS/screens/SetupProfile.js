import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TextInput,
  Alert,
  TouchableOpacity,
  Animated,
  Dimensions,
  Keyboard,
  Pressable,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useNavigation, useRoute } from "@react-navigation/native";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import * as ImagePicker from "expo-image-picker";
import {
  collection,
  collectionGroup,
  query,
  where,
  onSnapshot,
} from "@react-native-firebase/firestore";
import { MaterialIcons, Entypo, Feather } from "@expo/vector-icons";
import { auth, db } from "../config/firebase";
import { red } from "./colors";
import { EN, CZ } from "./../assets/strings";
import * as Haptics from "expo-haptics";

const { width } = Dimensions.get("window");

// Keep the full array of profile color options
const PROFILE_COLORS = [
  "#f7f465",
  "#ffd6a5",
  "#9bf6ff",
  "#a0c4ff",
  "#bdb2ff",
  "#f6e58d",
  "#badc58",
  "#7ed6df",
  "#3498db",
  "#2ecc71",
  "#f1c40f",
  "#16a085",
  "#2980b9",
  "#f8c291",
  "#6a89cc",
  "#82ccdd",
  "#60a3bc",
  "#BDC581",
  "#D6A2E8",
  "#6A89CC",
  "#FA983A",
  "#B8E994",
  "#78E08F",
  "#38ADA9",
  "#079992",
  "#A8D1FF",
  "#CFC9FF",
  "#CAE892",
  "#9CE8E0",
  "#A1A6FF",
  "#79B6FF",
  "#A3F0B1",
  "#FFDD57",
  "#FFC674",
  "#6FFFB0",
  "#8FF0AA",
  "#88C0FF",
  "#FFE775",
  "#FFDAB1",
  "#90B5FF",
  "#8EA2FF",
  "#9CDEEB",
  "#85B5C9",
  "#FFD47F",
  "#D2E89C",
  "#9DEAB5",
  "#71CEC1",
  "#62BFBF",
  "#B0E0E6",
  "#ADD8E6",
  "#87CEFA",
  "#00BFFF",
  "#AFEEEE",
  "#7FFFD4",
  "#40E0D0",
  "#F0E68C",
  "#D8BFD8",
  "#DDA0DD",
  "#FFB8D1",
  "#D9B3FF",
  "#FFABAB",
  "#B8B3FF",
  "#D9E2FF",
  "#B0E0A8",
  "#FFB482",
  "#FFD9B0",
  "#B5D8EB",
  "#B5DEFF",
  "#D1BEFF",
  "#DCE4EF",
  "#DAC3E8",
  "#B5CCE7",
  "#FFA8A8",
  "#D1E8E2",
];

const LanguageToggle = ({ language, setLanguage }) => {
  return (
    <View style={styles.languageToggleContainer}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          if (language === "CZ") return;
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setLanguage("CZ");
        }}
        style={[
          styles.languageButton,
          language === "CZ" && styles.languageButtonActive,
        ]}
      >
        <Text
          style={[
            styles.languageText,
            language === "CZ" && styles.languageTextActive,
          ]}
        >
          CZ
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => {
          if (language === "EN") return;
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          setLanguage("EN");
        }}
        style={[
          styles.languageButton,
          language === "EN" && styles.languageButtonActive,
        ]}
      >
        <Text
          style={[
            styles.languageText,
            language === "EN" && styles.languageTextActive,
          ]}
        >
          EN
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const ProfileAvatar = ({
  name,
  profileImage,
  size,
  profileColor,
  onChangeColor,
}) => {
  return (
    <View style={styles.avatarContainer}>
      <View
        style={[
          styles.avatar,
          {
            backgroundColor: profileImage ? "transparent" : profileColor,
            width: size,
            height: size,
            borderRadius: size / 2,
          },
        ]}
      >
        {profileImage ? (
          <Image
            source={{ uri: profileImage }}
            style={{ width: size, height: size, borderRadius: size / 2 }}
          />
        ) : (
          <Text style={[styles.avatarText, { fontSize: size / 2.5 }]}>
            {name ? name.charAt(0).toUpperCase() : ""}
          </Text>
        )}
      </View>

      {!profileImage && (
        <TouchableOpacity
          style={styles.changeColorButton}
          onPress={onChangeColor}
        >
          <Feather name="refresh-cw" size={16} color="white" />
          <Text style={styles.changeColorText}>Change Color</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default function SetupProfile() {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const [name, setName] = useState("");
  const [profileImage, setProfileImage] = useState(null);
  const [date, setDate] = useState(new Date());
  const [dateChanged, setDateChanged] = useState(false);
  const [description, setDescription] = useState("");
  const [profileColor, setProfileColor] = useState(PROFILE_COLORS[0]);
  const [languageCode, setLanguageCode] = useState("CZ");
  const [languageStrings, setLanguageStrings] = useState(CZ);
  const [nameFree, setNameFree] = useState(null);
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);

  const scrollViewRef = useRef(null);

  // Listen for keyboard events
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  // Set random initial profile color
  useEffect(() => {
    const randomColor =
      PROFILE_COLORS[Math.floor(Math.random() * PROFILE_COLORS.length)];
    setProfileColor(randomColor);
  }, []);

  // Update language strings when language code changes
  useEffect(() => {
    setLanguageStrings(languageCode === "CZ" ? CZ : EN);
  }, [languageCode]);

  // Check if username is available
  useEffect(() => {
    if (!name || name.trim() === "") return;

    const q = query(
      collectionGroup(db, "public"),
      where("name", "==", name.trim())
    );
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      if (
        querySnapshot.empty ||
        querySnapshot?.docs[0]?.data()?.uid === auth.currentUser.uid
      ) {
        setNameFree(true);
      } else {
        setNameFree(false);
      }
    });

    return () => unsubscribe();
  }, [name]);

  // Date picker functions
  const showDatePicker = () => setDatePickerVisibility(true);
  const hideDatePicker = () => setDatePickerVisibility(false);
  const handleConfirm = (selectedDate) => {
    setDate(selectedDate);
    setDateChanged(true);
    hideDatePicker();
  };

  // Calculate age from date
  const calculateAge = (dob) => {
    const diff_ms = Date.now() - dob.getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  };

  // Image picker functions
  const pickImage = async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          languageStrings.permissionRequired,
          languageStrings.mediaPermissionMessage
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.log("Error picking image:", error);
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          languageStrings.permissionRequired,
          languageStrings.cameraPermissionMessage
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.log("Error taking photo:", error);
    }
  };

  const changeProfileColor = () => {
    const currentIndex = PROFILE_COLORS.indexOf(profileColor);
    const nextIndex = (currentIndex + 1) % PROFILE_COLORS.length;
    setProfileColor(PROFILE_COLORS[nextIndex]);
  };

  const handleContinue = () => {
    if (!nameFree) {
      Alert.alert(languageStrings.error, languageStrings.usernameTaken);
      return;
    }

    if (name.trim() === "") {
      Alert.alert(languageStrings.error, languageStrings.noUsername);
      return;
    }

    if (!dateChanged) {
      Alert.alert(languageStrings.error, languageStrings.noDate);
      return;
    }

    if (calculateAge(date) < 15) {
      Alert.alert(languageStrings.error, languageStrings.lowAge);
      return;
    }

    navigation.navigate("ChooseCoords", {
      name: name.trim(),
      date: date,
      description: description?.trim() || null,
      profileImage: profileImage,
      profileColor: profileColor,
      language: languageStrings,
      language2: languageCode,
    });
  };

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Header with Language Toggle */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>{languageStrings.setupProfile}</Text>
          <LanguageToggle
            language={languageCode}
            setLanguage={setLanguageCode}
          />
        </View>

        {/* Profile Avatar Section */}
        <ProfileAvatar
          name={name}
          profileImage={profileImage}
          size={width * 0.35}
          profileColor={profileColor}
          onChangeColor={changeProfileColor}
        />

        {/* Photo Options */}
        <View style={styles.photoOptionsContainer}>
          <TouchableOpacity
            style={styles.photoOptionButton}
            onPress={pickImage}
          >
            <MaterialIcons name="photo-library" size={20} color={red} />
            <Text style={styles.photoOptionText}>
              {languageStrings.chooseImage}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.photoOptionButton}
            onPress={takePhoto}
          >
            <Entypo name="camera" size={20} color={red} />
            <Text style={styles.photoOptionText}>
              {languageStrings.takeImage}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Profile Information Form */}
        <View style={styles.formContainer}>
          {/* Name Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.name}</Text>
            <TextInput
              style={[
                styles.textInput,
                !nameFree && name.trim() !== "" && styles.inputError,
              ]}
              value={name}
              onChangeText={(text) => {
                if (text.length <= 18) {
                  setName(text);
                } else {
                  Alert.alert(
                    languageStrings.error,
                    languageStrings.nameTooLong
                  );
                }
              }}
              placeholder={languageStrings.enterYourName}
              placeholderTextColor="rgba(0,0,0,0.3)"
              maxLength={18}
            />
            {!nameFree && name.trim() !== "" && (
              <Text style={styles.errorText}>
                {languageStrings.usernameTaken}
              </Text>
            )}
          </View>
          {/* Date of Birth */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.dateOfBirth}</Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={showDatePicker}
            >
              <Text style={styles.dateText}>
                {date.toLocaleDateString(languageStrings.dateLocale, {
                  day: "numeric",
                  month: "long",
                  year: "numeric",
                })}
              </Text>
              <MaterialIcons name="date-range" size={20} color={red} />
            </TouchableOpacity>
            <DateTimePickerModal
              locale={languageStrings.dateLocale}
              isVisible={isDatePickerVisible}
              mode="date"
              onConfirm={handleConfirm}
              onCancel={hideDatePicker}
              is24Hour={true}
              maximumDate={new Date()}
            />
            {dateChanged && calculateAge(date) < 15 && (
              <Text style={styles.errorText}>
                {languageStrings.ageRestriction}
              </Text>
            )}
          </View>
          {/* Description
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.aboutYou}</Text>
            <Text style={styles.inputHelper}>
              {languageStrings.userInfoGuide}
            </Text>
            <TextInput
              style={styles.textAreaInput}
              placeholder={languageStrings.descriptionPlaceholder}
              placeholderTextColor="rgba(0,0,0,0.3)"
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              value={description}
              onChangeText={setDescription}
              maxLength={150}
            />
            <Text style={styles.characterCount}>{description.length}/150</Text>
          </View> */}
        </View>
      </KeyboardAwareScrollView>

      {/* Continue Button */}
      {/* <View
        style={[
          styles.buttonContainer,
          { paddingBottom: insets.bottom * 0.7 + 10 },
          isKeyboardVisible && styles.buttonContainerKeyboard,
        ]}
      > */}
      <TouchableOpacity
        style={[
          styles.continueButton,
          { marginBottom: insets.bottom * 0.7 + 10 },
        ]}
        onPress={handleContinue}
        activeOpacity={0.8}
      >
        <Text style={styles.continueButtonText}>
          {languageStrings.continue}
        </Text>
        <MaterialIcons
          name="arrow-forward"
          size={20}
          color="white"
          style={styles.continueButtonIcon}
        />
      </TouchableOpacity>
      {/* </View> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 10,
    // borderBottomWidth: 1,
    // borderBottomColor: "rgba(0,0,0,0.1)",
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#333",
    // maxWidth: "70%",
  },
  languageToggleContainer: {
    flexDirection: "row",
    backgroundColor: "rgba(0,0,0,0.05)",
    borderRadius: 25,
    padding: 3,
    width: 100,
    height: 36,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  languageButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 22,
  },
  languageButtonActive: {
    backgroundColor: red,
    shadowColor: red,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  languageText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#888",
  },
  languageTextActive: {
    color: "white",
  },
  avatarContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 25,
  },
  avatar: {
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },
  avatarText: {
    color: "white",
    fontWeight: "bold",
  },
  changeColorButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginTop: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  changeColorText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 6,
  },
  photoOptionsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
    marginBottom: 10,
  },
  photoOptionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.02)",
    borderRadius: 25,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 6,
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.06)",
  },
  photoOptionText: {
    color: red,
    fontWeight: "600",
    marginLeft: 6,
    fontSize: 13,
  },
  formContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  inputHelper: {
    fontSize: 14,
    color: "#999",
    marginBottom: 8,
    lineHeight: 20,
  },
  textInput: {
    height: 48,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  inputError: {
    borderColor: "#e74c3c",
    backgroundColor: "rgba(231, 76, 60, 0.03)",
  },
  textAreaInput: {
    height: 100,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingTop: 12,
    paddingBottom: 12,
    fontSize: 16,
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  datePickerButton: {
    height: 48,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  dateText: {
    fontSize: 16,
    color: "#333",
  },
  characterCount: {
    alignSelf: "flex-end",
    marginTop: 5,
    fontSize: 12,
    color: "#aaa",
  },
  errorText: {
    color: "#e74c3c",
    fontSize: 14,
    marginTop: 5,
  },
  buttonContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "white",
    padding: 20,
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.1)",
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 8,
  },
  buttonContainerKeyboard: {
    position: "relative",
    marginTop: 20,
    borderTopWidth: 0,
    shadowOpacity: 0,
    elevation: 0,
  },
  continueButton: {
    backgroundColor: red,
    borderRadius: 20,
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    shadowColor: red,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
    // paddingHorizontal: 20,
    // marginHorizontal: 15,
    width: "90%",
    alignSelf: "center",
  },
  continueButtonText: {
    color: "white",
    fontSize: 17,
    fontWeight: "700",
  },
  continueButtonIcon: {
    marginLeft: 8,
  },
});
