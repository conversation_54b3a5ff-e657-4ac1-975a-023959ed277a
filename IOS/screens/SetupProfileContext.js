// SetupProfileContext.js
import React, { createContext, useState, useContext } from "react";
import { PROFILE_COLORS } from "../constants";

const SetupProfileContext = createContext();

export const useSetupProfile = () => useContext(SetupProfileContext);

export const SetupProfileProvider = ({ children }) => {
  const [name, setName] = useState("");
  const [profileImage, setProfileImage] = useState(null);
  const [gender, setGender] = useState(null); // "male" or "female"
  const [date, setDate] = useState(new Date());
  const [dateChanged, setDateChanged] = useState(false);
  const [description, setDescription] = useState("");
  const [profileColor, setProfileColor] = useState(
    PROFILE_COLORS[Math.floor(Math.random() * PROFILE_COLORS.length)]
  );
  const [nameFree, setNameFree] = useState(null);
  const [languageCode, setLanguageCode] = useState("CZ");
  const [currentStep, setCurrentStep] = useState(0);
  const [skillLevel, setSkillLevel] = useState(null);
  const [basketballDate, setBasketballDate] = useState(new Date());
  const [basketballDateChanged, setBasketballDateChanged] = useState(false);
  const [height, setHeight] = useState("");
  const [weight, setWeight] = useState("");

  return (
    <SetupProfileContext.Provider
      value={{
        name,
        setName,
        profileImage,
        setProfileImage,
        gender,
        setGender,
        date,
        setDate,
        dateChanged,
        setDateChanged,
        description,
        setDescription,
        profileColor,
        setProfileColor,
        nameFree,
        setNameFree,
        languageCode,
        setLanguageCode,
        currentStep,
        setCurrentStep,
        skillLevel,
        setSkillLevel,
        basketballDate,
        setBasketballDate,
        basketballDateChanged,
        setBasketballDateChanged,
        height,
        setHeight,
        weight,
        setWeight,
      }}
    >
      {children}
    </SetupProfileContext.Provider>
  );
};
