import React, { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  Image,
  SafeAreaView,
  TouchableOpacity,
  Animated,
  KeyboardAvoidingView,
  Dimensions,
  Pressable,
  TouchableWithoutFeedback,
  Keyboard,
  Modal,
  Linking,
  Platform,
  Alert,
} from "react-native";
import {
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithCredential,
  sendPasswordResetEmail,
  OAuthProvider,
} from "@react-native-firebase/auth";
import { red } from "./colors";
import {
  doc,
  getDoc,
  collection,
  getDocs,
} from "@react-native-firebase/firestore";
import { auth, db } from "../config/firebase";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";

import * as Google from "expo-auth-session/providers/google";
import * as AppleAuthentication from "expo-apple-authentication";
import FastImage from "react-native-fast-image";

export default function Login({ navigation }) {
  const screenWidth = Dimensions.get("window").width;
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [urls, setUrls] = useState();
  const [showEmail, setShowEmail] = useState(false);
  const [signUpType, setSignUpType] = useState(null);
  const [emailReset, setEmailReset] = useState("");

  // Modals state
  const [isModalVisible2, setModalVisible2] = useState(false);
  const [isModalVisible3, setModalVisible3] = useState(false);

  // Animation references
  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;
  const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const fetchData = async () => {
      const infoCollection = collection(db, "info");
      const infoSnapshot = await getDocs(infoCollection);
      infoSnapshot.docs.forEach((doc) => {
        setUrls(doc.data());
      });
    };

    fetchData();
  }, []);

  const checkIfUserExists = async () => {
    const docRef = doc(db, "users/", auth.currentUser.uid);
    const docSnap = await getDoc(docRef);
    return docSnap.exists;
  };

  const onHandleLogin = async () => {
    try {
      if (email !== "" && password !== "") {
        await signInWithEmailAndPassword(auth, email, password).catch((err) =>
          Alert.alert("Login error", err.message)
        );
      }
    } catch (error) {
      Alert.alert("Error", error.message);
    }
  };

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId:
      "99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n.apps.googleusercontent.com",
  });

  useEffect(() => {
    if (response?.type === "success") {
      const { id_token } = response.params;
      const credential = GoogleAuthProvider.credential(id_token);

      signInWithCredential(auth, credential).catch((error) => {
        Alert.alert("Error signing in with Google", error.message);
      });
    }
  }, [response]);

  const onResetPassword = () => {
    sendPasswordResetEmail(auth, emailReset)
      .then(() => {
        Alert.alert("Success", "Password reset email sent to " + emailReset);
      })
      .catch((error) => {
        Alert.alert("Error", error.message);
      });
  };

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const toggleModal3 = () => {
    if (isModalVisible3) {
      Animated.timing(modalAnimatedValue3, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible3(false));
    } else {
      setModalVisible3(true);
      Animated.timing(modalAnimatedValue3, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const handleAppleSignIn = async () => {
    try {
      const appleAuthCredential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      const provider = new OAuthProvider("apple.com");
      const credential = provider.credential({
        idToken: appleAuthCredential.identityToken,
        rawNonce: appleAuthCredential.nonce,
      });

      await signInWithCredential(auth, credential).catch((error) => {
        Alert.alert("Error signing in with Apple:", error.message);
      });
    } catch (e) {
      if (e.code === "ERR_CANCELED") {
        // User canceled the sign-in flow
      } else {
        Alert.alert(
          "Error",
          "An error occurred during Apple Sign In: " + e.message
        );
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
        {/* Terms and Conditions Modal */}
        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible3}
          onRequestClose={toggleModal3}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal3}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue3,
              }}
            >
              <TouchableWithoutFeedback>
                <View style={styles.modalContainer}>
                  <View style={styles.modalContent}>
                    <Text style={styles.modalText}>
                      Please read the full
                      <Text
                        style={styles.linkText}
                        onPress={() =>
                          Linking.openURL(`${urls?.termsAndConditions}`)
                        }
                      >
                        {" "}
                        Terms and Conditions
                      </Text>
                      ,{" "}
                      <Text
                        style={styles.linkText}
                        onPress={() =>
                          Linking.openURL(`${urls?.privacyPolicy}`)
                        }
                      >
                        {" "}
                        Privacy Policy
                      </Text>{" "}
                      and{" "}
                      <Text
                        style={styles.linkText}
                        onPress={() =>
                          Linking.openURL(`${urls?.copyrightPolicy}`)
                        }
                      >
                        {" "}
                        Copyright Policy
                      </Text>{" "}
                      before proceeding.
                    </Text>
                  </View>

                  <View style={styles.modalButtonContainer}>
                    <Pressable
                      onPress={() => {
                        toggleModal3();
                        setTimeout(() => {
                          if (signUpType === "google") {
                            promptAsync().catch((error) => {
                              Alert.alert(
                                "Error",
                                "Google auth error: " + error.message
                              );
                            });
                          }

                          if (signUpType === "apple") {
                            handleAppleSignIn();
                          }
                        }, 200);
                      }}
                      style={({ pressed }) => [
                        styles.modalAgreeButton,
                        { transform: [{ scale: pressed ? 0.97 : 1 }] },
                      ]}
                    >
                      <Text style={styles.modalAgreeButtonText}>
                        I Understand and Agree
                      </Text>
                    </Pressable>

                    <Pressable
                      onPress={toggleModal3}
                      style={({ pressed }) => [
                        styles.modalCancelButton,
                        { transform: [{ scale: pressed ? 0.97 : 1 }] },
                      ]}
                    >
                      <Text style={styles.modalCancelButtonText}>Cancel</Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        {/* Password Reset Modal */}
        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View style={styles.passwordResetContainer}>
                  <View style={styles.passwordResetHeader}>
                    <Text style={styles.passwordResetTitle}>
                      Reset your password
                    </Text>

                    <Text style={styles.passwordResetInstructions}>
                      Enter your email that is associated with your Let's Hoop
                      account
                    </Text>
                  </View>

                  <TextInput
                    style={styles.passwordResetInput}
                    value={emailReset}
                    onChangeText={setEmailReset}
                    placeholder="Email"
                    keyboardType="email-address"
                  />

                  <View style={styles.passwordResetButtonContainer}>
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(() => {
                          onResetPassword();
                        }, 200);
                      }}
                      style={({ pressed }) => [
                        styles.passwordResetButton,
                        { transform: [{ scale: pressed ? 0.97 : 1 }] },
                      ]}
                    >
                      <Text style={styles.passwordResetButtonText}>
                        Send email
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "position" : "height"}
          keyboardVerticalOffset={-150}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.container}>
            {showEmail ? (
              <Text
                style={styles.contactEmail}
                onPress={() => setShowEmail(false)}
              >
                Contact <NAME_EMAIL>
              </Text>
            ) : (
              <Pressable
                style={styles.helpButton}
                onPress={() => setShowEmail(true)}
              >
                <AntDesign
                  name="questioncircleo"
                  size={24}
                  color="rgba(0,0,0,0.5)"
                />
              </Pressable>
            )}

            <View style={styles.logoContainer}>
              <FastImage
                source={require("./../images/streetball2e.jpg")}
                style={styles.logo}
              />
            </View>

            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <MaterialIcons
                  name="alternate-email"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View style={styles.inputWrapper}>
                  <TextInput
                    style={styles.input}
                    placeholder="Email"
                    placeholderTextColor="rgba(0, 0, 0, 0.4)"
                    autoCapitalize="none"
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoFocus={false}
                    value={email}
                    onChangeText={setEmail}
                  />
                </View>
              </View>

              <View style={styles.inputContainer}>
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View style={styles.inputWrapper}>
                  <TextInput
                    style={styles.input}
                    placeholder="Password"
                    placeholderTextColor="rgba(0, 0, 0, 0.4)"
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password}
                    onChangeText={setPassword}
                  />
                </View>
              </View>

              <View style={styles.forgotPasswordContainer}>
                <TouchableOpacity
                  style={styles.forgotPasswordButton}
                  onPress={toggleModal2}
                >
                  <Text style={styles.forgotPasswordText}>
                    Forgot password?
                  </Text>
                </TouchableOpacity>
              </View>

              <Pressable
                onPress={onHandleLogin}
                style={({ pressed }) => [
                  styles.loginButton,
                  { transform: [{ scale: pressed ? 0.99 : 1 }] },
                ]}
              >
                <Text style={styles.loginButtonText}>Log in</Text>
              </Pressable>

              <View style={styles.dividerContainer}>
                <View style={styles.divider}></View>
                <Text style={styles.dividerText}>OR</Text>
                <View style={styles.divider}></View>
              </View>

              <View style={styles.socialButtonsContainer}>
                <Pressable
                  onPress={() => {
                    setSignUpType("google");
                    toggleModal3();
                  }}
                  style={({ pressed }) => [
                    styles.googleButton,
                    { transform: [{ scale: pressed ? 0.99 : 1 }] },
                  ]}
                >
                  <Text style={styles.googleButtonText}>Google</Text>
                  <Image
                    style={styles.googleIcon}
                    source={require("../images/google.png")}
                  />
                </Pressable>

                <AppleAuthentication.AppleAuthenticationButton
                  buttonStyle={
                    AppleAuthentication.AppleAuthenticationButtonStyle
                      .WHITE_OUTLINE
                  }
                  buttonType={
                    AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN
                  }
                  cornerRadius={5}
                  style={styles.appleButton}
                  onPress={() => {
                    setSignUpType("apple");
                    toggleModal3();
                  }}
                />
              </View>

              <View style={styles.signupContainer}>
                <Text style={styles.signupText}>
                  Don't have an account yet?{"  "}
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("Signup")}>
                  <Text style={styles.signupLink}>Sign up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    width: "100%",
    backgroundColor: "transparent",
    alignSelf: "center",
  },
  container: {
    width: "100%",
    height: "100%",
    alignItems: "center",
  },
  contactEmail: {
    color: "rgba(0,0,0,1)",
    zIndex: 100,
    fontSize: 16,
  },
  helpButton: {
    top: 15,
    left: 20,
    position: "absolute",
    padding: 3,
    zIndex: 100,
  },
  logoContainer: {
    alignSelf: "center",
    width: "100%",
    height: "50%",
    marginTop: "10%",
  },
  logo: {
    height: Dimensions.get("window").width * 0.55,
    width: Dimensions.get("window").width * 0.55,
    alignSelf: "center",
  },
  formContainer: {
    position: "absolute",
    width: "100%",
    height: 300,
    bottom: 40,
    alignSelf: "center",
    justifyContent: "center",
  },
  inputContainer: {
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    width: "88%",
    marginBottom: 5,
  },
  inputWrapper: {
    flex: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
    borderBottomWidth: 1,
    marginLeft: 15,
  },
  input: {
    height: 40,
  },
  forgotPasswordContainer: {
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 10,
    width: "88%",
    justifyContent: "flex-end",
  },
  forgotPasswordButton: {
    backgroundColor: "white",
    padding: 4,
    paddingHorizontal: 10,
  },
  forgotPasswordText: {
    fontSize: 13,
    color: "rgba(0,0,0,0.5)",
  },
  loginButton: {
    marginTop: 25,
    width: "90%",
    alignItems: "center",
    backgroundColor: red,
    borderRadius: 10,
    height: 46,
    justifyContent: "center",
    alignSelf: "center",
  },
  loginButtonText: {
    fontWeight: "700",
    color: "white",
    fontSize: 16,
    marginTop: 2,
  },
  dividerContainer: {
    marginTop: 13,
    alignSelf: "center",
    width: "92%",
    flexDirection: "row",
    justifyContent: "space-around",
  },
  divider: {
    marginTop: 0,
    alignSelf: "center",
    width: "40%",
    height: 1,
    backgroundColor: "rgba(0,0,0,0.1)",
  },
  dividerText: {
    fontWeight: "400",
    fontSize: 13,
    color: "rgba(0,0,0,0.5)",
  },
  socialButtonsContainer: {
    flexDirection: "row",
    marginTop: 13,
    width: "90%",
    justifyContent: "space-between",
    alignSelf: "center",
  },
  googleButton: {
    width: "46%",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.04)",
    borderRadius: 10,
    height: 47,
    justifyContent: "center",
    alignSelf: "center",
  },
  googleButtonText: {
    fontWeight: "600",
    color: "rgba(0,0,0,0.7)",
    fontSize: 14,
    marginTop: 2,
    left: 10,
  },
  googleIcon: {
    height: 27,
    width: 27,
    position: "absolute",
    left: 15,
  },
  appleButton: {
    width: "46%",
    height: 46,
  },
  signupContainer: {
    marginTop: 30,
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "center",
  },
  signupText: {
    color: "gray",
    fontWeight: "600",
    fontSize: 14,
  },
  signupLink: {
    color: red,
    fontWeight: "600",
    fontSize: 14,
  },
  // Modal styles
  modalContainer: {
    width: "80%",
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
  },
  modalContent: {
    width: "100%",
    alignSelf: "center",
    justifyContent: "center",
    marginTop: "0%",
  },
  modalText: {
    textAlign: "center",
    fontWeight: "400",
    fontSize: 15,
    color: "rgba(0,0,0,0.8)",
    marginTop: 15,
    lineHeight: 21,
  },
  linkText: {
    color: "#2a99fa",
  },
  modalButtonContainer: {
    width: "95%",
    alignSelf: "center",
    marginTop: "9%",
    marginBottom: "3%",
  },
  modalAgreeButton: {
    borderRadius: 8,
    height: 40,
    backgroundColor: red,
    alignSelf: "center",
    justifyContent: "center",
  },
  modalAgreeButtonText: {
    fontWeight: "500",
    alignSelf: "center",
    color: "white",
    fontSize: 14,
    paddingHorizontal: 15,
  },
  modalCancelButton: {
    borderRadius: 8,
    height: 37,
    backgroundColor: "rgba(230,230,230,1)",
    alignSelf: "center",
    justifyContent: "center",
    marginTop: 18,
  },
  modalCancelButtonText: {
    fontWeight: "500",
    alignSelf: "center",
    color: "rgba(0,0,0,0.7)",
    fontSize: 14,
    paddingHorizontal: 20,
  },
  // Password reset modal
  passwordResetContainer: {
    width: "90%",
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
  },
  passwordResetHeader: {
    width: "90%",
    alignSelf: "center",
    justifyContent: "center",
    marginTop: "3%",
  },
  passwordResetTitle: {
    textAlign: "center",
    fontWeight: "500",
    fontSize: 16,
  },
  passwordResetInstructions: {
    textAlign: "center",
    fontWeight: "400",
    fontSize: 14,
    marginTop: 15,
  },
  passwordResetInput: {
    alignSelf: "center",
    width: "95%",
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.2)",
    borderRadius: 7,
    paddingHorizontal: 15,
    paddingTop: 12,
    paddingBottom: 13,
    marginTop: "7%",
  },
  passwordResetButtonContainer: {
    width: "95%",
    alignSelf: "center",
    marginTop: "9%",
    marginBottom: 11,
  },
  passwordResetButton: {
    borderRadius: 8,
    width: "70%",
    height: 40,
    backgroundColor: red,
    alignSelf: "center",
    justifyContent: "center",
  },
  passwordResetButtonText: {
    fontWeight: "600",
    alignSelf: "center",
    color: "white",
    fontSize: 15,
  },
});
