// SetupProfileNavigator.js
import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  Alert,
  TouchableWithoutFeedback,
} from "react-native";
import { createStackNavigator } from "@react-navigation/stack";
import { SetupProfileProvider, useSetupProfile } from "./SetupProfileContext";
import SetupProfileScreen1 from "./SetupProfileScreen1";
import SetupProfileScreen2 from "./SetupProfileScreen2";
import SetupHeader from "../components/SetupHeader";
import StepIndicator from "../components/StepIndicator";
import NavigationButton from "../components/NavigationButton";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { EN, CZ } from "./../assets/strings";
import {
  getDownloadURL,
  ref,
  uploadBytesResumable,
} from "@react-native-firebase/storage";
import * as ImageManipulator from "expo-image-manipulator";
import { auth, storage, functions } from "../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";
import { useToast } from "../hooks/toast/useToast";

const Stack = createStackNavigator();
const createUserProfileV2 = httpsCallable(functions, "createUserProfileV2");

const NavigatorContent = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const showToast = useToast();
  const {
    currentStep,
    name,
    nameFree,
    gender,
    date,
    languageCode,
    skillLevel,
    description,
    height,
    weight,
    basketballDate,
    basketballDateChanged,
    profileImage,
    profileColor,
  } = useSetupProfile();

  const languageStrings = languageCode === "CZ" ? CZ : EN;
  const [isUploading, setIsUploading] = useState(false);

  // Calculate age from date
  const calculateAge = (dob) => {
    const diff_ms = Date.now() - dob.getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  };

  // Function to check if the current step is valid
  const isCurrentStepValid = () => {
    switch (currentStep) {
      case 0:
        // Validate profile basics
        return (
          name.trim() !== "" &&
          nameFree === true &&
          gender !== null &&
          calculateAge(date) >= 15
        );
      case 1:
        // Basketball info is optional, so always valid
        return true;
      default:
        return false;
    }
  };

  // Function to check if the user has changed basketball info
  const hasBasketballInfoChanged = () => {
    return (
      skillLevel !== null ||
      description.trim() !== "" ||
      height !== "" ||
      weight !== "" ||
      basketballDateChanged
    );
  };

  // Upload profile images and create user profile
  const createUserProfile = async () => {
    setIsUploading(true);

    // Show loading toast
    const toastId = showToast.loading(
      languageStrings.creatingProfile || "Creating your profile..."
    );

    try {
      let profileImageRef = null;
      let profileImageRefSmall = null;
      let profileImagePath = null;
      let profileImagePathSmall = null;
      // Upload profile images if provided
      if (profileImage) {
        // Full-size profile image
        profileImagePath = `/users-profile-images/${auth.currentUser.uid}/profile-image`;
        const storageRef = ref(storage, profileImagePath);
        const compressedImage = await ImageManipulator.manipulateAsync(
          profileImage,
          [{ resize: { width: 600, height: 600 } }],
          { format: "jpeg" }
        );
        const img = await fetch(compressedImage.uri);
        const bytes = await img.blob();
        await uploadBytesResumable(storageRef, bytes);
        profileImageRef = await getDownloadURL(storageRef);
        // Small profile image for thumbnails
        profileImagePathSmall = `/users-profile-images/${auth.currentUser.uid}/profile-image-small`;
        const storageRefSmall = ref(storage, profileImagePathSmall);
        const compressedImageSmall = await ImageManipulator.manipulateAsync(
          profileImage,
          [{ resize: { width: 300, height: 300 } }],
          { format: "jpeg" }
        );
        const imgSmall = await fetch(compressedImageSmall.uri);
        const bytesSmall = await imgSmall.blob();
        await uploadBytesResumable(storageRefSmall, bytesSmall);
        profileImageRefSmall = await getDownloadURL(storageRefSmall);
      }
      // Prepare user data for cloud function
      const userData = {
        name,
        gender,
        date: date.getTime(), // Convert to milliseconds timestamp
        description,
        skillLevel,
        profileColor,
        languageCode,
        profileImageRef,
        profileImageRefSmall,
        profileImagePath,
        profileImagePathSmall,
        height,
        weight,
        basketballDate: basketballDate.getTime(), // Convert to milliseconds timestamp
      };
      // Call cloud function to create user profile
      const result = await createUserProfileV2(userData);
      if (result.data.success) {
        showToast.update(
          toastId,
          languageStrings.done || "Profile created successfully!",
          "success"
        );
        // Navigate to main app after short delay
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{ name: "MainTabs" }],
          });
        }, 500);
        return true;
      } else {
        throw new Error(result.data.message || "Failed to create profile");
      }
    } catch (error) {
      console.error("Error creating user profile:", error);

      showToast.update(
        toastId,
        error.message || languageStrings.errorCreatingProfile,
        "error"
      );

      setIsUploading(false);
      return false;
    }
  };

  const handleContinue = async () => {
    if (!isCurrentStepValid()) {
      // Show appropriate validation message
      if (currentStep === 0) {
        if (name.trim() === "") {
          Alert.alert(languageStrings.error, languageStrings.noUsername);
          return;
        }
        if (!nameFree) {
          Alert.alert(languageStrings.error, languageStrings.usernameTaken);
          return;
        }
        if (!gender) {
          Alert.alert(languageStrings.error, languageStrings.selectGender);
          return;
        }
        if (calculateAge(date) < 15) {
          Alert.alert(languageStrings.error, languageStrings.ageRestriction);
          return;
        }
      }

      return;
    }

    // Navigation logic
    switch (currentStep) {
      case 0:
        navigation.navigate("SetupProfileScreen2");
        break;
      case 1:
        // Final submission - create user profile
        if (!isUploading) {
          await createUserProfile();
        }
        break;
    }
  };

  const handleBack = () => {
    if (currentStep === 1) {
      navigation.navigate("SetupProfileScreen1");
    }
  };

  // Get the continue button label
  const getContinueButtonLabel = () => {
    if (currentStep === 1) {
      if (!hasBasketballInfoChanged()) {
        return languageStrings.skip;
      }
      return languageStrings.finish;
    }
    return languageStrings.continue;
  };

  return (
    <View style={styles.container}>
      <SetupHeader />

      <View style={styles.contentContainer}>
        <Stack.Navigator
          initialRouteName="SetupProfileScreen1"
          screenOptions={{
            headerShown: false,
            gestureEnabled: false,
            cardStyle: { backgroundColor: "white" },
            cardStyleInterpolator: ({ current, layouts }) => {
              return {
                cardStyle: {
                  transform: [
                    {
                      translateX: current.progress.interpolate({
                        inputRange: [0, 1],
                        outputRange: [layouts.screen.width, 0],
                      }),
                    },
                  ],
                },
              };
            },
          }}
        >
          <Stack.Screen
            name="SetupProfileScreen1"
            component={SetupProfileScreen1}
          />
          <Stack.Screen
            name="SetupProfileScreen2"
            component={SetupProfileScreen2}
          />
        </Stack.Navigator>
      </View>

      <View style={styles.bottomContainer}>
        <NavigationButton
          onPress={handleContinue}
          disabled={!isCurrentStepValid()}
          label={getContinueButtonLabel()}
          showBackButton={currentStep > 0}
          onBackPress={handleBack}
          isLoading={isUploading}
        />
        <StepIndicator currentStep={currentStep} totalSteps={2} />
      </View>

      {/* Overlay to block touches when uploading */}
      {isUploading && (
        <TouchableWithoutFeedback>
          <View style={styles.touchBlocker} />
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

const SetupProfileNavigator = ({ navigation }) => {
  return (
    <SetupProfileProvider>
      <NavigatorContent navigation={navigation} />
    </SetupProfileProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  contentContainer: {
    flex: 1,
  },
  bottomContainer: {
    paddingTop: 15,
    paddingBottom: 25,
    backgroundColor: "white",
  },
  touchBlocker: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "transparent",
  },
});

export default SetupProfileNavigator;
