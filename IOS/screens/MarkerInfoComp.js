import React, { useEffect, useState, useRef } from "react";
import { View, Text, Image, ActivityIndicator, Animated, Pressable } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import { MaterialIcons, AntDesign } from "@expo/vector-icons";
import { red } from "./colors";

const MarkerInfoComp = ({ selectedMarker, setSelectedMarker, language }) => {
  const navigation = useNavigation();
  const user = useSelector((state) => state.socials.userData);
  const images = selectedMarker.imageRefs;
  const isFavourite = user?.favouriteCourts?.includes(selectedMarker.id) || false;

  const [imageLoaded, setImageLoaded] = useState(false);
  const [index, setIndex] = useState(0);
  const [loadingNewImage, setLoadingNewImage] = useState(false);
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const handleImageChange = (direction) => {
    setLoadingNewImage(true);
    setIndex((prevIndex) => (prevIndex + direction + images.length) % images.length);
  };

  return (
    <View style={styles.container}>
      <ActivityIndicator style={styles.centeredIndicator} />
      {loadingNewImage && <ActivityIndicator style={styles.overlayIndicator} color="white" />}

      <Animated.View style={{ opacity }}>
        <Image
          source={{ uri: images[index] }}
          style={styles.image}
          onLoadEnd={() => {
            setImageLoaded(true);
            setLoadingNewImage(false);
          }}
        />
        <AntDesign name="close" size={29} color="white" style={styles.closeIcon} onPress={() => setSelectedMarker(null)} />
        <MaterialIcons name="arrow-forward-ios" size={24} color="white" style={styles.arrowRight} onPress={() => handleImageChange(1)} />
        <MaterialIcons name="arrow-back-ios" size={24} color="white" style={styles.arrowLeft} onPress={() => handleImageChange(-1)} />

        <FavouriteBadge isFavourite={isFavourite} />
        <AddressOverlay address={selectedMarker.address} />

        <ActionButtons
          navigation={navigation}
          selectedMarker={selectedMarker}
          language={language}
        />
      </Animated.View>
    </View>
  );
};

const FavouriteBadge = ({ isFavourite }) => (
  <View style={styles.badgeContainer}>
    <Image
      source={
        isFavourite
          ? require("./../images/qq-star2.png")
          : require("./../images/qq2.png")
      }
      style={styles.badgeImage}
    />
  </View>
);

const AddressOverlay = ({ address }) => (
  <View style={styles.addressOverlay}>
    <Text style={styles.addressText} numberOfLines={1} ellipsizeMode="tail">
      {address}
    </Text>
  </View>
);

const ActionButtons = ({ navigation, selectedMarker, language }) => (
  <View style={styles.actionButtonsContainer}>
    <Pressable
      onPress={() => navigation.navigate("MyGamesStack", selectedMarker)}
      style={({ pressed }) => [
        styles.createGameButton,
        { transform: [{ scale: pressed ? 0.97 : 1 }] },
      ]}
    >
      <Text style={styles.createGameText}>{language.createGame}</Text>
    </Pressable>
    <Pressable
      onPress={() => navigation.navigate("CourtInfo", { selectedMarker, language })}
      style={({ pressed }) => [
        styles.moreInfoButton,
        { transform: [{ scale: pressed ? 0.97 : 1 }] },
      ]}
    >
      <Text style={styles.moreInfoText}>{language.moreCourtInfo}</Text>
    </Pressable>
  </View>
);

const styles = {
  container: {
    width: "100%",
    height: 330,
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 0.6,
    borderColor: "rgba(0,0,0,0.4)",
  },
  centeredIndicator: {
    position: "absolute",
    alignSelf: "center",
    marginTop: 140,
    opacity: 1,
  },
  overlayIndicator: {
    position: "absolute",
    alignSelf: "center",
    marginTop: 115,
    opacity: 0.5,
    zIndex: 10,
  },
  image: {
    width: "100%",
    height: 245,
    borderTopRightRadius: 9,
    borderTopLeftRadius: 9,
  },
  closeIcon: {
    position: "absolute",
    marginTop: 5,
    right: 4,
    opacity: 1,
  },
  arrowRight: {
    position: "absolute",
    marginTop: 115,
    right: 3,
    opacity: 0.8,
  },
  arrowLeft: {
    position: "absolute",
    marginTop: 115,
    left: 13,
    opacity: 0.8,
  },
  badgeContainer: {
    position: "absolute",
    width: 57,
    height: 42,
    backgroundColor: "white",
    bottom: -5,
    borderTopRightRadius: 100,
    overflow: "hidden",
    zIndex: 1,
  },
  badgeImage: {
    width: 49,
    height: 49,
    position: "absolute",
    left: -1,
    bottom: -17,
    overflow: "visible",
    zIndex: 1,
  },
  addressOverlay: {
    width: "100%",
    height: 25,
    backgroundColor: "rgba(0,0,0,0.3)",
    bottom: 0,
    position: "absolute",
  },
  addressText: {
    color: "white",
    marginLeft: 62,
    zIndex: 2,
    fontWeight: "600",
    fontSize: 12,
    marginTop: 5,
    maxWidth: 280,
  },
  actionButtonsContainer: {
    bottom: -30,
    flexDirection: "row",
    justifyContent: "space-between",
    width: "90%",
    alignSelf: "center",
    flex: 1,
  },
  createGameButton: {
    borderRadius: 8,
    width: "60%",
    height: 40,
    backgroundColor: red,
    justifyContent: "center",
    bottom: 6,
  },
  createGameText: {
    fontWeight: "700",
    alignSelf: "center",
    color: "white",
    fontSize: 15,
  },
  moreInfoButton: {
    borderRadius: 8,
    width: "30%",
    height: 40,
    backgroundColor: "rgba(0,0,0,0.04)",
    justifyContent: "center",
    borderColor: "rgba(0,0,0,0.1)",
    borderWidth: 0.5,
    bottom: 6,
  },
  moreInfoText: {
    fontWeight: "600",
    alignSelf: "center",
  },
};

export default MarkerInfoComp;
