import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
} from "react-native";
import { BlurView } from "expo-blur";

const FilterModal = ({ isVisible, onClose, applyFilters, resetFilters }) => {
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  const [skillLevels, setSkillLevels] = useState([]);
  const [teamType, setTeamType] = useState("");
  const [ageGroups, setAgeGroups] = useState([]);
  const [genderPolicy, setGenderPolicy] = useState("");

  const resetFiltersHandler = () => {
    setSkillLevels([]);
    setTeamType("");
    setAgeGroups([]);
    setGenderPolicy("");
    resetFilters(); // Call the resetFilters prop to reset filters in parent
  };

  const teamAttributes = [
    {
      title: "Skill Level",
      options: ["beginner", "intermediate", "advanced", "professional"],
      value: skillLevels,
      setValue: setSkillLevels,
      multiSelect: true,
    },
    {
      title: "Team Type",
      options: ["casual", "competitive"],
      value: teamType,
      setValue: setTeamType,
      multiSelect: false,
    },
    {
      title: "Age Group",
      options: ["youth", "adult", "senior"],
      value: ageGroups,
      setValue: setAgeGroups,
      multiSelect: true,
    },
    {
      title: "Gender Policy",
      options: ["mixed", "menOnly", "womenOnly"],
      value: genderPolicy,
      setValue: setGenderPolicy,
      multiSelect: false,
    },
  ];

  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 150, // Faster animation
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible]);

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 150, // Faster animation
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 0.8,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Ensure callback runs after animation completes
      onClose();
    });
  };

  return isVisible ? (
    <BlurView intensity={50} style={styles.blurView}>
      <Animated.View style={[styles.overlay, { opacity: opacityAnim }]}>
        <Animated.View
          style={[styles.modalContent, { transform: [{ scale: scaleAnim }] }]}
        >
          {teamAttributes.map((attribute) => (
            <View key={attribute.title} style={styles.attributeSection}>
              <Text style={styles.attributeTitle}>{attribute.title}</Text>
              <View style={styles.buttonGroup}>
                {attribute.options.map((option) => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.optionButton,
                      attribute.multiSelect
                        ? attribute.value.includes(option) &&
                          styles.optionButtonSelected
                        : attribute.value === option &&
                          styles.optionButtonSelected,
                    ]}
                    onPress={() =>
                      attribute.setValue(
                        attribute.multiSelect
                          ? attribute.value.includes(option)
                            ? attribute.value.filter((v) => v !== option)
                            : [...attribute.value, option]
                          : attribute.value === option
                          ? ""
                          : option
                      )
                    }
                  >
                    <Text
                      style={[
                        styles.optionText,
                        (attribute.multiSelect
                          ? attribute.value.includes(option)
                          : attribute.value === option) &&
                          styles.optionTextSelected,
                      ]}
                    >
                      {option}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          ))}
          <View style={styles.filterButtons}>
            <TouchableOpacity
              style={[styles.styledButton, styles.applyButton]}
              onPress={() => applyFilters({
                skillLevels,
                teamType,
                ageGroups,
                genderPolicy,
              })}
            >
              <Text style={styles.buttonText}>Apply</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.styledButton, styles.resetButton]}
              onPress={resetFiltersHandler}
            >
              <Text style={styles.buttonTextNeutral}>Reset</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.styledButton, styles.cancelButton]}
              onPress={handleClose}
            >
              <Text style={styles.buttonTextNeutral}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </BlurView>
  ) : null;
};

const styles = StyleSheet.create({
  blurView: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    width: "80%",
    backgroundColor: "white",
    borderRadius: 15,
    padding: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 5,
  },
  attributeSection: {
    marginBottom: 15,
    alignItems: "center",
  },
  attributeTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
  },
  buttonGroup: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
  },
  optionButton: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "red",
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    marginBottom: 10,
    backgroundColor: "white",
  },
  optionButtonSelected: {
    backgroundColor: "red",
  },
  optionText: {
    color: "red",
  },
  optionTextSelected: {
    color: "white",
  },
  filterButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 15,
  },
  styledButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: "center",
    marginHorizontal: 5,
  },
  applyButton: {
    borderColor: "red",
    backgroundColor: "red",
  },
  resetButton: {
    borderColor: "#ccc",
    backgroundColor: "#f5f5f5",
  },
  cancelButton: {
    borderColor: "#ccc",
    backgroundColor: "#f5f5f5",
  },
  buttonText: {
    color: "white",
    fontWeight: "bold",
    fontSize: 14,
  },
  buttonTextNeutral: {
    color: "black",
    fontWeight: "bold",
    fontSize: 14,
  },
});

export default FilterModal;
