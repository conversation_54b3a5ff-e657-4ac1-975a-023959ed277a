import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
} from "react-native";

import { auth, db } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

const Stack = createStackNavigator();

function UsersScreen() {
  const [users, setUsers] = useState([]);

  useEffect(() => {
    const docRef = collectionGroup(db, `public`);

    const unsubscribe = onSnapshot(docRef, (querySnapshot) => {
      let usersArray = [];
      console.log(querySnapshot.docs.length);
      querySnapshot.forEach((doc) => {
        usersArray.push(doc.data());
      });
      setUsers(usersArray);
    });

    return unsubscribe;
  }, []);

  const renderItem = ({ item }) => (
    <View
      style={{ flexDirection: "row", alignItems: "center", marginVertical: 10 }}
    >
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          width: 10,
          marginRight: 10,
        }}
      >
        <View style={{ height: 50, width: 2, backgroundColor: "grey" }} />
      </View>
      <Image
        source={{ uri: item.profileImageRef }}
        style={{ width: 100, height: 100, borderRadius: 25, marginRight: 10 }}
      />
      <View style={{ flexDirection: "column", justifyContent: "center" }}>
        <Text style={{ fontSize: 16, fontWeight: "bold" }}>{item.name}</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <Text style={{ height: 30, fontSize: 20 }}>{users.length}</Text>
      <View style={{ flex: 1, backgroundColor: "white" }}>
        {users.length > 0 ? (
          <FlatList
            data={users}
            renderItem={renderItem}
            keyExtractor={(item) => item.uid}
          />
        ) : (
          <ActivityIndicator size="large" color="#ff0000" />
        )}
      </View>
    </SafeAreaView>
  );
}

function CourtsScreen() {
  const [courts, setCourts] = useState([]);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const handleImagePress = (image) => {
    setSelectedImage(image);
    setModalVisible(true);
  };

  useEffect(() => {
    const docRef = collection(db, "new_courts");

    const unsubscribe = onSnapshot(docRef, (querySnapshot) => {
      let courtsArray = [];
      querySnapshot.forEach((doc) => {
        const courtData = { id: doc.id, ...doc.data() }; // Including the document ID
        courtsArray.push(courtData);
      });
      setCourts(courtsArray);
    });

    return unsubscribe;
  }, []);

  const renderItem = ({ item }) => (
    <View
      style={{
        flexDirection: "column",
        marginVertical: 10,
        padding: 10,
        borderColor: "grey",
        borderWidth: 1,
        borderRadius: 5,
      }}
    >
      <Text style={{ fontSize: 16, fontWeight: "bold", marginBottom: 5 }}>
        ID: {item.id}{" "}
      </Text>
      <Text style={{ fontSize: 14, marginBottom: 5 }}>
        Description: {item.courtDescription}{" "}
      </Text>

      <Text style={{ fontSize: 14, marginBottom: 5 }}>
        Baskets Count: {item.basketsCount}
      </Text>
      <Text style={{ fontSize: 14, marginBottom: 5 }}>
        Surface: {item.surface} (Different Surface: {item.differentSurface})
      </Text>
      <Text style={{ fontSize: 14, marginBottom: 5 }}>
        Latitude: {item.coords.latitude}, Longitude: {item.coords.longitude}
      </Text>
      <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
        {item.imagesRefs.map((imageRef, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handleImagePress(imageRef)}
          >
            <FastImage
              source={{ uri: imageRef }}
              style={{ width: 100, height: 100, borderRadius: 5, margin: 5 }}
            />
          </TouchableOpacity>
        ))}
      </View>
      <Text style={{ fontSize: 14 }}>
        User Email: {item.userEmail}, User UID: {item.userUid}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <Modal
        visible={modalVisible}
        transparent={true}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={{ flex: 1, backgroundColor: "rgba(0,0,0,0.9)" }}
          onPress={() => setModalVisible(false)}
        >
          <FastImage
            source={{ uri: selectedImage }}
            style={{ width: "100%", height: "100%" }}
            resizeMode={FastImage.resizeMode.contain}
          />
        </TouchableOpacity>
      </Modal>
      {courts.length > 0 ? (
        <FlatList
          data={courts}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
        />
      ) : (
        <ActivityIndicator size="large" color="#ff0000" />
      )}
    </SafeAreaView>
  );
}

function Screen3() {
  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <Text>Screen 3</Text>
    </View>
  );
}

function HomeScreen({ navigation }) {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginVertical: 10,
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            flexDirection: "row",
            alignItems: "center",
            padding: 10,
            borderColor: "grey",
            borderWidth: 1,
          }}
          onPress={() => navigation.navigate("Users")}
        >
          <Text style={{ fontSize: 16, fontWeight: "bold" }}>Users</Text>
        </TouchableOpacity>
      </View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginVertical: 10,
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            flexDirection: "row",
            alignItems: "center",
            padding: 10,
            borderColor: "grey",
            borderWidth: 1,
          }}
          onPress={() => navigation.navigate("CourtsScreen")}
        >
          <Text style={{ fontSize: 16, fontWeight: "bold" }}>CourtsScreen</Text>
        </TouchableOpacity>
      </View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginVertical: 10,
        }}
      >
        <TouchableOpacity
          style={{
            flex: 1,
            flexDirection: "row",
            alignItems: "center",
            padding: 10,
            borderColor: "grey",
            borderWidth: 1,
          }}
          onPress={() => navigation.navigate("Screen3")}
        >
          <Text style={{ fontSize: 16, fontWeight: "bold" }}>Screen 3</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

export default function MyGames() {
  return (
    <NavigationContainer independent={true}>
      <Stack.Navigator initialRouteName="Home">
        <Stack.Screen name="Home" component={HomeScreen} />
        <Stack.Screen name="Users" component={UsersScreen} />
        <Stack.Screen name="CourtsScreen" component={CourtsScreen} />
        <Stack.Screen name="Screen3" component={Screen3} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
