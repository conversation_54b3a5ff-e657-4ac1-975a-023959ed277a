import React from "react";
import { Image, Pressable, StyleSheet } from "react-native";
import { Entypo, FontAwesome } from "@expo/vector-icons";

const MapButton = ({
  onPress,
  iconName = "filter",
  iconColor = "red",
  imageSource = null,
  buttonSize = 60,
  iconSize = 24,
  position = { bottom: 5, left: 5 },
  backgroundColor = "white",
  style
}) => {
  return (
    <Pressable
      style={[
        styles.button,
        { width: buttonSize, height: buttonSize, backgroundColor, ...position },
        style
      ]}
      onPress={onPress}
    >
      {/* Using the props for the icon */}
      <FontAwesome name={iconName} size={iconSize} color={iconColor} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  button: {
    position: "absolute",
    borderRadius: 100,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "rgba(0,0,0,0.17)",
    borderWidth: 0.6,
    zIndex: 1,
  },
  image: {
    height: 39,
    width: 39,
    position: "absolute",
    left: "50%",
    top: "50%",
    transform: [{ translateX: -20 }, { translateY: -20 }],
  },
  icon: {
    position: "absolute",
    right: 4,
    top: 7,
  },
});

export default MapButton;