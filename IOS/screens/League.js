import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
} from "react-native";

import { auth, db, functions, crashlytics } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Entypo } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
// import ProfileIcon from "./../assets/comps/ProfileIcon";
import { crash, recordError } from "@react-native-firebase/crashlytics";

import Svg, {
  Defs,
  ClipPath,
  Polygon,
  Image as SvgImage,
} from "react-native-svg";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

const extendedWidth = screenWidth * 0.92 * 0.53; // Increased width
const triangleHeight = 140;

// Adjust the top right X position to move the cut a bit to the left
const topRightX = extendedWidth - extendedWidth * 0.132; // Slightly to the left from the right edge

const bottomLeftX = extendedWidth * 0.132;

const acceptFriendRequest = httpsCallable(functions, "acceptFriendRequestV2");

const ProfileIcon = React.memo(
  ({ profileImageRef, profileColor, userName, navigation }) => {
    return (
      <Pressable
        onPress={() => {
          navigation.navigate("Profile");
        }}
        style={{ marginRight: 10, paddingBottom: 3, paddingHorizontal: 5 }}
      >
        {profileImageRef && (
          <FastImage
            style={{ width: 38, height: 38, borderRadius: 100 }}
            source={{ uri: profileImageRef }}
            //   onLoadEnd={() => {
            //     setProfileImageLoaded(true);
            //   }}
          />
        )}
        {!profileImageRef && (
          <View
            style={{
              width: 50,
              height: 50,
              borderRadius: 100,
              backgroundColor: `${profileColor}`,
            }}
            // source={{ uri: profileImageUrl }}
            // onLoadEnd={() => {
            //   setProfileImageLoaded(true);
            // }}
          >
            <Text
              style={{
                alignSelf: "center",
                fontSize: 25,
                fontWeight: "bold",
                color: "white",
                paddingTop: 12,
                paddingLeft: 0,
              }}
            >
              {userName[0].toUpperCase()}
            </Text>
          </View>
        )}
      </Pressable>
    );
  }
);

export default function Socials() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const user = useSelector((state) => state.socials.userData);
  const chats = useSelector((state) => state.socials.chats);

  const [language, setLanguage] = useState(null);

  //   console.log(user);

  const [chatNotifsCount, setChatNotifsCount] = useState(null);

  const notifications = useSelector((state) => state.socials.notifications);

  useEffect(() => {
    const count = chats.reduce((total, chat) => {
      // console.warn(chat);
      if (!chat) return;
      return total + (chat.unreadCount || 0);
    }, 0);
    setChatNotifsCount(count);
  }, [chats]);

  // useEffect(() => {
  //   const docRef = doc(db, "users", auth.currentUser.uid);

  //   const unsubscribe = onSnapshot(docRef, (docSnap) => {
  //     if (docSnap.exists) {
  //       const doc = docSnap.data();

  //       if (doc.language !== language2) {
  //         setLanguage2(doc.language);
  //         // setFriends(doc.friends);
  //       }

  //       // setProfileImageRef;
  //     }
  //   });

  //   return () => unsubscribe();
  // }, []);

  useEffect(() => {
    if (user?.language === "CZ") {
      setLanguage(CZ);
    } else if (user?.language === "EN") {
      setLanguage(EN);
    }
  }, [user?.language, language]);

  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (event) => {
    setInputValue(event.target.value);
  };

  const handleButtonClick = async () => {
    try {
      const q = query(
        collectionGroup(db, "public"),
        where("name", "==", inputValue)
      );

      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
        const document = querySnapshot.docs[0]; // Assume the name field is unique

        acceptFriendRequest({
          to: auth.currentUser.uid,
          from: querySnapshot.docs[0].data().uid,
        });
      } else {
      }
    } catch (error) {
      console.error("Error fetching document:", error);
    }
  };

  if (
    !language ||
    notifications == null ||
    chats == null ||
    chatNotifsCount == null ||
    !user.name
  ) {
    return;
  }

  return (
    // <AppWithErrorBoundary>

    <>
      <TouchableOpacity
        style={{
          position: "absolute",
          width: 100,
          height: 100,
          backgroundColor: "red",
          top: 100,
          left: 100,
          zIndex: 100,
        }}
        onPress={() => {
          throw new Error("new log info test");
          // crash();
          // recordError(crashlytics, new Error("FFFFFFF"));
          // crashlytics().recordError(new Error("DDDDDDDDDDd"));
          // crash();
          // crashlytics().crash();
          // crash(crashlytics);
        }}
      ></TouchableOpacity>
      <View
        style={{
          width: screenWidth,
          height: "100%",
          backgroundColor: "white",
          paddingTop: insets.top,
        }}
        onStartShouldSetResponder={() => true}
      >
        <View
          style={{
            height: 45,
            // backgroundColor: "green",
            alignSelf: "center",
            width: "97%",
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <View>
            <Text
              style={{
                // fontFamily: "Nunito-Bold",
                fontFamily: "Roboto",
                fontWeight: "700",
                fontSize: 25,
                color: "rgba(0,0,0,0.87)",
                paddingLeft: 15,
                paddingTop: 7,
              }}
            >
              3x3 League
            </Text>
            <View
              style={{
                position: "absolute",
                backgroundColor: red,
                justifyContent: "center",
                alignItems: "center",
                borderRadius: 6,
                padding: 2,
                paddingHorizontal: 5,
                right: -40,
              }}
            >
              <Text
                style={{
                  // fontFamily: "Nunito-Bold",
                  fontSize: 11,
                  color: "white",
                  fontWeight: "700",
                  // paddingLeft: 15,
                  // paddingTop: 7,
                }}
              >
                BETA
              </Text>
            </View>
          </View>

          <View
            style={{
              flexDirection: "row",
              paddingTop: 4,
              paddingRight: 2,
              alignItems: "center",
            }}
          >
            <Pressable
              onPress={() => {
                navigation.navigate("NotificationsSc", {
                  language: language,
                });
              }}
              style={{
                // backgroundColor: "red",
                // paddingHorizontal: 8,
                // paddingVertical: 5,
                // position: "absolute",
                // right: 100,
                // paddingRight: 24,
                paddingHorizontal: 13,
                paddingTop: 7,
                paddingBottom: 5,
                alignItems: "center",
                justifyContent: "center",
                alignContent: "center",
              }}
            >
              <Feather name="bell" size={27} color="black" style={{}} />
              {notifications.length > 0 && (
                <View
                  style={{
                    backgroundColor: red,
                    width: 18,
                    height: 18,
                    borderRadius: 20,
                    position: "absolute",
                    top: 2,
                    right: 6,
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      fontWeight: "600",
                      alignSelf: "center",
                      fontSize: 13,
                      marginTop: 1,
                      marginLeft: 0.5,
                    }}
                  >
                    {notifications.length}
                  </Text>
                </View>
              )}
            </Pressable>
            <Pressable
              onPress={() => {
                navigation.navigate("ChatsList", { language: language });
              }}
              style={{
                // backgroundColor: "red",
                // paddingHorizontal: 8,
                // paddingVertical: 5,
                // position: "absolute",
                // right: 50,
                // top: 1,
                // paddingRight: 20,
                paddingHorizontal: 13,
                marginRight: 4,

                paddingTop: 5,
                paddingBottom: 5,
                // width: 50,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="chatbubble-ellipses-outline"
                size={28}
                color="black"
                // style={{ zIndex: 1000 }}
              />
              {chatNotifsCount > 0 && (
                <View
                  style={{
                    backgroundColor: red,
                    width: 18,
                    height: 18,
                    borderRadius: 20,
                    position: "absolute",
                    top: 3,
                    right: 3,
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      fontWeight: "600",
                      alignSelf: "center",
                      fontSize: 13,
                      marginTop: 1,
                      marginLeft: 0.5,
                    }}
                  >
                    {chatNotifsCount}
                  </Text>
                </View>
              )}
            </Pressable>
            <ProfileIcon
              profileImageRef={user.profileImageRef}
              profileColor={user.profileColor}
              userName={user.name}
              navigation={navigation}
            />
          </View>
        </View>
        <View
          style={{
            width: screenWidth * 0.92,
            height: 140,
            alignSelf: "center",
            marginTop: 35,
            borderRadius: 13,
            position: "relative",

            backgroundColor: "transparent", // Set to transparent to see the cut
            // borderColor: "black",
            // borderWidth: 1,
            // borderRadius: 13,
            // borderLeftWidth: 2,
            // borderTopWidth: 2,
            shadowColor: "black",
            shadowOpacity: 0.7,
            shadowRadius: 5,
            shadowOffset: {
              width: 0,
              height: 5,
            },
          }}
        >
          {/* <View
            style={{
              width: screenWidth * 0.92,
              height: 140,
              alignSelf: "center",
              marginTop: 35,
              borderRadius: 13,
              position: "relative",
              overflow: "hidden",
              backgroundColor: "transparent", // Set to transparent to see the cut
              borderColor: "black",
              borderWidth: 1,
              borderRadius: 13,
              // borderLeftWidth: 2,
              // borderTopWidth: 2,
              shadowColor: red,
              shadowOpacity: 0.5,
              shadowRadius: 10,
              shadowOffset: {
                width: 0,
                height: 0,
              },
              position: "absolute",
            }}
          /> */}
          <View
            style={{
              width: screenWidth * 0.92,
              height: 140,
              alignSelf: "center",
              // marginTop: 35,
              borderRadius: 13,
              position: "relative",
              overflow: "hidden",
              backgroundColor: "transparent", // Set to transparent to see the cut
              // borderColor: "black",
              // borderWidth: 1,
              borderRadius: 13,
              // borderLeftWidth: 2,
              // borderTopWidth: 2,
              // shadowColor: red,
              // shadowOpacity: 0.5,
              // shadowRadius: 10,
              // shadowOffset: {
              //   width: 0,
              //   height: 0,
              // },
            }}
          >
            <View style={{ flex: 1, backgroundColor: "white" }}></View>
            {/* <View
              style={{
                backgroundColor: "white",
                position: "absolute",
                height: 140,
                width: "45%",
                // zIndex: 10,
                alignSelf: "flex-end",
              }}
            ></View>
            <View
              style={{
                backgroundColor: "white",
                position: "absolute",
                height: 140,
                width: "45%",
                // zIndex: 10,
                alignSelf: "flex-start",
              }}
            ></View> */}
            <Pressable
              style={({ pressed }) => ({
                position: "absolute",
                width: extendedWidth,
                height: triangleHeight,
                alignSelf: "flex-start",
                opacity: pressed ? 0.9 : 1, // Decrease opacity when pressed
                overflow: "hidden",
                // transform: [{ scale: pressed ? 0.98 : 1 }],

                // borderColor: red,
                // // borderLeftWidth: 2,
                // borderRadius: 13,
                // borderLeftWidth: 2,
                // borderTopWidth: 2,
              })}
            >
              {({ pressed }) => (
                <>
                  <Svg
                    height={triangleHeight}
                    width={extendedWidth}
                    viewBox={`0 0 ${extendedWidth} ${triangleHeight}`}
                  >
                    <Defs>
                      <ClipPath id="clip">
                        {/* Adjusted points for the cut */}
                        <Polygon
                          points={`0,0 ${topRightX},0 ${extendedWidth},${triangleHeight} 0,${triangleHeight}`}
                        />
                      </ClipPath>
                    </Defs>
                    <SvgImage
                      href={require("./../images/streetball.jpg")}
                      width="100%"
                      height="100%"
                      preserveAspectRatio="xMidYMid slice"
                      clipPath="url(#clip)"
                      style={{
                        transform: pressed
                          ? [{ scale: 1.005 }]
                          : [{ scale: 1 }], // Zoom in when pressed
                      }}
                    />
                  </Svg>
                  {/* Overlay Linear Gradient */}
                  <LinearGradient
                    colors={[
                      // "rgba(0,0,0,0.65)",
                      // "rgba(0,0,0,0.65)",
                      // "rgba(0,0,0,0.7)",
                      // "rgba(0,0,0,0.65)",
                      // "rgba(0,0,0,0.55)",

                      // "rgba(0,0,0,0.45)",
                      // "rgba(0,0,0,0.35)",
                      "rgba(0,0,0,0.25)",
                      "rgba(0,0,0,0.15)",
                      "transparent",
                    ]}
                    start={{ x: 0, y: 0.6 }}
                    end={{ x: 0.2, y: 0.2 }}
                    style={{
                      position: "absolute",
                      left: 0,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      height: triangleHeight,
                      width: extendedWidth,
                    }}
                  />
                  <Text
                    style={{
                      position: "absolute",
                      fontSize: 20,
                      color: "white",
                      fontWeight: "600",
                      fontFamily: "Anton",
                      bottom: 5,
                      left: 10,
                      // gap: 10,
                      letterSpacing: 1,
                      // shadowColor:'black'
                      textShadowColor: "rgba(0, 0, 0, 0.4)",
                      textShadowOffset: { width: 0, height: 1 },
                      textShadowRadius: 1,
                    }}
                  >
                    CREATE A TEAM
                  </Text>
                  <MaterialCommunityIcons
                    name="account-group-outline"
                    size={33}
                    color="white"
                    style={{ position: "absolute", bottom: 35, left: 10 }}
                  />
                  <View
                    style={{
                      position: "absolute",
                      bottom: 50,
                      left: 48,
                      // backgroundColor: "red",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <View
                      style={{
                        // position: "absolute",
                        // top: 40,
                        // left: 10,
                        height: 12,
                        width: 2.5,
                        backgroundColor: "white",
                        borderRadius: 10,
                        zIndex: 10,
                      }}
                    ></View>
                    <View
                      style={{
                        position: "absolute",
                        // top: 40,
                        // left: 10,
                        height: 2.5,
                        width: 12,
                        backgroundColor: "white",
                        borderRadius: 10,
                        zIndex: 10,
                        marginBottom: 5,
                      }}
                    ></View>
                  </View>
                </>
              )}
            </Pressable>

            <Pressable
              style={({ pressed }) => [
                {
                  position: "absolute",
                  width: extendedWidth,
                  height: triangleHeight,
                  alignSelf: "flex-end",
                  opacity: pressed ? 0.9 : 1,
                  // Decrease opacity
                },
              ]}
            >
              {({ pressed }) => (
                <>
                  <Svg
                    height={triangleHeight}
                    width={extendedWidth}
                    viewBox={`0 0 ${extendedWidth} ${triangleHeight}`}
                  >
                    <Defs>
                      <ClipPath id="clip">
                        {/* Adjusted points for the cut */}
                        <Polygon
                          points={`0,0 ${extendedWidth},0 ${extendedWidth},${triangleHeight} ${bottomLeftX},${triangleHeight}`}
                        />
                      </ClipPath>
                    </Defs>
                    <SvgImage
                      href={require("./../images/streetball2e.jpg")}
                      width="100%"
                      height="100%"
                      preserveAspectRatio="xMidYMid slice"
                      clipPath="url(#clip)"
                      style={{
                        transform: pressed
                          ? [{ scale: 1.005 }]
                          : [{ scale: 1 }], // Zoom in when pressed
                      }}
                    />
                  </Svg>

                  <LinearGradient
                    colors={[
                      // "rgba(0,0,0,0.8)",
                      // // "rgba(0,0,0,0.8)",
                      // "rgba(0,0,0,0.7)",
                      // "rgba(0,0,0,0.65)",
                      // "rgba(0,0,0,0.7)",
                      // "rgba(0,0,0,0.65)",
                      // "rgba(255,255,255,0.55)",

                      // "rgba(255,255,255,0.45)",
                      // "rgba(255,255,255,0.35)",
                      // "rgba(255,255,255,0.25)",
                      // "rgba(255,255,255,0.15)",
                      // "rgba(0,0,0,0.25)",
                      "rgba(0,0,0,0.2)",
                      // "rgba(0,0,0,0.15)",
                      // "transparent",
                      // "transparent",

                      "transparent",
                      "transparent",
                    ]}
                    start={{ x: 0.8, y: 0.1 }}
                    end={{ x: 0.6, y: 0.8 }}
                    style={{
                      position: "absolute",
                      left: 0,
                      right: 0,
                      top: 0,
                      bottom: 0,
                      height: triangleHeight,
                      width: extendedWidth,
                    }}
                  />
                  <Text
                    style={{
                      position: "absolute",
                      fontSize: 20,
                      color: "white",
                      fontWeight: "600",
                      fontFamily: "Anton",
                      top: 5,
                      right: 10,
                      // gap: 10,
                      letterSpacing: 1,
                      // shadowColor:'black'
                      textShadowColor: "rgba(0, 0, 0, 0.4)",
                      textShadowOffset: { width: 0, height: 1 },
                      textShadowRadius: 1,
                    }}
                  >
                    FIND TEAMATES
                  </Text>
                  {/* <AntDesign
                    name="search1"
                    size={33}
                    color="white"
                    style={{ position: "absolute", top: 40, right: 10 }}
                  />
                  <AntDesign name="search1" size={33} color={color} /> */}
                  <Entypo
                    name="magnifying-glass"
                    size={33}
                    color="white"
                    style={{ position: "absolute", top: 35, right: 15 }}
                  />
                </>
              )}
            </Pressable>
          </View>
        </View>
      </View>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      ></View>
    </>
    // </AppWithErrorBoundary>
  );
}
