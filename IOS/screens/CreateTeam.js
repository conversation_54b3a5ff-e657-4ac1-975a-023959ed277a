import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import {
  Ionicons,
  MaterialCommunityIcons,
  Feather,
  Entypo,
} from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { red } from "./colors";
import { useSelector } from "react-redux";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useRoute } from "@react-navigation/native";
import { auth, db, functions, storage } from "../config/firebase";

import { httpsCallable } from "@react-native-firebase/functions";
import {
  doc,
  setDoc,
  serverTimestamp,
  collection,
  updateDoc,
  GeoPoint,
} from "@react-native-firebase/firestore";
import {
  ref,
  uploadBytes,
  getDownloadURL,
} from "@react-native-firebase/storage";
import * as ImageManipulator from "expo-image-manipulator";

const colors = [
  "#f7f465",
  "#ffd6a5",
  "#9bf6ff",
  "#a0c4ff",
  "#bdb2ff",

  "#f6e58d",
  "#badc58",
  "#7ed6df",

  "#3498db",
  "#2ecc71",
  "#f1c40f",

  "#16a085",

  "#2980b9",

  "#f8c291",

  "#6a89cc",

  "#82ccdd",
  "#60a3bc",

  "#BDC581",
  "#D6A2E8",
  "#6A89CC",

  "#FA983A",

  "#B8E994",
  "#78E08F",
  "#38ADA9",
  "#079992",

  //////

  "#A8D1FF",
  "#CFC9FF",

  "#CAE892",
  "#9CE8E0",
  "#A1A6FF",
  "#79B6FF",
  "#A3F0B1",
  "#FFDD57",
  "#FFC674",
  "#6FFFB0",
  "#8FF0AA",
  "#88C0FF",
  "#FFE775",
  "#FFDAB1",
  "#90B5FF",
  "#8EA2FF",
  "#9CDEEB",
  "#85B5C9",
  "#FFD47F",
  "#D2E89C",
  "#9DEAB5",
  "#71CEC1",
  "#62BFBF",

  /////
  "#B0E0E6",
  "#ADD8E6",
  "#87CEFA",
  "#00BFFF",
  "#AFEEEE",
  "#7FFFD4",
  "#40E0D0",

  "#F0E68C",
  "#D8BFD8",
  "#DDA0DD",

  ////////

  "#FFB8D1",
  "#D9B3FF",
  "#FFABAB",
  "#B8B3FF",
  "#D9E2FF",
  "#B0E0A8",
  "#FFB482",
  "#FFD9B0",
  "#B5D8EB",
  "#B5DEFF",
  "#D1BEFF",

  "#DCE4EF",
  "#DAC3E8",
  "#B5CCE7",

  "#FFA8A8",

  "#D1E8E2",
];

const getRandomColor = () => {
  return colors[Math.floor(Math.random() * colors.length)];
};

const Avatar = ({ name, size, teamImage, teamColor }) => {
  const getInitials = (name) => {
    return name.trim().charAt(0).toUpperCase();
  };

  return (
    <View
      style={{
        backgroundColor: teamImage ? "transparent" : teamColor,
        width: size,
        height: size,
        borderRadius: size / 3.5,
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {teamImage ? (
        <Image
          source={{ uri: teamImage }}
          style={{ width: size, height: size, borderRadius: size / 3.5 }}
        />
      ) : name.trim() !== "" ? (
        <Text
          style={{ color: "white", fontSize: size / 2.5, fontWeight: "bold" }}
        >
          {getInitials(name)}
        </Text>
      ) : (
        <MaterialCommunityIcons
          name="camera-plus"
          size={size / 3}
          color="white"
        />
      )}
    </View>
  );
};

const getInitials = (name) => {
  return name.trim().charAt(0).toUpperCase();
};

const createTeam = async (teamData) => {
  console.warn("Creating team with data:", teamData);
  try {
    const user = auth.currentUser;
    if (!user) throw new Error("User not authenticated");

    // Process the first location to create coordinates
    const firstLocation = teamData.locations[0];
    const coordinates =
      firstLocation && firstLocation.latitude && firstLocation.longitude
        ? {
            latitude: firstLocation.latitude,
            longitude: firstLocation.longitude,
          }
        : null;

    // Call the cloud function first to get the team ID
    const createTeamFunction = httpsCallable(functions, "createTeamV2"); // Updated to V2
    const result = await createTeamFunction({
      teamName: teamData.teamName,
      teamDescription: teamData.teamDescription,
      teamColor: teamData.teamColor,
      locations: teamData.locations,
      coordinates: coordinates,
      mainLocationId: firstLocation?.place_id || null,
      recruitingMessage: teamData.recruitingMessage,
      skillLevels: teamData.skillLevels,
      teamType: teamData.teamType,
      ageGroups: teamData.ageGroups,
      genderPolicy: teamData.genderPolicy,
      teamOpenness: teamData.teamOpenness,
      mainLocationLatitude: teamData.mainLocationLatitude,
      mainLocationLongitude: teamData.mainLocationLongitude,
    });

    if (!result.data.success) {
      throw new Error("Failed to create team");
    }

    const teamId = result.data.teamId;
    const teamInfo = result.data.teamInfo;
    const chatId = result.data.chatId;

    // Upload team image if exists
    let teamImageUrl = null;
    if (teamData.teamImage) {
      const manipResult = await ImageManipulator.manipulateAsync(
        teamData.teamImage,
        [{ resize: { width: 1200 } }],
        { format: ImageManipulator.SaveFormat.JPEG, compress: 0.8 }
      );

      const imageRef = ref(storage, `team_images/${teamId}/${Date.now()}.jpg`);
      const response = await fetch(manipResult.uri);
      const blob = await response.blob();
      await uploadBytes(imageRef, blob);
      teamImageUrl = await getDownloadURL(imageRef);
    }

    // Upload location images with primary location flag
    const locationsWithImages = await Promise.all(
      teamData.locations.map(async (location, index) => {
        const isFirstLocation = index === 0;
        if (location.image) {
          const manipResult = await ImageManipulator.manipulateAsync(
            location.image,
            [{ resize: { width: 1200 } }],
            { format: ImageManipulator.SaveFormat.JPEG, compress: 0.8 }
          );

          const imageRef = ref(
            storage,
            `team_location_images/${teamId}/${index}_${Date.now()}.jpg`
          );
          const response = await fetch(manipResult.uri);
          const blob = await response.blob();
          await uploadBytes(imageRef, blob);
          const imageUrl = await getDownloadURL(imageRef);
          return { ...location, image: imageUrl, isPrimary: isFirstLocation };
        }
        return { ...location, isPrimary: isFirstLocation };
      })
    );

    // Update the team document with the image URLs and location data
    const teamRef = doc(db, "teams", teamId);
    await updateDoc(teamRef, {
      imageUrl: teamImageUrl,
      locations: locationsWithImages,
    });

    teamInfo.imageUrl = teamImageUrl;
    console.log("Team info:", teamInfo);

    const chatRef = doc(db, "chats", chatId);
    await updateDoc(chatRef, {
      teamInfo: teamInfo,
    });

    console.log("Team created successfully:", result.data);
    return teamId;
  } catch (error) {
    console.error("Error creating team:", error);
    throw error;
  }
};

const CreateTeam = ({ navigation }) => {
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const [teamName, setTeamName] = useState(route.params?.teamName || "");
  const [teamDescription, setTeamDescription] = useState(
    route.params?.teamDescription || ""
  );
  const [teamImage, setTeamImage] = useState(route.params?.teamImage || null);
  const [teamColor, setTeamColor] = useState(
    route.params?.teamColor || getRandomColor()
  );
  const [locations, setLocations] = useState(
    route.params?.locations || [
      {
        name: "",
        image: null,
        place_id: null,
        latitude: null,
        longitude: null,
      },
    ]
  );

  const [mainLocationLongitude, setMainLocationLongitude] = useState(null);
  const [mainLocationLatitude, setMainLocationLatitude] = useState(null);

  const [recruitingMessage, setRecruitingMessage] = useState(
    route.params?.recruitingMessage || ""
  );

  const [teamType, setTeamType] = useState(route.params?.teamType || "");
  const [skillLevels, setSkillLevels] = useState(
    route.params?.skillLevels || []
  );
  const [ageGroups, setAgeGroups] = useState(route.params?.ageGroups || []);
  const [genderPolicy, setGenderPolicy] = useState(
    route.params?.genderPolicy || ""
  );
  const { language } = useSelector((state) => state.language);

  const teamOpenness = route.params?.teamOpenness || {
    isVisible: true,
    allowJoinRequests: true,
    allowChallenges: true,
  };

  const [isCreating, setIsCreating] = useState(false);

  const handleCreateTeam = async () => {
    // console.warn(mainLocationLatitude, mainLocationLongitude);
    // console.warn({
    //   teamName,
    //   teamDescription,
    //   teamImage,
    //   teamColor,
    //   locations,
    //   recruitingMessage,
    //   skillLevels,
    //   teamType,
    //   ageGroups,
    //   genderPolicy,
    //   teamOpenness,
    //   mainLocationLatitude,
    //   mainLocationLongitude,
    // });
    if (isCreating) return; // Prevent multiple clicks
    setIsCreating(true);
    try {
      const teamId = await createTeam({
        teamName,
        teamDescription,
        teamImage,
        teamColor,
        locations,
        recruitingMessage,
        skillLevels,
        teamType,
        ageGroups,
        genderPolicy,
        teamOpenness,
        mainLocationLatitude,
        mainLocationLongitude,
      });
      console.log("Team created with ID:", teamId);
      navigation.navigate("Teams"); // Or navigate to the new team's screen
    } catch (error) {
      alert("Failed to create team: " + error.message);
    } finally {
      setIsCreating(false);
    }
  };

  const handleBackPress = () => {
    navigation.navigate("ChooseTeamType", {
      teamName,
      teamDescription,
      teamImage,
      teamColor,
      locations,
      recruitingMessage,
      teamOpenness,
      skillLevels,
      teamType,
      ageGroups,
      genderPolicy,
    });
  };

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    console.log(result.assets[0].uri);

    if (!result.canceled) {
      setTeamImage(result.assets[0].uri);
    }
  };

  const deleteTeamImage = () => {
    setTeamImage(null);
  };

  const addLocation = () => {
    if (locations.length < 3) {
      setLocations([...locations, { name: "", image: null }]);
    }
  };

  const updateLocation = (index, locationData, mainLocationCoordinates) => {
    console.log("lllllllll", locationData);
    const newLocations = [...locations];
    newLocations[index] = { ...newLocations[index], ...locationData };
    setLocations(newLocations);
    if (mainLocationCoordinates) {
      setMainLocationLongitude(mainLocationCoordinates?.longitude || null);
      setMainLocationLatitude(mainLocationCoordinates?.latitude || null);
    }
  };

  const pickLocationImage = async (index) => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [16, 9],
      quality: 1,
    });

    if (!result.canceled) {
      updateLocation(index, { image: result.assets[0].uri });
    }
  };

  const deleteLocationImage = (index) => {
    updateLocation(index, { image: null });
  };

  const inputStyle = {
    backgroundColor: "rgba(0,0,0,0.0)",
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 10,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.15)",
  };

  const teamAttributes = [
    {
      title: "skillLevel",
      options: ["beginner", "intermediate", "advanced", "professional"],
      value: skillLevels,
      setValue: setSkillLevels,
      multiSelect: true,
    },
    {
      title: "teamType",
      options: ["casual", "competitive"],
      value: teamType,
      setValue: setTeamType,
      multiSelect: false,
    },
    {
      title: "ageGroup",
      options: ["youth", "adult", "senior"],
      value: ageGroups,
      setValue: setAgeGroups,
      multiSelect: true,
    },
    {
      title: "genderPolicy",
      options: ["mixed", "menOnly", "womenOnly"],
      value: genderPolicy,
      setValue: setGenderPolicy,
      multiSelect: false,
    },
  ];

  const TeamAttribute = ({ title, options, value, setValue, multiSelect }) => (
    <View style={{ marginBottom: 20 }}>
      <Text
        style={{
          fontSize: 16,
          color: "#333",
          marginBottom: 8,
          fontWeight: "600",
        }}
      >
        {language[title]}
        {multiSelect && (
          <Text
            style={{
              fontSize: 13,
              color: "rgba(0,0,0,0.7)",
              fontWeight: "500",
            }}
          >
            {"  "}({language.multipleSelectionsAllowed})
          </Text>
        )}
      </Text>
      <View
        style={{
          flexDirection: "row",
          flexWrap: "wrap",
          marginHorizontal: -5,
        }}
      >
        {options.map((option) => (
          <TouchableOpacity
            key={option}
            style={{
              backgroundColor: multiSelect
                ? value.includes(option)
                  ? red
                  : "white"
                : value === option
                ? red
                : "white",
              borderColor: red,
              borderWidth: 1,
              borderRadius: 20,
              paddingVertical: 8,
              paddingHorizontal: 16,
              margin: 5,
            }}
            onPress={() => {
              if (multiSelect) {
                setValue((prev) =>
                  prev.includes(option)
                    ? prev.filter((item) => item !== option)
                    : [...prev, option]
                );
              } else {
                setValue(option);
              }
            }}
          >
            <Text
              style={{
                color: multiSelect
                  ? value.includes(option)
                    ? "white"
                    : red
                  : value === option
                  ? "white"
                  : red,
                fontWeight: "500",
              }}
            >
              {language[option]}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const isFormValid = () => {
    return (
      teamName.trim() !== "" &&
      locations.some((location) => location.name.trim() !== "") &&
      recruitingMessage.trim() !== "" &&
      skillLevels.length > 0 &&
      teamType !== "" &&
      ageGroups.length > 0 &&
      genderPolicy !== ""
    );
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAwareScrollView
        style={{ flex: 1, backgroundColor: "white" }}
        contentContainerStyle={{ paddingTop: insets.top }}
        resetScrollToCoords={{ x: 0, y: 0 }}
        scrollEnabled={true}
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={{ padding: 20 }}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 40,
            }}
          >
            <TouchableOpacity
              onPress={() => handleBackPress()}
              style={{ marginRight: 10, padding: 5 }}
            >
              <Feather name="arrow-left" size={24} color={red} />
            </TouchableOpacity>
            <Text style={{ fontSize: 23, fontWeight: "bold", color: "#333" }}>
              {language.createTeam}
            </Text>
          </View>

          <TouchableOpacity
            onPress={pickImage}
            style={{
              alignItems: "center",
              marginBottom: 40,
              // backgroundColor: "red",
              width: 160,
              alignSelf: "center",
              marginTop: 5,
            }}
          >
            <Avatar
              name={teamName}
              size={120}
              teamImage={teamImage}
              teamColor={teamColor}
            />
            {teamImage && (
              <TouchableOpacity
                style={{
                  position: "absolute",
                  top: -5,
                  right: 10,
                  backgroundColor: "white",
                  borderRadius: 15,
                  // padding: 5,
                  borderColor: "rgba(0,0,0,0.2)",
                  borderWidth: 1,
                  width: 30,
                  height: 30,
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onPress={deleteTeamImage}
              >
                <Feather
                  name="x"
                  size={17}
                  color={red}
                  style={{ paddingTop: 1 }}
                />
              </TouchableOpacity>
            )}
            <Text
              style={{
                color: "rgba(0,0,0,0.6)",
                marginTop: 10,
                fontWeight: "500",
              }}
            >
              {language.addTeamLogo}
            </Text>
            <Text
              style={{
                color: "rgba(0,0,0,0.6)",
                marginTop: 2,
                fontWeight: "500",
              }}
            >
              {"(" + language.optional + ")"}
            </Text>
          </TouchableOpacity>

          <View style={{ marginBottom: 30 }}>
            <Text
              style={{
                fontSize: 16,
                color: "#333",
                marginBottom: 8,
                fontWeight: "600",
              }}
            >
              {language.teamName}
            </Text>
            <TextInput
              style={inputStyle}
              placeholder={language.enterTeamName}
              value={teamName}
              onChangeText={setTeamName}
            />
          </View>

          <View style={{ marginBottom: 20 }}>
            <Text
              style={{
                fontSize: 16,
                color: "#333",
                marginBottom: 8,
                fontWeight: "600",
              }}
            >
              {language.teamDescription}
            </Text>
            <TextInput
              style={[inputStyle, { height: 100, textAlignVertical: "top" }]}
              placeholder={language.describeTeam}
              value={teamDescription}
              onChangeText={setTeamDescription}
              multiline
            />
          </View>

          {locations.map((location, index) => (
            <View
              key={index}
              style={{
                marginBottom: 20,
                backgroundColor: "rgba(255, 255, 255, 0.8)",
                borderRadius: 12,
                padding: 15,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 0 },
                shadowOpacity: 0.05,
                shadowRadius: 3,
                elevation: 1,
                borderColor: "rgba(0,0,0,0.15)",
                borderWidth: 1,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 15,
                }}
              >
                <Text
                  style={{ fontSize: 16, color: "#333", fontWeight: "600" }}
                >
                  {`${language.location} ${index + 1}`}
                </Text>
                {index > 0 && (
                  <TouchableOpacity
                    style={{
                      backgroundColor: "rgba(255, 0, 0, 0.1)",
                      borderRadius: 20,
                      padding: 5,
                    }}
                    onPress={() => {
                      const newLocations = [...locations];
                      newLocations.splice(index, 1);
                      setLocations(newLocations);
                    }}
                  >
                    <Feather name="x" size={20} color={red} />
                  </TouchableOpacity>
                )}
              </View>

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  backgroundColor: "rgba(0, 0, 0, 0.03)",
                  borderRadius: 10,
                  padding: 12,
                  marginBottom: 15,
                }}
                onPress={() => {
                  navigation.navigate("SearchLocation", {
                    onLocationSelected: (
                      selectedLocation,
                      mainLocationCoordinates
                    ) => {
                      updateLocation(
                        index,
                        selectedLocation,
                        mainLocationCoordinates
                      );
                    },
                    existingLocations: locations.filter((loc) => loc.place_id), // Only pass locations that have a place_id
                  });
                }}
              >
                <Entypo
                  name="location-pin"
                  size={24}
                  color={red}
                  style={{ marginRight: 10 }}
                />
                <Text
                  style={{
                    fontSize: 16,
                    color: location.name ? "#333" : "#999",
                    flex: 1,
                  }}
                >
                  {location.name || language.chooseTeamLocation}
                </Text>
                <Feather name="chevron-right" size={24} color="#999" />
              </TouchableOpacity>

              {location.image ? (
                <View style={{ position: "relative" }}>
                  <Image
                    source={{ uri: location.image }}
                    style={{
                      width: "100%",
                      height: 150,
                      borderRadius: 10,
                    }}
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    style={{
                      position: "absolute",
                      top: 10,
                      right: 10,
                      backgroundColor: "rgba(255, 255, 255, 0.8)",
                      borderRadius: 20,
                      padding: 8,
                    }}
                    onPress={() => deleteLocationImage(index)}
                  >
                    <Feather name="x" size={20} color={red} />
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "rgba(0, 0, 0, 0.03)",
                    paddingVertical: 15,
                    borderRadius: 10,
                    borderStyle: "dashed",
                    borderWidth: 1,
                    borderColor: "rgba(0, 0, 0, 0.1)",
                  }}
                  onPress={() => pickLocationImage(index)}
                >
                  <MaterialCommunityIcons
                    name="image-plus"
                    size={28}
                    color={red}
                    style={{ marginRight: 10 }}
                  />
                  <Text style={{ color: red, fontSize: 16, fontWeight: "600" }}>
                    {language.addLocationImage}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ))}

          {locations.length < 3 && (
            <TouchableOpacity
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(255, 0, 0, 0.05)",
                paddingVertical: 10,
                borderRadius: 10,
                marginBottom: 20,
                borderWidth: 0.7,
                borderColor: "rgba(255, 0, 0, 0.2)",
              }}
              onPress={addLocation}
            >
              <Ionicons
                name="add-circle-outline"
                size={22}
                color={red}
                style={{ marginRight: 10 }}
              />
              <Text style={{ color: red, fontSize: 15, fontWeight: "600" }}>
                {language.addAnotherLocation}
              </Text>
            </TouchableOpacity>
          )}

          {teamOpenness.allowJoinRequests && (
            <View style={{ marginBottom: 20 }}>
              <Text
                style={{
                  fontSize: 16,
                  color: "#333",
                  marginBottom: 8,
                  fontWeight: "600",
                }}
              >
                {language.recruitingMessage}
              </Text>
              <TextInput
                style={[inputStyle, { height: 100, textAlignVertical: "top" }]}
                placeholder={language.recruitingMessageExample}
                value={recruitingMessage}
                onChangeText={setRecruitingMessage}
                multiline
              />
            </View>
          )}

          {teamAttributes.map((attr) => (
            <TeamAttribute
              key={attr.title}
              title={attr.title}
              options={attr.options}
              value={attr.value}
              setValue={attr.setValue}
              multiSelect={attr.multiSelect}
            />
          ))}

          <Text
            style={{
              fontSize: 13,
              color: "#666",
              textAlign: "center",
              marginTop: 10,
              paddingHorizontal: 15,
              marginBottom: 10,
            }}
          >
            {language.changeOpenessLater}
          </Text>

          <TouchableOpacity
            style={{
              backgroundColor: isFormValid() ? red : "#ccc",
              paddingVertical: 15,
              borderRadius: 20,
              alignItems: "center",
              flexDirection: "row",
              justifyContent: "center",
              elevation: 2,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.2,
              shadowRadius: 4,
              marginBottom: 30,
              marginTop: 10,
              opacity: isCreating ? 0.7 : 1,
            }}
            onPress={handleCreateTeam}
            disabled={isCreating || !isFormValid()}
          >
            {isCreating ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text
                style={{ color: "white", fontSize: 18, fontWeight: "bold" }}
              >
                {language.createTeamButton}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAwareScrollView>
    </TouchableWithoutFeedback>
  );
};

export default CreateTeam;
