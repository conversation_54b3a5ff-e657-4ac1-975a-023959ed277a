import React, {
  useEffect,
  useLayoutEffect,
  useState,
  create<PERSON>ontext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import ProfileImage from "./../assets/ProfileImage";

const sentFriendRequestNotificationV2 = httpsCallable(
  functions,
  "sentFriendRequestNotificationV2"
);

const sendFriendRequest = async (userToAdd) => {
  // Optimistically update our users list
  try {
    setUsers((currentUsers) =>
      currentUsers.map((u) =>
        u.uid === userToAdd.uid ? { ...u, friendRequestSent: true } : u
      )
    );

    console.log("Sending friend request to:", userToAdd);
    try {
      await sentFriendRequestNotificationV2({
        from: auth.currentUser.uid,
        to: userToAdd.uid,
      });
      // Success case - no need to do anything as the useEffect will handle it
    } catch (error) {
      // Revert the optimistic update
      setUsers((currentUsers) =>
        currentUsers.map((u) =>
          u.uid === userToAdd.uid ? { ...u, friendRequestSent: false } : u
        )
      );

      let errorMessage = "Failed to send friend request";
      if (error.code === "already-exists") {
        errorMessage = "Friend request already sent";
      } else if (error.code === "not-found") {
        errorMessage = "User not found";
      }
      Alert.alert("Error", errorMessage);
    }
  } catch (error) {
    console.error("Error sending friend request:", error);
  }
};

const User = ({ route, navigation }) => {
  const insets = useSafeAreaInsets();
  const { user } = route.params;
  const red = "#F53156"; // Assuming this is your red color

  const calculateAge = (dateString) => {
    if (dateString?.seconds) {
      const dateSeconds = dateString.seconds;
      const birthDate = new Date(dateSeconds * 1000);
      const ageDifMs = Date.now() - birthDate.getTime();
      const ageDate = new Date(ageDifMs);
      return Math.abs(ageDate.getUTCFullYear() - 1970);
    } else {
      const dateSeconds = Math.floor(new Date(dateString).getTime() / 1000);
      const birthDate = new Date(dateSeconds * 1000);
      const ageDifMs = Date.now() - birthDate.getTime();
      const ageDate = new Date(ageDifMs);
      return Math.abs(ageDate.getUTCFullYear() - 1970);
    }
  };

  const sendFriendRequest = async (userToAdd) => {
    // Optimistically update our users list
    try {
      // setUsers((currentUsers) =>
      //   currentUsers.map((u) =>
      //     u.uid === userToAdd.uid ? { ...u, friendRequestSent: true } : u
      //   )
      // );

      // console.log("Sending friend request to:", userToAdd);
      try {
        await sentFriendRequestNotificationV2({
          from: auth.currentUser.uid,
          to: userToAdd.uid,
        });
        // Success case - no need to do anything as the useEffect will handle it
      } catch (error) {
        // // Revert the optimistic update
        // setUsers((currentUsers) =>
        //   currentUsers.map((u) =>
        //     u.uid === userToAdd.uid ? { ...u, friendRequestSent: false } : u
        //   )
        // );

        let errorMessage = "Failed to send friend request";
        if (error.code === "already-exists") {
          errorMessage = "Friend request already sent";
        } else if (error.code === "not-found") {
          errorMessage = "User not found";
        }
        Alert.alert("Error", errorMessage);
      }
    } catch (error) {
      console.error("Error sending friend request:", error);
    }
  };

  return (
    <SafeAreaView
      style={{ flex: 1, backgroundColor: "white", paddingTop: insets.top }}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          paddingHorizontal: 20,
          height: 60,
          //   borderBottomWidth: 1,
          //   borderBottomColor: "rgba(0,0,0,0.1)",
        }}
      >
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <AntDesign
            name="arrowleft"
            size={31}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
          />
        </TouchableOpacity>
        <TouchableOpacity>
          <MaterialCommunityIcons
            name="dots-horizontal"
            size={40}
            color="black"
          />
          {/* <MaterialCommunityIcons
            name="dots-horizontal-circle-outline"
            size={35}
            color="black"
          /> */}
        </TouchableOpacity>
      </View>

      <View style={{ padding: 20 }}>
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <ProfileImage
            profileImageRef={user.profileImageRef}
            profileColor={user.profileColor}
            name={user.name}
            size={120}
            style={{ marginRight: 15 }}
          />
          {/* {user.profileImageRef ? (
            <FastImage
              source={{ uri: user.profileImageRef }}
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                marginRight: 15,
                borderWidth: 0.15,
                borderColor: "rgba(0,0,0,0.8)",
              }}
            />
          ) : (
            <View
              style={{
                width: 120,
                height: 120,
                borderRadius: 60,
                backgroundColor: user.profileColor || "#B5CCE7",
                justifyContent: "center",
                alignItems: "center",
                marginRight: 15,
              }}
            >
              <Text
                style={{ color: "white", fontSize: 35, fontWeight: "bold" }}
              >
                {user.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )} */}
          <View>
            <Text style={{ fontSize: 24, fontWeight: "600" }}>{user.name}</Text>
            <Text style={{ fontSize: 16, color: "gray" }}>
              {calculateAge(user.date)} years old
            </Text>
          </View>
        </View>

        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            marginTop: 25,
            paddingHorizontal: 5,
          }}
        >
          <TouchableOpacity
            style={{
              backgroundColor: "white",
              paddingHorizontal: 15,
              borderRadius: 5,
              borderColor: red,
              borderWidth: 1,
              flexDirection: "row",
              justifyContent: "center",
              height: 37,
              alignItems: "center",
              width: "48%",
            }}
            onPress={() => {
              // console.log("Add friend button pressed");
              sendFriendRequest(user);
            }}
          >
            <MaterialIcons name="person-add" size={18} color={red} />
            <Text
              style={{
                color: red,
                fontWeight: "600",
                fontSize: 14,
                marginLeft: 7,
              }}
            >
              Add friend
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: "rgba(0,0,0,0.0)",
              paddingHorizontal: 30,
              borderRadius: 5,
              borderColor: "rgba(0, 0, 0, 0.2)",
              borderWidth: 0.5,
              height: 37,
              justifyContent: "center",
              width: "48%",
              alignItems: "center",
            }}
            onPress={() => {
              console.log("Chat button pressed");
            }}
          >
            <Text
              style={{
                color: "rgba(0,0,0,0.8)",
                fontWeight: "500",
                fontSize: 14,
              }}
            >
              Message
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default User;
