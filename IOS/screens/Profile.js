import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  ScrollView,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Animated,
  Modal,
  TouchableWithoutFeedback,
  Dimensions,
  Linking,
} from "react-native";
import {
  signOut,
  verifyBeforeUpdateEmail,
  updateEmail,
  reauthenticateWithCredential,
  sendEmailVerification,
} from "@react-native-firebase/auth";
import { auth, db } from "../config/firebase";
import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useRoute,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  getDoc,
  addDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  onSnapshot,
  collectionGroup,
  query,
  where,
  increment,
} from "@react-native-firebase/firestore";
import * as ImageManipulator from "expo-image-manipulator";
import {
  ref,
  uploadBytesResumable,
  getBlob,
  getBytes,
  getDownloadURL,
  uploadString,
} from "@react-native-firebase/storage";

// import ImageResizer from "react-native-image-resizer";
import * as ImagePicker from "expo-image-picker";
// import FastImage from "react-native-fast-image";
// import CachedImage from "react-native-expo-cached-image";
import { red } from "./colors";

import { Feather } from "@expo/vector-icons";

import { MaterialIcons } from "@expo/vector-icons";
import { Entypo } from "@expo/vector-icons";

import DateTimePickerModal from "react-native-modal-datetime-picker";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
// import tr from "date-fns/esm/locale/tr/index.js";

// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Icon from "react-native-vector-icons/FontAwesome";

import { Ionicons } from "@expo/vector-icons";

import { FontAwesome } from "@expo/vector-icons";
import { EN, CZ } from "./../assets/strings";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";

const Stack = createStackNavigator();

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

const ImageDiv = React.memo(
  ({ name, image2, profileColor, setImageLoaded }) => {
    // console.log(name, "name");
    if (image2) {
      return (
        <FastImage
          style={{
            width: 130,
            height: 130,
            borderRadius: 100,
          }}
          source={{
            uri: image2,
          }}
          onLoadEnd={() => {
            setTimeout(() => {
              setImageLoaded(true);
            }, 100);
          }}
        />
      );
    } else {
      return (
        <View
          style={{
            width: 130,
            height: 130,
            borderRadius: 100,
            backgroundColor: `${profileColor}`,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Text
            style={{
              alignSelf: "center",
              fontSize: 60,
              color: "white",
              // paddingTop: screenHeight / 30,
              // paddingLeft: 0,
            }}
          >
            {name[0].toUpperCase()}
          </Text>
        </View>
      );
    }
  }
);

const ImageDivEdit = React.memo(
  ({ name, image2, profileColor, setImageLoaded }) => {
    // console.log(name, "name");
    if (image2) {
      return (
        <FastImage
          style={{
            width: 130,
            height: 130,
            borderRadius: 100,
          }}
          source={{
            uri: image2,
          }}
          onLoadEnd={() => {
            setTimeout(() => {
              setImageLoaded(true);
            }, 100);
          }}
        />
      );
    } else {
      return (
        <View
          style={{
            width: 130,
            height: 130,
            borderRadius: 100,
            backgroundColor: `${profileColor}`,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Text
            style={{
              alignSelf: "center",
              fontSize: 60,
              color: "white",
              // paddingTop: screenHeight / 34,
              // paddingLeft: 0,
            }}
          >
            {name.length > 0 ? name[0].toUpperCase() : "..."}
          </Text>
        </View>
      );
    }
  }
);

const LanguageToggle = ({
  selectionColor = red,
  initialLanguage = "EN",
  language,
  setLanguage,
}) => {
  // const [selectedLanguage, setSelectedLanguage] = useState(initialLanguage);

  return (
    <View
      style={{
        flexDirection: "row",
        backgroundColor: "white",
        borderRadius: 10,
        borderWidth: 0.5,
        borderColor: selectionColor,
        justifyContent: "center",
        padding: 2,
        height: 37,
      }}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => setLanguage("CZ")}
        style={{
          flex: 1,
          backgroundColor: language == "CZ" ? selectionColor : "white",
          borderRadius: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text style={{ color: language == "CZ" ? "white" : selectionColor }}>
          CZ
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        activeOpacity={1}
        onPress={() => setLanguage("EN")}
        style={{
          flex: 1,
          backgroundColor: language == "EN" ? selectionColor : "white",
          borderRadius: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text style={{ color: language == "EN" ? "white" : selectionColor }}>
          EN
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const Info = () => {
  const insets = useSafeAreaInsets();

  const [email, setEmail] = useState(null);
  const [name, setName] = useState(null);
  const [date, setDate] = useState(null);
  const [image2, setImage2] = useState(false);
  const [profileColor, setProfileColor] = useState(null);
  const [infoLoaded, setInfoLoaded] = useState(null);
  const [description, setDescription] = useState(null);

  const [language2, setLanguage2] = useState("CZ");
  const [language, setLanguage] = useState(null);

  const [imageLoaded, setImageLoaded] = useState(null);

  const opacity1 = useRef(new Animated.Value(0)).current;

  // console.log(auth.currentUser.providerData[0].providerId);

  useEffect(() => {
    if (imageLoaded && image2) {
      setTimeout(() => {
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }).start();
      }, 500);
    } else {
      setTimeout(() => {
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }).start();
      }, 500);
    }
  }, [imageLoaded]);

  const navigation = useNavigation();

  const LoadingCircle = () => {
    return (
      <View
        style={{
          width: "100%",
          height: "100%",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "white",
        }}
      >
        <ActivityIndicator size="small" />
        <View
          style={{
            width: "100%",
            height: 0.8,
            backgroundColor: "lightgrey",
            position: "absolute",
            bottom: 0,
          }}
        />
      </View>
    );
  };

  // useEffect(() => {}, [infoLoaded, imageLoaded]);

  const onSignOut = async () => {
    const docRef = doc(db, "users", auth.currentUser.uid);

    // await updateDoc(docRef, {
    //   logCounter: increment(-1),
    // });

    signOut(auth).catch((error) => console.log(error));
  };

  useEffect(() => {
    const docRef = doc(db, "users", auth.currentUser.uid);

    const unsubscribe = onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists) {
        const data = docSnap.data();

        if (data.email !== email) {
          setEmail(data.email);
        }
        if (data.name !== name) {
          setName(data.name);
        }
        if (data.date !== date) {
          setDate(data.date);
        }
        if (data.profileColor !== profileColor) {
          setProfileColor(data.profileColor);
        }
        if (data.description !== description) {
          setDescription(data.description);
        }
        if (data.profileImageRef !== image2) {
          setImage2(data.profileImageRef);
        }
        if (data.language !== language2) {
          setLanguage2(data.language);
        }

        setTimeout(() => {
          setInfoLoaded(true);
        }, 800);
      } else {
        console.log("No such document!");
      }
    });

    // Cleanup function
    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (language2 === "CZ") {
      setLanguage(CZ);
      // console.log(CZ);
    } else {
      setLanguage(EN);
    }
  }, [language2]);

  function calculateAge(dob) {
    // console.log(new Date(dob.seconds), "/////////////////////////////////");
    const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  }

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  if (!infoLoaded || !language) {
    return <LoadingCircle />;
  }

  // console.log(image2);

  return (
    // <SafeAreaView>
    <View
      style={{
        backgroundColor: "white",
        width: "100%",
        height: "100%",
        // flex: 1,
        paddingBottom: insets.bottom,
      }}
    >
      <Animated.View
        style={{
          backgroundColor: "white",
          width: "100%",
          height: "100%",
          opacity: opacity1,
        }}
      >
        <Pressable
          style={{
            position: "absolute",
            width: 45,
            height: 45,
            borderRadius: 50,
            // backgroundColor: "rgba(220,220,220,0.8)",
            top: 60,
            left: 24,
            zIndex: 10,
            justifyContent: "center",
          }}
          onPress={() => {
            navigation.goBack();
          }}
        >
          <AntDesign
            name="arrowleft"
            size={31}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
          />
        </Pressable>
        <View
          style={{
            alignItems: "center",
            // flex: 1,
            justifyContent: "center",
            marginTop: screenHeight * 0.04 + insets.top,
            // marginBottom: 70,
          }}
        >
          <ImageDiv
            name={name}
            profileColor={profileColor}
            image2={image2}
            setImageLoaded={setImageLoaded}
          />
          <Text style={{ fontSize: 19, paddingTop: 20 }}>{name}</Text>
        </View>

        <Pressable
          style={({ pressed }) => ({
            backgroundColor: pressed ? "#dbdbdb" : "#f1f1f1", // change the color on press
            borderRadius: 5,
            padding: 10,
            alignItems: "center",
            marginTop: "6%",
            width: "50%",
            alignSelf: "center",
          })}
          onPress={() => {
            navigation.navigate("Editinfo", {
              name,
              email,
              date,
              image2,
              profileColor,
              description,
              language,
            });
          }}
        >
          <Text
            style={{
              fontWeight: "500",
              fontSize: 15,
            }}
          >
            {language.editProfile}
          </Text>
        </Pressable>

        <View
          style={{
            width: "90%",
            marginTop: "12%",

            // padding: 10,
            borderRadius: 8,
            alignSelf: "center",
          }}
        >
          <Text
            style={{
              fontWeight: "600",
              fontSize: 15,
              marginBottom: 5,
              color: "rgba(2,2,2,1)",
            }}
          >
            Email:
          </Text>
          <Text style={{ fontSize: 14, color: "rgba(20,20,20,1)" }}>
            {email}
          </Text>
        </View>

        <View
          style={{
            width: "90%",
            marginTop: "5%",

            // padding: 10,
            borderRadius: 8,
            alignSelf: "center",
          }}
        >
          <Text
            style={{
              fontWeight: "600",
              fontSize: 15,
              marginBottom: 5,
              color: "rgba(2,2,2,1)",
            }}
          >
            {language.age}:
          </Text>
          <Text style={{ fontSize: 14, color: "rgba(20,20,20,1)" }}>
            {calculateAge(date)}
          </Text>
        </View>

        <View
          style={{
            width: "90%",
            marginTop: "5%",

            // padding: 10,
            borderRadius: 8,
            alignSelf: "center",
          }}
        >
          <Text
            style={{
              fontWeight: "600",
              fontSize: 15,
              marginBottom: 5,
              color: "rgba(2,2,2,1)",
            }}
          >
            {language.description}:
          </Text>
          <ScrollView
            style={{ height: 58 }}
            showsVerticalScrollIndicator={false}
          >
            <Text style={{ fontSize: 13, color: "rgba(20,20,20,1)" }}>
              {description}
            </Text>
          </ScrollView>
        </View>

        <View style={{ width: "100%", position: "absolute", bottom: 0 }}>
          <TouchableOpacity
            style={{
              height: 45,
              flexDirection: "row",
              marginBottom: 5,
              alignItems: "center",
              paddingLeft: "6%",
            }}
            onPress={() => {
              navigation.navigate("Settings", language2);
            }}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-evenly",
                alignItems: "center",
              }}
            >
              <Feather name="settings" size={24} color="black" />
              <Text style={{ fontWeight: "600", marginLeft: 20 }}>
                {language.settings}
              </Text>
            </View>
          </TouchableOpacity>
          <View
            style={{
              width: "95%",
              height: 0.6,
              backgroundColor: "rgba(0,0,0,0.3)",
              alignSelf: "center",
            }}
          ></View>
          <TouchableOpacity
            style={{
              height: 52,
              flexDirection: "row",
              paddingLeft: 4,
              alignItems: "center",
              paddingLeft: "6%",
            }}
            onPress={toggleModal2}
          >
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-evenly",
                alignItems: "center",
              }}
            >
              <Feather
                name="log-out"
                size={24}
                color="red"
                style={{ paddingTop: 5 }}
              />
              <Text
                style={{
                  fontWeight: "600",
                  color: "red",
                  marginLeft: 20,
                  paddingTop: 5,
                }}
              >
                {language.logOut}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "80%",
                    height: "22%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "3%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 15.5,
                      }}
                    >
                      {language.logOutEnsure}
                    </Text>
                  </View>

                  <View
                    style={{
                      // bottom: -30,
                      position: "absolute",
                      flexDirection: "row",
                      justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      bottom: 25,
                      // marginTop: 20,
                      // alignItems: "center",

                      // flex: 1,
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        width: "45%",
                        height: 35,
                        backgroundColor: "rgba(0,0,0,0.04)",
                        justifyContent: "center",
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        borderColor: "rgba(0,0,0,0.1)",
                        borderWidth: 0.5,
                        // bottom: 6,
                      })}
                    >
                      <Text style={{ fontWeight: "600", alignSelf: "center" }}>
                        {language.no}
                      </Text>
                    </Pressable>
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(onSignOut, 200);
                        // onSignOut();
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        width: "45%",
                        height: 35,
                        backgroundColor: red,
                        // shadowColor: red,
                        // shadowOpacity: 0.3,
                        // shadowRadius: 5,
                        // shadowOffset: { width: 0, height: 0 },
                        justifyContent: "center",
                        // marginBottom: 30,
                        // bottom: 6,
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "800",
                          alignSelf: "center",
                          color: "white",
                          fontSize: 15,
                          // fontFamily: "roboto",
                        }}
                      >
                        {language.yes}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>
      </Animated.View>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      />
    </View>
    // </SafeAreaView>
  );
};

const Editinfo = () => {
  const route = useRoute();
  const insets = useSafeAreaInsets();

  console.log();

  const navigation = useNavigation();

  const [profileColor, setProfileColor] = useState(route.params.profileColor);

  const [email, setEmail] = useState(route.params.email);
  const [name, setName] = useState(route.params.name);
  const [date, setDate] = useState(new Date(route.params.date.seconds * 1000));
  const [description, setDescription] = useState(route.params.description);

  const [language, setLanguage] = useState(route.params.language);

  const [image2, setImage2] = useState(route.params.image2);
  const [changedImage, setChangedImage] = useState(false);

  const [savingChanges, setSavingChanges] = useState(false);

  const [imageLoaded, setImageLoaded] = useState(null);

  const [nameFree, setNameFree] = useState(null);

  useEffect(() => {
    const q = query(collectionGroup(db, "public"), where("name", "==", name));

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      if (
        querySnapshot?.empty ||
        querySnapshot?.docs[0]?.data()?.uid === auth.currentUser.uid
      ) {
        console.log("No matching documents found");
        setNameFree(true);
        return;
      }

      console.log("false");

      setNameFree(false);

      // querySnapshot.forEach((doc) => {
      //   console.log(`Found document with name ${name}: `, doc.data());
      // });
    });

    // Cleanup function to detach the listener when the component is unmounted or name changes
    return () => unsubscribe();
  }, [name]);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded && image2) {
      setTimeout(() => {
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }).start();
      }, 150);
    } else {
      setTimeout(() => {
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }).start();
      }, 150);
    }
  }, [imageLoaded]);

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };
  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  function calculateAge2(dob) {
    // console.log(dob, "/////////////////////////////////");
    const diff_ms = Date.now() - dob.getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  }
  const handleConfirm = (selectedDate) => {
    if (calculateAge2(selectedDate) < 15) {
      alert(language.lowAge);
    }
    setDate(selectedDate);

    hideDatePicker();
  };

  useLayoutEffect(() => {
    navigation.setOptions({
      title: "Edit your profile",
      headerBackVisible: false,
      headerLeft: null,
    });

    return () => {};
  }, [navigation]);

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
      allowsMultipleSelection: false,
    });

    if (!result.canceled) {
      setImage2(result.assets[0].uri);
      setChangedImage(true);
    }
  };

  const takePhoto = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setImage2(result.assets[0].uri);
      setChangedImage(true);
    }
  };

  const saveChanges = async () => {
    let previousImage = image2;
    if (!nameFree) {
      alert(language.usernameTaken);
      // return;
    } else {
      setSavingChanges(true);

      if (changedImage === true) {
        try {
          const nameUIDProfileImage = `/users-profile-images/${auth.currentUser.uid}/profile-image`;

          const storageRefProfileImage = ref(storage, nameUIDProfileImage);

          ///////////////////////////// Normal size profile image///////////////////////////////////
          const compressedImage = await ImageManipulator.manipulateAsync(
            image2,
            [{ resize: { width: 600, height: 600 } }],
            { format: "jpeg" }
          );

          const img = await fetch(compressedImage.uri);
          const bytes = await img.blob();

          await uploadBytesResumable(storageRefProfileImage, bytes)
            .then(() => {
              console.log("huuuuuuuuuuuuuuuuuuh");
            })
            .catch((err) => {
              setImage2(previousImage);

              Alert.alert(err.message);
            });

          await getDownloadURL(ref(storage, nameUIDProfileImage))
            .then((url) => {
              tempImageRef = url;
              setImage2(url);
            })
            .catch((err) => {
              Alert.alert(err.message);
            });
          ///////////////////////////// Normal size profile image///////////////////////////////////

          ///////////////////////////////Small image////////////////////////////////////////////////
          const nameUIDProfileImageSmall = `/users-profile-images/${auth.currentUser.uid}/profile-image-small`;
          const storageRefProfileImageSmall = ref(
            storage,
            nameUIDProfileImageSmall
          );

          const compressedImageSmall = await ImageManipulator.manipulateAsync(
            image2,
            [{ resize: { width: 300, height: 300 } }],
            { format: "jpeg" }
          );
          // console.log(compressedImage);

          const imgSmall = await fetch(compressedImageSmall.uri);
          const bytesSmall = await imgSmall.blob();

          await uploadBytesResumable(storageRefProfileImageSmall, bytesSmall)
            .then(() => {
              console.log("huuuuuuuuuuuuuuuuuuhsmaaaaaaaalll");
            })
            .catch((err) => {
              // Handle any errors;
              // setImage2(previousImage);
              // console.log("lol");

              Alert.alert(err.message);
            });

          // get the image url and then store the info into firestore
          await getDownloadURL(ref(storage, nameUIDProfileImageSmall))
            .then((url) => {
              tempImageRefSmall = url;
              // setImage2(url);
              // setImageEdit(url);
            })
            .catch((err) => {
              // Handle any errors;
              Alert.alert(err.message);
            });
          ///////////////////////////////Small image////////////////////////////////////////////////

          updateDoc(doc(db, "users/", auth.currentUser.uid), {
            profileImageRef: tempImageRef,
            profileImageRefSmall: tempImageRefSmall,
          })
            .then(() => {
              // Alert.alert("Changes Saved");
              const docRef = doc(
                db,
                "users",
                auth.currentUser.uid,
                "public",
                auth.currentUser.uid + "public"
              );

              updateDoc(docRef, {
                profileImageRef: tempImageRef,
                profileImageRefSmall: tempImageRefSmall,
              });
              setChangedImage(false);
            })
            .catch((err) => {
              // Handle any errors
              // setSaveChangesLoading(false);
              Alert.alert(err.message);
            });

          // setSaveChangesLoading(false);
        } catch (error) {
          alert("Failed to update profile image");
          setSavingChanges(false);
        }
      }
      console.log(email);

      if (name.length > 0) {
        try {
          try {
            await updateEmail(auth.currentUser, email);
          } catch (error) {
            if (error.code === "auth/requires-recent-login") {
              Alert.alert(
                language.signOutTitleEmail,
                language.signOutTitleEmail2,
                [
                  {
                    text: language.logOut,
                    onPress: () => {
                      auth.signOut();
                    },
                  },
                  {
                    text: language.cancel,
                    style: "cancel",
                  },
                ]
              );
            } else {
              Alert.alert(language.signOutText2);
            }
          }

          await updateEmail(auth.currentUser, email);
          // await updateDoc(doc(db, "users/", auth.currentUser.uid), {
          //   email: email,
          // });

          const docRef = doc(db, "users", auth.currentUser.uid);

          await updateDoc(docRef, {
            name: name,
            email: email,
            date: date,
            description: description,
          });

          const publicDataRef = doc(
            docRef,
            "public",
            auth.currentUser.uid + "public"
          );
          await updateDoc(publicDataRef, {
            name: name,
            uid: auth.currentUser.uid,

            date: date,
            description: description,
          });

          setSavingChanges(false);
          navigation.goBack();
          Alert.alert("Změny uloženy");
        } catch (error) {
          alert(error);

          setSavingChanges(false);
          try {
            await updateEmail(auth.currentUser, route.params.email);
            await updateDoc(doc(db, "users/", auth.currentUser.uid), {
              email: route.params.email,
            });
          } catch (error) {
            // Handle the error if needed
            alert(error);
          }
        }
      } else {
        setSavingChanges(false);
        alert("Set your username");
      }
    }
  };

  return (
    <>
      <ScrollView
        style={{ backgroundColor: "white", height: "100%", width: "100%" }}
      >
        <Pressable
          style={{
            position: "absolute",
            width: 45,
            height: 45,
            borderRadius: 50,
            // backgroundColor: "rgba(220,220,220,0.8)",
            top: 60,
            left: 24,
            zIndex: 10,
            justifyContent: "center",
          }}
          onPress={() => {
            if (!savingChanges) {
              navigation.goBack();
            }
          }}
        >
          <AntDesign
            name="arrowleft"
            size={31}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
          />
        </Pressable>

        {!imageLoaded && image2 && (
          <ActivityIndicator
            style={{ marginTop: "60%", position: "absolute" }}
          ></ActivityIndicator>
        )}
        <Animated.View
          style={{
            backgroundColor: "white",
            height: "100%",
            width: "100%",
            opacity: opacity1,
            marginTop: insets.top * 0.5 + 20,
          }}
        >
          <KeyboardAwareScrollView
            style={{ backgroundColor: "white", height: "100%", width: "100%" }}
            resetScrollToCoords={{ x: 0, y: 0 }}
            contentContainerStyle={{ flex: 1 }}
            scrollEnabled={false}
          >
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                // marginBottom: 50,
                marginTop: "10%",
              }}
            >
              <ImageDivEdit
                name={name}
                profileColor={profileColor}
                image2={image2}
                setImageLoaded={setImageLoaded}
              />
            </View>

            <View
              style={{
                flexDirection: "row",
                marginTop: "7%",
                width: "95%",
                alignSelf: "center",
                justifyContent: "space-evenly",
              }}
            >
              <Pressable
                onPress={() => {
                  pickImage();
                }}
                style={{
                  backgroundColor: "transparent",
                  paddingHorizontal: 10,
                  paddingVertical: 10,
                  borderRadius: 10,
                  flexDirection: "row",
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: "transparent",
                }}
              >
                <MaterialIcons name="insert-photo" size={20} color={red} />
                <Text
                  style={{
                    color: red,
                    fontWeight: "600",
                    marginLeft: 5,
                  }}
                >
                  {language.chooseImage}
                </Text>
              </Pressable>
              <Pressable
                onPress={() => {
                  takePhoto();
                }}
                style={{
                  backgroundColor: "transparent",
                  paddingHorizontal: 10,
                  paddingVertical: 10,
                  borderRadius: 10,
                  flexDirection: "row",
                  alignItems: "center",
                  borderWidth: 1,
                  borderColor: "transparent",
                }}
              >
                <Entypo name="camera" size={20} color={red} />
                <Text
                  style={{
                    color: red,
                    fontWeight: "600",
                    marginLeft: 5,
                  }}
                >
                  {language.takeImage}
                </Text>
              </Pressable>
            </View>

            <View
              style={{ width: "87%", alignSelf: "center", marginTop: "10%" }}
            >
              <Text style={{ color: "black", marginBottom: 9 }}>
                {language.name}:
              </Text>
              <TextInput
                style={{
                  // backgroundColor: "#f0f0f0",
                  paddingHorizontal: 10,
                  borderRadius: 8,
                  fontSize: 17,
                  color: "black",
                  marginBottom: 10,
                }}
                value={name}
                onChangeText={(text) => {
                  if (text.length > 18) {
                    alert("Maximum 18 characters");
                  } else {
                    setName(text.trimStart().trimEnd());
                  }
                }}
              />

              <View
                style={{
                  backgroundColor: "#ddd",
                  height: 1,
                  width: "100%",
                  marginBottom: 20,
                }}
              />
              {auth.currentUser.providerData[0].providerId !== "google.com" &&
              auth.currentUser.providerData[0].providerId !== "apple.com" ? (
                <>
                  <Text style={{ color: "black", marginBottom: 9 }}>
                    Email:
                  </Text>
                  <TextInput
                    style={{
                      // backgroundColor: "#f0f0f0",
                      paddingHorizontal: 10,
                      borderRadius: 8,
                      fontSize: 17,
                      color: "black",
                      marginBottom: 10,
                    }}
                    value={email}
                    onChangeText={(newText) => setEmail(newText)}
                  />

                  <View
                    style={{
                      backgroundColor: "#ddd",
                      height: 1,
                      width: "100%",
                    }}
                  />
                </>
              ) : null}
            </View>

            <View
              style={{
                width: "88%",
                // height: 30,
                flexDirection: "row",
                alignSelf: "center",
                marginTop: "8%",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Text style={{ fontSize: 14, fontWeight: "400" }}>
                {language.dateOfBirth}:
              </Text>

              <Pressable
                onPress={showDatePicker}
                style={({ pressed }) => ({
                  width: "45%",
                  height: 30,
                  // marginTop: 15,
                  backgroundColor: "rgba(247,245,246,1)",
                  borderRadius: 6,
                  borderWidth: 1,
                  borderColor: "rgba(215,212,212,1)",
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                })}
              >
                <Text style={{ alignSelf: "center", marginTop: 5 }}>
                  {date.toLocaleDateString(language.dateLocale, {
                    // weekday: "short",
                    day: "numeric",
                    month: "short",
                    year: "numeric",
                  })}
                </Text>
              </Pressable>
              <DateTimePickerModal
                locale={language.dateLocale}
                isVisible={isDatePickerVisible}
                mode="date"
                onConfirm={handleConfirm}
                onCancel={hideDatePicker}
                is24Hour={true}
                // maximumDate={new Date(new Date())}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignSelf: "center",
                marginTop: "7%",
              }}
            >
              <Text
                style={{
                  width: "99%",
                  fontSize: 14,
                  lineHeight: 19,
                  color: "rgba(0,0,0,0.9)", // Use dark gray instead of black for a softer look
                  marginBottom: 10, // Add some space below the text
                }}
              >
                {language.description}
              </Text>
              <TextInput
                style={{
                  alignSelf: "center",
                  width: "100%",
                  height: 60,
                  borderWidth: 1,
                  borderColor: "rgba(0,0,0,0.1)",
                  borderRadius: 5,
                  paddingHorizontal: 10,
                  // paddingVertical: 10,
                  backgroundColor: "rgba(0,0,0,0.02)",
                  fontSize: 14,
                }}
                placeholder={`...`}
                placeholderTextColor="rgba(0,0,0,0.4)"
                onChangeText={(text) => setDescription(text)}
                value={description}
                multiline={true}
              />
            </View>

            <View
              style={{
                alignItems: "center",
                // flex: 1,
                justifyContent: "center",
                marginTop:
                  auth.currentUser.providerData[0].providerId !== "google.com"
                    ? "7%"
                    : "20%",
              }}
            >
              <Pressable
                style={({ pressed }) => ({
                  backgroundColor: red, // change the color on press
                  borderRadius: 8,
                  padding: 12,
                  marginBottom: 10,
                  alignItems: "center",

                  width: "45%",
                  alignSelf: "center",
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                })}
                onPress={() => {
                  if (!savingChanges) saveChanges();
                }}
              >
                {!savingChanges && (
                  <Text
                    style={{
                      fontWeight: "600",
                      fontSize: 15,
                      color: "white",
                    }}
                  >
                    {language.saveChanges}
                  </Text>
                )}
                {savingChanges && (
                  <ActivityIndicator color={"white"}></ActivityIndicator>
                )}
              </Pressable>
            </View>
          </KeyboardAwareScrollView>
        </Animated.View>
      </ScrollView>
      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      />
    </>
  );
};

const Settings = () => {
  const route = useRoute();
  const insets = useSafeAreaInsets();

  const navigation = useNavigation();

  const [infoLoaded, setInfoLoaded] = useState(false);
  const [savingChanges, setSavingChanges] = useState(false);
  const [initialLoad, setInitialLoad] = useState(false);

  const [language2, setLanguage2] = useState(route.params);
  const [language, setLanguage] = useState(null);

  const [urls, setUrls] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      const infoCollection = collection(db, "info");
      const infoSnapshot = await getDocs(infoCollection);
      const infoData = infoSnapshot.docs.map((doc) => {
        setUrls(doc.data());
      });
    };

    fetchData();
  }, []);

  useEffect(() => {
    if (language2 === "CZ") {
      updateDoc(doc(db, "users/", auth.currentUser.uid), {
        language: "CZ",
      })
        .then(() => {})
        .catch((err) => {
          Alert.alert(err.message);
        });
      setLanguage(CZ);
      // console.log(CZ);
    } else {
      updateDoc(doc(db, "users/", auth.currentUser.uid), {
        language: "EN",
      })
        .then(() => {})
        .catch((err) => {
          Alert.alert(err.message);
        });
      setLanguage(EN);
    }
  }, [language2]);

  // console.log(route.params);

  const [notificationsAllowed, setNotificationsAllowed] = useState(null);
  const [chatNotifications, setChatNotifications] = useState(null);
  const [favCourtNotifications, setFavCourtNotifications] = useState(null);
  const [eventChangeNotifications, setEventChangeNotifications] =
    useState(null);
  const [usersChangeNotifications, setUsersChangeNotifications] =
    useState(null);

  useEffect(() => {
    const fetchInfo = async () => {
      const docRef = await doc(db, "users", auth.currentUser.uid);

      const docSnap = await getDoc(docRef);

      // console.log(docSnap);

      if (docSnap.exists) {
        const data = docSnap.data();

        console.log(data.notifications);

        setNotificationsAllowed(data.notifications.allowed);
        setChatNotifications(data.notifications.chat);
        setFavCourtNotifications(data.notifications.favCourt);
        setEventChangeNotifications(data.notifications.eventChange);
        setUsersChangeNotifications(data.notifications.usersChange);

        setTimeout(() => {
          setInfoLoaded(true);
          setInitialLoad(true);
        }, 300);
      } else {
        alert("Couldn't fetch your data");
      }
    };

    fetchInfo();
  }, []);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    setTimeout(() => {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start();
    }, 150);
  }, []);

  const saveChanges = async () => {
    let previousImage = image2;

    setSavingChanges(true);

    if (changedImage === true) {
      try {
        const nameUIDProfileImage = `/users-profile-images/${auth.currentUser.uid}/profile-image`;

        const storageRefProfileImage = ref(storage, nameUIDProfileImage);

        ///////////////////////////// Normal size profile image///////////////////////////////////
        const compressedImage = await ImageManipulator.manipulateAsync(
          image2,
          [{ resize: { width: 600, height: 600 } }],
          { format: "jpeg" }
        );

        const img = await fetch(compressedImage.uri);
        const bytes = await img.blob();

        await uploadBytesResumable(storageRefProfileImage, bytes)
          .then(() => {
            console.log("huuuuuuuuuuuuuuuuuuh");
          })
          .catch((err) => {
            setImage2(previousImage);

            Alert.alert(err.message);
          });

        await getDownloadURL(ref(storage, nameUIDProfileImage))
          .then((url) => {
            tempImageRef = url;
            setImage2(url);
          })
          .catch((err) => {
            Alert.alert(err.message);
          });
        ///////////////////////////// Normal size profile image///////////////////////////////////

        ///////////////////////////////Small image////////////////////////////////////////////////
        const nameUIDProfileImageSmall = `/users-profile-images/${auth.currentUser.uid}/profile-image-small`;
        const storageRefProfileImageSmall = ref(
          storage,
          nameUIDProfileImageSmall
        );

        const compressedImageSmall = await ImageManipulator.manipulateAsync(
          image2,
          [{ resize: { width: 300, height: 300 } }],
          { format: "jpeg" }
        );
        // console.log(compressedImage);

        const imgSmall = await fetch(compressedImageSmall.uri);
        const bytesSmall = await imgSmall.blob();

        await uploadBytesResumable(storageRefProfileImageSmall, bytesSmall)
          .then(() => {
            console.log("huuuuuuuuuuuuuuuuuuhsmaaaaaaaalll");
          })
          .catch((err) => {
            // Handle any errors;
            // setImage2(previousImage);
            // console.log("lol");

            Alert.alert(err.message);
          });

        // get the image url and then store the info into firestore
        await getDownloadURL(ref(storage, nameUIDProfileImageSmall))
          .then((url) => {
            tempImageRefSmall = url;
            // setImage2(url);
            // setImageEdit(url);
          })
          .catch((err) => {
            // Handle any errors;
            Alert.alert(err.message);
          });
        ///////////////////////////////Small image////////////////////////////////////////////////

        updateDoc(doc(db, "users/", auth.currentUser.uid), {
          profileImageRef: tempImageRef,
          profileImageRefSmall: tempImageRefSmall,
        })
          .then(() => {
            // Alert.alert("Changes Saved");
            setChangedImage(false);
          })
          .catch((err) => {
            // Handle any errors
            // setSaveChangesLoading(false);
            Alert.alert(err.message);
          });

        // setSaveChangesLoading(false);
      } catch (error) {
        alert("Failed to update profile image");
        setSavingChanges(false);
      }
    }
    console.log(email);

    try {
      await updateEmail(auth.currentUser, email);
      // await updateDoc(doc(db, "users/", auth.currentUser.uid), {
      //   email: email,
      // });

      const docRef = doc(db, "users", auth.currentUser.uid);

      await updateDoc(docRef, {
        name: name,
        email: email,
        date: date,
        description: description,
      });

      setSavingChanges(false);
      navigation.goBack();
      Alert.alert("Změny uloženy");
    } catch (error) {
      alert(error);

      setSavingChanges(false);
      try {
        await updateEmail(auth.currentUser, route.params.email);
        await updateDoc(doc(db, "users/", auth.currentUser.uid), {
          email: route.params.email,
        });
      } catch (error) {
        // Handle the error if needed
        alert(error);
      }
    }

    // try {
    //   await updateEmail(auth.currentUser, email)
    //     .then(() => {
    //       updateDoc(doc(db, "users/", auth.currentUser.uid), {
    //         email: email,
    //       });
    //     })
    //     .catch((err) => {
    //       // alert("Failed to update email 1:", err);
    //       throw new Error();
    //     });

    //   await updateDoc(doc(db, "users/", auth.currentUser.uid), {
    //     name: name,
    //     email: email,
    //     date: date,
    //     description: description,
    //   })
    //     .then(() => {
    //       setSavingChanges(false);
    //       navigation.goBack();
    //     })
    //     .catch((err) => {
    //       Alert.alert(err.message);
    //     });

    //   Alert.alert("Změny uloženy");
    // } catch (error) {
    //   alert(error);

    //   setSavingChanges(false);
    //   try {
    //     await updateEmail(auth.currentUser, route.params.email)
    //       .then(() => {
    //         updateDoc(doc(db, "users/", auth.currentUser.uid), {
    //           email: route.params.email,
    //         })
    //           .then(() => {})
    //           .catch((err) => {
    //             Alert.alert(err.message);
    //           });
    //       })
    //       .catch((err) => {
    //         alert("Failed to update email 2:", err);
    //       });
    //   } catch (error) {}
    // }
  };

  useEffect(() => {
    if (initialLoad) {
      updateDoc(doc(db, "users/", auth.currentUser.uid), {
        notifications: {
          allowed: notificationsAllowed,
          chat: chatNotifications,
          favCourt: favCourtNotifications,
          eventChange: eventChangeNotifications,
          usersChange: usersChangeNotifications,
        },
      })
        .then(() => {
          // setChangedImage(false);
        })
        .catch((err) => {
          Alert.alert(err.message);
        });
    }
  }, [
    notificationsAllowed,
    chatNotifications,
    eventChangeNotifications,
    favCourtNotifications,
    usersChangeNotifications,
  ]);

  const MyToggleComponent = ({ isOn, onToggle, size, color }) => {
    // const toggleColor = isOn ? colo : red;

    return (
      <View>
        <Pressable
          onPress={() => {
            if (isOn) {
              onToggle(false);
            } else {
              onToggle(true);
            }
          }}
        >
          <Icon
            name={isOn ? "toggle-on" : "toggle-off"}
            color={color}
            size={size}
          />
        </Pressable>
      </View>
    );
  };

  const [feedback, setFeedback] = useState("");

  const uploadFeedback = async () => {
    const coll = collection(db, `feedbacks-cz/`);

    if (feedback !== "") {
      try {
        const eventDocRef = await addDoc(coll, {
          feedback: feedback,

          user: auth.currentUser.uid,
          date: new Date(),
        });

        alert(language.feedbackSent);
        setFeedback("");
      } catch (error) {
        alert(error);
      }
    }
  };

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [bugReport, setBugReport] = useState("");

  const uploadBugReport = async () => {
    const coll = collection(db, `bugReports-cz/`);

    if (bugReport !== "") {
      try {
        const eventDocRef = await addDoc(coll, {
          bugReport: bugReport,

          user: auth.currentUser.uid,
          date: new Date(),
        });

        alert(language.reportBugSent);
        setBugReport("");
      } catch (error) {
        alert(error);
      }
    }
  };

  const [isModalVisible3, setModalVisible3] = useState(false);

  const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

  const toggleModal3 = () => {
    if (isModalVisible3) {
      Animated.timing(modalAnimatedValue3, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible3(false));
    } else {
      setModalVisible3(true);

      Animated.timing(modalAnimatedValue3, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [deleteConfirm, setDeleteConfirm] = useState("");

  const deleteAccount = async () => {
    if (deleteConfirm === "delete" || deleteConfirm === "Delete") {
      try {
        await auth.currentUser.delete();
      } catch (error) {
        if (error.code === "auth/requires-recent-login") {
          // Ask the user to sign out and sign in again
          Alert.alert(language.signOutTitle, language.signOutText, [
            {
              text: language.logOut,
              onPress: () => {
                auth.signOut();
                // Here you'd want to navigate the user to the login screen
              },
            },
            {
              text: language.cancel,
              style: "cancel",
            },
          ]);
        } else {
          Alert.alert(language.signOutText2);
        }
      }
    } else {
      alert(language.accountNotDeleted);
    }
  };

  const [isModalVisible4, setModalVisible4] = useState(false);

  const modalAnimatedValue4 = useRef(new Animated.Value(0)).current;

  const toggleModal4 = () => {
    if (isModalVisible4) {
      Animated.timing(modalAnimatedValue4, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible4(false));
      setTimeout(() => {
        setDeleteConfirm("");
      }, 100);
    } else {
      setModalVisible4(true);

      Animated.timing(modalAnimatedValue4, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  if (!language || !urls) {
    return (
      <View style={{ backgroundColor: "white", height: "100%", width: "100%" }}>
        <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
        <View
          style={{
            width: "100%",
            height: 0.8,
            backgroundColor: "lightgrey",
            position: "absolute",
            bottom: 0,
          }}
        />
      </View>
    );
  }

  return (
    <View style={{ backgroundColor: "white", height: "100%", width: "100%" }}>
      <Pressable
        style={{
          position: "absolute",
          width: 45,
          height: 45,
          borderRadius: 50,
          // backgroundColor: "rgba(220,220,220,0.8)",
          top: insets.top * 0.5 + 25,
          left: 15,
          zIndex: 10,
          justifyContent: "center",
        }}
        onPress={() => {
          if (!savingChanges) {
            navigation.goBack();
          }
        }}
      >
        <AntDesign
          name="arrowleft"
          size={31}
          color="black"
          style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
        />
      </Pressable>

      <View
        style={{
          // position: "absolute",
          width: "40%",
          // height: 50,
          alignSelf: "center",
          // zIndex: 10,
          justifyContent: "center",
          marginTop: insets.top * 0.5 + 30,
          marginBottom: 10,
        }}
      >
        <Text
          style={{
            // fontFamily: "HelveticaNeue-Medium",
            // fontFamily: "AvenirNext-DemiBold",
            // fontFamily: "Nunito-Bold",
            alignSelf: "center",
            textAlign: "center",
            fontWeight: "600",

            fontSize: 20,
            color: "rgba(0,0,0,0.87)",
            overflow: "visible",
          }}
        >
          {language.settings}
        </Text>
      </View>

      <Animated.ScrollView
        style={{
          backgroundColor: "white",
          height: "100%",
          width: "100%",
          opacity: opacity1,
        }}
        showsVerticalScrollIndicator={false}
      >
        <View
          style={{
            marginTop: "7%",
            marginBottom: "5%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            // alignItems: "center",
            // alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 17,
              fontWeight: "500",
              color: "rgba(0,0,0,0.87)",
              overflow: "visible",
              marginTop: 10,
            }}
          >
            {language.notifications}
          </Text>
          <MyToggleComponent
            isOn={notificationsAllowed}
            onToggle={setNotificationsAllowed}
            size={43}
            color={red}
          ></MyToggleComponent>
        </View>

        <View
          style={{
            width: "100%",
            height: 1,
            backgroundColor: "rgba(0,0,0,0.3)",
            alignSelf: "center",
          }}
        ></View>

        <View
          style={{
            marginTop: "4%",
            marginBottom: "4%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            alignItems: "center",
            alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              // textAlign: "center",

              fontSize: 14,
              maxWidth: "80%",
              fontWeight: "400",
              color: notificationsAllowed
                ? "rgba(0,0,0,0.87)"
                : "rgba(0,0,0,0.5)",
              overflow: "visible",
              // marginTop: 10,
            }}
          >
            {language.favCourtNotif}
          </Text>
          <MyToggleComponent
            isOn={favCourtNotifications}
            onToggle={setFavCourtNotifications}
            size={36}
            color={notificationsAllowed ? red : "grey"}
          ></MyToggleComponent>
        </View>

        <View
          style={{
            width: "100%",
            height: 1,
            backgroundColor: "rgba(0,0,0,0.3)",
            alignSelf: "center",
          }}
        ></View>

        <View
          style={{
            marginTop: "4%",
            marginBottom: "4%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            alignItems: "center",
            alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              // textAlign: "center",

              fontSize: 14,
              maxWidth: "80%",
              fontWeight: "400",
              color: notificationsAllowed
                ? "rgba(0,0,0,0.87)"
                : "rgba(0,0,0,0.5)",
              overflow: "visible",
              // marginTop: 10,
            }}
          >
            {language.newChatNotif}
          </Text>
          <MyToggleComponent
            isOn={chatNotifications}
            onToggle={setChatNotifications}
            size={36}
            color={notificationsAllowed ? red : "grey"}
          ></MyToggleComponent>
        </View>

        <View
          style={{
            width: "100%",
            height: 1,
            backgroundColor: "rgba(0,0,0,0.3)",
            alignSelf: "center",
          }}
        ></View>

        <View
          style={{
            marginTop: "4%",
            marginBottom: "4%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            alignItems: "center",
            alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              // textAlign: "center",

              fontSize: 14,
              maxWidth: "80%",
              fontWeight: "400",
              color: notificationsAllowed
                ? "rgba(0,0,0,0.87)"
                : "rgba(0,0,0,0.5)",
              overflow: "visible",
              // marginTop: 10,
            }}
          >
            {language.eventChangeNotif}
          </Text>
          <MyToggleComponent
            isOn={eventChangeNotifications}
            onToggle={setEventChangeNotifications}
            size={36}
            color={notificationsAllowed ? red : "grey"}
          ></MyToggleComponent>
        </View>

        <View
          style={{
            width: "100%",
            height: 1,
            backgroundColor: "rgba(0,0,0,0.3)",
            alignSelf: "center",
          }}
        ></View>

        <View
          style={{
            marginTop: "4%",
            marginBottom: "4%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            alignItems: "center",
            alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              // textAlign: "center",

              fontSize: 14,
              maxWidth: "80%",
              fontWeight: "400",
              color: notificationsAllowed
                ? "rgba(0,0,0,0.87)"
                : "rgba(0,0,0,0.5)",
              overflow: "visible",
              // marginTop: 10,
            }}
          >
            {language.usersChangeNotif}
          </Text>
          <MyToggleComponent
            isOn={usersChangeNotifications}
            onToggle={setUsersChangeNotifications}
            size={36}
            color={notificationsAllowed ? red : "grey"}
          ></MyToggleComponent>
        </View>

        <View
          style={{
            width: "100%",
            height: 1,
            backgroundColor: "rgba(0,0,0,0.3)",
            alignSelf: "center",
          }}
        ></View>

        <View
          style={{
            marginTop: "7%",
            marginBottom: "5%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
            // alignItems: "center",
            // alignContent: "center",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 17,
              fontWeight: "500",
              color: "rgba(0,0,0,0.87)",
              overflow: "visible",
              marginTop: 10,
            }}
          >
            {language.languageLabel}
          </Text>
          <View
            style={{
              width: 110,
              // position: "absolute?",
              // right: 15,
              zIndex: 10,
              top: 5,
            }}
          >
            <LanguageToggle
              initialLanguage={language2}
              language={language2}
              setLanguage={setLanguage2}
            ></LanguageToggle>
          </View>
        </View>

        <View
          style={{
            marginTop: "3%",
            // marginBottom: "5%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 17,
              fontWeight: "500",
              color: "rgba(0,0,0,0.87)",
              overflow: "visible",
              marginTop: 10,
            }}
          >
            {language.other}
          </Text>
        </View>

        <TouchableOpacity
          style={{
            marginTop: "5%",
            height: 50,

            width: "90%",
            alignSelf: "center",
            backgroundColor: "#DAE8FF",
            // alignContent: "center",
            // alignItems: "center",
            justifyContent: "center",
            borderRadius: 5,
            flexDirection: "row",
          }}
          activeOpacity={0.6}
          onPress={() => {
            toggleModal2();
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 15,
              fontWeight: "500",
              color: "#0390F9",
              overflow: "visible",
              // marginTop: 10,
              // padding: 5,
            }}
          >
            {language.feedback}
          </Text>
          <FontAwesome
            name="send-o"
            size={20}
            color="#0390F9"
            style={{ position: "absolute", top: 14, right: 13 }}
          />
        </TouchableOpacity>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "90%",
                    height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "3%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 17,
                      }}
                    >
                      {language.feedback}
                    </Text>
                  </View>

                  <TextInput
                    style={{
                      alignSelf: "center",
                      width: "95%",
                      height: "52%",
                      borderWidth: 1,
                      borderColor: "rgba(0,0,0,0.2)",
                      borderRadius: 7,
                      paddingHorizontal: 15,
                      paddingVertical: 15, // Remove padding
                      marginTop: "7%",
                    }}
                    value={feedback}
                    onChangeText={(newText) => {
                      setFeedback(newText);
                    }}
                    placeholder={"..."}
                    multiline={true} // Allow multiple lines of text
                  />

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "7%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(() => {
                          uploadFeedback();
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        width: "70%",
                        height: 40,
                        backgroundColor: "#DAE8FF",
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "700",
                          alignSelf: "center",
                          color: "#0390F9",
                          fontSize: 15,
                          // fontFamily: "roboto",
                        }}
                      >
                        {language.send}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <TouchableOpacity
          style={{
            marginTop: "5%",
            height: 50,

            width: "90%",
            alignSelf: "center",
            backgroundColor: "#FADEB0",
            // alignContent: "center",
            // alignItems: "center",
            justifyContent: "center",
            borderRadius: 5,
            flexDirection: "row",
          }}
          activeOpacity={0.6}
          onPress={() => {
            toggleModal3();
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 15,
              fontWeight: "500",
              color: "#F97F09",
              overflow: "visible",
              // marginTop: 10,
              // padding: 5,
            }}
          >
            {language.reportBug}
          </Text>

          <Ionicons
            name="ios-bug-outline"
            size={24}
            color="#F97F09"
            style={{ position: "absolute", top: 12, right: 10 }}
          />
        </TouchableOpacity>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible3}
          onRequestClose={toggleModal3}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal3}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue3,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "90%",
                    height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "3%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 17,
                      }}
                    >
                      {language.reportBug}
                    </Text>
                  </View>

                  <TextInput
                    style={{
                      alignSelf: "center",
                      width: "95%",
                      height: "52%",
                      borderWidth: 1,
                      borderColor: "rgba(0,0,0,0.2)",
                      borderRadius: 7,
                      paddingHorizontal: 15,
                      paddingVertical: 15, // Remove padding
                      marginTop: "7%",
                    }}
                    value={bugReport}
                    onChangeText={(newText) => {
                      setBugReport(newText);
                    }}
                    placeholder={language.reportGuide}
                    multiline={true} // Allow multiple lines of text
                  />

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "7%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal3();
                        setTimeout(() => {
                          uploadBugReport();
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        width: "70%",
                        height: 40,
                        backgroundColor: "#FADEB0",
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "700",
                          alignSelf: "center",
                          color: "#F97F09",
                          fontSize: 15,
                          // fontFamily: "roboto",
                        }}
                      >
                        {language.send}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <TouchableOpacity
          style={{
            marginTop: "5%",
            height: 50,

            width: "90%",
            alignSelf: "center",
            backgroundColor: "#FFB7C0",
            // alignContent: "center",
            // alignItems: "center",
            justifyContent: "center",
            borderRadius: 5,
            flexDirection: "row",
          }}
          activeOpacity={0.6}
          onPress={() => {
            toggleModal4();
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 15,
              fontWeight: "500",
              color: "#F70808",
              overflow: "visible",
              // marginTop: 10,
              // padding: 5,
            }}
          >
            {language.deleteAccount}
          </Text>
          <MaterialIcons
            name="delete-outline"
            size={26}
            color="#F70808"
            style={{ position: "absolute", top: 12, right: 10 }}
          />
        </TouchableOpacity>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible4}
          onRequestClose={toggleModal4}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal4}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue4,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "90%",
                    height: "33%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "3%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 17,
                      }}
                    >
                      {language.deleteAccount}
                    </Text>
                  </View>

                  <Text
                    style={{
                      textAlign: "center",
                      fontWeight: "500",
                      fontSize: 15,
                      marginTop: "5%",
                    }}
                  >
                    {language.deleteInfo}
                  </Text>

                  <TextInput
                    style={{
                      alignSelf: "center",
                      width: "95%",
                      height: 40,
                      borderWidth: 1,
                      borderColor: "rgba(0,0,0,0.2)",
                      borderRadius: 7,
                      paddingHorizontal: 15,
                      marginTop: 30,
                    }}
                    value={deleteConfirm}
                    onChangeText={(newText) => setDeleteConfirm(newText)}
                    placeholder={language.deleteAccountConfirm}
                  />

                  <View
                    style={{
                      width: "95%",
                      alignSelf: "center",

                      marginTop: "10%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal4();
                        setTimeout(() => {
                          deleteAccount();
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        width: "70%",
                        height: 40,
                        backgroundColor: "#FFB7C0",
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "800",
                          alignSelf: "center",
                          color: "#F70808",
                          fontSize: 15,
                          // fontFamily: "roboto",
                        }}
                      >
                        {language.deleteBtn}
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <View
          style={{
            marginTop: "5%",
            // marginBottom: "5%",
            flexDirection: "row",
            width: "90%",
            alignSelf: "center",
            justifyContent: "space-between",
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              // fontFamily: "Nunito-Bold",
              alignSelf: "center",
              textAlign: "center",

              fontSize: 17,
              fontWeight: "500",
              color: "rgba(0,0,0,0.87)",
              overflow: "visible",
              marginTop: 10,
            }}
          >
            Legal
          </Text>
        </View>

        <View
          style={{
            width: "90%",
            alignSelf: "center",
            // height: "20%",
            justifyContent: "center",
            // marginTop: "2%",
            marginBottom: "5%",
          }}
        >
          <Text
            style={{
              textAlign: "center",
              fontWeight: "400",
              fontSize: 15,
              color: "rgba(0,0,0,0.8)",
              marginTop: 15,
              lineHeight: 28,
            }}
          >
            Here you can read the full
            <Text
              style={{ color: "#2a99fa" }}
              onPress={() => Linking.openURL(`${urls.termsAndConditions}`)}
            >
              {" "}
              Terms and Conditions
            </Text>
            ,{" "}
            <Text
              style={{ color: "#2a99fa" }}
              onPress={() => Linking.openURL(`${urls.privacyPolicy}`)}
            >
              {" "}
              Privacy Policy
            </Text>{" "}
            and{" "}
            <Text
              style={{ color: "#2a99fa" }}
              onPress={() => Linking.openURL(`${urls.copyrightPolicy}`)}
            >
              {" "}
              Copyright Policy
            </Text>{" "}
            .
          </Text>
        </View>
      </Animated.ScrollView>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      />
    </View>
  );
};

function RootProfileStack() {
  return (
    // <NavigationContainer independent={true}>
    <Stack.Navigator
      defaultSreenOptions={Info}
      screenOptions={{
        unmountInactiveRoutes: false,
        // gestureEnabled: false,
      }}
    >
      <Stack.Screen
        name="Info"
        component={Info}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Editinfo"
        component={Editinfo}
        // options={{ lazy: false }}
        options={{ headerShown: false, lazy: false }}
      />
      <Stack.Screen
        name="Settings"
        component={Settings}
        // options={{ lazy: false }}
        options={{ headerShown: false, lazy: false }}
      />
    </Stack.Navigator>
    // </NavigationContainer>
  );
}

export default function Profile() {
  return <RootProfileStack />;
}
