import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Keyboard,
  Image,
  Modal,
  StyleSheet,
  Alert,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { useSelector } from "react-redux";
import { Feather, MaterialCommunityIcons } from "@expo/vector-icons";
import { red } from "./colors";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";

const { width } = Dimensions.get("window");

// Preload images
FastImage.preload([
  { uri: Image.resolveAssetSource(require("../images/qq-empty.png")).uri },
  { uri: Image.resolveAssetSource(require("./../images/standard.jpg")).uri },
  { uri: Image.resolveAssetSource(require("./../images/satellite.jpg")).uri },
]);

const ChooseLocation = ({ navigation, route }) => {
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [address, setAddress] = useState("");
  const [showPinpointInstructions, setShowPinpointInstructions] =
    useState(false);
  const { language } = useSelector((state) => state.language);
  const { onLocationSelected } = route.params;
  const mapRef = useRef(null);
  const insets = useSafeAreaInsets();
  const [mapType, setMapType] = useState("standard");
  const [showInstructions, setShowInstructions] = useState(true);

  const toggleMapType = useCallback(() => {
    setMapType((prevType) =>
      prevType === "standard" ? "satellite" : "standard"
    );
  }, []);

  const mapTypeImages = useMemo(
    () => ({
      standard: require("./../images/standard.jpg"),
      satellite: require("./../images/satellite.jpg"),
    }),
    []
  );

  const handleLocationSelect = useCallback(
    (event) => {
      Keyboard.dismiss();
      const { latitude, longitude } = event.nativeEvent.coordinate;
      setSelectedLocation({
        name: address,
        latitude,
        longitude,
      });
    },
    [address]
  );

  const confirmLocation = useCallback(() => {
    if (selectedLocation) {
      onLocationSelected(selectedLocation);
      navigation.goBack();
    }
  }, [selectedLocation, onLocationSelected, navigation]);

  const MarkerComponent = useMemo(() => {
    if (!selectedLocation) return null;
    return (
      <Marker
        coordinate={{
          latitude: selectedLocation.latitude,
          longitude: selectedLocation.longitude,
        }}
        anchor={{ x: 0.5, y: 1 }}
      >
        <FastImage
          source={require("../images/qq-empty.png")}
          style={{ width: 55, height: 55 }}
          resizeMode={FastImage.resizeMode.contain}
        />
      </Marker>
    );
  }, [selectedLocation]);

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          flexDirection: "row",
          position: "absolute",
          top: insets.top + 10,
          left: 20,
          right: 20,
          zIndex: 1,
        }}
      >
        <TouchableOpacity
          style={{
            backgroundColor: "white",
            borderRadius: 30,
            marginRight: 10,
            zIndex: 2,
            width: 42,
            height: 42,
            alignItems: "center",
            justifyContent: "center",
          }}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={23} color={red} />
        </TouchableOpacity>
        <View style={{ flex: 1, zIndex: 1 }}>
          <GooglePlacesAutocomplete
            placeholder="Search location"
            onPress={(data, details = null) => {
              const { lat, lng } = details.geometry.location;
              setAddress(data.description);
              setSelectedLocation({
                name: data.description,
                latitude: lat,
                longitude: lng,
              });
              mapRef.current.animateToRegion({
                latitude: lat,
                longitude: lng,
                latitudeDelta: 0.0922,
                longitudeDelta: 0.0421,
              });
              setShowPinpointInstructions(true);
            }}
            query={{
              key: "AIzaSyDeoJjMlI6YhcEWyWFLSe0r5w---lMiehk",
              language: "cs",
              components: "country:cz",
            }}
            fetchDetails={true}
            styles={{
              container: { flex: 1 },
              textInputContainer: { backgroundColor: "transparent" },
              textInput: {
                height: 40,
                color: "#5d5d5d",
                fontSize: 16,
                backgroundColor: "#ffffff",
                borderRadius: 20,
                borderWidth: 1,
                borderColor: "#e0e0e0",
                paddingHorizontal: 15,
                paddingVertical: 5,
              },
              listView: {
                backgroundColor: "#ffffff",
                borderRadius: 5,
                marginTop: 5,
                position: "absolute",
                top: 45,
                left: 0,
                right: 0,
              },
            }}
          />
        </View>
      </View>
      <MapView
        ref={mapRef}
        style={{ flex: 1 }}
        onPress={handleLocationSelect}
        initialRegion={{
          latitude: 50.0755,
          longitude: 14.4378,
          latitudeDelta: 0.0922,
          longitudeDelta: 0.0421,
        }}
        mapType={mapType}
      >
        {MarkerComponent}
      </MapView>
      <TouchableOpacity
        style={{
          position: "absolute",
          bottom: insets.bottom + 15,
          right: 10,
          backgroundColor: "white",
          borderRadius: 5,
          elevation: 3,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          width: 65,
          height: 65,
          alignItems: "center",
        }}
        onPress={toggleMapType}
      >
        <FastImage
          source={
            mapTypeImages[mapType === "standard" ? "satellite" : "standard"]
          }
          style={{ height: 60, width: 60, borderRadius: 5, marginTop: 2.5 }}
        />
        <MaterialCommunityIcons
          name="layers-outline"
          size={19}
          color={mapType !== "standard" ? "rgba(0,0,0,0.6)" : "white"}
          style={{ position: "absolute", bottom: 5, left: 5 }}
        />
        <Text style={{ color: red, fontWeight: "bold" }}>
          {mapType === "standard" ? language.satellite : language.standard}
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={{
          position: "absolute",
          bottom: insets.bottom + 18,
          left: 20,
          right: 20,
          backgroundColor: selectedLocation ? red : "rgba(0,0,0,0.3)",
          paddingVertical: 15,
          borderRadius: 25,
          alignItems: "center",
          width: "70%",
        }}
        onPress={confirmLocation}
        disabled={!selectedLocation}
      >
        <Text style={{ color: "white", fontSize: 18, fontWeight: "bold" }}>
          {language.confirm}
        </Text>
      </TouchableOpacity>

      <Modal
        animationType="fade"
        transparent={true}
        visible={showInstructions}
        onRequestClose={() => setShowInstructions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <FastImage
              source={require("../images/qq-empty.png")}
              style={{ width: 65, height: 65, marginBottom: 20 }}
              resizeMode={FastImage.resizeMode.contain}
            />
            <Text style={styles.instructionText}>
              {language.locationInstructions}
            </Text>
            <TouchableOpacity
              style={styles.button}
              onPress={() => setShowInstructions(false)}
            >
              <Text style={styles.buttonText}>{language.gotIt}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      <Modal
        animationType="fade"
        transparent={true}
        visible={showPinpointInstructions}
        onRequestClose={() => setShowPinpointInstructions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <FastImage
              source={require("../images/qq-empty.png")}
              style={{ width: 65, height: 65, marginBottom: 20 }}
              resizeMode={FastImage.resizeMode.contain}
            />
            <Text style={styles.instructionText}>
              {language.pinpointLocationInstructions}
            </Text>
            <TouchableOpacity
              style={styles.button}
              onPress={() => setShowPinpointInstructions(false)}
            >
              <Text style={styles.buttonText}>{language.gotIt}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.3)",
  },
  modalContent: {
    backgroundColor: "white",
    padding: 30,
    borderRadius: 15,
    alignItems: "center",
    maxWidth: "80%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  instructionText: {
    fontSize: 18,
    textAlign: "center",
    marginBottom: 25,
    color: "#333",
    lineHeight: 24,
  },
  button: {
    backgroundColor: red,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ChooseLocation;
