// File: screens/CourtsLeaderboardScreen.js
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { AntDesign } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import useCourtsLeaderboard from '../hooks/useCourtsLeaderboard';
import { red } from './colors';

// Import modular components
import LeaderboardPodium from '../components/leaderboard/LeaderboardPodium';
import LeaderboardContributorsList from '../components/leaderboard/LeaderboardContributorsList';
import UserStatsBar from '../components/leaderboard/UserStatsBar';
import MotivationSection from '../components/leaderboard/MotivationSection';

const { height } = Dimensions.get('window');

/**
 * Courts leaderboard screen - shows top court contributors
 * Fixed layout with scrollable contributors list
 */
const CourtsLeaderboardScreen = () => {
  const navigation = useNavigation();
  const { language } = useSelector(state => state.language);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [allContributors, setAllContributors] = useState([]);
  const [displayedContributors, setDisplayedContributors] = useState([]);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const ITEMS_PER_PAGE = 10;
  
  // Get leaderboard data from custom hook
  const { 
    topContributors, 
    userRank, 
    userContributions, 
    isLoading, 
    error,
    refreshLeaderboard 
  } = useCourtsLeaderboard();

  // Process contributors data when it changes
  useEffect(() => {
    if (topContributors.length > 0) {
      // Get top 3 for podium
      const podiumUsers = topContributors.slice(0, 3);
      // Get all other users for the list
      const otherUsers = topContributors.slice(3);
      
      // Create more mock data for testing pagination if needed
      // (remove this in production)
      const extendedUsers = [...otherUsers];
      for (let i = 0; i < 30; i++) {
        otherUsers.forEach(user => {
          extendedUsers.push({
            ...user,
            uid: `${user.uid}-${i}`,
            username: `${user.username || 'User'} ${i+1}`
          });
        });
      }
      
      setAllContributors(extendedUsers);
      setDisplayedContributors(extendedUsers.slice(0, ITEMS_PER_PAGE));
      setPage(1);
      setHasMore(extendedUsers.length > ITEMS_PER_PAGE);
      console.log(`Loaded ${extendedUsers.length} total contributors (showing first ${ITEMS_PER_PAGE})`);
    }
  }, [topContributors]);

  // Handle loading more contributors
  const handleLoadMore = async () => {
    if (!hasMore || isLoadingMore) return;
    
    console.log("Loading more contributors...");
    setIsLoadingMore(true);
    
    // Simulate a network delay for smoother UI
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const nextPage = page + 1;
    const startIndex = 0;  // Always start from the beginning
    const endIndex = nextPage * ITEMS_PER_PAGE;  // Load more items
    
    // Check if we have more items to load
    if (endIndex <= allContributors.length) {
      const newItems = allContributors.slice(startIndex, endIndex);
      setDisplayedContributors(newItems);
      setPage(nextPage);
      setHasMore(endIndex < allContributors.length);
      console.log(`Loaded ${newItems.length} contributors (page ${nextPage})`);
    } else {
      setHasMore(false);
      console.log("No more contributors to load");
    }
    
    setIsLoadingMore(false);
  };

  // Get top 3 for podium
  const podiumUsers = topContributors.slice(0, 3);
  
  // Render loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <AntDesign name="arrowleft" size={24} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>
            {language.courtsLeaderboard || "Courts Leaderboard"}
          </Text>
          
          <View style={styles.emptyRight} />
        </View>
        
        <View style={styles.loadingContainer}>
          <ActivityIndicator color={red} size="large" />
          <Text style={styles.loadingText}>
            {language.loadingLeaderboard || "Loading leaderboard..."}
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <AntDesign name="arrowleft" size={24} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>
            {language.courtsLeaderboard || "Courts Leaderboard"}
          </Text>
          
          <View style={styles.emptyRight} />
        </View>
        
        <View style={styles.errorContainer}>
          <AntDesign name="exclamationcircleo" size={40} color={red} />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={refreshLeaderboard}>
            <Text style={styles.retryButtonText}>
              {language.retry || "Retry"}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <AntDesign name="arrowleft" size={24} color="#333" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>
          {language.courtsLeaderboard || "Courts Leaderboard"}
        </Text>
        
        <TouchableOpacity 
          style={styles.refreshButton} 
          onPress={refreshLeaderboard}
        >
          <AntDesign name="reload1" size={18} color="#333" />
        </TouchableOpacity>
      </View>
      
      {/* Fixed layout with individual sections */}
      <View style={styles.content}>
        {/* User statistics section */}
        <UserStatsBar 
          userRank={userRank} 
          userContributions={userContributions}
          language={language}
        />
        
        {/* Top 3 Podium */}
        <LeaderboardPodium 
          podiumUsers={podiumUsers} 
          language={language}
        />
        
        {/* Other Contributors List - Now with pagination */}
        <View style={styles.listWrapper}>
          <LeaderboardContributorsList 
            contributors={displayedContributors}
            isLoadingMore={isLoadingMore}
            hasMore={hasMore}
            onLoadMore={handleLoadMore}
            language={language}
          />
        </View>
        
        {/* Motivation section */}
        <MotivationSection
          onAddCourtPress={() => navigation.navigate('AddCourt')}
          language={language}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    paddingBottom: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  backButton: {
    padding: 8,
  },
  refreshButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  emptyRight: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    color: '#666',
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    marginBottom: 24,
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: red,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  listWrapper: {
    flex: 1,
    marginVertical: 8,
    minHeight: 300, // Ensure there's enough space for the list
    height: '45%', // Fixed height percentage
  }
});

export default CourtsLeaderboardScreen;