import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  // Vibration,
  Keyboard,
  Pressable,
  Modal,
  TouchableHighlight,
  ViewBase,
  Easing,
  PanResponder,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import { httpsCallable } from "@react-native-firebase/functions";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import * as Notifications from "expo-notifications";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  limit,
  addDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
  writeBatch,
  serverTimestamp,
} from "@react-native-firebase/firestore";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

import { useSelector } from "react-redux";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
import { Ionicons } from "@expo/vector-icons";

import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import {
  GiftedChat,
  Bubble,
  InputToolbar,
  Composer,
  Send,
  Message,
  MessageText,
} from "react-native-gifted-chat";

import { useRoute } from "@react-navigation/native";

import { MaterialCommunityIcons } from "@expo/vector-icons";
import { EvilIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Entypo } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome5 } from "@expo/vector-icons";

import FastImage from "react-native-fast-image";
import Swiper from "react-native-swiper";

import Svg, { Ellipse, Defs, ClipPath, Rect } from "react-native-svg";

import FontAwesome6 from "@expo/vector-icons/FontAwesome6";

import { BlurView } from "expo-blur";
import { setLanguage } from "../slices/socialsSlice";

import { Swipeable } from "react-native-gesture-handler";

import * as Haptics from "expo-haptics";

const Tab = createMaterialTopTabNavigator();

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

const removeUserNotification = httpsCallable(
  functions,
  "removeUserNotificationV2"
);

const removeUser = async (game, userUid, setUsers) => {
  const gameRef = doc(db, `games/${game.id}`);

  console.log(game.id, userUid);

  try {
    await updateDoc(gameRef, {
      going: arrayRemove(userUid),
      interested: arrayRemove(userUid),
      users: arrayRemove(userUid),
    });

    // Update local state
    setUsers((prevUsers) => ({
      going: prevUsers.going.filter((user) => user.uid !== userUid),
      interested: prevUsers.interested.filter((user) => user.uid !== userUid),
    }));

    if (auth.currentUser) {
      try {
        const result = await removeUserNotification({
          userId: userUid,
          eventName: game.eventName,
        });
        console.warn("removeUserNotification result:", result);
      } catch (error) {
        Alert.alert("Error", error.message);
        console.error("Error removing user:", error);
      }
    } else {
      console.error("User not authenticated");
    }
  } catch (error) {
    console.error("Error removing user:", error);
    alert("An error occurred while removing the user. Please try again.");
  }
};

const sentFriendRequestNotificationV2 = httpsCallable(
  functions,
  "sentFriendRequestNotificationV2"
);

const ModalComp = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [slideAnimation] = useState(new Animated.Value(screenHeight));
  const fadeAnimation = useState(new Animated.Value(0))[0];
  const [deleteCounter, setDeleteCounter] = useState(0);

  const showModal = () => {
    setModalVisible(true);
    Animated.parallel([
      Animated.timing(slideAnimation, {
        toValue: screenHeight / 1.3,
        duration: 190,
        useNativeDriver: false,
      }),
      Animated.timing(fadeAnimation, {
        toValue: 0.5,
        duration: 160,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnimation, {
        toValue: screenHeight,
        duration: 190,
        useNativeDriver: false,
      }),
      Animated.timing(fadeAnimation, {
        toValue: 0,
        duration: 160,
        useNativeDriver: false,
      }),
    ]).start(() => setModalVisible(false));

    setTimeout(() => {
      setDeleteCounter(0);
    }, 100);
  };

  const deleteEvent = async () => {
    setDeleteCounter(deleteCounter + 1);
    if (deleteCounter === 3) {
      const [creatorID, docID] = game.eventID.split("_");
      const docRef = doc(db, `events/${game.creator}/user_events/`, `${docID}`);

      const batch = writeBatch(db);

      // Delete 'chat' subcollection documents
      const chatSubcollectionQuery = collection(docRef, "chat");
      const chatQuerySnapshot = await getDocs(chatSubcollectionQuery);
      chatQuerySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      // Delete 'eventUsers' subcollection documents
      const eventUsersSubcollectionQuery = collection(docRef, "eventUsers");
      const eventUsersQuerySnapshot = await getDocs(
        eventUsersSubcollectionQuery
      );
      eventUsersQuerySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      // Delete the document
      await deleteDoc(docRef);

      hideModal();
      setTimeout(() => {
        navigation.goBack();
      }, 200);
    }
  };

  if (!game.users) {
    return null;
  }

  // ModalComp return statement here (omitted for brevity)
};

const GameHeaderComp = ({ game, navigation, language, showMenu, hideMenu }) => {
  const insets = useSafeAreaInsets();
  const images = game?.courtObj?.imageRefs || [];

  const [loadingStatus, setLoadingStatus] = useState(images.map(() => true));
  const [imageLoaded, setImageLoaded] = useState(false);
  const opacity1 = useRef(new Animated.Value(0)).current;
  const opacityValues = useRef(images.map(() => new Animated.Value(0))).current;

  const chat = useSelector((state) =>
    state.socials.chats.filter((chat) => chat.gameId === game.id)
  );

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const updateLoadingStatus = (index, status) => {
    setLoadingStatus((prevLoadingStatus) => {
      const newLoadingStatus = [...prevLoadingStatus];
      newLoadingStatus[index] = status;
      return newLoadingStatus;
    });
  };

  if (!game) {
    return (
      <View style={{ marginTop: 100 }}>
        <ActivityIndicator />
      </View>
    );
  }

  return (
    <View
      style={{
        width: "100%",
        height: screenHeight / 3.1 + insets.top * 0.3,
        backgroundColor: "white",
      }}
    >
      <TouchableOpacity
        style={{
          position: "absolute",
          right: 10,
          bottom: 10,
          backgroundColor: "rgba(255,255,255,1)",
          borderRadius: 20,
          paddingVertical: 6,
          paddingLeft: 8,
          paddingRight: 8,
          zIndex: 10,
          flexDirection: "row",
          alignItems: "center",
          maxWidth: "70%",
          alignSelf: "flex-end", // This will allow the button to shrink
        }}
        onPress={() => {
          console.warn(chat[0].id);

          navigation.navigate("ChatScreen", {
            user: "game",
            chat: chat[0],
          });
        }}
        activeOpacity={0.6}
      >
        {chat[0].unreadMessages[auth.currentUser.uid] > 0 && (
          <View
            style={{
              width: chat[0].unreadMessages[auth.currentUser.uid] > 5 ? 21 : 17,
              height: 17,
              backgroundColor: red,
              borderRadius: 10,
              marginRight: 5,
              justifyContent: "center",
              alignItems: "center",
              flexShrink: 0,
            }}
          >
            <Text
              style={{
                color: "white",
                fontSize: 11,
                fontWeight: "700",
                marginLeft:
                  chat[0].unreadMessages[auth.currentUser.uid] > 5 ? 1.5 : 0.3,
              }}
            >
              {chat[0].unreadMessages[auth.currentUser.uid] > 5
                ? "5+"
                : chat[0].unreadMessages[auth.currentUser.uid]}
            </Text>
          </View>
        )}

        <View style={{ flexShrink: 1, marginRight: 8, marginLeft: 2 }}>
          {chat[0].lastUnreadMessage ? (
            <Text
              numberOfLines={1}
              style={{ flexDirection: "row", alignItems: "center" }}
            >
              <Text
                style={{
                  color: "black",
                  fontSize: 13,
                  fontWeight: "700",
                }}
              >
                {chat[0].lastUnreadMessage.user.name}:
              </Text>
              <Text
                style={{
                  color: "black",
                  fontSize: 13,
                  fontWeight: "500",
                }}
              >
                {" " + chat[0].lastUnreadMessage.text}
              </Text>
            </Text>
          ) : (
            <Text
              style={{
                color: "black",
                fontSize: 13,
                fontWeight: "700",
              }}
            >
              {language.openChat}
            </Text>
          )}
        </View>
        <View style={{ width: 24, alignItems: "center", flexShrink: 0 }}>
          <Ionicons name="chatbubble-ellipses" size={24} color={red} />
        </View>
      </TouchableOpacity>
      <Pressable
        style={{
          position: "absolute",
          width: 40,
          height: 40,
          borderRadius: 50,
          backgroundColor: "rgba(250,250,250,0.9)",
          top: 17 + insets.top * 0.6,
          right: 15,
          zIndex: 10,
          justifyContent: "center",
        }}
        onPress={showMenu}
      >
        <Entypo
          name="dots-three-horizontal"
          size={24}
          color="black"
          style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
        />
        {/* <Feather
          name="settings"
          size={26}
          color="black"
          style={{ alignSelf: "center", marginTop: 0, marginLeft: 0 }}
        /> */}
      </Pressable>
      <Pressable
        style={{
          position: "absolute",
          width: 40,
          height: 40,
          borderRadius: 50,
          backgroundColor: "rgba(250,250,250,0.9)",
          top: 17 + insets.top * 0.6,
          left: 15,
          zIndex: 10,
          justifyContent: "center",
        }}
        onPress={() => {
          navigation.goBack();
        }}
      >
        <AntDesign
          name="arrowleft"
          size={24}
          color="black"
          style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
        />
      </Pressable>

      {/* <ActivityIndicator
        style={{
          position: "absolute",
          alignSelf: "center",
          top: screenHeight / 5.6,
        }}
      /> */}
      <Animated.View
        style={{
          opacity: opacity1,
          width: "100%",
          height: "100%",
        }}
      >
        <Swiper
          loadMinimal
          loadMinimalSize={2}
          loop={false}
          paginationStyle={{
            bottom: 10, // Adjust this value to move the dots up or down
            // left: "50%", // Center the pagination container
            transform: [{ translateX: -screenWidth / 2.4 }], // Offset by half of its own width
            position: "absolute",
            flexDirection: "row",
            justifyContent: "center",
            // left: 20,
            // backgroundColor: "rgba(0,0,0,0.3)",
            // width: 50
          }}
          dot={
            <View
              style={{
                backgroundColor: "rgba(255,255,255,0.5)",
                width: 8,
                height: 8,
                borderRadius: 4,
                marginLeft: 3,
                marginRight: 3,
                marginTop: 3,
                marginBottom: 3,
              }}
            />
          }
          activeDot={
            <View
              style={{
                backgroundColor: "rgba(255,255,255,0.9)",
                width: 8,
                height: 8,
                borderRadius: 4,
                marginLeft: 3,
                marginRight: 3,
                marginTop: 3,
                marginBottom: 3,
              }}
            />
          }
        >
          {images.map((image, index) => (
            <View key={index} style={{ flex: 1, backgroundColor: "#ceefc8" }}>
              {/* {loadingStatus[index] && (
                <ActivityIndicator
                  style={{
                    position: "absolute",
                    alignSelf: "center",
                    top: screenHeight / 5.6,
                    zIndex: 1,
                  }}
                />
              )} */}
              <Animated.View
                style={{
                  opacity: opacityValues[index],
                  flex: 1,
                }}
              >
                <FastImage
                  source={{ uri: image }}
                  style={{
                    width: "100%",
                    height: "100%",
                    borderTopRightRadius: 9,
                    borderTopLeftRadius: 9,
                  }}
                  onLoadEnd={() => {
                    Animated.timing(opacityValues[index], {
                      toValue: 1,
                      duration: 250,
                      useNativeDriver: true,
                    }).start();
                    updateLoadingStatus(index, false);
                    if (index === 0) setImageLoaded(true);
                  }}
                />
              </Animated.View>
            </View>
          ))}
        </Swiper>
      </Animated.View>
    </View>
  );
};

const UserItem = ({
  user,
  game,
  language,
  navigation,
  removingUsers,
  setUsers,
}) => {
  // const navigation = useNavigation();

  const [imageLoading, setImageLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const opacity = useRef(new Animated.Value(0)).current;
  const tiltAnimation = useRef(new Animated.Value(0)).current;
  const modalAnimation = useRef(new Animated.Value(0)).current;

  const profileImageUrl = user.profileImageRef || null;

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }).start();
  }, []);

  useEffect(() => {
    if (removingUsers && user.uid !== game.creator) {
      const delay = Math.floor(Math.random() * 151);
      setTimeout(() => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(tiltAnimation, {
              toValue: 0.008,
              duration: 100,
              useNativeDriver: true,
            }),
            Animated.timing(tiltAnimation, {
              toValue: -0.008,
              duration: 100,
              useNativeDriver: true,
            }),
            Animated.timing(tiltAnimation, {
              toValue: 0.008,
              duration: 100,
              useNativeDriver: true,
            }),
            Animated.timing(tiltAnimation, {
              toValue: 0,
              duration: 100,
              useNativeDriver: true,
            }),
          ])
        ).start();
      }, delay);
    } else {
      Animated.timing(tiltAnimation, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }).start();
    }
  }, [removingUsers]);

  const tiltStyle = {
    transform: [
      { perspective: 1000 },
      {
        rotateZ: tiltAnimation.interpolate({
          inputRange: [-0.05, 0.05],
          outputRange: ["-5deg", "5deg"],
        }),
      },
    ],
  };

  const showModal = () => {
    setModalVisible(true);
    Animated.timing(modalAnimation, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const hideModal = () => {
    Animated.timing(modalAnimation, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start(() => setModalVisible(false));
  };

  // console.log(user.uid, user.status);

  if (!navigation) {
    return;
  }

  return (
    <>
      {user.name ? (
        <Animated.View
          style={[
            {
              opacity,
              marginRight: game.creator === user.uid ? 22 : 10,
              alignItems: "center",
              flexDirection: "row",
              // backgroundColor: "green",
            },
            tiltStyle,
          ]}
        >
          <Pressable
            style={{ alignItems: "center", width: 60 }}
            onPress={() => {
              if (removingUsers) {
                if (game.creator !== user.uid) {
                  showModal();
                }
              } else {
                if (auth.currentUser.uid == user.uid) {
                  console.log(navigation);
                  navigation.navigate("Profile");
                } else {
                  navigation.navigate("User", {
                    user: user,
                    navigation: navigation,
                  });
                }
              }
            }}
          >
            <View style={{ position: "relative", width: 55, height: 55 }}>
              {profileImageUrl ? (
                <>
                  <FastImage
                    source={{ uri: profileImageUrl }}
                    style={{
                      width: 60,
                      height: 60,
                      borderRadius: 30,
                    }}
                    onLoadStart={() => setImageLoading(true)}
                    onLoadEnd={() => setImageLoading(false)}
                  />
                  {imageLoading && (
                    <View
                      style={{
                        position: "absolute",
                        width: 60,
                        height: 60,
                        borderRadius: 30,
                        backgroundColor: "rgba(200, 200, 200, 0.5)",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <ActivityIndicator size="small" color="white" />
                    </View>
                  )}
                </>
              ) : (
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: user.profileColor || "#ccc",
                    justifyContent: "center",
                    alignItems: "center",
                    zIndex: 100,
                  }}
                >
                  <Text
                    style={{ color: "white", fontSize: 25, fontWeight: "bold" }}
                  >
                    {user.name ? user.name[0].toUpperCase() : "?"}
                  </Text>
                </View>
              )}
              {removingUsers && user.uid !== game.creator && (
                <>
                  <View
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: 60,
                      height: 60,
                      borderRadius: 30,
                      backgroundColor: red,
                      opacity: 0.7,
                      justifyContent: "center",
                      alignItems: "center",
                      zIndex: 100,
                    }}
                  ></View>
                  <MaterialIcons
                    name="remove-circle-outline"
                    size={28}
                    color="white"
                    style={{
                      zIndex: 100,
                      position: "absolute",
                      left: 16,
                      top: 16,
                    }}
                  />
                </>
              )}
            </View>
            <Text
              style={{
                fontSize: 12,
                marginTop: 10,
                textAlign: "center",
                maxWidth: 60,
              }}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {user.name}
            </Text>
            <View
              style={{
                position: "absolute",
                top: -2,
                right: -2,
                width: 18,
                height: 18,
                borderRadius: 9,
                backgroundColor: "white",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              {game.creator === user.uid ? (
                <View
                  style={{
                    width: 12,
                    height: 12,
                    borderRadius: 6,
                    backgroundColor: red,
                  }}
                />
              ) : (
                <View
                  style={{
                    width: 12,
                    height: 12,
                    borderRadius: 6,
                    backgroundColor: user.status == "going" ? red : "orange",
                  }}
                />
              )}
            </View>
          </Pressable>
        </Animated.View>
      ) : (
        <View
          style={[
            {
              marginRight: 22,
              alignItems: "center",
              justifyContent: "center",
              flexDirection: "row",
              // backgroundColor: "green",
              width: 60,
            },
          ]}
        >
          <ActivityIndicator
            style={{ alignSelf: "center", marginTop: 21 }}
          ></ActivityIndicator>
        </View>
      )}

      <Modal
        transparent={true}
        visible={modalVisible}
        onRequestClose={hideModal}
      >
        <TouchableWithoutFeedback onPress={hideModal}>
          <Animated.View
            style={{
              flex: 1,
              backgroundColor: "rgba(0,0,0,0.5)",
              justifyContent: "center",
              alignItems: "center",
              opacity: modalAnimation,
            }}
          >
            <BlurView
              intensity={10}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
              }}
            />
            <TouchableWithoutFeedback>
              <View
                style={{
                  backgroundColor: "white",
                  padding: 20,
                  borderRadius: 10,
                  width: "90%",
                }}
              >
                <Text
                  style={{
                    fontSize: 17,
                    fontWeight: "600",
                    marginBottom: 50,
                    marginTop: 20,
                    textAlign: "center",
                  }}
                >
                  {`Are you sure you want to remove ${user.name}?`}
                </Text>
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-around",
                  }}
                >
                  <Pressable
                    // style={{
                    //   backgroundColor: "lightgray",
                    //   padding: 10,
                    //   borderRadius: 5,
                    //   width: "40%",
                    // }}
                    style={({ pressed }) => ({
                      backgroundColor: pressed
                        ? "rgba(0, 0, 0, 0.1)" // Darker grey when pressed
                        : "rgba(0, 0, 0, 0.06)", // Light grey normally
                      borderRadius: 10,
                      paddingHorizontal: 20,
                      paddingVertical: 13,
                      alignItems: "center",
                      width: "43%",
                      flexDirection: "row",
                      justifyContent: "center",
                      transform: [{ scale: pressed ? 0.99 : 1 }],
                      height: 45,
                      alignSelf: "center",
                    })}
                    onPress={hideModal}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "600",
                        fontSize: 15,
                        color: "rgba(0, 0, 0,0.8)",
                      }}
                    >
                      Cancel
                    </Text>
                  </Pressable>
                  <Pressable
                    style={({ pressed }) => ({
                      backgroundColor: pressed
                        ? "rgba(251, 31, 80, 0.3)"
                        : "rgba(251, 31, 80, 0.17)",
                      // backgroundColor: "green",
                      borderRadius: 10,
                      paddingHorizontal: 20,
                      paddingVertical: 13,
                      alignItems: "center",
                      width: "43%",
                      flexDirection: "row",
                      justifyContent: "center",
                      transform: [{ scale: pressed ? 0.99 : 1 }],
                      height: 45,
                      alignSelf: "center",
                    })}
                    onPress={() => {
                      removeUser(game, user.uid, setUsers);
                      hideModal();
                    }}
                  >
                    <Text
                      style={{
                        color: "rgb(251, 31, 80)",
                        textAlign: "center",
                        fontWeight: "600",
                        fontSize: 15,
                      }}
                    >
                      Remove
                    </Text>
                  </Pressable>
                </View>
              </View>
            </TouchableWithoutFeedback>
          </Animated.View>
        </TouchableWithoutFeedback>
      </Modal>
    </>
  );
};

const joinGame = async (game) => {
  const gameRef = doc(db, `games/${game.id}`);

  if (game.usersLimit !== null && game.going.length >= game.usersLimit) {
    alert(language.eventFull);
  } else {
    try {
      await updateDoc(gameRef, {
        going: arrayUnion(auth.currentUser.uid),
        users: arrayUnion(auth.currentUser.uid),
      });
    } catch (error) {
      alert(error);
    }
  }
};

const interestedGame = async (game) => {
  const gameRef = doc(db, `games/${game.id}`);

  try {
    await updateDoc(gameRef, {
      interested: arrayUnion(auth.currentUser.uid),
      users: arrayUnion(auth.currentUser.uid),
    });
  } catch (error) {
    alert(error);
  }
};

const leaveGame = async (game) => {
  const gameRef = doc(db, `games/${game.id}`);

  try {
    await updateDoc(gameRef, {
      going: arrayRemove(auth.currentUser.uid),
      users: arrayRemove(auth.currentUser.uid),
    });
  } catch (error) {
    alert(error);
  }
};

const uninterestedGame = async (game) => {
  const gameRef = doc(db, `games/${game.id}`);

  try {
    await updateDoc(gameRef, {
      interested: arrayRemove(auth.currentUser.uid),
      users: arrayRemove(auth.currentUser.uid),
    });
  } catch (error) {
    alert(error);
  }
};

const switchFromInterestedToGoing = async (game) => {
  await uninterestedGame(game);
  joinGame(game);
};

const GameInfo = ({
  game,

  removingUsers,
  setRemovingUsers,
}) => {
  const insets = useSafeAreaInsets();
  const [users, setUsers] = useState({
    going: game.goingUsers || [],
    interested: game.interestedUsers || [],
  });

  const navigation = useNavigation();
  const { language } = useSelector((state) => state.language);

  const [shouldShowButtons, setShouldShowButtons] = useState(false);
  const slideAnim = useRef(new Animated.Value(20)).current;

  useEffect(() => {
    const fetchUserData = async (uid) => {
      const userDocRef = doc(db, `users/${uid}/public/${uid}public`);
      const userDocSnap = await getDoc(userDocRef);
      if (userDocSnap.exists) {
        return { ...userDocSnap.data(), uid };
      }
      return null;
    };

    const fetchAllUsers = async () => {
      if (!game.goingUsers || !game.interestedUsers) {
        const goingPromises = game.going.map(fetchUserData);
        const interestedPromises = game.interested.map(fetchUserData);

        const [goingResults, interestedResults] = await Promise.all([
          Promise.all(goingPromises),
          Promise.all(interestedPromises),
        ]);

        setUsers({
          going: goingResults.filter(Boolean),
          interested: interestedResults.filter(Boolean),
        });
      }
    };

    fetchAllUsers();
  }, [game.going, game.interested]);

  useEffect(() => {
    const shouldShow =
      !game.going.includes(auth.currentUser.uid) &&
      !game.interested.includes(auth.currentUser.uid);

    setShouldShowButtons(shouldShow);

    Animated.timing(slideAnim, {
      toValue: shouldShow ? 0 : 100,
      duration: 150,
      useNativeDriver: true,
      // easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    }).start();
  }, [game.going, game.interested, slideAnim]);

  if (!game || !language || !navigation) {
    return null;
  }

  return (
    <>
      <ScrollView
        style={{ height: "100%", width: "100%", backgroundColor: "white" }}
        showsVerticalScrollIndicator={false}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            paddingHorizontal: 15,
            paddingTop: 25,
            paddingBottom: 10,
          }}
        >
          <Text
            style={{
              fontSize: 22,
              fontWeight: "bold",
              color: "black",
              flex: 1,
            }}
          >
            {game.eventName}
          </Text>
          <View style={{ flexDirection: "column", alignItems: "flex-end" }}>
            {!game.ageLimitMin && !game.ageLimitMax ? (
              <>
                <Text
                  style={{
                    fontSize: 13,
                    color: "gray",
                    marginRight: 5,
                  }}
                >
                  No age limit
                </Text>
              </>
            ) : (
              <>
                <Text
                  style={{
                    fontSize: 11,
                    color: "gray",
                    marginRight: 4,
                  }}
                >
                  {game.ageLimitMin
                    ? `Min ${language.age}: ${game.ageLimitMin}`
                    : ""}
                </Text>
                <Text
                  style={{
                    fontSize: 11,
                    color: "gray",
                    marginRight: 4,
                  }}
                >
                  {game.ageLimitMax
                    ? `Max ${language.age}: ${game.ageLimitMax}`
                    : ""}
                </Text>
              </>
            )}
          </View>
        </View>

        <View style={{ marginTop: 8, width: "96%", alignSelf: "center" }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-evenly",
              alignItems: "center",
              backgroundColor: "white",
              borderRadius: 12,
              padding: 12,
              // borderWidth: 0.5,
              // borderColor: "rgba(0, 0, 0, 0.1)",
            }}
          >
            <View style={{ flex: 1 }}>
              <Text
                style={{
                  color: "black",
                  fontWeight: "500",
                  fontSize: 16,
                }}
              >
                {`${("0" + new Date(game.timeStart).getHours()).slice(-2)}:${(
                  "0" + new Date(game.timeStart).getMinutes()
                ).slice(-2)} - ${(
                  "0" + new Date(game.timeEnd).getHours()
                ).slice(-2)}:${(
                  "0" + new Date(game.timeEnd).getMinutes()
                ).slice(-2)}`}
              </Text>
              <Text
                style={{
                  color: "black",
                  fontWeight: "400",
                  fontSize: 13,
                  marginTop: 4,
                  paddingLeft: 1,
                }}
              >
                {`${new Date(game.date).getDate()}. ${
                  new Date(game.date).getMonth() + 1
                }. ${new Date(game.date).getFullYear()}`}
              </Text>
            </View>
            <View
              style={{
                backgroundColor: red,
                paddingVertical: 6,
                paddingHorizontal: 10,
                borderRadius: 8,
              }}
            >
              <Text
                style={{
                  color: "white",
                  fontWeight: "600",
                  fontSize: 12,
                }}
              >
                {`${language.weekdays[new Date(game.date).getDay()]
                  // .slice(0, 3)
                  .toUpperCase()}`}
              </Text>
            </View>
          </View>
        </View>

        <View
          style={{
            width: "94%",
            alignSelf: "center",
            backgroundColor: "white",
            marginTop: 15,
          }}
        >
          <Pressable
            onPress={() => {
              // console.log(navigation);
              navigation.navigate("CourtInfo", {
                selectedMarker: game.courtObj,
                language: language,
              });
            }}
            style={({ pressed }) => ({
              borderRadius: 8,
              width: "100%",
              height: 51,
              backgroundColor: "white",
              justifyContent: "center",
              transform: [{ scale: pressed ? 0.98 : 1 }],
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 1 },
              shadowOpacity: 0.1,
              shadowRadius: 2,
              elevation: 2,
              marginBottom: 10,
              borderColor: "rgba(0,0,0,0.08)",
              borderWidth: 0.5,
              flexDirection: "row",
              alignItems: "center",
              paddingLeft: 15,
              paddingRight: 10,
              // overflow: "hidden",
            })}
          >
            <View
              style={{
                position: "relative",
                width: 40,
                height: 40,
                marginRight: 10,
                // backgroundColor: "green",
              }}
            >
              <FastImage
                source={require("./../images/qq2.png")}
                style={{
                  width: 35,
                  height: 35,
                  borderRadius: 100,
                  position: "absolute",
                  zIndex: 100,
                  top: 0,
                  left: -2,
                }}
              />
              <FastImage
                source={require("./../images/elipse.png")}
                style={{
                  width: 53,
                  height: 53,
                  borderRadius: 100,
                  position: "absolute",
                  zIndex: 99,
                  bottom: -29.5,
                  left: -1.5,
                }}
              />
            </View>
            <View
              style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "flex-start",
                // backgroundColor: "yellow",
                justifyContent: "space-between",
              }}
            >
              <Text
                style={{
                  color: "black",
                  maxWidth: "85%",
                  fontWeight: "500",
                  fontSize: 13.5,
                  textAlign: "right",
                  marginRight: 7,
                  paddingTop: 3,
                }}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {game.courtObj.address}
              </Text>
              <MaterialIcons name="arrow-outward" size={25} color={red} />
            </View>
          </Pressable>
        </View>

        <View style={{ marginTop: 5 }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginHorizontal: 15,
              marginBottom: 15,
              height: 45,
              alignItems: "center",
              // backgroundColor: "green",
            }}
          >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "600",
                  color: "black",
                  marginRight: 5,
                }}
              >
                {language.going}
              </Text>
              <View
                style={{
                  backgroundColor: red,
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                }}
              >
                <Text style={{ color: "white", fontWeight: "600" }}>
                  {game.going.length}
                  {`${!game.usersLimit ? "" : `/${game.usersLimit}`}`}
                </Text>
              </View>
            </View>

            {removingUsers && (
              <Pressable
                style={({ pressed }) => ({
                  backgroundColor: pressed
                    ? "rgba(251, 31, 80, 0.3)"
                    : "rgba(251, 31, 80, 0.17)",
                  borderRadius: 10,
                  paddingHorizontal: 15,
                  // paddingVertical: 13,
                  alignItems: "center",
                  width: "30%",
                  flexDirection: "row",
                  justifyContent: "center",
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  height: 35,
                })}
                onPress={() => {
                  setRemovingUsers(false);
                }}
              >
                <Text
                  style={{
                    color: "rgb(251, 31, 80)",
                    fontWeight: "600",
                    fontSize: 13,
                  }}
                >
                  {language.done}
                </Text>
              </Pressable>
            )}
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Text
                style={{
                  fontSize: 16,
                  fontWeight: "600",
                  color: "black",
                  marginRight: 5,
                }}
              >
                {language.interested}
              </Text>
              <View
                style={{
                  backgroundColor: "orange",
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                }}
              >
                <Text style={{ color: "white", fontWeight: "600" }}>
                  {game.interested.length}
                </Text>
              </View>
            </View>
          </View>

          <FlatList
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{
              width: "100%",
              height: 100,
            }}
            contentContainerStyle={{
              paddingHorizontal: 15,
            }}
            data={[
              {
                ...users.going.find((user) => user.uid === game.creator),
                status: "creator",
              },
              { id: "separator", isSeparator: true },
              ...users.going
                .filter((user) => user.uid !== game.creator)
                .map((user) => ({ ...user, status: "going" })),
              ...users.interested.map((user) => ({
                ...user,
                status: "interested",
              })),
            ]}
            keyExtractor={(item, index) => {
              if (item.isSeparator) return "separator";
              return `${item.uid}-${item.status}`;
            }}
            renderItem={({ item, index }) => {
              if (item.isSeparator) {
                return (
                  <View
                    style={{
                      height: 35,
                      width: 1.5,
                      backgroundColor: "rgba(0,0,0,0.16)",
                      marginTop: 15,
                      marginLeft: -10,
                    }}
                  />
                );
              }

              if (!item) {
                console.warn(`Item at index ${index} is undefined`);
                return null;
              }

              return (
                <View style={{ position: "relative" }}>
                  <UserItem
                    user={item}
                    game={game}
                    language={language}
                    navigation={navigation}
                    removingUsers={removingUsers}
                    setUsers={setUsers}
                  />
                </View>
              );
            }}
          />
        </View>

        {game.description !== "" && (
          <View
            style={{
              marginTop: 0,
              width: "92%",
              alignSelf: "center",
              paddingBottom: 120,
            }}
          >
            <Text
              style={{
                color: "black",
                fontWeight: "600",
                fontSize: 15,
                // lineHeight: 20,
                marginBottom: 5,
              }}
            >
              {language.notes}:
            </Text>
            <View
              style={{
                backgroundColor: "white",
                // borderRadius: 12,
                // padding: 12,
                // borderWidth: 1,
                // borderColor: "rgba(0, 0, 0, 0.1)",
              }}
            >
              <Text
                style={{
                  color: "black",
                  fontWeight: "400",
                  fontSize: 14,
                  lineHeight: 20,
                }}
              >
                {game.description}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
      <View
        style={{
          position: "absolute",
          zIndex: 100,
          bottom: 0,
          left: 0,
          right: 0,
          overflow: "visible", // Ensure this is not 'hidden'
        }}
      >
        <Animated.View
          style={{
            position: "absolute",
            zIndex: 100,
            bottom: 0,
            left: 0,
            right: 0,
            // height: buttonContainerHeight,
            flexDirection: "row",
            justifyContent: "space-evenly",
            paddingTop: 15,
            backgroundColor: "white",
            shadowColor: "#000",
            shadowOffset: {
              width: 0,
              height: -1,
            },
            shadowOpacity: 0.07,
            shadowRadius: 2,
            elevation: 3,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            borderWidth: 0.2,
            borderColor: "rgba(0,0,0,0.2)",
            // overflow: "hidden",
            transform: [{ translateY: slideAnim }],
            height: insets.bottom + 70,
          }}
        >
          <Pressable
            style={({ pressed }) => ({
              backgroundColor: pressed
                ? "rgba(251, 31, 80, 0.3)"
                : "rgba(251, 31, 80, 0.17)",
              borderRadius: 10,
              paddingHorizontal: 20,
              paddingVertical: 13,
              alignItems: "center",
              width: "45%",
              flexDirection: "row",
              justifyContent: "center",
              transform: [{ scale: pressed ? 0.99 : 1 }],
              height: 47,
            })}
            onPress={() => {
              joinGame(game);
            }}
          >
            <Text
              style={{
                color: "rgb(251, 31, 80)",
                fontWeight: "600",
                fontSize: 15,
              }}
            >
              {language.joinGame}
            </Text>
          </Pressable>
          <Pressable
            style={({ pressed }) => ({
              backgroundColor: pressed
                ? "rgba(255,165,0,0.4)"
                : "rgba(255,165,0,0.2)",
              borderRadius: 10,
              paddingHorizontal: 20,
              paddingVertical: 13,
              alignItems: "center",
              width: "45%",
              flexDirection: "row",
              justifyContent: "center",
              transform: [{ scale: pressed ? 0.99 : 1 }],
              height: 47,
            })}
            onPress={() => {
              interestedGame(game);
            }}
          >
            <Text
              style={{
                color: "darkorange",
                fontWeight: "600",
                fontSize: 15,
              }}
            >
              {language.interested}
            </Text>
          </Pressable>
        </Animated.View>
      </View>
    </>
  );
};

export default function Game() {
  const insets = useSafeAreaInsets();
  const route = useRoute();
  const navigation = useNavigation();

  const { language } = useSelector((state) => state.language);

  const [game, setGame] = useState(null);
  const [isDeleteConfirmVisible, setIsDeleteConfirmVisible] = useState(false);

  const [removingUsers, setRemovingUsers] = useState(false);

  const reduxGame = useSelector((state) => {
    return state.games.games.find((g) => g.id === route.params?.gameId);
  });

  useEffect(() => {
    if (reduxGame) {
      setGame(reduxGame);
    } else {
      const gameRef = doc(db, "games", route.params?.gameId);
      const unsubscribe = onSnapshot(gameRef, (doc) => {
        if (doc.exists) {
          setGame({ id: doc.id, ...doc.data() });
        } else {
          setGame(null);
        }
      });

      return () => {
        unsubscribe();
      };
    }
  }, [reduxGame, route.params?.gameId]);

  const [menuVisible, setMenuVisible] = useState(false);
  const slideAnim = useRef(
    new Animated.Value(Dimensions.get("window").height * 0.08)
  ).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  const showMenu = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsDeleteConfirmVisible(false);
    setMenuVisible(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideMenu = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: Dimensions.get("window").height * 0.08,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      }),
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => setMenuVisible(false));
  };

  const deleteGame = async () => {
    if (game && game.id) {
      const gameRef = doc(db, "games", game.id);

      console.log(game.id);
      try {
        navigation.goBack();
        await deleteDoc(gameRef);
        alert(language.deletedEvent);
      } catch (error) {
        alert("Error deleting game:", error);
      }
    } else {
      console.log("No game found to delete");
    }
    hideMenu();
  };

  const pan = useRef(new Animated.ValueXY()).current;

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => isDeleteConfirmVisible,
    onPanResponderMove: Animated.event([null, { dx: pan.x }], {
      useNativeDriver: false,
    }),
    onPanResponderRelease: (_, gestureState) => {
      if (gestureState.dx > 200) {
        // Adjust this threshold as needed
        deleteGame();
      } else {
        Animated.spring(pan, {
          toValue: { x: 0, y: 0 },
          useNativeDriver: false,
        }).start();
      }
    },
  });

  const renderDeleteButton = () => (
    <Pressable
      style={({ pressed }) => ({
        backgroundColor: pressed
          ? "rgba(251, 31, 80, 0.3)"
          : "rgba(251, 31, 80, 0.17)",
        borderRadius: 10,
        paddingHorizontal: 20,
        paddingVertical: 13,
        alignItems: "center",
        width: "100%",
        flexDirection: "row",
        justifyContent: "center",
        height: 45,
        alignSelf: "center",
        overflow: "hidden",
      })}
      onPress={() => setIsDeleteConfirmVisible(true)}
    >
      <Animated.View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          transform: [{ translateX: pan.x }],
          // backgroundColor: "green",
          flex: 1,
          height: 40,
        }}
        {...panResponder.panHandlers}
      >
        {isDeleteConfirmVisible && (
          <>
            <MaterialIcons
              name="arrow-forward-ios"
              size={18}
              color="rgb(251, 31, 80)"
              style={{ marginRight: -5 }}
            />
            <MaterialIcons
              name="arrow-forward-ios"
              size={18}
              color="rgb(251, 31, 80)"
              style={{ marginRight: 5 }}
            />
          </>
        )}
        <Text
          style={{ color: "rgb(251, 31, 80)", fontWeight: "600", fontSize: 15 }}
        >
          {isDeleteConfirmVisible
            ? language.swipeToConfirm
            : language.deleteEvent}
        </Text>
      </Animated.View>
      {!isDeleteConfirmVisible && (
        <MaterialIcons
          name="delete-forever"
          size={28}
          color={red}
          style={{ position: "absolute", right: 13 }}
        />
      )}
    </Pressable>
  );

  if (!game) {
    return null;
  }

  return (
    <>
      {/* <NavigationContainer independent={true}> */}
      <Animated.View
        style={{ width: "100%", height: "100%", backgroundColor: "white" }}
      >
        <View style={{ width: "100%" }}>
          <GameHeaderComp
            game={game}
            navigation={navigation}
            language={language}
            showMenu={showMenu}
            hideMenu={hideMenu}
          />
        </View>

        <SafeAreaProvider>
          <View style={{ height: 4, backgroundColor: red }} />
          <GameInfo
            game={game}
            language={language}
            navigation={navigation}
            removingUsers={removingUsers}
            setRemovingUsers={setRemovingUsers}
          />
        </SafeAreaProvider>

        {menuVisible && (
          <Animated.View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: opacityAnim,
            }}
          >
            <BlurView style={{ flex: 1 }} intensity={25} tint="dark">
              <TouchableWithoutFeedback onPress={hideMenu}>
                <View style={{ flex: 1 }} />
              </TouchableWithoutFeedback>
            </BlurView>
            <Animated.View
              style={{
                position: "absolute",
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: "white",
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                padding: 20,
                paddingBottom: insets.bottom + 20,
                transform: [{ translateY: slideAnim }],
              }}
            >
              <Text
                style={{ fontSize: 18, fontWeight: "bold", marginBottom: 30 }}
              >
                Menu
              </Text>
              {game.creator === auth.currentUser.uid && (
                <>
                  <Pressable
                    style={({ pressed }) => ({
                      backgroundColor: pressed
                        ? "rgba(0, 0, 0, 0.1)" // Darker grey when pressed
                        : "rgba(0, 0, 0, 0.05)", // Light grey normally
                      borderRadius: 10,
                      paddingHorizontal: 20,
                      paddingVertical: 13,
                      alignItems: "center",
                      width: "100%",
                      flexDirection: "row",
                      justifyContent: "center",
                      transform: [{ scale: pressed ? 0.99 : 1 }],
                      height: 45,
                      alignSelf: "center",
                      marginBottom: 20,
                    })}
                    onPress={() => {
                      removingUsers
                        ? setRemovingUsers(false)
                        : setRemovingUsers(true);
                      hideMenu();
                    }}
                  >
                    <Text
                      style={{
                        color: "rgba(0, 0, 0,0.8)", // Black text
                        fontWeight: "600",
                        fontSize: 15,
                      }}
                    >
                      {!removingUsers
                        ? language.removeUsers
                        : language.doneRemovingUsers}
                    </Text>
                    <MaterialCommunityIcons
                      name="account-remove-outline"
                      size={26}
                      color="black"
                      style={{ position: "absolute", right: 16 }}
                    />
                  </Pressable>
                  <Pressable
                    style={({ pressed }) => ({
                      backgroundColor: pressed
                        ? "rgba(0, 0, 0, 0.1)" // Darker grey when pressed
                        : "rgba(0, 0, 0, 0.05)", // Light grey normally
                      borderRadius: 10,
                      paddingHorizontal: 20,
                      paddingVertical: 13,
                      alignItems: "center",
                      width: "100%",
                      flexDirection: "row",
                      justifyContent: "center",
                      transform: [{ scale: pressed ? 0.99 : 1 }],
                      height: 45,
                      alignSelf: "center",
                      marginBottom: 20,
                    })}
                    onPress={() => {
                      hideMenu();
                    }}
                  >
                    <Text
                      style={{
                        color: "rgba(0, 0, 0,0.8)", // Black text
                        fontWeight: "600",
                        fontSize: 15,
                      }}
                    >
                      {language.editEvent}
                    </Text>
                    <Feather
                      name="edit"
                      size={22}
                      color="black"
                      style={{ position: "absolute", right: 15 }}
                    />
                  </Pressable>
                </>
              )}
              {game.creator === auth.currentUser.uid ? (
                renderDeleteButton()
              ) : (
                <>
                  {game.going.includes(auth.currentUser.uid) && (
                    <Pressable
                      style={({ pressed }) => ({
                        backgroundColor: pressed
                          ? "rgba(251, 31, 80, 0.3)"
                          : "rgba(251, 31, 80, 0.17)",
                        borderRadius: 10,
                        paddingHorizontal: 20,
                        paddingVertical: 13,
                        alignItems: "center",
                        width: "100%",
                        flexDirection: "row",
                        justifyContent: "center",
                        transform: [{ scale: pressed ? 0.99 : 1 }],
                        height: 45,
                        alignSelf: "center",
                      })}
                      onPress={() => {
                        hideMenu();
                        leaveGame(game);
                      }}
                    >
                      <Text
                        style={{
                          color: "rgb(251, 31, 80)",
                          fontWeight: "600",
                          fontSize: 15,
                        }}
                      >
                        {language.leaveGame}
                      </Text>
                    </Pressable>
                  )}
                  {game.interested.includes(auth.currentUser.uid) && (
                    <>
                      <Pressable
                        style={({ pressed }) => ({
                          backgroundColor: pressed
                            ? "rgba(255,165,0,0.4)"
                            : "rgba(255,165,0,0.2)",
                          borderRadius: 10,
                          paddingHorizontal: 20,
                          paddingVertical: 13,
                          alignItems: "center",
                          width: "100%",
                          flexDirection: "row",
                          justifyContent: "center",
                          transform: [{ scale: pressed ? 0.99 : 1 }],
                          height: 45,
                          alignSelf: "center",
                          marginBottom: 20,
                        })}
                        onPress={() => {
                          hideMenu();
                          uninterestedGame(game);
                        }}
                      >
                        <Text
                          style={{
                            color: "darkorange",
                            fontWeight: "600",
                            fontSize: 15,
                          }}
                        >
                          {language.uninterested}
                        </Text>
                      </Pressable>
                      <Pressable
                        style={({ pressed }) => ({
                          backgroundColor: pressed
                            ? "rgba(251, 31, 80, 0.3)"
                            : "rgba(251, 31, 80, 0.17)",
                          borderRadius: 10,
                          paddingHorizontal: 20,
                          paddingVertical: 13,
                          alignItems: "center",
                          width: "100%",
                          flexDirection: "row",
                          justifyContent: "center",
                          transform: [{ scale: pressed ? 0.99 : 1 }],
                          height: 47,
                        })}
                        onPress={() => {
                          hideMenu();
                          switchFromInterestedToGoing(game);
                        }}
                      >
                        <Text
                          style={{
                            color: "rgb(251, 31, 80)",
                            fontWeight: "600",
                            fontSize: 15,
                          }}
                        >
                          {language.joinGame}
                        </Text>
                      </Pressable>
                    </>
                  )}
                </>
              )}
            </Animated.View>
          </Animated.View>
        )}
      </Animated.View>
      {/* </NavigationContainer> */}
    </>
  );
}
