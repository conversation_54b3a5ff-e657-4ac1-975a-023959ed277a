import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import CustomSwitch from "../components/CustomSwitch";
import { red } from "./colors";

const CourtFilterModal = ({ visible, onClose, onApplyFilter, language = {} }) => {
  // Surface type filter
  const [surfaceType, setSurfaceType] = useState(1); // Default to "All"
  
  // Court features filters
  const [indoorOutdoor, setIndoorOutdoor] = useState(1); // Default to "All"
  const [publicPrivate, setPublicPrivate] = useState(1); // Default to "All"
  const [fullCourt, setFullCourt] = useState(1); // Default to "All"
  const [lighting, setLighting] = useState(1); // Default to "All"
  const [lines, setLines] = useState(1); // Default to "All"
  
  // Filter option definitions
  const surfaceOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.artificial || "Artificial", value: 2 },
    { label: language.concrete || "Concrete", value: 3 },
    { label: language.other || "Other", value: 4 }
  ];
  
  const indoorOutdoorOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.outdoor || "Outdoor", value: 2 },
    { label: language.indoor || "Indoor", value: 3 }
  ];
  
  const publicPrivateOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.public || "Public", value: 2 },
    { label: language.private || "Private", value: 3 }
  ];
  
  const yesNoAllOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.yes || "Yes", value: 2 },
    { label: language.no || "No", value: 3 }
  ];

  // Reset all filters to default values
  const resetFilters = () => {
    setSurfaceType(1);
    setIndoorOutdoor(1);
    setPublicPrivate(1);
    setFullCourt(1);
    setLighting(1);
    setLines(1);
  };

  // Apply filters and close modal
  const applyFilter = () => {
    // Convert option values to labels
    const filter = {
      surfaceType: getSurfaceLabel(surfaceType),
      indoorOutdoor: getIndoorOutdoorLabel(indoorOutdoor),
      publicPrivate: getPublicPrivateLabel(publicPrivate),
      fullCourt: getYesNoAllLabel(fullCourt),
      lighting: getYesNoAllLabel(lighting),
      lines: getYesNoAllLabel(lines)
    };
    
    // Remove "All" filters to simplify processing in useCourts hook
    const activeFilters = {};
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== "All") {
        activeFilters[key] = value;
      }
    });
    
    // Only apply filters if at least one is active
    const filtersToApply = Object.keys(activeFilters).length > 0 ? activeFilters : null;
    
    console.log("Applying filter:", filtersToApply);
    onApplyFilter(filtersToApply);
    onClose();
  };
  
  // Helper functions to convert value to label
  const getSurfaceLabel = (value) => surfaceOptions.find(opt => opt.value === value)?.label || "All";
  const getIndoorOutdoorLabel = (value) => indoorOutdoorOptions.find(opt => opt.value === value)?.label || "All";
  const getPublicPrivateLabel = (value) => publicPrivateOptions.find(opt => opt.value === value)?.label || "All";
  const getYesNoAllLabel = (value) => yesNoAllOptions.find(opt => opt.value === value)?.label || "All";

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.title}>{language.filterCourts || "Filter Courts"}</Text>
          
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Surface Type */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.surfaceType || "Surface Type"}</Text>
              <CustomSwitch
                options={surfaceOptions}
                selectedValue={surfaceType}
                onValueChange={setSurfaceType}
                selectionColor={red}
              />
            </View>
            
            {/* Indoor/Outdoor */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.type || "Type"}</Text>
              <CustomSwitch
                options={indoorOutdoorOptions}
                selectedValue={indoorOutdoor}
                onValueChange={setIndoorOutdoor}
                selectionColor={red}
              />
            </View>
            
            {/* Public/Private */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.access || "Access"}</Text>
              <CustomSwitch
                options={publicPrivateOptions}
                selectedValue={publicPrivate}
                onValueChange={setPublicPrivate}
                selectionColor={red}
              />
            </View>
            
            {/* Full Court */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.fullCourt || "Full Court"}</Text>
              <CustomSwitch
                options={yesNoAllOptions}
                selectedValue={fullCourt}
                onValueChange={setFullCourt}
                selectionColor={red}
              />
            </View>
            
            {/* Lines */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.lines || "Court Lines"}</Text>
              <CustomSwitch
                options={yesNoAllOptions}
                selectedValue={lines}
                onValueChange={setLines}
                selectionColor={red}
              />
            </View>
            
            {/* Lighting */}
            <View style={styles.filterSection}>
              <Text style={styles.label}>{language.lighting || "Lighting"}</Text>
              <CustomSwitch
                options={yesNoAllOptions}
                selectedValue={lighting}
                onValueChange={setLighting}
                selectionColor={red}
              />
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={resetFilters}
              style={[styles.button, styles.resetButton]}
            >
              <Text style={styles.resetButtonText}>{language.reset || "Reset"}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.button, styles.cancelButton]}
            >
              <Text style={styles.buttonText}>{language.cancel || "Cancel"}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={applyFilter}
              style={[styles.button, styles.applyButton]}
            >
              <Text style={styles.buttonText}>{language.apply || "Apply"}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContainer: {
    width: "90%",
    maxHeight: "80%",
    backgroundColor: "white",
    padding: 20,
    borderRadius: 12,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  scrollView: {
    maxHeight: 450,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    color: red,
    textAlign: "center",
  },
  filterSection: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 8,
    color: "#333",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
  },
  button: {
    flex: 1,
    padding: 12,
    alignItems: "center",
    borderRadius: 8,
    marginHorizontal: 5,
  },
  resetButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#999",
  },
  resetButtonText: {
    color: "#666",
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#999",
  },
  applyButton: {
    backgroundColor: red,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
  },
});

export default CourtFilterModal;