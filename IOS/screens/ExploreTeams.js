import React, { useEffect, useState, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { BlurView } from "expo-blur";
import TeamItem from "./TeamItem";
import FilterModal from "./FilterModal";
import useTeams from "../hooks/useTeams";

const ExploreTeams = ({ navigation }) => {
  const { teams, fetchTeams, loading, loadingMore, hasMore } = useTeams();

  const [searchQuery, setSearchQuery] = useState("");
  const [isFilterModalVisible, setFilterModalVisible] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState({});

  const applyFilters = (filters) => {
    setFilterModalVisible(false);
    setAppliedFilters(filters);
    fetchTeams(false, filters);
  };

  const handleCloseModal = () => {
    setFilterModalVisible(false);
  };

  const resetFilters = () => {
    setAppliedFilters({});
    fetchTeams(false, {}); // Reset filters and fetch default data
    setFilterModalVisible(false); // Close modal
  };

  const renderFooter = () => {
    if (!loadingMore) return null;
    return <ActivityIndicator style={{ marginVertical: 15 }} />;
  };

  const onEndReachedHandler = () => {
    if (!loadingMore && hasMore) {
      fetchTeams(true, appliedFilters);
    }
  };

  // Memoized renderItem to avoid unnecessary re-renders
  const renderItem = useCallback(
    ({ item }) => <TeamItem team={item} navigation={navigation} />,
    [navigation]
  );

  const keyExtractor = useCallback((item) => item.id, []);

  const getItemLayout = useCallback(
    (_, index) => ({
      length: 50, // Approximate height of each item
      offset: 50 * index,
      index,
    }),
    []
  );

  // Fetch initial data
  useEffect(() => {
    fetchTeams(false, appliedFilters);
  }, []);

  return (
    <View style={styles.container}>
      {loading && (
        <BlurView intensity={80} style={StyleSheet.absoluteFill}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="red" />
          </View>
        </BlurView>
      )}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search teams..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => fetchTeams(false, { ...appliedFilters, name: searchQuery })}
        />
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setFilterModalVisible(true)}
        >
          <Text style={styles.filterButtonText}>Filter</Text>
        </TouchableOpacity>
      </View>
      {!loading && (
        <FlatList
          data={teams}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          getItemLayout={getItemLayout}
          initialNumToRender={10} // Only render the first 10 items initially
          windowSize={10} // Render extra items around the viewport
          onEndReached={onEndReachedHandler}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
        />
      )}
      <FilterModal
        isVisible={isFilterModalVisible}
        onClose={handleCloseModal}
        applyFilters={applyFilters}
        resetFilters={resetFilters}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
    paddingTop: 10,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginHorizontal: 15,
    marginBottom: 10,
  },
  searchInput: {
    flex: 1,
    height: 36,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 10,
    backgroundColor: "#f9f9f9",
    marginRight: 10,
  },
  filterButton: {
    height: 36,
    width: 90,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "red",
    borderRadius: 5,
  },
  filterButtonText: {
    color: "white",
    fontWeight: "bold",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
});

export default ExploreTeams;
