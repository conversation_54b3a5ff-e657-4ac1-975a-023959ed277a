import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Switch,
} from "react-native";
import CustomSwitch from "../components/CustomSwitch";
import { red } from "./colors";

const TeamFilterModal = ({ visible, onClose, onApplyFilter, language = {} }) => {
  // Changed to use value-based selection instead of index-based
  const [skillLevel, setSkillLevel] = useState(1); // Default to "All"
  const [teamType, setTeamType] = useState(1); // Default to "All"
  const [ageGroup, setAgeGroup] = useState(1); // Default to "All"
  const [isActive, setIsActive] = useState(false);

  // Using the same structure as in BasicInfoSection and FeaturesSection
  const skillLevelOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.beginner || "Beginner", value: 2 },
    { label: language.intermediate || "Intermediate", value: 3 },
    { label: language.advanced || "Advanced", value: 4 }
  ];
  
  const teamTypeOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.casual || "Casual", value: 2 },
    { label: language.competitive || "Competitive", value: 3 }
  ];
  
  const ageGroupOptions = [
    { label: language.all || "All", value: 1 },
    { label: language.kids || "Kids", value: 2 },
    { label: language.teens || "Teens", value: 3 },
    { label: language.adults || "Adults", value: 4 }
  ];

  const resetFilters = () => {
    // Reset all filters to default values
    setSkillLevel(1);
    setTeamType(1);
    setAgeGroup(1);
    setIsActive(false);
  };

  const applyFilter = () => {
    // Convert numeric values back to string labels for the filter logic
    const filter = {
      skillLevel: skillLevelOptions.find(opt => opt.value === skillLevel)?.label,
      type: teamTypeOptions.find(opt => opt.value === teamType)?.label,
      ageGroup: ageGroupOptions.find(opt => opt.value === ageGroup)?.label,
      isActive
    };
    
    onApplyFilter(filter);
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.title}>{language.filterTeams || "Filter Teams"}</Text>

          <Text style={styles.label}>{language.skillLevel || "Skill Level"}</Text>
          <CustomSwitch
            options={skillLevelOptions}
            selectedValue={skillLevel}
            onValueChange={setSkillLevel}
            selectionColor={red}
          />

          <Text style={styles.label}>{language.teamType || "Team Type"}</Text>
          <CustomSwitch
            options={teamTypeOptions}
            selectedValue={teamType}
            onValueChange={setTeamType}
            selectionColor={red}
          />

          <Text style={styles.label}>{language.ageGroup || "Age Group"}</Text>
          <CustomSwitch
            options={ageGroupOptions}
            selectedValue={ageGroup}
            onValueChange={setAgeGroup}
            selectionColor={red}
          />

          <View style={styles.switchContainer}>
            <Text style={styles.label}>{language.activeTeamsOnly || "Active Teams Only"}</Text>
            <Switch 
              value={isActive} 
              onValueChange={setIsActive}
              trackColor={{ false: '#d3d3d3', true: `${red}80` }}
              thumbColor={isActive ? red : '#f4f3f4'}
            />
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              onPress={resetFilters}
              style={[styles.button, styles.resetButton]}
            >
              <Text style={styles.resetButtonText}>{language.reset || "Reset"}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.button, styles.cancelButton]}
            >
              <Text style={styles.buttonText}>{language.cancel || "Cancel"}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={applyFilter}
              style={[styles.button, styles.applyButton]}
            >
              <Text style={styles.buttonText}>{language.apply || "Apply"}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContainer: {
    width: "90%",
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    color: red,
    textAlign: "center",
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    marginTop: 15,
    marginBottom: 8,
    color: "#333",
  },
  switchContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginVertical: 15,
    paddingVertical: 5,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 25,
  },
  button: {
    flex: 1,
    padding: 12,
    alignItems: "center",
    borderRadius: 8,
    marginHorizontal: 5,
  },
  resetButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#999",
  },
  resetButtonText: {
    color: "#666",
    fontWeight: "600",
  },
  cancelButton: {
    backgroundColor: "#999",
  },
  applyButton: {
    backgroundColor: red,
  },
  buttonText: {
    color: "white",
    fontWeight: "600",
  },
});

export default TeamFilterModal;