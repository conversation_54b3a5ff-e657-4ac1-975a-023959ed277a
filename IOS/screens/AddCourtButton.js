import React from "react";
import { Image, Pressable } from "react-native";

import { red } from "./colors";

import { Entypo } from "@expo/vector-icons";

const AddCourtButton = React.memo(
  ({ navigation, selectedMarker, language }) => {
    return (
      <Pressable
        style={{
          position: "absolute",
          width: 60,
          height: 60,
          backgroundColor: "white",
          bottom: 5,
          right: 5,
          borderRadius: 100,
          justifyContent: "center",
          borderColor: "rgba(0,0,0,0.17)",
          borderWidth: 0.6,
          // opacity: selectedMarker ? 0 : 1,
          zIndex: selectedMarker ? -1 : 1,
        }}
        activeOpacity={0.6}
        onPress={() => {
          navigation.navigate("AddCourt", language);
        }}
      >
        <Image
          style={{
            height: 39,
            width: 39,
            alignSelf: "center",
            marginLeft: -12,
            marginBottom: -5,
          }}
          source={require("./../images/qq2.png")}
        ></Image>
        <Entypo
          name="plus"
          size={24}
          color={red}
          style={{ position: "absolute", right: 4, top: 7 }}
        />
      </Pressable>
    );
  }
);
export default AddCourtButton;
