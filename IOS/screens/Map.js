// File: screens/Map.js
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Image,
  ImageBackground,
  Text,
  StyleSheet,
  TouchableOpacity,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { useNavigation } from "@react-navigation/native";
import { TapGestureHandler } from "react-native-gesture-handler";
import { useSelector } from "react-redux";

import AddCourtButton from "./AddCourtButton";
import MarkerInfoComp from "./MarkerInfoComp";
import MapTabSwitch from "../components/map/MapTabSwitch";
import MapButton from "./MapButton";
import LeaderboardButton from "../components/map/LeaderboardButton";
import EnhancedCourtFilterModal from "../components/EnhancedCourtFilterModal";
import TeamFilterModal from "./TeamFilterModal";
import { useCourts } from "../hooks/useCourts";
import { useTeams } from "../hooks/useTeams";
import { red } from "./colors";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import TeamInfoComp from "./TeamInfoComp";
import ClusteredMapView from "react-native-map-clustering";

const iconSize = 42;
const iconSizeSelected = 47;

function Map() {
  const navigation = useNavigation();
  const mapRef = useRef(null);
  const { language } = useSelector((state) => state.language);
  const user = useSelector((state) => state.socials.userData);

  const [filterType, setFilterType] = useState("courts");
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState(null);
  const [selectedMarker, setSelectedMarker] = useState(null);
  const [filteredCount, setFilteredCount] = useState(null);

  const { courts, filteredCourts } = useCourts(
    user,
    filterType,
    appliedFilters
  );
  const { filteredTeams, fetchTeams } = useTeams(appliedFilters);

  useEffect(() => {
    if (appliedFilters) {
      if (filterType === "courts") {
        setFilteredCount(filteredCourts.length);
      } else if (filterType === "teams") {
        setFilteredCount(filteredTeams.length);
      }
    } else {
      setFilteredCount(null);
    }
  }, [filteredCourts, filteredTeams, appliedFilters, filterType]);

  const handleApplyFilters = (filters) => {
    setAppliedFilters(filters);
    setFilterVisible(false);
  };

  const handleClearFilters = () => {
    setAppliedFilters(null);
  };

  useEffect(() => {
    fetchTeams(false, appliedFilters);
  }, []);

  const renderMarkerIcon = (item, isSelected, isTeam = false) => {
    const size = isSelected ? iconSizeSelected : iconSize;
    const color = "red";

    if (isTeam) {
      return (
        <MaterialCommunityIcons
          name="account-group"
          size={size}
          color={color}
        />
      );
    }

    return item.eventsCount === 0 ? (
      <Image
        source={
          item.isFavourite
            ? require("./../images/qq-star5.png")
            : require("./../images/qq5.png")
        }
        style={{ width: size, height: size }}
      />
    ) : (
      <ImageBackground
        source={
          item.isFavourite
            ? require("./../images/qq-empty-star5.png")
            : require("./../images/qq-empty5.png")
        }
        style={{ width: size, height: size }}
      >
        <Text style={styles.eventText}>{item.eventsCount}</Text>
      </ImageBackground>
    );
  };

  // Filter options for the tab switch
  const tabOptions = [
    { label: language.courts || "Courts", value: 1 },
    { label: language.teams || "Teams", value: 2 },
  ];

  // Determine if filters are active to style the filter button
  const isFilterActive = appliedFilters !== null;

  return (    
      <View style={styles.container}>
        <View style={styles.switchContainer}>
          <MapTabSwitch
            options={tabOptions}
            selectedValue={filterType === "courts" ? 1 : 2}
            onValueChange={(value) =>
              setFilterType(value === 1 ? "courts" : "teams")
            }
            selectionColor={red}
          />
        </View>

      <ClusteredMapView
        ref={mapRef}
        onPress={() => setSelectedMarker(null)}
        style={styles.map}
        clusterColor={red}
        clusterTextColor="white"
        clusterFontFamily="System"
        clusterFontSize={14}
        clusterBorderColor="white"
        clusterBorderWidth={2}
        initialRegion={{
          latitude: user.centerCoord.latitude,
          longitude: user.centerCoord.longitude,
          latitudeDelta: 0.922 * 0.7,
          longitudeDelta: 0.421 * 0.7,
        }}
      >
        {filterType === "courts" &&
          filteredCourts.map((court) => (
            <Marker
              key={court.id}
              coordinate={{
                latitude: court.coords[1],
                longitude: court.coords[0],
              }}
              onPress={() => setSelectedMarker(court)}
            >
              <TapGestureHandler onEnded={() => setSelectedMarker(court)}>
                <View style={styles.markerWrapper}>
                  {renderMarkerIcon(court, selectedMarker === court)}
                </View>
              </TapGestureHandler>
            </Marker>
          ))}

        {filterType === "teams" &&
          filteredTeams.map((team) =>
            team.coordinates ? (
              <Marker
                key={team.id}
                coordinate={{
                  latitude: team.coordinates.latitude,
                  longitude: team.coordinates.longitude,
                }}
                onPress={() => setSelectedMarker(team)}
              >
                <TapGestureHandler onEnded={() => setSelectedMarker(team)}>
                  <View style={styles.markerWrapper}>
                    {renderMarkerIcon(team, selectedMarker === team, true)}
                  </View>
                </TapGestureHandler>
              </Marker>
            ) : null
          )}
      </ClusteredMapView>

      {selectedMarker && (
        <View style={styles.infoContainer}>
          {filterType === "courts" ? (
            <MarkerInfoComp
              selectedMarker={selectedMarker}
              setSelectedMarker={setSelectedMarker}
              language={language}
            />
          ) : (
            <TeamInfoComp
              selectedMarker={selectedMarker}
              setSelectedMarker={setSelectedMarker}
              language={language}
            />
          )}
        </View>
      )}

        {/* Filter button with active state */}
        <MapButton
          onPress={() => setFilterVisible(true)}
          style={[
            { zIndex: selectedMarker ? -1 : 1 },
            // No need to pass backgroundColor here as it's handled in the MapButton component
          ]}
          iconName="filter"
          iconColor={isFilterActive ? "white" : red}
          backgroundColor={isFilterActive ? red : "white"}
          imageSource={require("./../images/qq2.png")}
        />

        {/* Filter badge showing count when filters are active */}
        {isFilterActive && (
          <View style={styles.filterBadge}>
            <Text style={styles.filterBadgeText}>
              {filteredCount}{" "}
              {filterType === "courts"
                ? language.courtsFound || "courts found"
                : language.teamsFound || "teams found"}
            </Text>
            <TouchableOpacity
              style={styles.clearFilterButton}
              onPress={handleClearFilters}
            >
              <Text style={styles.clearFilterButtonText}>
                {language.clear || "Clear"}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Leaderboard Button */}
        <LeaderboardButton
          selectedMarker={selectedMarker}
          style={{ zIndex: selectedMarker ? -1 : 1 }}
        />

        <AddCourtButton
          navigation={navigation}
          selectedMarker={selectedMarker}
          language={language}
        />

        {filterType === "courts" ? (
          <EnhancedCourtFilterModal
            visible={isFilterVisible}
            onClose={() => setFilterVisible(false)}
            onApplyFilter={handleApplyFilters}
            appliedFilters={appliedFilters} // Add this line!  
            language={language}
          />
        ) : (
          <TeamFilterModal
            visible={isFilterVisible}
            onClose={() => setFilterVisible(false)}
            onApplyFilter={handleApplyFilters}
          />
        )}
      </View>    
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "green",
  },
  switchContainer: {
    position: "absolute",
    top: 50,
    left: 0,
    right: 0,
    zIndex: 10,
    alignItems: "center",
  },
  map: {
    flex: 1,
  },
  markerWrapper: {
    marginBottom: 30,
  },
  eventText: {
    position: "absolute",
    color: red,
    fontWeight: "600",
    fontSize: 16,
    top: 8.5,
    left: 16,
  },
  infoContainer: {
    position: "absolute",
    bottom: 15,
    width: "95%",
    alignSelf: "center",
  },
  // New styles for filter indicators
  filterBadge: {
    position: "absolute",
    top: 100,
    alignSelf: "center",
    backgroundColor: "rgba(0,0,0,0.7)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    flexDirection: "row",
    alignItems: "center",
    zIndex: 10,
  },
  filterBadgeText: {
    color: "white",
    fontWeight: "500",
    fontSize: 14,
    marginRight: 8,
  },
  clearFilterButton: {
    backgroundColor: red,
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 14,
  },
  clearFilterButtonText: {
    color: "white",
    fontWeight: "600",
    fontSize: 12,
  },
});

export default Map;
