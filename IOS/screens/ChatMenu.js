import React, {
  useEffect,
  useLayoutEffect,
  useState,
  create<PERSON>ontext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import Spinner from "react-native-spinkit";
import { BlurView } from "expo-blur";
import { Octicons, Entypo } from "@expo/vector-icons";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";

import ImageViewer from "react-native-image-zoom-viewer";
import TeamProfileIcon from "./../assets/TeamProfileIcon";

const calculateAge = (dateString) => {
  const dateSeconds = Math.floor(new Date(dateString).getTime() / 1000);
  const birthDate = new Date(dateSeconds * 1000);
  const ageDifMs = Date.now() - birthDate.getTime();
  const ageDate = new Date(ageDifMs);
  return Math.abs(ageDate.getUTCFullYear() - 1970);
};

function formatDate(dateString) {
  const messageDate = new Date(dateString.seconds * 1000);
  const currentDate = new Date();

  // Calculate the difference in days
  const diffTime = Math.abs(currentDate - messageDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Format the time if the message was sent today
  if (
    diffDays === 1 &&
    messageDate.toDateString() === currentDate.toDateString()
  ) {
    return messageDate.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // If the message was sent within the last 7 days
  if (diffDays < 7) {
    return `${diffDays} days ago`;
  }

  // If the message was sent more than 7 days ago
  return messageDate.toLocaleDateString([], {
    day: "2-digit",
    month: "2-digit",
  });
}

const RenderImageItem = React.memo(({ item, imageSize }) => {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <View
      style={{
        width: imageSize,
        height: imageSize,
        marginRight: 2,
        marginBottom: 2,
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      {isLoading && (
        <Spinner
          type="Circle"
          color="rgba(0,0,0,0.6)"
          size={20}
          style={{
            position: "absolute",
            zIndex: 1,
            opacity: 0.4,
          }}
        />
      )}
      <FastImage
        source={{ uri: item.image }}
        style={{
          width: "100%",
          height: "100%",
        }}
        onLoad={() => setIsLoading(false)}
      />
    </View>
  );
});

const ChatMenu = ({ route, navigation }) => {
  const insets = useSafeAreaInsets();
  const { chat, user } = route.params;
  const { language } = useSelector((state) => state.language);

  const screenWidth = Dimensions.get("window").width;
  const screenHeight = Dimensions.get("window").height;
  const imageSize = (screenWidth - 4) / 3;

  const [galleryImages, setGalleryImages] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageModalVisible, setImageModal] = useState(false);
  const imageModalValue = useRef(new Animated.Value(0)).current;
  const [showZoomImage, setShowZoomImage] = useState(false);
  const [shareLoading, setShareLoading] = useState(false);

  const [chatUsers, setChatUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(true);

  useEffect(() => {
    const fetchImages = async () => {
      setIsLoading(true);
      const messagesRef = collection(db, `chats/${chat.id}/messages`);
      const imageQuery = query(messagesRef, where("image", "!=", null));

      try {
        const querySnapshot = await getDocs(imageQuery);
        const images = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        }));
        setGalleryImages(images);
      } catch (error) {
        console.error("Error fetching images:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchImages();
  }, [chat.id]);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoadingUsers(true);
      const userPromises = chat.users.map(async (userId) => {
        const userDocRef = doc(db, `users/${userId}/public/${userId}public`);
        const userDocSnap = await getDoc(userDocRef);
        if (userDocSnap.exists) {
          return { id: userId, ...userDocSnap.data() };
        }
        return null;
      });

      const users = await Promise.all(userPromises);
      setChatUsers(users.filter(Boolean));
      setLoadingUsers(false);
    };

    fetchUsers();
  }, [chat.users]);

  const toggleImageModal = useCallback(() => {
    if (imageModalVisible) {
      Animated.timing(imageModalValue, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setImageModal(false));
    } else {
      setImageModal(true);
      Animated.timing(imageModalValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [imageModalVisible, imageModalValue]);

  const renderUserItem = (item) => (
    <Pressable
      key={item.id}
      style={{ flexDirection: "row", alignItems: "center", padding: 10 }}
      onPress={() => {
        item.id === auth.currentUser.uid
          ? navigation.navigate("Profile")
          : navigation.navigate("User", { user: item });
      }}
    >
      {item.profileImageRef ? (
        <FastImage
          source={{ uri: item.profileImageRef }}
          style={{ width: 50, height: 50, borderRadius: 25 }}
        />
      ) : (
        <View
          style={{
            width: 50,
            height: 50,
            borderRadius: 25,
            backgroundColor: item.profileColor || "#B5CCE7",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Text style={{ color: "white", fontSize: 21, fontWeight: "bold" }}>
            {item.name.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}
      <Text style={{ marginLeft: 10, fontSize: 16 }}>{item.name}</Text>

      <MaterialIcons
        name="keyboard-arrow-right"
        size={24}
        color="rgba(0,0,0,0.7)"
        style={{ position: "absolute", right: 10 }}
      />
    </Pressable>
  );

  return (
    <ScrollView
      style={{ flex: 1, backgroundColor: "white", paddingTop: insets.top }}
      showsVerticalScrollIndicator={false}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          height: 55,
        }}
      >
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={{
            position: "absolute",
            left: 20,
            top: 15,
          }}
        >
          <MaterialIcons
            name="arrow-back-ios"
            size={26}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 3 }}
          />
        </TouchableOpacity>

        <Text
          style={{
            fontSize: 17,
            fontWeight: "600",
            color: "black",
          }}
        >
          {language.chatMenu}
        </Text>
      </View>

      {chat.type === "game" ? (
        <View style={{ padding: 15, marginTop: 0 }}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <FastImage
              source={{ uri: chat.gameInfo.courtInfo.imageRefs[0] }}
              style={{
                width: screenWidth * 0.34,
                height: screenWidth * 0.29,
                borderRadius: 10,
              }}
            />
            <View
              style={{
                marginLeft: 15,
                flex: 1,
                flexDirection: "column",
                justifyContent: "space-evenly",
                height: screenWidth * 0.29,
              }}
            >
              <View>
                <Text
                  style={{ fontSize: 19, fontWeight: "600" }}
                  numberOfLines={1}
                >
                  {chat.gameInfo.name}
                </Text>
                <Text
                  style={{ fontSize: 13, color: "gray", marginTop: 5 }}
                  numberOfLines={2}
                >
                  {chat.gameInfo.courtInfo.address}
                </Text>
                <Text
                  style={{ fontSize: 13, color: "gray", marginTop: 5 }}
                  numberOfLines={2}
                >
                  {new Date(chat.gameInfo.date).toLocaleDateString()}
                </Text>
              </View>

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  alignSelf: "flex-start",
                  paddingVertical: 6,
                  paddingHorizontal: 15,
                  width: "90%",
                  borderColor: red,
                  borderWidth: 1,
                  borderRadius: 13,
                  justifyContent: "space-between",
                  bottom: -5,
                  left: -2,
                }}
                onPress={() =>
                  navigation.navigate("Game", { gameId: chat.gameId })
                }
              >
                <Text
                  style={{
                    color: red,
                    fontSize: 14,
                    fontWeight: "600",
                    marginRight: 5,
                  }}
                >
                  {language.navigateGame}
                </Text>
                <AntDesign name="arrowright" size={19} color={red} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      ) : chat.type === "team" ? (
        <View style={{ alignItems: "center", marginTop: 20 }}>
          {/* <View
            style={{
              width: 110,
              height: 110,
              borderRadius: 25,
              backgroundColor: chat.teamInfo.color,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text style={{ color: "white", fontSize: 45, fontWeight: "bold" }}>
              {chat.teamInfo.name.charAt(0).toUpperCase()}
            </Text>
          </View> */}
          <TeamProfileIcon team={chat.teamInfo} size={110} />
          <Text style={{ fontSize: 22, fontWeight: "600", marginTop: 15 }}>
            {chat.teamInfo.name}
          </Text>
        </View>
      ) : (
        <View style={{ alignItems: "center", marginTop: 20 }}>
          {user.profileImageRef ? (
            <FastImage
              source={{ uri: user.profileImageRef }}
              style={{
                width: 110,
                height: 110,
                borderRadius: 60,
                borderColor: "rgba(0,0,0,0.5)",
                borderWidth: 0.3,
              }}
            />
          ) : (
            <View
              style={{
                width: 100,
                height: 100,
                borderRadius: 50,
                backgroundColor: user.profileColor || "#B5CCE7",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{ color: "white", fontSize: 45, fontWeight: "bold" }}
              >
                {user.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <Text style={{ fontSize: 22, fontWeight: "600", marginTop: 10 }}>
            {user.name}
          </Text>
          <Text style={{ fontSize: 16, color: "gray", marginTop: 5 }}>
            {calculateAge(user.date)} {language.yearsOld}
          </Text>
        </View>
      )}

      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-around",
          marginTop: 30,
          marginBottom: 20,
          paddingHorizontal: 20,
        }}
      >
        {chat.type === "private" && (
          <TouchableOpacity
            style={{
              alignItems: "center",
            }}
            onPress={() => {
              navigation.navigate("User", { user });
            }}
          >
            <Feather name="user" size={28} color="black" />
            <Text
              style={{
                fontSize: 12,
                color: "black",
                marginTop: 7,
              }}
            >
              {language.profile}
            </Text>
          </TouchableOpacity>
        )}
        {chat.type === "team" && (
          <TouchableOpacity
            style={{
              alignItems: "center",
            }}
            onPress={() => {
              navigation.navigate("TeamDetails", { teamId: chat.teamId });
            }}
          >
            {/* <Ionicons name="people" size={28} color="black" /> */}
            {/* <Ionicons name="people-outline" size={28} color="black" /> */}
            {/* <FontAwesome5 name="users" size={26} color="black" /> */}
            <Ionicons name="people-circle-outline" size={28} color="black" />
            <Text
              style={{
                fontSize: 12,
                color: "black",
                marginTop: 7,
              }}
            >
              {language.team}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={{
            alignItems: "center",
          }}
        >
          <Ionicons name="search" size={27} color="black" />
          <Text
            style={{
              fontSize: 12,
              color: "black",
              marginTop: 8,
            }}
          >
            {language.search}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            alignItems: "center",
          }}
        >
          <Feather name="bell" size={27} color="black" />
          <Text
            style={{
              fontSize: 12,
              color: "black",
              marginTop: 7,
            }}
          >
            {language.mute}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            alignItems: "center",
          }}
        >
          <Entypo name="dots-three-horizontal" size={27} color="black" />
          <Text
            style={{
              fontSize: 12,
              color: "black",
              marginTop: 6,
            }}
          >
            {language.settings}
          </Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={{
          flexDirection: "column",
          justifyContent: "space-between",
          paddingTop: 15,
          borderWidth: 0.35,
          borderColor: "rgba(0,0,0,0.3)",
          marginHorizontal: 15,
          borderRadius: 10,
          marginTop: 10,
          overflow: "hidden",
        }}
        activeOpacity={0.6}
        onPress={() =>
          navigation.navigate("ChatMedia", { images: galleryImages, chat })
        }
      >
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            marginBottom: 12,
            paddingHorizontal: 20,
          }}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Ionicons
              name="images-outline"
              size={22}
              color="black"
              style={{ marginRight: 10 }}
            />
            <Text style={{ fontSize: 15, fontWeight: "500" }}>Media</Text>
            {galleryImages.length > 0 && (
              <Text style={{ fontSize: 14, color: "gray", marginLeft: 10 }}>
                ({galleryImages.length})
              </Text>
            )}
          </View>
          <View style={{ flexDirection: "row", paddingBottom: 4 }}>
            <Text style={{ fontWeight: "500", color: "rgba(0,0,0,0.7)" }}>
              {language.more}
            </Text>
            <AntDesign
              name="right"
              size={15}
              color="rgba(0,0,0,0.7)"
              style={{ paddingLeft: 3, paddingTop: 1 }}
            />
          </View>
        </View>
        <View
          style={{
            width: "100%",
            height: 0.35,
            backgroundColor: "rgba(0,0,0,0.3)",
          }}
        />

        {galleryImages.length > 0 ? (
          <View
            style={{
              flexDirection: "row",
              marginTop: 0,
            }}
          >
            {galleryImages.slice(0, 3).map((image, index) => (
              <Pressable
                key={index}
                onPress={() => {
                  setSelectedImage(image);
                  toggleImageModal();
                }}
              >
                <FastImage
                  source={{ uri: image.image }}
                  style={{
                    width: (screenWidth - 30) / 3,
                    height: (screenWidth - 30) / 3,
                    marginRight: index < 2 ? 1 : 0,
                    borderBottomLeftRadius: index === 0 ? 10 : 0,
                    borderBottomRightRadius: index === 2 ? 10 : 0,
                  }}
                  resizeMode={FastImage.resizeMode.cover}
                />
              </Pressable>
            ))}
            {galleryImages.length < 3 &&
              [...Array(3 - galleryImages.length)].map((_, index) => (
                <View
                  key={index + galleryImages.length}
                  style={{
                    width: (screenWidth - 30) / 3,
                    height: (screenWidth - 30) / 3,
                    marginRight: index < 2 ? 1 : 0,
                    backgroundColor: "rgba(200,200,200,0.1)",
                    borderBottomLeftRadius:
                      galleryImages.length === 0 && index === 0 ? 10 : 0,
                    borderBottomRightRadius:
                      index === 2 - galleryImages.length ? 10 : 0,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Ionicons
                    name="image-outline"
                    size={25}
                    color="rgba(0,0,0,0.3)"
                  />
                </View>
              ))}
          </View>
        ) : (
          <Text
            style={{ alignSelf: "center", paddingVertical: 15, color: "gray" }}
          >
            {language.noImagesInChat}
          </Text>
        )}
      </TouchableOpacity>

      <View
        style={{
          marginHorizontal: 15,
          marginBottom: 20,
          marginTop: 25,
          paddingBottom: 80,
        }}
      >
        <Text
          style={{
            fontSize: 16,
            fontWeight: "600",
            marginBottom: 10,
            marginLeft: 3,
          }}
        >
          {chatUsers.length > 1
            ? chatUsers.length + " " + language.users
            : chatUsers.length + " " + language.user}
        </Text>
        <View
          style={{
            borderWidth: 0.35,
            borderColor: "rgba(0,0,0,0.3)",
            borderRadius: 10,
            overflow: "hidden",
          }}
        >
          {loadingUsers ? (
            <ActivityIndicator style={{ padding: 20 }} />
          ) : (
            chatUsers.map(renderUserItem)
          )}
        </View>
      </View>

      <Modal
        animationType="none"
        transparent={true}
        visible={imageModalVisible}
        onRequestClose={toggleImageModal}
      >
        <TouchableOpacity style={{ flex: 1 }} activeOpacity={1}>
          <Animated.View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0,0,0,0.5)",
              opacity: imageModalValue,
            }}
          >
            {selectedImage && (
              <>
                <View
                  style={{
                    position: "absolute",
                    height: "100%",
                    width: "100%",
                    backgroundColor: "white",
                    zIndex: 9,
                    opacity: 0.7,
                  }}
                />
                <BlurView
                  style={{
                    height: "100%",
                    width: "100%",
                    position: "absolute",
                    zIndex: 10,
                  }}
                  intensity={30}
                  tint="light"
                >
                  <View
                    style={{
                      position: "absolute",
                      height: "100%",
                      width: "100%",
                      backgroundColor: "white",
                      opacity: 0.4,
                    }}
                  />
                  <View
                    style={{
                      height: 60 + insets.top,
                      alignSelf: "center",
                      width: "100%",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: "transparent",
                      zIndex: 2,
                      paddingTop: insets.top - 10,
                      position: "absolute",
                      top: 0,
                    }}
                  >
                    <View
                      style={{
                        alignSelf: "flex-start",
                        flexDirection: "row",
                        marginTop: 19,
                      }}
                    >
                      <Pressable
                        style={{
                          marginRight: 20,
                          marginLeft: 15,
                          marginTop: 3,
                        }}
                        onPress={toggleImageModal}
                      >
                        <AntDesign name="close" size={35} color="black" />
                      </Pressable>
                      <FastImage
                        source={{ uri: selectedImage.user.avatar }}
                        style={{ height: 40, width: 40, borderRadius: 100 }}
                      />
                      <View
                        style={{
                          paddingTop: 2,
                          marginLeft: 3,
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 16,
                            color: "rgba(0,0,0,0.87)",
                            paddingLeft: 5,
                            fontWeight: "600",
                          }}
                        >
                          {selectedImage.user.name}
                        </Text>
                        <Text
                          style={{
                            fontSize: 13,
                            color: "rgba(0,0,0,0.6)",
                            paddingLeft: 5,
                            fontWeight: "500",
                          }}
                        >
                          {formatDate(selectedImage.createdAt)}
                        </Text>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        marginRight: 20,
                        alignItems: "center",
                        paddingTop: 5,
                      }}
                    >
                      <Pressable
                        style={{
                          padding: 4,
                          marginRight: 20,
                        }}
                        onPress={() => {
                          console.log("save image");
                          saveImage(selectedImage.image);
                        }}
                      >
                        <MaterialIcons
                          name="save-alt"
                          size={29}
                          color="black"
                        />
                      </Pressable>
                      <Pressable
                        onPress={() => {
                          console.log("share image");
                          setShareLoading(true);
                          shareImage(selectedImage.image, setShareLoading);
                        }}
                      >
                        {shareLoading ? (
                          <ActivityIndicator style={{ marginRight: 17 }} />
                        ) : (
                          <MaterialIcons
                            name="ios-share"
                            size={29}
                            color="black"
                            style={{
                              padding: 4,
                            }}
                          />
                        )}
                      </Pressable>
                    </View>
                  </View>
                  <Pressable
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onPress={toggleImageModal}
                  >
                    {selectedImage?.image &&
                      (() => {
                        const { aspectRatio } = selectedImage.imageSize;

                        const maxWidth = screenWidth * 0.9;
                        const maxHeight = screenHeight * 0.62;

                        let calculatedWidth = maxWidth;
                        let calculatedHeight = maxWidth / aspectRatio;

                        if (calculatedHeight > maxHeight) {
                          calculatedHeight = maxHeight;
                          calculatedWidth = maxHeight * aspectRatio;
                        }

                        return (
                          <Pressable
                            style={{
                              borderWidth: 0.4,
                              borderColor: "rgba(0,0,0,0.35)",
                              borderRadius: 14,
                            }}
                            onPress={() => {
                              setShowZoomImage(true);
                            }}
                          >
                            <FastImage
                              source={{ uri: selectedImage.image }}
                              style={{
                                alignSelf: "center",
                                height: calculatedHeight,
                                width: calculatedWidth,
                                aspectRatio: aspectRatio,
                                resizeMode: "contain",
                                zIndex: 1,
                                borderRadius: 15,
                              }}
                            />
                          </Pressable>
                        );
                      })()}

                    <TouchableOpacity
                      style={{
                        position: "absolute",
                        bottom: insets.bottom + 5,
                        flexDirection: "row",
                        paddingVertical: 10,
                        width: "50%",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: 10,
                        borderWidth: 1,
                        borderColor: "rgba(0,0,0,0.3)",
                      }}
                      activeOpacity={0.4}
                      onPress={() => {
                        toggleImageModal();
                        navigation.navigate("ChatScreen", {
                          replyMessage: selectedImage,
                        });
                      }}
                    >
                      <Text
                        style={{
                          fontWeight: "600",
                          marginRight: 10,
                          fontSize: 15,
                        }}
                      >
                        {language.reply}
                      </Text>
                      <Octicons
                        name="reply"
                        size={22}
                        color="black"
                        style={{ position: "absolute", right: 10 }}
                      />
                    </TouchableOpacity>
                  </Pressable>
                  {showZoomImage && (
                    <>
                      <Pressable
                        style={{
                          position: "absolute",
                          width: "100%",
                          zIndex: 200,
                          top: insets.top + 11,
                          left: 15,
                        }}
                        onPress={() => {
                          setShowZoomImage(false);
                        }}
                      >
                        <AntDesign name="close" size={35} color="white" />
                      </Pressable>

                      <ImageViewer
                        imageUrls={[{ url: selectedImage.image }]}
                        onSwipeDown={() => {
                          setShowZoomImage(false);
                        }}
                        enableSwipeDown={true}
                        style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          backgroundColor: "",
                          zIndex: 100,
                        }}
                      />
                    </>
                  )}
                </BlurView>
              </>
            )}
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </ScrollView>
  );
};

export default ChatMenu;
