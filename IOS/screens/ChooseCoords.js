// screens/ChooseCoords.js
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Animated,
  TouchableWithoutFeedback,
} from "react-native";
import MapView, { Circle } from "react-native-maps";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import Slider from "@react-native-community/slider";
import { Feather, Ionicons } from "@expo/vector-icons";
import { setDoc, doc } from "@react-native-firebase/firestore";
import {
  getDownloadURL,
  ref,
  uploadBytesResumable,
} from "@react-native-firebase/storage";
import * as ImageManipulator from "expo-image-manipulator";
import { auth, db, storage } from "../config/firebase";
import { red } from "./colors";
import { useSetupProfile } from "./SetupProfileContext";
import { EN, CZ } from "./../assets/strings";
import { useFocusEffect } from "@react-navigation/native";

export default function ChooseCoords() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const {
    name,
    profileImage,
    gender,
    date,
    description,
    profileColor,
    languageCode,
    setCurrentStep,
    skillLevel,
  } = useSetupProfile();

  const languageStrings = languageCode === "CZ" ? CZ : EN;

  // Map refs and state
  const mapRef = useRef(null);
  const [centerCoordinate, setCenterCoordinate] = useState({
    latitude: 50.052324884074504,
    longitude: 14.46223913539908,
  });
  const [radius, setRadius] = useState(17000);
  const [isUploading, setIsUploading] = useState(false);
  const [mapIsLoading, setMapIsLoading] = useState(true);

  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const instructionsFade = useRef(new Animated.Value(1)).current;

  // Show instructions panel initially
  const [showInstructions, setShowInstructions] = useState(true);

  // Set current step when component mounts
  useFocusEffect(() => {
    setCurrentStep(2);
  });

  useEffect(() => {
    // Hide map loading indicator after a delay
    const timeoutId = setTimeout(() => {
      setMapIsLoading(false);

      // Fade in map when loaded
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();

      // Automatically hide instructions after 5 seconds
      setTimeout(() => {
        dismissInstructions();
      }, 5000);
    }, 700);

    return () => clearTimeout(timeoutId);
  }, []);

  const dismissInstructions = () => {
    Animated.timing(instructionsFade, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowInstructions(false);
    });
  };

  // Expose uploadUserData to be called from the Navigator
  // This function is attached to the window object so it can be accessed from outside
  useEffect(() => {
    // Attach the uploadUserData function to window for access from NavigationButton
    window.uploadUserData = async () => {
      try {
        setIsUploading(true);
        let tempImageRef;
        let tempImageRefSmall;

        if (profileImage) {
          // Upload full-size profile image
          const nameUID = `/users-profile-images/${auth.currentUser.uid}/profile-image`;
          const storageRef = ref(storage, nameUID);

          const compressedImage = await ImageManipulator.manipulateAsync(
            profileImage,
            [{ resize: { width: 600, height: 600 } }],
            { format: "jpeg" }
          );

          const img = await fetch(compressedImage.uri);
          const bytes = await img.blob();

          await uploadBytesResumable(storageRef, bytes);
          tempImageRef = await getDownloadURL(ref(storage, nameUID));

          // Upload small profile image for thumbnails
          const nameUIDProfileImageSmall = `/users-profile-images/${auth.currentUser.uid}/profile-image-small`;
          const storageRefProfileImageSmall = ref(
            storage,
            nameUIDProfileImageSmall
          );

          const compressedImageSmall = await ImageManipulator.manipulateAsync(
            profileImage,
            [{ resize: { width: 300, height: 300 } }],
            { format: "jpeg" }
          );

          const imgSmall = await fetch(compressedImageSmall.uri);
          const bytesSmall = await imgSmall.blob();

          await uploadBytesResumable(storageRefProfileImageSmall, bytesSmall);
          tempImageRefSmall = await getDownloadURL(
            ref(storage, nameUIDProfileImageSmall)
          );
        }

        // Create user document
        await setDoc(doc(db, "users/", auth.currentUser.uid), {
          language: languageCode,
          uid: auth.currentUser.uid,
          email: auth.currentUser.email,
          name: name,
          date: date,
          gender: gender,
          description: description ? description : null,
          skillLevel: skillLevel,
          profileColor: profileColor,
          profileImageRef: tempImageRef || null,
          profileImageRefSmall: tempImageRefSmall || null,
          centerCoord: centerCoordinate,
          radius: radius,
          logCounter: 1,
          favouriteCourts: [],
          eventsArray: [],
          notifications: {
            allowed: true,
            chat: true,
            eventChange: true,
            favCourt: true,
            usersChange: true,
          },
        });

        // Create secondary user document
        await setDoc(doc(db, "users2/", auth.currentUser.uid), {
          uid: auth.currentUser.uid,
        });

        // Create events document
        await setDoc(doc(db, "events", auth.currentUser.uid), {});

        // Create public user data
        const uid = auth.currentUser.uid;
        const publicData = {
          name: name,
          date: date,
          description: description ? description : null,
          skillLevel: skillLevel,
          profileColor: profileColor,
          profileImageRef: tempImageRef || null,
          profileImageRefSmall: tempImageRefSmall || null,
          uid: auth.currentUser.uid,
        };

        const userDocRef = doc(db, "users", uid);
        const publicDataRef = doc(userDocRef, "public", uid + "public");
        await setDoc(publicDataRef, publicData);

        // Navigate to main app
        navigation.reset({
          index: 0,
          routes: [{ name: "MainTabs" }],
        });

        return true;
      } catch (error) {
        console.error("Error uploading user data:", error);
        Alert.alert(
          languageStrings.error || "Error",
          languageStrings.errorCreatingProfile ||
            "There was an error creating your profile. Please try again."
        );
        setIsUploading(false);
        return false;
      }
    };

    // Clean up
    return () => {
      delete window.uploadUserData;
    };
  }, [
    centerCoordinate,
    radius,
    name,
    profileImage,
    gender,
    date,
    description,
    profileColor,
    languageCode,
    skillLevel,
    navigation,
  ]);

  return (
    <View style={{ flex: 1, backgroundColor: "transparent" }}>
      {/* Map Loading Indicator */}
      {mapIsLoading && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "white",
            zIndex: 5,
          }}
        >
          <ActivityIndicator size="large" color={red} />
        </View>
      )}

      {/* Main Content */}
      <Animated.View
        style={{
          flex: 1,
          opacity: fadeAnim,
          backgroundColor: "transparent",
        }}
      >
        {/* Instructions Panel */}
        {showInstructions && (
          <Animated.View
            style={{
              position: "absolute",
              top: insets.top + 70,
              left: 20,
              right: 20,
              backgroundColor: "rgba(0,0,0,0.7)",
              borderRadius: 12,
              padding: 15,
              zIndex: 10,
              opacity: instructionsFade,
            }}
          >
            <TouchableOpacity
              style={{
                position: "absolute",
                right: 10,
                top: 10,
                zIndex: 1,
              }}
              onPress={dismissInstructions}
              disabled={isUploading}
            >
              <Feather name="x" size={20} color="white" />
            </TouchableOpacity>
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <Ionicons
                name="information-circle-outline"
                size={24}
                color="white"
                style={{ marginRight: 10 }}
              />
              <Text
                style={{
                  color: "white",
                  fontWeight: "600",
                  fontSize: 16,
                }}
              >
                {languageStrings.areaInstructions || "Area Selection"}
              </Text>
            </View>
            <Text
              style={{
                color: "white",
                marginTop: 10,
                fontSize: 14,
                lineHeight: 20,
              }}
            >
              {languageStrings.areaInfo2 ||
                "Move the map to center your search area and adjust the radius with the slider below. This helps us find relevant courts and games near you."}
            </Text>
            <Text
              style={{
                color: "rgba(255,255,255,0.8)",
                marginTop: 10,
                fontSize: 13,
                fontStyle: "italic",
              }}
            >
              {languageStrings.changeLater ||
                "You can always change this later in settings."}
            </Text>
          </Animated.View>
        )}

        {/* Map */}
        <MapView
          ref={mapRef}
          style={{ flex: 1 }}
          onRegionChange={(value) => {
            if (!isUploading) {
              setCenterCoordinate({
                latitude: value.latitude,
                longitude: value.longitude,
              });
            }
          }}
          initialRegion={{
            latitude: centerCoordinate.latitude,
            longitude: centerCoordinate.longitude,
            latitudeDelta: 0.5,
            longitudeDelta: 0.5,
          }}
          scrollEnabled={!isUploading}
          zoomEnabled={!isUploading}
          rotateEnabled={!isUploading}
          pitchEnabled={!isUploading}
        >
          {/* Only render circles if centerCoordinate is defined */}
          {centerCoordinate &&
            centerCoordinate.latitude &&
            centerCoordinate.longitude && (
              <>
                <Circle
                  center={{
                    latitude: centerCoordinate.latitude,
                    longitude: centerCoordinate.longitude,
                  }}
                  radius={radius}
                  strokeColor={red}
                  strokeWidth={2}
                  fillColor={`${red}20`}
                />
                <Circle
                  center={{
                    latitude: centerCoordinate.latitude,
                    longitude: centerCoordinate.longitude,
                  }}
                  radius={200}
                  strokeColor={red}
                  fillColor={red}
                  strokeWidth={1}
                />
              </>
            )}
        </MapView>

        {/* Radius Slider */}
        <View
          style={{
            position: "absolute",
            bottom: 20,
            left: 10,
            right: 10,
            backgroundColor: "white",
            borderRadius: 15,
            padding: 15,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
            opacity: isUploading ? 0.7 : 1,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginBottom: 5,
            }}
          >
            <Text style={{ fontWeight: "600", color: "#333" }}>
              {languageStrings.searchRadius || "Search Radius"}
            </Text>
            <Text style={{ fontWeight: "600", color: red }}>
              {Math.round(radius / 1000)} km
            </Text>
          </View>
          <Slider
            style={{ width: "100%", height: 40 }}
            minimumValue={1000}
            maximumValue={40000}
            minimumTrackTintColor={red}
            maximumTrackTintColor={"rgba(0,0,0,0.1)"}
            thumbTintColor={red}
            value={radius}
            onValueChange={(newValue) => {
              if (!isUploading) {
                setRadius(newValue);
              }
            }}
            disabled={isUploading}
          />
        </View>
      </Animated.View>

      {/* Overlay to disable all interactions when uploading */}
      {isUploading && (
        <TouchableWithoutFeedback>
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "transparent",
              zIndex: 20,
            }}
          ></View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
}
