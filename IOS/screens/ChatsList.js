import React, { useState, useRef } from "react";
import {
  View,
  Text,
  Pressable,
  ActivityIndicator,
  FlatList,
  Animated,
} from "react-native";
import { useSelector } from "react-redux";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import FastImage from "react-native-fast-image";
import { red } from "./colors";
import { auth, db, functions } from "../config/firebase";
import { httpsCallable } from "@react-native-firebase/functions";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import {
  doc,
  getDoc,
  updateDoc,
  arrayUnion,
  deleteDoc,
  collection,
  setDoc,
  serverTimestamp,
} from "@react-native-firebase/firestore";

import TeamProfileIcon from "./../assets/TeamProfileIcon";

const sentFriendRequestNotificationV2 = httpsCallable(
  functions,
  "sentFriendRequestNotificationV2"
);

export default function ChatsList() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const route = useRoute();
  const { language } = useSelector((state) => state.language);
  const chats = useSelector((state) => state.socials.chats);

  const [isModalVisible3, setModalVisible3] = useState(false);
  const [friendRequestStatus, setFriendRequestStatus] = useState(null);
  const [user, setUser] = useState(null);

  const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

  const toggleModal3 = async () => {
    if (isModalVisible3) {
      Animated.timing(modalAnimatedValue3, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible3(false));
    } else {
      const docRef = doc(db, "users", auth.currentUser.uid);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists) {
        const friendRequests = docSnap.data().friendRequestsSent || [];
        if (friendRequests.includes(user.uid)) {
          setFriendRequestStatus("sent");
        } else {
          setFriendRequestStatus("notsent");
        }
      }

      setModalVisible3(true);
      Animated.timing(modalAnimatedValue3, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const sendFriendRequest = async () => {
    try {
      const currentUserDoc = doc(db, "users", auth.currentUser.uid);
      const docSnap = await getDoc(currentUserDoc);

      if (docSnap.exists) {
        const data = docSnap.data();
        const friendRequestsSent = data.friendRequestsSent || [];
        if (friendRequestsSent.includes(user.uid)) {
          return;
        }
      }

      const coll = collection(db, `users/${user.uid}/notifications`);
      await setDoc(doc(coll, auth.currentUser.uid), {
        type: "friendRequest",
        from: auth.currentUser.uid,
        date: serverTimestamp(),
      });

      await updateDoc(currentUserDoc, {
        friendRequestsSent: arrayUnion(user.uid),
      });

      setFriendRequestStatus("sent");

      await sentFriendRequestNotification({
        from: auth.currentUser.uid,
        to: user.uid,
      });
      console.log("success");
    } catch (error) {
      console.error("Error sending friend request: ", error);
    }
  };

  const deleteFriendRequest = (notifUID) => {
    const docRef = doc(
      db,
      `users/${auth.currentUser.uid}/notifications/${notifUID}`
    );

    deleteDoc(docRef)
      .then((result) => {})
      .catch((error) => {
        alert(error);
      });
  };

  const renderChatItem = ({ item }) => {
    const isGameChat = item.type === "game";
    const isTeamChat = item.type === "team";
    const otherUser = item.otherUser;
    const gameInfo = isGameChat ? item.gameInfo : null;
    const teamInfo = isTeamChat ? item.teamInfo : null;

    return (
      <Pressable
        style={{
          width: "97%",
          marginTop: 0,
          alignSelf: "center",
        }}
        onPress={() => {
          navigation.navigate("ChatScreen", {
            user: otherUser ? otherUser : isGameChat ? "game" : "team",
            chat: item,
          });
        }}
      >
        <View
          style={{
            width: "100%",
            height: 85,
            flexDirection: "row",
            marginTop: 5,
            width: "95%",
            alignSelf: "center",
            marginBottom: 0,
            marginTop: 7,
            alignItems: "center",
          }}
          key={isGameChat ? item.gameId : isTeamChat ? item.id : otherUser.uid}
        >
          {isGameChat ? (
            <Pressable
              onPress={() => {
                navigation.navigate("Game", {
                  gameId: item.gameId,
                  language: language,
                  navigation: navigation,
                });
              }}
            >
              <FastImage
                style={{
                  width: 70,
                  height: 70,
                  borderRadius: 15,
                  zIndex: 10,
                  borderWidth: 0.2,
                  borderColor: "rgba(0,0,0,0.3)",
                  marginTop: 2,
                }}
                source={{ uri: gameInfo.courtInfo.imageRefs[0] }}
              />
            </Pressable>
          ) : isTeamChat ? (
            <Pressable
              onPress={() => {
                // console.warn("teamInfo", item.teamId);
                navigation.navigate("TeamDetails", {
                  teamId: item.teamId,
                });
              }}
            >
              {/* <View
                style={{
                  width: 70,
                  height: 70,
                  borderRadius: 15,
                  zIndex: 10,
                  borderWidth: 0.2,
                  borderColor: "rgba(0,0,0,0.1)",
                  marginTop: 2,
                  backgroundColor: teamInfo.color,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{ fontSize: 25, color: "white", fontWeight: "bold" }}
                >
                  {teamInfo.name[0].toUpperCase()}
                </Text>
              </View> */}

              <TeamProfileIcon team={teamInfo} size={70} />
            </Pressable>
          ) : otherUser.profileImageRef ? (
            <Pressable
              onPress={() => {
                navigation.navigate("User", {
                  user: otherUser,
                });
              }}
            >
              <FastImage
                style={{
                  width: 70,
                  height: 70,
                  borderRadius: 100,
                  zIndex: 10,
                  borderWidth: 0.2,
                  borderColor: "rgba(0,0,0,0.3)",
                  marginTop: 2,
                }}
                source={{ uri: `${otherUser.profileImageRef}` }}
              />
            </Pressable>
          ) : (
            <Pressable
              onPress={() => {
                navigation.navigate("User", { user: otherUser });
              }}
            >
              <View
                style={{
                  width: 70,
                  height: 70,
                  borderRadius: 100,
                  zIndex: 10,
                  marginTop: 2,
                  backgroundColor: `${otherUser.profileColor}`,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    alignSelf: "center",
                    fontSize: 25,
                    color: "white",
                  }}
                >
                  {otherUser.name[0].toUpperCase()}
                </Text>
              </View>
            </Pressable>
          )}

          <View
            style={{
              flex: 1,
              marginLeft: 10,
              marginRight: 10,
            }}
          >
            <View style={{ flexDirection: "row" }}>
              <Text
                style={{
                  fontWeight:
                    item.unreadMessages[auth.currentUser.uid] > 0
                      ? "700"
                      : "500",
                  marginBottom: 8,
                  fontSize: 15,
                }}
              >
                {isGameChat
                  ? item.gameInfo.name
                  : isTeamChat
                  ? item.teamInfo.name
                  : otherUser.name}
              </Text>
              {isGameChat && (
                <Ionicons
                  name="basketball-outline"
                  size={18}
                  color={"black"}
                  style={{ marginLeft: 5, bottom: 1 }}
                />
              )}
              {/* {isTeamChat && (
                <Ionicons
                  name="people-outline"
                  size={18}
                  color={"black"}
                  style={{ marginLeft: 5, bottom: 1 }}
                />
                <FontAwesome5
                  name="users"
                  size={14}
                  color="black"
                  style={{ marginLeft: 5, bottom: -1 }}
                />
                <Ionicons
                  name="people-circle-sharp"
                  size={19}
                  color="black"
                  style={{ marginLeft: 5, bottom:  }}
                />
                <Ionicons
                  name="people-circle-outline"
                  size={19}
                  color="black"
                />
              )} */}
            </View>

            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <View
                style={{
                  maxWidth: "80%",
                  flexDirection: "row",
                  alignItems: "center",
                }}
              >
                <Text
                  style={{
                    fontWeight: "400",
                    color:
                      item.unreadMessages[auth.currentUser.uid] > 0
                        ? "rgba(0,0,0,1)"
                        : "rgba(0,0,0,0.5)",
                    fontSize: 15,
                  }}
                  numberOfLines={1}
                >
                  {item.lastUnreadMessage
                    ? item.lastUnreadMessage.text
                    : "Tap to start chatting"}
                  {item?.lastUnreadMessage?.imageSize && "Sent a photo"}
                </Text>
                {item?.lastUnreadMessage && (
                  <View
                    style={{
                      height: 2.5,
                      width: 2.5,
                      backgroundColor: "rgba(0,0,0,0.5)",
                      borderRadius: 10,
                      marginHorizontal: 4,
                    }}
                  />
                )}
                {item?.lastUnreadMessageDate && (
                  <Text
                    style={{
                      fontWeight: "400",
                      color:
                        item.unreadMessages[auth.currentUser.uid] > 0
                          ? "rgba(0,0,0,1)"
                          : "rgba(0,0,0,0.5)",
                      fontSize: 14,
                    }}
                  >
                    {(() => {
                      const messageDate = new Date(item.lastUnreadMessageDate);
                      const now = new Date();
                      const diffMinutes = Math.floor(
                        (now - messageDate) / (1000 * 60)
                      );
                      const diffHours = Math.floor(diffMinutes / 60);
                      const diffDays = Math.floor(diffHours / 24);

                      if (diffMinutes < 60) {
                        return `${diffMinutes}m`;
                      } else if (diffHours < 24) {
                        return `${diffHours}h`;
                      } else if (diffDays < 7) {
                        return route.params.language.weekdays[
                          messageDate.getDay()
                        ].slice(0, 3);
                      } else {
                        return messageDate.toLocaleDateString([], {
                          month: "numeric",
                          day: "numeric",
                        });
                      }
                    })()}
                  </Text>
                )}
              </View>
            </View>
          </View>
          {item.unreadMessages[auth.currentUser.uid] > 0 && (
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
                marginLeft: 5,
              }}
            >
              <View
                style={{
                  backgroundColor: red,
                  borderRadius: 10,
                  minWidth: 20,
                  height: 20,
                  alignItems: "center",
                  justifyContent: "center",
                  paddingHorizontal: 5,
                }}
              >
                <Text
                  style={{
                    color: "white",
                    fontWeight: "700",
                    fontSize: 12,
                  }}
                >
                  {item.unreadMessages[auth.currentUser.uid] > 99
                    ? "99+"
                    : item.unreadMessages[auth.currentUser.uid]}
                </Text>
              </View>
            </View>
          )}
        </View>
      </Pressable>
    );
  };

  return (
    <View
      style={{
        width: "100%",
        height: "100%",
        backgroundColor: "white",
        paddingTop: insets.top,
      }}
    >
      <View
        style={{
          height: 50,
          alignSelf: "center",
          width: "100%",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          borderBottomColor: "rgba(0,0,0,0.2)",
          borderBottomWidth: 0.3,
        }}
      >
        <Text
          style={{
            fontFamily: "Roboto",
            fontWeight: "700",
            fontSize: 20,
            color: "rgba(0,0,0,0.87)",
            paddingLeft: 5,
          }}
        >
          Messages
        </Text>
        <Pressable
          style={{
            position: "absolute",
            left: 15,
          }}
          onPress={() => {
            navigation.goBack();
          }}
        >
          <MaterialIcons
            name="arrow-back-ios"
            size={30}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 3 }}
          />
        </Pressable>
      </View>

      {!chats ? (
        <ActivityIndicator style={{ marginTop: "30%" }} />
      ) : (
        <FlatList
          style={{ backgroundColor: "white", marginTop: 5 }}
          data={chats}
          renderItem={renderChatItem}
          keyExtractor={(item) =>
            item.type === "game"
              ? item.gameId
              : item.type === "team"
              ? item.id
              : item.otherUser.uid
          }
        />
      )}
    </View>
  );
}
