import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  TouchableHighlight,
  ScrollView,
  Pressable,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
  Modal,
  ImageBackground,
  Easing,
  // Picker,
} from "react-native";

import { auth, db } from "../config/firebase";
import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  onSnapshot,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { MaterialIcons } from "@expo/vector-icons";

import {
  MyGamesDataContext,
  MyGamesDataContextProvider,
} from "./ChooseCourtProvider";
import { LinearGradient } from "expo-linear-gradient";

import {
  TapGestureHandler,
  PanGestureHandler,
  State,
} from "react-native-gesture-handler";
import DateTimePickerModal from "react-native-modal-datetime-picker";

import MapView, {
  Marker,
  PROVIDER_GOOGLE,
  Polygon,
  Circle,
} from "react-native-maps";

import { EN, CZ } from "./../assets/strings";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

function RootMyGamesStack() {
  const insets = useSafeAreaInsets();
  const Map = () => {
    // const { selectedCourt, setSelectedCourt } = useContext(MyGamesDataContext);

    //   console.log(selectedCourt);

    const [courts, setCourts] = useState(null);
    const [centerCoordinate, setCenterCoordinate] = useState(null);

    const [language2, setLanguage2] = useState(null);
    const [language, setLanguage] = useState(null);

    //  const [mapIsLoading1, setMapIsLoading1] = useState(true);

    //  useEffect(() => {
    //    const timeoutId = setTimeout(() => {
    //      setMapIsLoading(false);
    //    }, 700);

    //    return () => clearTimeout(timeoutId);
    //  }, []);

    // const navigation = useNavigation();

    useLayoutEffect(() => {
      navigation.setOptions({
        // headerBackVisible: false,
        // headerShown: false,

        //   title: "Choose a court",
        //   headerTintColor: "black",
        headerTitle: () => (
          <Text
            style={{
              fontFamily: "BungeeInline-Regular",
              fontSize: 18,
              color: "white",
              overflow: "visible",
              lineHeight: 25,
            }}
          >
            {language.chooseCourt}
          </Text>
        ),

        headerStyle: {
          backgroundColor: red,
          // height: 90,
          // backgroundColor: "rgba(197,42,71,1)",
        },
        headerLeft: () => (
          <TouchableHighlight
            style={{
              marginRight: 10,
              bottom: 3,
              backgroundColor: red,
              padding: 10,
            }}
            onPress={() => {
              navigation.goBack();
            }}
            underlayColor="rgba(0, 0, 0, 0)"
          >
            <MaterialIcons
              name="arrow-back-ios"
              size={28}
              color="white"
              style={{ marginLeft: 10 }}

              // onPress={() => {}}
            />
          </TouchableHighlight>
        ),
      });
    }, [navigation]);

    //   console.log(props);

    useEffect(() => {
      const docRef = doc(db, "users", auth.currentUser.uid);

      const unsubscribe = onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists) {
          setCenterCoordinate(docSnap.data().centerCoord);
          setLanguage2(docSnap.data().language);
        } else {
          console.log("No such document!");
        }
      });

      // Cleanup function
      return () => {
        unsubscribe();
      };
    }, []);

    useEffect(() => {
      if (language2 === "CZ") {
        setLanguage(CZ);
        console.log(CZ);
      } else {
        setLanguage(EN);
      }
    }, [language2]);

    useEffect(() => {
      const q = query(collection(db, "courts-cz1"));

      const unsubscribe = onSnapshot(q, (querySnapshot) => {
        // console.log(
        //   "Data source: ",
        //   querySnapshot.metadata.fromCache ? "cache" : "server"
        // );
        const docs = [];
        querySnapshot.forEach((doc) => {
          const courtData = doc.data();
          if (courtData?.storage_id) {
            docs.push({
              id: doc.id,
              // pfp: courtData.courtImageRef || "",
              // nazev: courtData.name || "",
              coords: [courtData.coords[1], courtData.coords[0]],
              imageRefs: courtData.imageRefs,
              address: courtData.address,
              // isFavourite, // add isFavourite field
              storage_id: courtData.storage_id,
              description: courtData.description,
              baskets: courtData.baskets,
              surface: courtData.surface,
            });
          }
        });
        // console.log(docs);
        setCourts(docs);
      });
      return unsubscribe;
    }, []);

    useEffect(() => {
      const q = query(collection(db, "courts-cz1"));
      const currentUserRef = doc(db, "users", auth.currentUser.uid); // reference to current user document

      const unsubscribe = onSnapshot(q, async (querySnapshot) => {
        const docs = [];
        const userSnap = await getDoc(currentUserRef); // fetch current user data
        const userFavouriteCourts = userSnap.data().favouriteCourts || []; // get favourite courts or default to empty array

        querySnapshot.forEach((doc) => {
          const courtData = doc.data();
          const isFavourite = userFavouriteCourts.includes(doc.id); // check if the court is in favourite courts

          console.log(courtData.open, "opne");

          if (courtData?.storage_id) {
            docs.push({
              id: doc.id,
              // pfp: courtData.courtImageRef || "",
              // nazev: courtData.name || "",
              coords: [courtData.coords[1], courtData.coords[0]],
              imageRefs: courtData.imageRefs,
              address: courtData.address,
              isFavourite, // add isFavourite field
              storage_id: courtData.storage_id,
              description: courtData.description,
              baskets: courtData.baskets,
              surface: courtData.surface,
              open: courtData.open ? courtData.open : null,
            });
          }
        });

        // console.log(docs);

        setCourts(docs);
      });
      return unsubscribe;
    }, []);

    const hristeClick = (marker) => {
      setSelectedMarker(marker);
    };

    const mapaClick = () => {
      setSelectedMarker(null);
    };

    // STATE

    //   const { selectedCourt, setSelectedCourt } = useContext(MyGamesDataContext);

    const [selectedFeature, setSelectedFeature] = useState();

    const [selectedMarker, setSelectedMarker] = useState(null);

    const [zoomLevel, setZoomLevel] = useState(14);

    const [viewCoords, setViewCoords] = useState([50.5, 14.5]);

    const [mapIsLoading, setMapIsLoading] = useState(true);

    const opacityMap = useRef(new Animated.Value(0)).current;

    // console.log(opacity1);

    useEffect(() => {
      if (!mapIsLoading) {
        Animated.timing(opacityMap, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      }
    }, [mapIsLoading]);

    // const [mapYPosition, setMapYPosition] = useState(new Animated.Value(-100));

    // useEffect(() => {
    //   if (!mapIsLoading) {
    //     Animated.timing(mapYPosition, {
    //       toValue: 0,
    //       duration: 400,
    //       useNativeDriver: true,
    //     }).start();
    //   }
    // }, [mapIsLoading, mapYPosition]);

    // useEffect(() => {
    //   const timeoutId = setTimeout(() => {
    //     setMapIsLoading(false);
    //   }, 1000);

    //   return () => clearTimeout(timeoutId);
    // }, []);

    const [favouriteCourts, setFavouriteCourts] = useState([]);

    useEffect(() => {
      const userRef = doc(db, "users", auth.currentUser.uid); // reference to current user document

      const unsubscribe = onSnapshot(
        userRef,
        (doc) => {
          if (doc.exists) {
            const userData = doc.data();
            setFavouriteCourts(userData.favouriteCourts || []); // Update the favouriteCourts state
          } else {
            console.log("No such document!");
          }
        },
        (error) => {
          console.log("Error fetching document:", error);
        }
      );

      return () => {
        unsubscribe && unsubscribe();
      };
    }, []);

    const MarkerInfoComp = () => {
      const images = selectedMarker.imageRefs;

      const isFavourite = favouriteCourts.includes(selectedMarker.id);

      const [imageLoaded, setImageLoaded] = useState(false);
      const [index, setIndex] = useState(0);
      const [loadingNewImage, setLoadingNewImage] = useState(false); // Add this state

      // const [opacity1] = useState(new Animated.Value(0));
      const opacity1 = useRef(new Animated.Value(0)).current;

      console.log(opacity1);

      useEffect(() => {
        if (imageLoaded) {
          Animated.timing(opacity1, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      }, [imageLoaded]);

      // const fadeInOpacity = useRef(new Animated.Value(0)).current;

      //  const [imageLoaded, setImageLoaded] = useState(false);

      // useEffect(() => {
      //   if (imageLoaded) {
      //     Animated.timing(fadeInOpacity, {
      //       toValue: 1,
      //       duration: 300,
      //       useNativeDriver: true,
      //     }).start();
      //   }
      // }, [imageLoaded]);

      if (!language) {
        return (
          <View
            style={{ backgroundColor: "white", height: "100%", width: "100%" }}
          >
            <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
          </View>
        );
      }

      return (
        <View
          style={{
            width: "100%",
            height: 330,
            backgroundColor: "white",
            borderRadius: 10,
            borderWidth: 0.6,
            borderColor: "rgba(0,0,0,0.4)",
            // zIndex: 10,
            // alignItems: "center",
            // opacity: imageLoaded ? 1 : 0.5,
          }}
        >
          <ActivityIndicator
            style={{
              position: "absolute",
              alignSelf: "center",
              alignSelf: "center",
              marginTop: 140,
              opacity: 1,
            }}
          ></ActivityIndicator>
          {loadingNewImage && (
            <ActivityIndicator
              style={{
                position: "absolute",
                alignSelf: "center",
                marginTop: 115,
                opacity: 0.5,
                zIndex: 10,
              }}
              color="white"
            />
          )}
          <Animated.View
            style={{
              opacity: opacity1,
            }}
          >
            <Image
              source={{
                uri: `${images[index]}`,
              }}
              style={{
                width: "100%",
                height: 245,
                borderTopRightRadius: 9,
                borderTopLeftRadius: 9,
              }}
              onLoadEnd={() => {
                setImageLoaded(true);
                setLoadingNewImage(false); // Add this line
              }}
            ></Image>
            <AntDesign
              name="close"
              size={29}
              color="white"
              style={{
                position: "absolute",
                marginTop: 5,
                right: 4,
                opacity: 1,
              }}
              onPress={() => {
                setSelectedMarker(null);
              }}
            />
            <MaterialIcons
              name="arrow-forward-ios"
              size={24}
              color="white"
              style={{
                position: "absolute",
                marginTop: 115,
                right: 3,
                opacity: 0.8,
              }}
              onPress={() => {
                setLoadingNewImage(true);
                setIndex((index + 1) % images.length);
              }}
            />
            <MaterialIcons
              name="arrow-back-ios"
              size={24}
              color="white"
              style={{
                position: "absolute",
                marginTop: 115,
                left: 13,
                opacity: 0.8,
              }}
              onPress={() => {
                setLoadingNewImage(true);
                setIndex((index - 1 + images.length) % images.length);
              }}
            />
            <View
              style={{
                position: "absolute",
                width: 57,
                height: 42,
                backgroundColor: "white",
                bottom: -5,
                // left: -20,
                borderTopRightRadius: 100,
                overflow: "hidden",
                zIndex: 1,
              }}
            ></View>
            {isFavourite ? (
              <Image
                source={require("./../images/qq-star5.png")}
                style={{
                  width: 45,
                  height: 45,
                  position: "absolute",
                  // marginTop: -49,
                  left: 1,
                  bottom: -13,
                  overflow: "visible",
                  zIndex: 1,
                }}
              />
            ) : (
              <Image
                source={require("./../images/qq5.png")}
                style={{
                  width: 45,
                  height: 45,
                  position: "absolute",
                  // marginTop: -49,
                  left: 1,
                  bottom: -13,
                  overflow: "visible",
                  zIndex: 1,
                }}
              />
            )}
            <View
              style={{
                width: "100%",
                height: 25,
                backgroundColor: "rgba(0,0,0,0.3)",
                // opacity: 0.5,
                bottom: 0,
                position: "absolute",
              }}
            >
              {/* <Text>{selectedMarker.nazev}</Text> */}
              <Text
                style={{
                  color: "white",
                  marginLeft: 62,
                  zIndex: 2,
                  fontWeight: "600",
                  fontSize: 12,
                  marginTop: 5,
                  maxWidth: 280, // Set this value according to your requirements
                }}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {selectedMarker.address}
              </Text>
            </View>

            <View
              style={{
                bottom: -25,
                flexDirection: "row",
                justifyContent: "space-between",
                width: "90%",
                alignSelf: "center",
                // alignItems: "center",

                flex: 1,
              }}
            >
              <Pressable
                onPress={() => {
                  setSelectedCourt(selectedMarker);
                  // navigation.goBack();
                  setChoosingCourt(false);
                }}
                style={({ pressed }) => ({
                  borderRadius: 8,
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                  width: "55%",
                  height: 43,
                  backgroundColor: red,
                  // shadowColor: red,
                  // shadowOpacity: 0.6,
                  // shadowRadius: 5,
                  // shadowOffset: { width: 0, height: 0 },
                  justifyContent: "center",
                  bottom: 6,
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    alignSelf: "center",
                    color: "white",
                    fontSize: 15,
                    // fontFamily: "roboto",
                  }}
                >
                  {language.chooseThisCourt}
                </Text>
              </Pressable>
              <Pressable
                onPress={() => {
                  navigation.navigate("CourtInfo", {
                    selectedMarker: selectedMarker,
                    language: language,
                  });
                }}
                style={({ pressed }) => ({
                  borderRadius: 8,
                  width: "30%",
                  height: 40,
                  backgroundColor: "rgba(0,0,0,0.04)",
                  justifyContent: "center",
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                  borderColor: "rgba(0,0,0,0.1)",
                  borderWidth: 0.5,
                  bottom: 6,
                })}
              >
                <Text style={{ fontWeight: "600", alignSelf: "center" }}>
                  {language.moreCourtInfo}
                </Text>
              </Pressable>
            </View>
          </Animated.View>
        </View>
      );
    };

    if (courts === null || centerCoordinate === null) {
      console.log("aasa");
      return;
    }

    // const courtsList = courts.map((hriste, index) => (
    //   // <View key={hriste.nazev} style={{ overflow: "visible" }}>
    //   <MarkerView
    //     style={{ overflow: "visible" }}
    //     coordinate={hriste.coords}
    //     title={"title"}
    //     description={"description"}
    //     // onTouchEnd={() => {
    //     //   console.log("aaaaaaaaaaaaa");
    //     //   hristeClick(hriste);
    //     // }}
    //     allowOverlap={true}
    //     anchor={{ x: 0.47, y: 0.88 }}
    //   >
    //     {/* <View> */}
    //     <TapGestureHandler
    //       onEnded={() => {
    //         console.log(hriste.coords);
    //         setViewCoords(hriste.coords);
    //         hristeClick(hriste);
    //       }}
    //     >
    //       <View>
    //         <Image
    //           source={require("./../images/iconizer-mapmarker.png")}
    //           style={{
    //             width: selectedMarker === hriste ? 55 : 50,
    //             height: selectedMarker === hriste ? 55 : 50,
    //             // marginTop: -49,
    //             overflow: "visible",
    //           }}
    //         />
    //       </View>
    //     </TapGestureHandler>
    //     {/* </View> */}
    //   </MarkerView>
    //   // </View>
    // ));

    // if (mapIsLoading) {
    //   console.log("map is loading");
    //   return <ActivityIndicator></ActivityIndicator>;
    // }

    // console.log("aasaaaaa");

    const iconSize = 42;
    const iconSizeSelected = 47;

    return (
      <View style={{ backgroundColor: "#ceefc8" }}>
        <View
          style={{
            height: insets.top * 0.5 + 60,
            width: "100%",
            backgroundColor: red,
            position: "absolute",

            zIndex: 2,
          }}
        >
          <Text
            style={{
              fontFamily: "BungeeInline-Regular",
              fontSize: 19,
              color: "white",
              alignSelf: "center",
              bottom: 15,
              position: "absolute",
              lineHeight: 25,
            }}
          >
            {language.chooseCourt}
          </Text>
        </View>

        <Animated.View
          style={{ width: "100%", height: "100%", opacity: opacityMap }}
        >
          <View
            style={{ width: "100%", height: "100%", backgroundColor: "white" }}
            onStartShouldSetResponder={() => true}
          >
            <MapView
              attributionEnabled={false}
              onPress={() => setSelectedMarker(null)}
              style={{ flex: 1 }}
              customMapStyle={[
                {
                  featureType: "all",
                  elementType: "labels",
                  stylers: [{ visibility: "off" }],
                },
              ]}
              pitchEnabled={false}
              rotateEnabled={true}
              provider={MapView.PROVIDER_DEFAULT}
              initialRegion={{
                latitude: centerCoordinate.latitude,
                longitude: centerCoordinate.longitude,
                latitudeDelta: 0.922 * 0.7,
                longitudeDelta: 0.421 * 0.7,
              }}
              onMapReady={() => {
                setMapIsLoading(false);
              }}
            >
              {courts.map((court, index) => (
                <Marker
                  key={index}
                  coordinate={{
                    latitude: court.coords[1],
                    longitude: court.coords[0],
                  }}
                  onPress={() => setSelectedMarker(court)}
                  // anchor={{ x: 5.5, y: -9.5 }}
                >
                  <TapGestureHandler
                    onEnded={() => {
                      hristeClick(court);
                    }}
                    style={{
                      // height: 60,
                      // width: 60,
                      backgroundColor: "red",
                      overflow: "visible",
                    }}
                  >
                    <View style={{ marginBottom: 30 }}>
                      {court.isFavourite ? (
                        <Image
                          source={require("./../images/qq-star5.png")}
                          style={{
                            width:
                              selectedMarker === court
                                ? iconSizeSelected
                                : iconSize,
                            height:
                              selectedMarker === court
                                ? iconSizeSelected
                                : iconSize,
                            // marginTop: -49,
                            overflow: "visible",
                            // marginTop: -5,
                            // position: "absolute",
                          }}
                        />
                      ) : (
                        <Image
                          source={require("./../images/qq5.png")}
                          style={{
                            width:
                              selectedMarker === court
                                ? iconSizeSelected
                                : iconSize,
                            height:
                              selectedMarker === court
                                ? iconSizeSelected
                                : iconSize,
                            // marginTop: -49,
                            // overflow: "visible",
                            // marginTop: -40,
                          }}
                        />
                      )}
                    </View>
                  </TapGestureHandler>
                </Marker>
              ))}
            </MapView>
            {selectedMarker ? (
              <View
                style={{
                  position: "absolute",
                  bottom: 35,
                  width: "95%",
                  alignSelf: "center",
                }}
              >
                <MarkerInfoComp />
              </View>
            ) : null}
          </View>
        </Animated.View>
      </View>
    );
  };

  const navigation = useNavigation();

  const uploadGame = async (setShowActivityIndicator, setSlideCompleted) => {
    console.log(date);
    console.log(selectedRestrictionType);
    // console.log(ageLimit);

    const docRef = doc(
      db,
      `events/${auth.currentUser.uid}/user_events/${
        route.params.eventID.split("_")[1]
      }`
    );

    setSlideCompleted(true);
    setShowActivityIndicator(true);

    let newDate1 = new Date();
    newDate1.setFullYear(date.getFullYear());
    newDate1.setMonth(date.getMonth());
    newDate1.setDate(date.getDate());
    newDate1.setHours(time1.getHours());
    newDate1.setMinutes(time1.getMinutes());
    newDate1.setSeconds(0);

    let newDate2 = new Date();
    newDate2.setFullYear(date.getFullYear());
    newDate2.setMonth(date.getMonth());
    newDate2.setDate(date.getDate());
    newDate2.setHours(time2.getHours());
    newDate2.setMinutes(time2.getMinutes());
    newDate2.setSeconds(0);

    try {
      const eventDocRef = await updateDoc(docRef, {
        eventName: eventName,
        date: date,
        // dateEnd: dateEnd,
        timeStart: newDate1,
        timeEnd: newDate2,
        courtObj: selectedCourt,
        courtID: selectedCourt.id,
        selectedRestrictionType: selectedRestrictionType,
        ageLimitMin: ageLimitMin ? parseInt(ageLimitMin) : null,
        ageLimitMax: ageLimitMax ? parseInt(ageLimitMax) : null,
        // users: [auth.currentUser.uid],
        creator: auth.currentUser.uid,
        coords: new GeoPoint(selectedCourt.coords[1], selectedCourt.coords[0]),
        usersLimit: limitOfUsers ? parseInt(limitOfUsers) : null,
        description: eventDescription,
        isLive: newDate1 < new Date(),
      });

      //   const eventID = `${auth.currentUser.uid}_${eventDocRef.id}`;

      //   await updateDoc(eventDocRef, {
      //     eventID: eventID,
      //   });

      // await updateDoc(doc(db, `users/${auth.currentUser.uid}`), {
      //   eventsArray: arrayUnion(
      //     `${route.params.eventID.split("_")[0]}_${
      //       route.params.eventID.split("_")[1]
      //     }`
      //   ),
      // });

      console.log("success");
      // navigation.navigate("MyGames");
      navigation.goBack();
    } catch (error) {
      console.log(error);
      setSlideCompleted(false);
      setShowActivityIndicator(false);
      alert(error);
    }
  };

  const route = useRoute();

  console.log(route);
  const language = route.params.language;

  // const { selectedCourt, setSelectedCourt } = useContext(MyGamesDataContext);
  const [selectedCourt, setSelectedCourt] = useState(route.params.courtObj);

  const [choosingCourt, setChoosingCourt] = useState(false);

  const [selectedRestrictionType, setSelectedRestrictionType] = useState(
    route.params.selectedRestrictionType
  );

  // const [dateEnd, setDateEnd] = useState(new Date(Date.now()));
  const [eventName, setEventName] = useState(route.params.eventName);

  // const [limitOfUsers, setLimitOfUsers] = useState(route.params.usersLimit);

  const [limitOfUsers, setLimitOfUsers] = useState(
    route?.params?.usersLimit?.toString()
  );

  const [ageLimitMin, setAgeLimitMin] = useState(
    route.params.ageLimitMin?.toString()
  );
  const [ageLimitMax, setAgeLimitMax] = useState(
    route.params.ageLimitMax?.toString()
  );

  const [eventDescription, setEventDescription] = useState(
    route.params.description
  );

  const [date, setDate] = useState(new Date(route.params.date.seconds * 1000));

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);

  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };

  const handleConfirm = (selectedDate) => {
    const year = selectedDate.getFullYear();
    const month = ("0" + selectedDate.getMonth()).slice(-2);
    const day = ("0" + selectedDate.getDate()).slice(-2);
    const hours = time1.getHours();
    const minutes = time1.getMinutes();

    setDate(selectedDate);
    hideDatePicker();
  };

  const [time1, setTime1] = useState(
    new Date(route.params.timeStart.seconds * 1000)
  );

  const [isTimePickerVisible1, setTimePickerVisibility1] = useState(false);

  const showTimePicker1 = () => {
    setTimePickerVisibility1(true);
  };

  const hideTimePicker1 = () => {
    setTimePickerVisibility1(false);
  };

  const handleConfirmTime1 = (selectedTime) => {
    // console.log(selectedTime);
    setTime1(selectedTime);
    hideTimePicker1();
  };

  const [time2, setTime2] = useState(
    new Date(route.params.timeEnd.seconds * 1000)
  );

  const [isTimePickerVisible2, setTimePickerVisibility2] = useState(false);

  const showTimePicker2 = () => {
    setTimePickerVisibility2(true);
  };

  const hideTimePicker2 = () => {
    setTimePickerVisibility2(false);
  };

  const handleConfirmTime2 = (selectedTime) => {
    setTime2(selectedTime);
    hideTimePicker2();
  };

  const year = date.getFullYear();
  const month = ("0" + date.getMonth()).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = time1.getHours();
  const minutes = time1.getMinutes();

  const [courtImageLoaded, setCourtImageLoaded] = useState(false);

  const opacity1 = useRef(new Animated.Value(0)).current;

  const [showPicker, setShowPicker] = useState(false);

  const handlePress = () => {
    setShowPicker(true);
  };

  const handlePickerChange = (newValue) => {
    setLimitOfUsers(newValue);
    setShowPicker(false);
  };

  useEffect(() => {
    if (courtImageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [courtImageLoaded]);

  const isTime1LessThanTime2 =
    time1.getHours() * 60 + time1.getMinutes() <
    time2.getHours() * 60 + time2.getMinutes();

  const ImageDiv = useMemo(() => {
    if (selectedCourt) {
      return (
        <Pressable
          style={{ width: "100%", marginTop: -30 }}
          onPress={() => {
            // navigation.navigate("MapCC");
            setChoosingCourt(true);
            setCourtImageLoaded(false);
          }}
        >
          <Animated.View
            style={{
              width: "100%",
              alignItems: "center",
              opacity: opacity1,
            }}
          >
            <ActivityIndicator
              style={{
                bottom: -70,
                zIndex: 2,
                opacity: courtImageLoaded ? 0 : 1,
              }}
            />

            <View
              style={{
                // height: 10,
                width: "92%",
                borderRadius: 10,
                overflow: "hidden",
                position: "relative",
              }}
            >
              <View>
                <Image
                  source={{ uri: selectedCourt.imageRefs[0] }}
                  style={{
                    height: 155,
                    width: "100%",
                    borderRadius: 10,
                    alignSelf: "center",
                  }}
                  onLoadEnd={() => {
                    setCourtImageLoaded(true);
                  }}
                />
                <LinearGradient
                  colors={[
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.2)",
                    // "rgba(0,0,0,0.1)",
                    "rgba(0,0,0,0)",
                  ]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={{
                    position: "absolute",
                    left: 0,
                    top: 0,
                    paddingTop: 10,
                    // height: 135,
                    height: "100%",
                    width: "50%",
                  }}
                >
                  <Image
                    source={require("./../images/white-marker.png")}
                    style={{
                      width: 70,
                      height: 70,
                      marginTop: 3,
                      marginLeft: "26%",
                      overflow: "visible",
                    }}
                  />
                  <View
                    style={{
                      // marginLeft: 10,
                      marginTop: 3,
                      // backgroundColor: "red",
                      height: 60,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: "white",
                        // alignSelf: "center",
                        fontSize: 11,
                        bottom: 3,
                        // marginLeft: "10%",
                        // paddingLeft: 8,
                        fontWeight: "700",
                        maxWidth: 140,
                        textAlign: "center",
                        flexWrap: "wrap",
                        // hyphenationFrequency: "normal", // Add this for Android
                        overflow: "hidden",
                        // wordWrap: "break-word",
                      }}
                      numberOfLines={4}
                      ellipsizeMode="tail"
                    >
                      {selectedCourt.address}
                    </Text>
                  </View>
                </LinearGradient>
              </View>
            </View>
          </Animated.View>
        </Pressable>
      );
    }
    if (route?.params?.imageRefs) {
      return (
        <Pressable
          style={{ width: "100%", marginTop: -30 }}
          onPress={() => {
            // navigation.navigate("MapCC");
            setChoosingCourt(true);
            setCourtImageLoaded(false);
          }}
        >
          <Animated.View
            style={{
              width: "100%",
              alignItems: "center",
              opacity: opacity1,
            }}
          >
            <ActivityIndicator
              style={{
                bottom: -70,
                zIndex: 2,
                opacity: courtImageLoaded ? 0 : 1,
              }}
            />

            <View
              style={{
                // height: 10,
                width: "92%",
                borderRadius: 10,
                overflow: "hidden",
                position: "relative",
              }}
            >
              <View>
                <Image
                  source={{ uri: route.params.imageRefs[0] }}
                  style={{
                    height: 155,
                    width: "100%",
                    borderRadius: 10,
                    alignSelf: "center",
                  }}
                  onLoadEnd={() => {
                    setCourtImageLoaded(true);
                  }}
                />
                <LinearGradient
                  colors={[
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.45)",
                    "rgba(0,0,0,0.2)",
                    // "rgba(0,0,0,0.1)",
                    "rgba(0,0,0,0)",
                  ]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={{
                    position: "absolute",
                    left: 0,
                    top: 0,
                    paddingTop: 10,
                    // height: 135,
                    height: "100%",
                    width: "50%",
                  }}
                >
                  <Image
                    source={require("./../images/white-marker.png")}
                    style={{
                      width: 70,
                      height: 70,
                      marginTop: 3,
                      marginLeft: "26%",
                      overflow: "visible",
                    }}
                  />
                  <View
                    style={{
                      // marginLeft: 10,
                      marginTop: 3,
                      // backgroundColor: "red",
                      height: 60,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text
                      style={{
                        color: "white",
                        // alignSelf: "center",
                        fontSize: 11,
                        bottom: 3,
                        // marginLeft: "10%",
                        // paddingLeft: 8,
                        fontWeight: "700",
                        maxWidth: 140,
                        textAlign: "center",
                        flexWrap: "wrap",
                        // hyphenationFrequency: "normal", // Add this for Android
                        overflow: "hidden",
                        // wordWrap: "break-word",
                      }}
                      numberOfLines={4}
                      ellipsizeMode="tail"
                    >
                      {route.params.address}
                    </Text>
                  </View>
                </LinearGradient>
              </View>
            </View>
          </Animated.View>
        </Pressable>
      );
    }
  }, [courtImageLoaded, selectedCourt, route]);

  const SlidableComponent = (selectedDate) => {
    const screenWidth = Dimensions.get("window").width;
    const [slideCompleted, setSlideCompleted] = useState(false);
    const [showActivityIndicator, setShowActivityIndicator] = useState(false);

    const translateX = useRef(new Animated.Value(0)).current;

    const overlayTranslateX = useRef(
      new Animated.Value(-(screenWidth * 0.4))
    ).current;

    // console.log("widht", screenWidth);

    useEffect(() => {
      const loopAnimation = () => {
        Animated.timing(overlayTranslateX, {
          toValue: screenWidth * 0.7,
          duration: 4000,
          // easing: Easing.linear,
          useNativeDriver: true,
        }).start(() => {
          overlayTranslateX.setValue(-(screenWidth * 0.7));
          loopAnimation();
        });
      };

      loopAnimation();
    }, []);

    // const overlayTranslateX2 = useRef(new Animated.Value(0)).current;

    // useEffect(() => {
    //   const loopAnimation = () => {
    //     Animated.timing(overlayTranslateX2, {
    //       toValue: 300,
    //       duration: 2000,
    //       // easing: Easing.linear,
    //       useNativeDriver: true,
    //     }).start(() => {
    //       overlayTranslateX2.setValue(0);
    //       loopAnimation();
    //     });
    //   };

    //   loopAnimation();
    // }, []);

    const onSlide = (event) => {
      if (event.nativeEvent.oldState === State.ACTIVE) {
        const finalTranslateX = event.nativeEvent.translationX;
        if (finalTranslateX >= 120) {
          uploadGame(setShowActivityIndicator, setSlideCompleted);

          // setShowActivityIndicator(true);

          // setTimeout(() => {
          //   navigation.goBack();
          // }, 2000);

          Animated.timing(translateX, {
            toValue: 500,
            duration: 500,
            useNativeDriver: true,
          }).start();
        } else {
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      }
    };

    const onGestureEvent = (event) => {
      if (event.nativeEvent.state === State.ACTIVE) {
        translateX.setValue(event.nativeEvent.translationX);
      }
    };

    // if (date !== null)

    // console.log(`${year}-${month}-${day}`);

    const color = () => {
      if (
        !slideCompleted &&
        selectedCourt !== false &&
        eventName !== "" &&
        isTime1LessThanTime2
      ) {
        return red;
      } else {
        return "rgba(0,0,0,0.3)";
      }
    };

    return (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          backgroundColor: "white",
          borderRadius: 10,
          overflow: "hidden",
          width: "90%",
          // height: 50,
          borderWidth: 1,
          borderColor:
            selectedCourt !== false && eventName !== "" && isTime1LessThanTime2
              ? red
              : "rgba(0,0,0,0.3)",
        }}
      >
        {showActivityIndicator && (
          <ActivityIndicator style={{ marginLeft: "49%" }} color={red} />
        )}

        {/* <Animated.View
          style={{
            position: "absolute",
            // backgroundColor: "rgba(255, 255, 255, 0.5)",
            zIndex: 2,
            width: "20%",
            height: "100%",
            // shadowColor: "rgba(255, 255, 255, 0.5)",
            // shadowRadius: 5,
            // shadowOpacity: 10,
            transform: [{ translateX: overlayTranslateX2 }],
          }}
        >
          <LinearGradient
            colors={[
              "rgba(255, 25, 255, 0)",
              "rgba(255, 25, 255, 0.6)",
              "rgba(255, 25, 255, 0)",
            ]}
            style={{
              width: "100%",
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
          ></LinearGradient>
        </Animated.View> */}

        <PanGestureHandler
          onGestureEvent={onGestureEvent}
          onHandlerStateChange={onSlide}
          enabled={
            !slideCompleted &&
            selectedCourt !== false &&
            eventName !== null &&
            isTime1LessThanTime2
          }
          hitSlop={{ top: 150, bottom: 100, left: 150, right: 150 }}
        >
          <View style={{ height: "100%", width: "100%" }}>
            {!slideCompleted &&
              selectedCourt !== false &&
              eventName !== "" &&
              isTime1LessThanTime2 && (
                <Animated.View
                  style={{
                    position: "absolute",
                    // backgroundColor: "rgba(255, 255, 255, 0.5)",
                    zIndex: 2,
                    width: "90%",
                    height: "100%",
                    // shadowColor: "rgba(255, 255, 255, 0.5)",
                    // shadowRadius: 5,
                    // shadowOpacity: 10,
                    transform: [{ translateX: overlayTranslateX }],
                    justifyContent: "space-between",
                    flexDirection: "row",
                  }}
                >
                  <LinearGradient
                    colors={[
                      "rgba(255, 255, 255, 0)",
                      "rgba(255, 255, 255, 0.2)",
                      "rgba(255, 255, 255, 0.4)",
                      "rgba(255, 255, 255, 0.6)",
                      "rgba(255, 255, 255, 0.6)",
                      "rgba(255, 255, 255, 0.4)",
                      "rgba(255, 255, 255, 0.2)",
                      "rgba(255, 255, 255, 0)",
                    ]}
                    style={{
                      width: "50%",
                      height: "100%",
                      // justifyContent: "center",
                      // alignItems: "center",
                    }}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                  ></LinearGradient>
                  <LinearGradient
                    colors={[
                      "rgba(255, 255, 255, 0)",
                      "rgba(255, 255, 255, 0.2)",
                      "rgba(255, 255, 255, 0.4)",
                      "rgba(255, 255, 255, 0.6)",
                      "rgba(255, 255, 255, 0.6)",
                      "rgba(255, 255, 255, 0.4)",
                      "rgba(255, 255, 255, 0.2)",
                      "rgba(255, 255, 255, 0)",
                    ]}
                    style={{
                      width: "50%",
                      height: "100%",
                      // justifyContent: "center",
                      // alignItems: "center",
                    }}
                    start={{ x: 0, y: 0.5 }}
                    end={{ x: 1, y: 0.5 }}
                  ></LinearGradient>
                </Animated.View>
              )}
            <Animated.View
              style={{
                flexDirection: "row",
                flex: 1,
                backgroundColor: "transparent",
                justifyContent: "center",
                alignItems: "center",
                borderRadius: 4,
                height: 50,
                width: "100%",
                transform: [
                  {
                    translateX: translateX.interpolate({
                      inputRange: [0, 150, 500],
                      outputRange: [0, 150, 500],
                    }),
                  },
                ],
              }}
            >
              <>
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={18}
                  color={color()}
                  style={{
                    marginRight: -5,
                    opacity: 1,
                  }}
                  // onPress={() => {
                  //   setLoadingNewImage(true);
                  //   setIndex((index + 1) % images.length);
                  // }}
                />
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={18}
                  color={color()}
                  style={{
                    marginRight: 5,
                    opacity: 1,
                  }}
                  // onPress={() => {
                  //   setLoadingNewImage(true);
                  //   setIndex((index + 1) % images.length);
                  // }}
                />
                <Text
                  style={{
                    color: color(),
                    fontWeight: "600",
                    fontSize: 17,
                    alignSelf: "center",
                  }}
                >
                  {language.publishGame}
                </Text>
              </>
            </Animated.View>
          </View>
        </PanGestureHandler>
      </View>
    );
  };

  // useLayoutEffect(() => {
  //   navigation.setOptions({
  //     title: "Create a new game",
  //     headerBackVisible: false,
  //     headerLeft: null,
  //   });
  // }, [navigation]);

  // const AgeInput = () => {
  //   const { selectedRestrictionType, setSelectedRestrictionType } =
  //     useContext(MyGamesDataContext);

  //   const { ageLimit, setAgeLimit } = useContext(MyGamesDataContext);

  // };

  const CustomSwitch = React.memo(
    ({
      navigation,
      selectionMode,
      roundCorner,
      option1,
      option2,
      option3,
      selectionColor,
    }) => {
      const [getRoundCorner, setRoundCorner] = useState(roundCorner);

      // const { selectedRestrictionType, setSelectedRestrictionType } =
      //   useContext(MyGamesDataContext);

      const [tempNumber, setTempNumber] = useState(0);

      const updatedSwitchData = (val) => {
        setSelectedRestrictionType(val);
        // onSelectSwitch(val);
      };

      return (
        <View>
          <View
            style={{
              height: 44,
              width: "100%",
              backgroundColor: "white",
              borderRadius: getRoundCorner ? 25 : 0,
              borderWidth: 0.5,
              borderColor: selectionColor,
              flexDirection: "row",
              justifyContent: "center",
              padding: 2,
            }}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => updatedSwitchData(1)}
              style={{
                flex: 1,

                backgroundColor:
                  selectedRestrictionType == 1 ? selectionColor : "white",
                borderRadius: getRoundCorner ? 25 : 0,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color:
                    selectedRestrictionType == 1 ? "white" : selectionColor,
                }}
              >
                {option1}
              </Text>
            </TouchableOpacity>

            {/* <TouchableOpacity
              activeOpacity={1}
              onPress={() => updatedSwitchData(2)}
              style={{
                flex: 1,

                backgroundColor:
                  selectedRestrictionType == 2 ? selectionColor : "white",
                borderRadius: getRoundCorner ? 25 : 0,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color:
                    selectedRestrictionType == 2 ? "white" : selectionColor,
                }}
              >
                {option3}
              </Text>
            </TouchableOpacity> */}
            <TouchableOpacity
              TouchableOpacity
              activeOpacity={1}
              onPress={() => updatedSwitchData(3)}
              style={{
                flex: 1,

                backgroundColor:
                  selectedRestrictionType == 3 ? selectionColor : "white",
                borderRadius: getRoundCorner ? 25 : 0,
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text
                style={{
                  color:
                    selectedRestrictionType == 3 ? "white" : selectionColor,
                }}
              >
                {option2}
              </Text>
            </TouchableOpacity>
          </View>
          {/* <AgeInput />
           */}
        </View>
      );
    }
  );

  const screenWidth = Dimensions.get("window").width;
  // console.log(choosingCourt);

  // (prevProps, nextProps) => {
  //   return true;
  // }

  // const resetCreateGame = () => {
  //   setAgeLimit(0);
  //   setSelectedRestrictionType(1);
  //   setDate(new Date(Date.now()));
  //   setDateEnd(new Date(Date.now()));
  //   // navigation.navigate("MyGames");
  // };

  // useEffect(() => {
  //   resetCreateGame();

  //   return () => {
  //     // console.log("unmounted create game component");
  //   };
  // }, []);

  // const navigation = useNavigation();
  // console.log(margin2, "margin2");

  // if (choosingCourt) {
  //   return <Map />;
  // }

  const timeColor = () => {
    if (isTime1LessThanTime2) {
      return;
    }
  };

  // console.log(new Date(year, month, day, hours, minutes));
  // console.log(
  //   new Date().getDate() == day || new Date().getDate() == day[1],
  //   new Date().getDate(),
  //   "=",
  //   day[1]
  // );

  // console.log(time1, time2);

  return (
    <View style={{ flex: 1, backgroundColor: "rgba(0,0,0,0)" }}>
      {choosingCourt && <Map />}
      {/* {choosingCourt && <Map />} */}
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        // keyboardVerticalOffset={60}
        // behavior="height"
        style={{ flex: 1, backgroundColor: "rgba(0,0,0,0)" }}
        // enabled=[]
        // keyboardVerticalOffset={-180}
      >
        <View
          // behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{
            // flex: 1,
            // justifyContent: "center",
            // alignItems: "center",
            width: "100%",
            height: "100%",
            backgroundColor: "white",
            // top: margin2 ? -40 : 0,
          }}
        >
          <ScrollView>
            <TouchableHighlight
              style={{
                marginRight: 10,
                // bottom: 3,
                top: 55,
                // backgroundColor: "blue",
                // padding: 10,
                width: 45,
                height: 45,
                position: "absolute",
                marginTop: 2,
                right: -7,
                zIndex: 2,
              }}
              onPress={() => {
                navigation.goBack();
              }}
              underlayColor="rgba(0, 0, 0, 0)"
            >
              <AntDesign
                name="close"
                size={32}
                color={red}
                style={{
                  opacity: 1,
                }}
                // onPress={() => {
                //   setSelectedMarker(null);
                // }}
              />
            </TouchableHighlight>

            <View
              style={{
                alignSelf: "center",
                overflow: "visible",
                marginTop: 68,
              }}
            >
              <Text
                style={{
                  fontFamily: "BungeeInline-Regular",
                  //   fontWeight: "900",
                  fontSize: 24,
                  color: red,
                  overflow: "visible",
                  lineHeight: 30,
                  // height: "100%",
                }}
              >
                {language.editEvent}
              </Text>
            </View>

            <View style={{ marginTop: 50, alignSelf: "center", width: "100%" }}>
              <TextInput
                style={{ fontSize: 23, color: "black", textAlign: "center" }}
                value={eventName}
                placeholder={language.nameOfEvent}
                onChangeText={(newText) => {
                  setEventName(newText);
                }}
                maxLength={27}
              />
              <Text
                style={{
                  position: "absolute",
                  right: 19,
                  bottom: 5,
                  fontSize: 11,
                  color: "rgba(0,0,0,0.4)",
                }}
              >
                {`${eventName.length}/27`}
              </Text>
              <View
                style={{
                  backgroundColor: red,
                  marginTop: 6.5,
                  alignSelf: "center",
                  width: "90%",
                  height: 1.5,
                }}
              />
            </View>

            <View style={{ marginTop: 35, width: "100%" }}>
              <LinearGradient
                colors={[
                  "rgba(0,0,0,0.06)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.02)",
                  "rgba(0,0,0,0.03)",
                ]}
                start={{ x: 0, y: 1 }}
                end={{ x: 0, y: 0 }}
                style={{
                  left: 0,
                  height: 75,
                  width: "92%",
                  alignSelf: "center",
                }}
              >
                <TextInput
                  style={{
                    width: "84%",
                    // alignSelf: "center",
                    bottom: 12,
                    position: "absolute",
                    // maxWidth: "80%",
                    textAlign: "left",
                    paddingLeft: 10,
                  }}
                  placeholder={language.eventInfoInput}
                  placeholderTextColor={"rgba(0,0,0,0.5)"}
                  multiline={true}
                  numberOfLines={2}
                  value={eventDescription}
                  onChangeText={(text) => {
                    setEventDescription(text);
                  }}
                  maxLength={200}
                />
                <Text
                  style={{
                    position: "absolute",
                    right: 10,
                    bottom: 10,
                    fontSize: 10,
                    color: "rgba(0,0,0,0.4)",
                  }}
                >
                  {`${eventDescription.length}/200`}
                </Text>
                <View
                  style={{
                    width: "95%",
                    height: 1,
                    backgroundColor: "black",
                    alignSelf: "center",
                    bottom: 7,
                    position: "absolute",
                  }}
                ></View>
              </LinearGradient>
            </View>

            <View
              style={{ marginTop: 30, width: "100%", alignItems: "center" }}
            >
              {(selectedCourt || route?.params?.imageRefs) && ImageDiv}
              {!selectedCourt && !route?.params?.imageRefs && (
                <View
                  style={{
                    height: 155,
                    width: "90%",
                    backgroundColor: "rgba(0,0,0,0.015)",
                    borderRadius: 10,
                    borderWidth: 2,
                    borderColor: "rgba(0,0,0,0.1)",
                    borderStyle: "dashed",
                  }}
                >
                  <Pressable
                    onPress={() => {
                      setChoosingCourt(true);
                    }}
                    style={({ pressed }) => ({
                      borderRadius: 8,
                      transform: [{ scale: pressed ? 0.97 : 1 }],
                      width: "55%",
                      height: 40,
                      backgroundColor: "rgba(0,0,0,0.05)",
                      borderColor: "rgba(0,0,0,0.1)",
                      borderWidth: 1,
                      // shadowColor: red,
                      // shadowOpacity: 0.6,
                      // shadowRadius: 5,
                      // shadowOffset: { width: 0, height: 0 },
                      justifyContent: "center",
                      alignSelf: "center",
                      marginTop: 55,
                    })}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        alignSelf: "center",
                        color: "black",
                        fontSize: 15,
                        // fontFamily: "roboto",
                      }}
                    >
                      {language.chooseCourt}
                    </Text>
                  </Pressable>
                </View>
              )}
            </View>
            <View style={{ marginTop: 30 }}>
              <View
                style={{
                  width: "90%",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignSelf: "center",
                }}
              >
                <View style={{ width: "55%", height: 50 }}>
                  <Text style={{ fontSize: 14, fontWeight: "500" }}>
                    {language.date}:
                  </Text>
                  <View>
                    <Pressable
                      onPress={showDatePicker}
                      style={({ pressed }) => ({
                        width: "90%",
                        height: 30,
                        marginTop: 15,
                        backgroundColor: "rgba(247,245,246,1)",
                        borderRadius: 6,
                        borderWidth: 1,
                        borderColor: "rgba(215,212,212,1)",
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                      })}
                    >
                      <Text style={{ alignSelf: "center", marginTop: 5 }}>
                        {date.toLocaleDateString(language.dateLocale, {
                          weekday: "long",
                          day: "numeric",
                          month: "short",
                          year: "numeric",
                        })}
                      </Text>
                    </Pressable>
                    <DateTimePickerModal
                      locale={language.dateLocale}
                      isVisible={isDatePickerVisible}
                      mode="date"
                      onConfirm={handleConfirm}
                      onCancel={hideDatePicker}
                      is24Hour={true}
                      minimumDate={new Date()}
                      // maximumDate={new Date(new Date().getFullYear(), 11, 31)}
                    />
                  </View>
                  {/* <DateTimePickerModal
                // isVisible={isDatePickerVisible}
                mode="time"
                onConfirm={handleConfirm}
                onCancel={hideDatePicker}
                is24Hour={true}
              /> */}
                </View>

                <View
                  style={{
                    width: "45%",
                    height: 50,
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      width: "100%",
                      justifyContent: "space-between",
                    }}
                  >
                    <View style={{ width: "45%", marginRight: 10 }}>
                      <Text style={{ fontSize: 14, fontWeight: "500" }}>
                        {language.from}:
                      </Text>
                      <Pressable
                        onPress={showTimePicker1}
                        style={({ pressed }) => ({
                          width: "90%",
                          height: 30,
                          marginTop: 15,
                          backgroundColor: "rgba(247,245,246,1)",

                          borderRadius: 6,
                          borderWidth: 1,
                          borderColor: "rgba(215,212,212,1)",
                          transform: [{ scale: pressed ? 0.97 : 1 }],
                        })}
                      >
                        <Text style={{ alignSelf: "center", marginTop: 5 }}>
                          {time1.toLocaleTimeString("cs-CZ", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: false,
                          })}
                        </Text>
                      </Pressable>
                      <DateTimePickerModal
                        isVisible={isTimePickerVisible1}
                        mode="time"
                        onConfirm={handleConfirmTime1}
                        onCancel={hideTimePicker1}
                        is24Hour={true}
                        // minimumDate={
                        //   new Date().getDate() == day ||
                        //   new Date().getDate() == day[1]
                        //     ? new Date()
                        //     : null
                        // }
                        // maximumDate={new Date(new Date().getFullYear(), 11, 31)}
                      />
                    </View>

                    <View style={{ width: "45%" }}>
                      <Text style={{ fontSize: 14, fontWeight: "500" }}>
                        {language.to}:
                      </Text>
                      <Pressable
                        onPress={showTimePicker2}
                        style={({ pressed }) => ({
                          width: "90%",
                          height: 30,
                          marginTop: 15,
                          backgroundColor: isTime1LessThanTime2
                            ? "rgba(247,245,246,1)"
                            : "rgba(255,0,0,0.2)",
                          borderRadius: 6,
                          borderWidth: isTime1LessThanTime2 ? 1 : 2,
                          borderColor: isTime1LessThanTime2
                            ? "rgba(215,212,212,1)"
                            : "red",

                          transform: [{ scale: pressed ? 0.97 : 1 }],
                        })}
                      >
                        <Text style={{ alignSelf: "center", marginTop: 5 }}>
                          {time2.toLocaleTimeString("cs-CZ", {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: false,
                          })}
                        </Text>
                      </Pressable>

                      <DateTimePickerModal
                        isVisible={isTimePickerVisible2}
                        mode="time"
                        onConfirm={handleConfirmTime2}
                        onCancel={hideTimePicker2}
                        is24Hour={true}
                        // minimumDate={new Date(time1.getTime())}
                        // maximumDate={new Date(new Date().getFullYear(), 11, 31)}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </View>

            <View style={{ marginTop: 55, alignSelf: "center", width: "92%" }}>
              <CustomSwitch
                selectionMode={1}
                roundCorner={true}
                option1={language.public}
                option2={language.ageRestricted}
                // option3={"Request to join"}
                // onSelectSwitch={onSelectSwitch}
                selectionColor={red}
              />

              {selectedRestrictionType == 3 && (
                <View
                  style={{
                    // position: "absolute",
                    // marginTop: 47,

                    alignSelf: "center",
                    flexDirection: "row",
                    marginTop: 5,
                    justifyContent: "space-evenly",
                    // alignSelf: "flex-end",
                    // justifyContent: "flex-end",
                    width: "90%",
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      marginTop: 15,
                      width: "40%",
                      // justifyContent: "space-between",
                    }}
                  >
                    <Text
                      style={{
                        marginTop: 12,
                        marginRight: "5%",
                        color: "rgba(0,0,0,0.9)",
                      }}
                    >
                      {language.minAge}
                    </Text>
                    <TouchableOpacity
                      onPress={() => {
                        // setMargin(true);
                      }}
                    >
                      <TextInput
                        keyboardType="numeric"
                        style={{
                          fontSize: 15,
                          color: "black",
                          backgroundColor: "rgba(0,0,0,0.03)",
                          padding: 4,
                          borderColor: "rgba(0,0,0,0.1)",
                          borderWidth: 1,
                          borderRadius: 5,
                          width: 50,
                          height: 35,
                          alignSelf: "center",
                          textAlign: "center",
                        }}
                        autoCapitalize="none"
                        autoCorrect={false}
                        value={ageLimitMin}
                        onChangeText={(text) => {
                          let newText = "";
                          let numbers = "0123456789";

                          for (var i = 0; i < text.length; i++) {
                            if (numbers.indexOf(text[i]) > -1) {
                              newText = newText + text[i];
                            } else {
                              alert("Please enter numbers only");
                            }
                          }
                          setAgeLimitMin(newText);
                        }}
                      />
                    </TouchableOpacity>
                  </View>

                  <View
                    style={{
                      flexDirection: "row",
                      marginTop: 15,
                      width: "40%",

                      // justifyContent: "space-between",
                    }}
                  >
                    <Text
                      style={{
                        marginTop: 12,
                        marginRight: "5%",
                        color: "rgba(0,0,0,0.9)",
                      }}
                    >
                      {language.maxAge}
                    </Text>
                    <TextInput
                      keyboardType="numeric"
                      style={{
                        fontSize: 15,
                        color: "black",
                        // alignSelf: "center",
                        backgroundColor: "rgba(0,0,0,0.03)",
                        padding: 4,
                        // paddingBottom: 30,
                        // flex: 1,
                        borderColor: "rgba(0,0,0,0.1)",
                        borderWidth: 1,
                        borderRadius: 5,
                        width: 50,
                        height: 35,
                        alignSelf: "center",
                        textAlign: "center",
                        // marginTop: 15,
                      }}
                      // placeholder="min. Age"
                      autoCapitalize="none"
                      autoCorrect={false}
                      // onEndEditing={() => {
                      //   setLimitOfUsers(tempNumber);
                      // }}
                      value={ageLimitMax}
                      onChangeText={(text) => {
                        let newText = "";
                        let numbers = "0123456789";

                        for (var i = 0; i < text.length; i++) {
                          if (numbers.indexOf(text[i]) > -1) {
                            newText = newText + text[i];
                          } else {
                            alert("Please enter numbers only");
                          }
                        }
                        setAgeLimitMax(newText);
                      }}
                    />
                  </View>
                </View>
              )}
            </View>
            <View
              style={{
                marginTop: selectedRestrictionType == 3 ? 25 : 25,
                alignSelf: "center",
                width: "90%",
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  marginTop: 0,
                  width: "100%",
                  justifyContent: "space-between",
                }}
              >
                <Text
                  style={{
                    marginTop: 12,
                    marginRight: "15%",
                    color: "rgba(0,0,0,0.9)",
                  }}
                >
                  {language.usersLimit}
                </Text>
                {/* <TouchableOpacity
                  onPress={() => {
                    setMargin(true);
                  }}
                > */}
                <View>
                  <TextInput
                    keyboardType="numeric"
                    style={{
                      fontSize: 15,
                      color: "black",
                      backgroundColor: "rgba(0,0,0,0.03)",
                      padding: 4,
                      borderColor: "rgba(0,0,0,0.1)",
                      borderWidth: 1,
                      borderRadius: 5,
                      width: 120,
                      height: 35,
                      // paddingBottom: 10,
                      alignSelf: "center",
                      textAlign: "center",
                    }}
                    placeholder={language.defaultUserLimit}
                    autoCapitalize="none"
                    autoCorrect={false}
                    value={limitOfUsers}
                    onChangeText={(text) => {
                      let newText = "";
                      let numbers = "0123456789";

                      for (var i = 0; i < text.length; i++) {
                        if (numbers.indexOf(text[i]) > -1) {
                          newText = newText + text[i];
                        } else {
                          alert("Please enter numbers only");
                        }
                      }
                      setLimitOfUsers(newText);
                    }}
                  />
                </View>

                {/* </TouchableOpacity> */}
              </View>
            </View>
            <View
              style={{
                // position: "absolute",
                alignSelf: "center",
                // bottom: 50,
                width: "100%",
                // height: 50,
                marginTop: 30,

                paddingBottom: 50,
              }}
            >
              <View
                style={{
                  alignItems: "center",
                  // position: "",
                  // flex: 1,
                  // justifyContent: "center",
                  // marginTop: 75,
                  width: "100%",
                  // height: 0,
                }}
              >
                <SlidableComponent />
              </View>
            </View>
          </ScrollView>
        </View>
        {/* </KeyboardAvoidingView> */}

        <View
          style={{
            alignItems: "center",
            flex: 1,
            justifyContent: "center",
            // marginTop: 0,
            position: "absolute",
            top: 55,
            right: -5,
          }}
        ></View>
      </KeyboardAvoidingView>
    </View>
  );
}

export default function MyGamesStack() {
  return (
    <MyGamesDataContextProvider>
      {/* <Stack.Navigator
        // defaultSreenOptions={RootMyGamesStack}

        screenOptions={
          {
            // unmountInactiveRoutes: false,
            // unmountOnBlur: false,
          }
        }
      >
        <Stack.Screen
          name="RootMyGamesStack"
          component={RootMyGamesStack}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MapCC"
          component={MapCC}
          // options={{ headerShown: false }}
        />
      </Stack.Navigator> */}
      <RootMyGamesStack />
    </MyGamesDataContextProvider>
  );
}
