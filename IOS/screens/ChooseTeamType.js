import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Dimensions,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Feather, Ionicons } from "@expo/vector-icons";
import { red } from "./colors";
import { useSelector } from "react-redux";
import { useRoute } from "@react-navigation/native";

const { height } = Dimensions.get("window");

const TeamOpennessSelection = ({ navigation }) => {
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const { language } = useSelector((state) => state.language);
  const [isVisible, setIsVisible] = useState(true);
  const [allowJoinRequests, setAllowJoinRequests] = useState(true);
  const [allowChallenges, setAllowChallenges] = useState(true);

  useEffect(() => {
    if (!isVisible) {
      setAllowJoinRequests(false);
      setAllowChallenges(false);
    }

    if (isVisible) {
      setAllowJoinRequests(true);
      setAllowChallenges(true);
    }
  }, [isVisible]);

  console.log(route.params);

  const handleNextPress = () => {
    navigation.navigate("CreateTeam", {
      ...route.params,
      teamOpenness: { isVisible, allowJoinRequests, allowChallenges },
    });
  };

  const OptionSwitch = ({
    value,
    onValueChange,
    title,
    description,
    icon,
    disabled,
  }) => (
    <View
      style={{
        marginBottom: 20,
        paddingHorizontal: 15,
        backgroundColor: "white",
        borderRadius: 12,
        padding: 15,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 2,
        opacity: disabled ? 0.5 : 1,
        marginHorizontal: 10,
      }}
    >
      <View
        style={{ flexDirection: "row", marginBottom: 10, alignItems: "center" }}
      >
        <Ionicons
          name={icon}
          size={20}
          color={red}
          style={{ marginRight: 8 }}
        />
        <Text style={{ fontSize: 16, fontWeight: "600" }}>{title}</Text>
      </View>
      <Text
        style={{ fontSize: 14, color: "rgba(0,0,0,0.8)", marginBottom: 10 }}
      >
        {description}
      </Text>
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Text
          style={{ fontSize: 14, color: value && !disabled ? "black" : "#666" }}
        >
          {value && !disabled ? language.enabled : language.disabled}
        </Text>
        <Switch
          trackColor={{ false: "#767577", true: red }}
          thumbColor={value ? "#f4f3f4" : "#f4f3f4"}
          onValueChange={onValueChange}
          value={value}
          disabled={disabled}
        />
      </View>
    </View>
  );

  return (
    <View style={{ flex: 1, backgroundColor: "#f5f5f5" }}>
      <ScrollView
        style={{ paddingTop: insets.top }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 80 }}
      >
        <View style={{ padding: 15, marginBottom: 12 }}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={{ marginBottom: 10 }}
            >
              <Feather name="arrow-left" size={26} color={red} />
            </TouchableOpacity>
            <Text
              style={{
                fontSize: 20,
                fontWeight: "bold",
                marginBottom: 12,
                marginTop: 5,
                marginLeft: 10,
              }}
            >
              {language.chooseTeamOpenness}
            </Text>
          </View>

          <Text style={{ fontSize: 14, color: "rgba(0,0,0,0.8)" }}>
            {language.opennessDescription}
          </Text>
        </View>

        <OptionSwitch
          value={isVisible}
          onValueChange={setIsVisible}
          title={language.teamVisibility}
          description={language.teamVisibilityDescription}
          icon="eye-outline"
        />

        <OptionSwitch
          value={allowJoinRequests}
          onValueChange={setAllowJoinRequests}
          title={language.allowJoinRequests}
          description={language.joinRequestsDescription}
          icon="people-outline"
          disabled={!isVisible}
        />

        <OptionSwitch
          value={allowChallenges}
          onValueChange={setAllowChallenges}
          title={language.allowChallenges}
          description={language.challengesDescription}
          icon="trophy-outline"
          disabled={!isVisible}
        />
        <Text
          style={{
            fontSize: 13,
            color: "#666",
            textAlign: "center",
            marginTop: 0,
            paddingHorizontal: 15,
            marginBottom: 80,
          }}
        >
          {language.changeOpenessLater}
        </Text>
      </ScrollView>

      <View
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: "white",
          paddingTop: 20,
          paddingBottom: insets.bottom + 0,
          paddingHorizontal: 15,
          borderWidth: 1,
          borderColor: "#e0e0e0",
          // borderBottomEndRadius: 12,
          borderTopLeftRadius: 25,
          borderTopRightRadius: 25,
          // borderTopStartRadius: 12,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: -1 },
          shadowOpacity: 0.05,
          shadowRadius: 3,
          elevation: 2,
        }}
      >
        <TouchableOpacity
          style={{
            backgroundColor: red,
            paddingVertical: 15,
            borderRadius: 17,
            alignItems: "center",
          }}
          onPress={handleNextPress}
          activeOpacity={0.7}
        >
          <Text style={{ color: "white", fontSize: 16, fontWeight: "bold" }}>
            {language.next}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default TeamOpennessSelection;
