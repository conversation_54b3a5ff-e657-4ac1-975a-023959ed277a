import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { auth } from "../config/firebase";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log error messages to an error reporting service here
    this.setState({
      hasError: true,
      error: error,
      errorInfo: errorInfo,
    });
  }
  zz;

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <View style={styles.container}>
          <Text style={styles.text}>
            Something went wrong. Reload the app please
          </Text>
          {/* Uncomment below line to display error message */}
          <Text style={styles.text}>
            {this.state.error && this.state.error.toString()}
            {auth.currentUser.uid}
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  text: {
    fontSize: 18,
    color: "red",
  },
});

export default ErrorBoundary;
