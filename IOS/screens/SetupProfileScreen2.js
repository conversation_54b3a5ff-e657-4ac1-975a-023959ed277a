// screens/SetupProfileScreen2.js
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  Animated,
  Keyboard,
  TouchableOpacity,
  Platform,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import { MaterialIcons } from "@expo/vector-icons";
import { red } from "../screens/colors";
import { EN, CZ } from "./../assets/strings";
import * as Haptics from "expo-haptics";

// Components
import NavigationButton from "../components/NavigationButton";
import { useSetupProfile } from "./SetupProfileContext";
import { useFocusEffect } from "@react-navigation/native";

const SetupProfileScreen2 = ({ navigation }) => {
  const {
    date,
    setDate,
    dateChanged,
    setDateChanged,
    description,
    setDescription,
    languageCode,
    setCurrentStep,
    skillLevel,
    setSkillLevel,
    basketballDate,
    setBasketballDate,
    basketballDateChanged,
    setBasketballDateChanged,
    height,
    setHeight,
    weight,
    setWeight,
  } = useSetupProfile();

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const languageStrings = languageCode === "CZ" ? CZ : EN;
  const scrollViewRef = useRef(null);

  // Animation for fields
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useFocusEffect(() => {
    setCurrentStep(1);
  });

  useEffect(() => {
    // Start fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Calculate playing experience in years
  const calculateExperience = (startDate) => {
    const diff_ms = Date.now() - startDate.getTime();
    const years = Math.floor(diff_ms / (1000 * 60 * 60 * 24 * 365.25));
    return years;
  };

  // // Listen for keyboard events
  // useEffect(() => {
  //   const keyboardDidShowListener = Keyboard.addListener(
  //     "keyboardDidShow",
  //     () => {
  //       setKeyboardVisible(true);
  //       scrollViewRef.current?.scrollToPosition(0, 150, true);
  //     }
  //   );
  //   const keyboardDidHideListener = Keyboard.addListener(
  //     "keyboardDidHide",
  //     () => {
  //       setKeyboardVisible(false);
  //     }
  //   );

  //   return () => {
  //     keyboardDidHideListener.remove();
  //     keyboardDidShowListener.remove();
  //   };
  // }, []);

  // Date picker functions
  const showDatePicker = () => setDatePickerVisibility(true);
  const hideDatePicker = () => setDatePickerVisibility(false);
  const handleConfirm = (selectedDate) => {
    setBasketballDate(selectedDate);
    setBasketballDateChanged(true);
    hideDatePicker();
  };

  // Skill level options
  const skillLevelOptions = [
    "beginner",
    "intermediate",
    "advanced",
    "professional",
  ];

  // Input validation helpers
  const validateNumberInput = (text, setter, max) => {
    // Only allow digits
    const numericValue = text.replace(/[^0-9]/g, "");

    // Optional max check
    if (max && parseInt(numericValue) > max) {
      setter(max.toString());
    } else {
      setter(numericValue);
    }
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={100} // Adjust this value to scroll up more
        // keyboardOpeningTime={50}
        // extraHeight={Platform.select({
        //   android: 100,
        //   ios: 50,
        // })}
      >
        {/* Intro message */}
        <Animated.View style={[styles.welcomeContainer, { opacity: fadeAnim }]}>
          <Text style={styles.welcomeText}>
            {languageStrings.basketballExperienceText}
          </Text>
        </Animated.View>

        {/* Basketball Experience Form */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Start Date */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>
              {languageStrings.startPlayingDate}
            </Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={showDatePicker}
            >
              <Text style={styles.dateText}>
                {basketballDate.toLocaleDateString(languageStrings.dateLocale, {
                  // day: "numeric",
                  month: "long",
                  year: "numeric",
                })}
              </Text>
              <View style={styles.dateButtonContainer}>
                <MaterialIcons name="date-range" size={20} color={red} />
                <Text style={styles.changeDateText}>
                  {languageStrings.change}
                </Text>
              </View>
            </TouchableOpacity>
            <DateTimePickerModal
              locale={languageStrings.dateLocale}
              isVisible={isDatePickerVisible}
              mode="date"
              onConfirm={handleConfirm}
              onCancel={hideDatePicker}
              is24Hour={true}
              maximumDate={new Date()}
            />
            <Text style={styles.ageIndicator}>
              {calculateExperience(basketballDate) > 0
                ? `${languageStrings.playingFor} `
                : languageStrings.justStarted}
              {calculateExperience(basketballDate) > 0 && (
                <Text style={styles.ageNumber}>
                  {calculateExperience(basketballDate)}{" "}
                  {languageCode === "CZ"
                    ? calculateExperience(basketballDate) === 1
                      ? "rok"
                      : calculateExperience(basketballDate) < 5
                      ? "roky"
                      : "let"
                    : calculateExperience(basketballDate) === 1
                    ? languageStrings.year
                    : languageStrings.years}
                </Text>
              )}
            </Text>
          </View>

          {/* Height and Weight in one row */}
          <View style={styles.inputContainer}>
            <View style={styles.rowContainer}>
              <View style={styles.halfInputContainer}>
                <Text style={styles.inputLabel}>{languageStrings.height}</Text>
                <View style={styles.inputWithUnit}>
                  <TextInput
                    style={styles.numericInput}
                    keyboardType="numeric"
                    value={height}
                    onChangeText={(text) =>
                      validateNumberInput(text, setHeight, 250)
                    }
                    maxLength={3}
                    placeholder="175"
                  />
                  <Text style={styles.inputUnitLabel}>cm</Text>
                </View>
              </View>

              <View style={styles.halfInputContainer}>
                <Text style={styles.inputLabel}>{languageStrings.weight}</Text>
                <View style={styles.inputWithUnit}>
                  <TextInput
                    style={styles.numericInput}
                    keyboardType="numeric"
                    value={weight}
                    onChangeText={(text) =>
                      validateNumberInput(text, setWeight, 200)
                    }
                    maxLength={3}
                    placeholder="75"
                  />
                  <Text style={styles.inputUnitLabel}>kg</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Skill Level */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.skillLevel}</Text>
            <View style={styles.skillLevelsContainer}>
              {skillLevelOptions.map((level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.skillLevelButton,
                    skillLevel === level && styles.skillLevelButtonActive,
                  ]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    setSkillLevel(level);
                  }}
                >
                  <Text
                    style={[
                      styles.skillLevelText,
                      skillLevel === level && styles.skillLevelTextActive,
                    ]}
                  >
                    {languageStrings[level]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Description */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>
              {languageStrings.playingDetailsLabel}
            </Text>
            <TextInput
              style={styles.textAreaInput}
              placeholder={languageStrings.basketballDescriptionPlaceholder}
              placeholderTextColor="rgba(0,0,0,0.3)"
              multiline={true}
              numberOfLines={4}
              textAlignVertical="top"
              value={description}
              onChangeText={setDescription}
              maxLength={150}
            />
            <Text style={styles.characterCount}>{description.length}/150</Text>
          </View>
        </Animated.View>
      </KeyboardAwareScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 50,
  },
  welcomeContainer: {
    marginHorizontal: 20,
    marginTop: 5,
    marginBottom: 20,
  },
  welcomeText: {
    fontSize: 15,
    color: "#666",
    lineHeight: 20,
  },
  section: {
    marginBottom: 25,
  },
  inputContainer: {
    marginBottom: 20,
    paddingHorizontal: 15,
  },
  rowContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  halfInputContainer: {
    width: "48%",
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
    paddingLeft: 3,
  },
  inputHelper: {
    fontSize: 14,
    color: "#999",
    marginBottom: 8,
    lineHeight: 20,
  },
  datePickerButton: {
    height: 48,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  dateText: {
    fontSize: 16,
    color: "#333",
  },
  dateButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  changeDateText: {
    color: red,
    marginLeft: 5,
    fontSize: 14,
    fontWeight: "500",
  },
  textAreaInput: {
    height: 100,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingTop: 12,
    paddingBottom: 12,
    fontSize: 15,
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  characterCount: {
    alignSelf: "flex-end",
    marginTop: 5,
    fontSize: 12,
    color: "#aaa",
  },
  errorText: {
    color: "#e74c3c",
    fontSize: 14,
    marginTop: 5,
  },
  ageIndicator: {
    fontSize: 14,
    color: "#666",
    marginTop: 5,
  },
  ageNumber: {
    fontWeight: "bold",
    color: "#333",
  },
  skillLevelsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    marginHorizontal: -5,
  },
  skillLevelButton: {
    backgroundColor: "white",
    borderColor: red,
    borderWidth: 1,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 3,
    marginVertical: 5,
  },
  skillLevelButtonActive: {
    backgroundColor: red,
  },
  skillLevelText: {
    color: red,
    fontWeight: "500",
    fontSize: 13,
  },
  skillLevelTextActive: {
    color: "white",
  },
  inputWithUnit: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    backgroundColor: "rgba(0,0,0,0.01)",
    paddingHorizontal: 15,
  },
  numericInput: {
    flex: 1,
    height: 43,
    fontSize: 16,
  },
  inputUnitLabel: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
  },
});

export default SetupProfileScreen2;
