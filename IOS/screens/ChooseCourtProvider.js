import React, { createContext, useState, useMemo } from "react";

export const MyGamesDataContext = createContext({});

export const MyGamesDataContextProvider = ({ children }) => {
  const [eventName, setEventName] = useState(null);
  const [date, setDate] = useState(new Date(Date.now()));
  const [dateEnd, setDateEnd] = useState(new Date(Date.now()));
  const [selectedRestrictionType, setSelectedRestrictionType] = useState(1);
  const [ageLimit, setAgeLimit] = useState(0);
  const [limitOfUsers, setLimitOfUsers] = useState(null);

  const [creatingGame, setCreatingGame] = useState(false);

  const [selectedCourt, setSelectedCourt] = useState(null);

  const obj = {
    creatingGame: creatingGame,
    setcreatingGame: setCreatingGame,
    date: date,
    setDate: setDate,
    selectedRestrictionType: selectedRestrictionType,
    setSelectedRestrictionType: setSelectedRestrictionType,
    ageLimit: ageLimit,
    setAgeLimit: setAgeLimit,
    eventName: eventName,
    setEventName: setEventName,
    dateEnd: dateEnd,
    setDateEnd: setDateEnd,
    limitOfUsers: limitOfUsers,
    setLimitOfUsers: setLimitOfUsers,
    selectedCourt: selectedCourt,
    setSelectedCourt: setSelectedCourt,
  };

  return (
    <MyGamesDataContext.Provider value={obj}>
      {children}
    </MyGamesDataContext.Provider>
  );
};
