import React, { useEffect, useState, useRef } from "react";

import {
  StyleSheet,
  Text,
  View,
  Button,
  TextInput,
  Image,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
  Dimensions,
  Pressable,
  Animated,
  TouchableWithoutFeedback,
  Keyboard,
  Modal,
  Linking,
  Platform,
} from "react-native";
import {
  createUserWithEmailAndPassword,
  signInWithCredential,
  GoogleAuthProvider,
  OAuthProvider,
} from "@react-native-firebase/auth";
import { auth, db } from "../config/firebase";

import {
  collection,
  CollectionReference,
  doc,
  Firestore,
  getDocs,
  setDoc,
  increment,
  getDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
import * as Font from "expo-font";

import * as Google from "expo-auth-session/providers/google";

import * as AppleAuthentication from "expo-apple-authentication";

export default function Signup({ navigation }) {
  const screenWidth = Dimensions.get("window").width;
  const [password, setPassword] = useState("");
  const [password2, setPassword2] = useState("");
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [age, setAge] = useState("");

  const [imageRef, setImageRef] = useState();

  const [initialIimage, setinitialImage] = useState(null);
  const [image2, setImage2] = useState(null);

  const [clicked, setClicked] = useState(false);
  const [signUpClicked, setSignUpClicked] = useState(false);

  const [imageLoaded, setImageLoaded] = useState(false);

  const [showPage, setShowPage] = useState(false);

  const [userExists, setUserExists] = useState(null);

  const [fontLoaded, setFontLoaded] = useState(false);

  const [urls, setUrls] = useState();

  const [showEmail, setShowEmail] = useState(false);

  // setTimeout(() => {
  //   navigation.goBack();
  // }, 2000);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setShowPage(true);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const infoCollection = collection(db, "info");
      const infoSnapshot = await getDocs(infoCollection);
      const infoData = infoSnapshot.docs.map((doc) => {
        setUrls(doc.data());
      });
    };

    fetchData();
  }, []);

  let tempImage;

  const ContinueLoadingCircle = () => {
    if (clicked) {
      return <ActivityIndicator size="large" />;
    } else {
      return (
        <Text
          style={{
            fontWeight: "bold",
            color: "rgba(197,42,71,1)",
            fontSize: 50,
          }}
        >
          Continue
        </Text>
      );
    }
  };

  // useEffect(() => {
  //   const loadFonts = async () => {
  //     await Font.loadAsync({
  //       Roboto: require("../assets/fonts/Roboto-Regular.ttf"), // replace with your font's path
  //     });
  //     setFontLoaded(true);
  //   };

  //   loadFonts();
  // }, []);

  let [fontsLoaded] = Font.useFonts({
    Roboto: require("../assets/fonts/Roboto-Regular.ttf"),
  });

  useEffect(() => {}, [clicked, signUpClicked]);

  const onHandleSignup = async () => {
    // console.log("Signup success");
    if (password !== password2) {
      alert("Hesla se neshodují");
    } else {
      if (email !== "" && password !== "") {
        setClicked(false);
        // setSignUpClicked(true);
        // getInitialProfilePicture();

        try {
          // await checkIfUserExists();

          createUserWithEmailAndPassword(auth, email, password)
            .then(() => {
              console.log("Signup success");
              // uploadImage(auth.currentUser.uid);
              navigation.navigate("SetupProfile");
            })
            .catch((err) => {
              setSignUpClicked(false);
              Alert.alert("Login error", err.message);
            });
        } catch (error) {
          alert(error);
        }
      }
    }
  };

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId:
      "99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n.apps.googleusercontent.com",
  });

  // useEffect(() => {
  //   console.log("Google auth response", response?.type);
  //   console.log("Google auth response", response?.params);
  //   // ...your existing code...
  // }, [response]);

  useEffect(() => {
    if (response?.type === "success") {
      const { id_token } = response.params;

      const credential = GoogleAuthProvider.credential(id_token);

      // console.log(id_token, credential);

      signInWithCredential(auth, credential)
        .then(async (userCredential) => {
          // Signed in
          const user = userCredential.user;
          // alert("User is signed in with Google!", user);
          // Here you may want to navigate the user to the main screen of the app
          // navigation.navigate('MainScreen');

          const docRef = doc(db, "users", auth.currentUser.uid);
          const docRef2 = doc(db, "users2/", auth.currentUser.uid);
          const docSnap = await getDoc(docRef2);

          if (docSnap.exists) {
            console.log("exists");
            await updateDoc(docRef, {
              logCounter: increment(1),
            });
          }
        })
        .catch((error) => {
          alert("Error signing in with Google", error);
        });
    }
  }, [response]);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 2500,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const [agreed, setAgreed] = useState(false);

  const [signUpType, setSignUpType] = useState(null);

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  // const handleAppleSignIn = async () => {
  //   try {
  //     const appleAuthCredential = await AppleAuthentication.signInAsync({
  //       requestedScopes: [
  //         AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
  //         AppleAuthentication.AppleAuthenticationScope.EMAIL,
  //       ],
  //     });

  //     // Use the returned Apple credentials to authenticate with Firebase
  //     const provider = new OAuthProvider("apple.com");

  //     provider.addScope("email");
  //     provider.addScope("name");

  //     console.log(provider);

  //     const authCredential = provider.credential({
  //       idToken: appleAuthCredential.identityToken,
  //       rawNonce: appleAuthCredential.nonce,
  //     });

  //     console.log(authCredential);

  //     signInWithCredential(authCredential)
  //       .then((userCredential) => {
  //         // Signed in
  //         console.log("User signed in with Apple:", userCredential.user);
  //       })
  //       .catch((error) => {
  //         console.error("Error signing in with Apple:", error);
  //       });
  //   } catch (e) {
  //     if (e.code === "ERR_CANCELED") {
  //       console.log("Apple Sign In was canceled by user");
  //     } else {
  //       console.error("An error occurred during Apple Sign In:", e);
  //     }
  //   }
  // };

  const handleAppleSignIn = async () => {
    try {
      const appleAuthCredential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      // Create OAuthProvider instance for Apple
      const provider = new OAuthProvider("apple.com");
      // const firebaseAuth = getAuth(auth);
      const credential = provider.credential({
        idToken: appleAuthCredential.identityToken,
      });

      signInWithCredential(auth, credential)
        .then(async (userCredential) => {
          // alert("User signed in with Apple:", userCredential.user);
          // const docRef = doc(db, "users", auth.currentUser.uid);
          // const docRef2 = doc(db, "users2/", auth.currentUser.uid);
          // const docSnap = await getDoc(docRef2);
          // if (docSnap.exists) {
          //   console.log("exists");
          //   updateDoc(docRef, {
          //     logCounter: increment(1),
          //   });
          // }
        })
        .catch((error) => {
          alert("Error signing in with Apple:", error);
        });
    } catch (e) {
      if (e.code === "ERR_CANCELED") {
        // alert("Apple Sign In was canceled by the user");
      } else {
        alert("An error occurred during Apple Sign In:", e);
      }
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
        {(!imageLoaded || !showPage || !fontsLoaded) && (
          <View
            style={{
              width: "100%",
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <ActivityIndicator></ActivityIndicator>
          </View>
        )}

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "80%",
                    // height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "0%",
                    }}
                  >
                    {/* <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 15,
                        color: "rgba(0,0,0,0.8)",
                      }}
                    >
                      Terms and Conditions, Privacy Policy and Copyright Policy
                    </Text> */}

                    {/* <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 14,
                        color: "rgba(0,0,0,0.8)",
                        // marginTop: 15,
                      }}
                    >
                      By proceeding, you agree to our Terms and Conditions,
                      Privacy Policy and Copyright Policy.
                    </Text> */}

                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "400",
                        fontSize: 15,
                        color: "rgba(0,0,0,0.8)",
                        marginTop: 15,
                        lineHeight: 21,
                      }}
                    >
                      Please read the full
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.termsAndConditions}`)
                        }
                      >
                        {" "}
                        Terms and Conditions
                      </Text>
                      ,{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() => Linking.openURL(`${urls.privacyPolicy}`)}
                      >
                        {" "}
                        Privacy Policy
                      </Text>{" "}
                      and{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.copyrightPolicy}`)
                        }
                      >
                        {" "}
                        Copyright Policy
                      </Text>{" "}
                      before proceeding.
                    </Text>
                  </View>

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "9%",
                      marginBottom: "3%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(() => {
                          if (signUpType === "email") {
                            onHandleSignup();
                          }

                          if (signUpType === "google") {
                            promptAsync().catch((error) => {
                              console.error("Google auth error", error);
                            });
                          }

                          if (signUpType === "apple") {
                            handleAppleSignIn();
                          }
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        // width: "70%",
                        height: 40,
                        backgroundColor: red,
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "500",
                          alignSelf: "center",
                          color: "white",
                          fontSize: 14,
                          // fontFamily: "roboto",
                          paddingHorizontal: 15,
                        }}
                      >
                        I Understand and Agree
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <KeyboardAvoidingView
          // style={{ width: "100%", alignSelf: "center", alignItems: "center" }}
          // behavior="position"
          behavior={Platform.OS === "ios" ? "position" : "height"}
          keyboardVerticalOffset={-150}
          // behavior="height"
          style={{
            width: "100%",
            backgroundColor: "rgba(0,0,0,0)",
            alignSelf: "center",
            // alignItems: "center",
          }}
          // enabled=[]
          // keyboardVerticalOffset={-180}
        >
          <Animated.View style={{ flex: 1, opacity: opacity1 }}></Animated.View>
          <View style={{ width: "100%", height: "100%", alignItems: "center" }}>
            {showEmail ? (
              <Text
                style={{ color: "rgba(0,0,0,1)", zIndex: 100, fontSize: 16 }}
                onPress={() => {
                  setShowEmail(false);
                }}
              >
                Contact <NAME_EMAIL>
              </Text>
            ) : (
              <Pressable
                style={{
                  top: 15,
                  left: 20,
                  position: "absolute",
                  // backgroundColor: "red",
                  padding: 3,
                  zIndex: 100,
                }}
                onPress={() => {
                  setShowEmail(true);
                }}
              >
                <AntDesign
                  name="questioncircleo"
                  size={24}
                  color="rgba(0,0,0,0.5)"
                />
              </Pressable>
            )}

            <View
              style={{
                alignSelf: "center",
                width: "100%",
                height: "50%",
                marginTop: "-1%",
                // top: 0,
                // position: "absolute",
                // marginLeft: -screenWidth,
                // marginRight: 100,
                // left: screenWidth * 0.047,
              }}
            >
              <Image
                onLoadEnd={() => {
                  setImageLoaded(true);
                }}
                source={require("./../images/newlogo.png")}
                style={{
                  height: screenWidth * 0.65,
                  width: screenWidth * 0.65,
                  alignSelf: "center",
                  // flex: 1,
                  // resizeMode: "contain",
                }}
              />
            </View>

            <View
              style={{
                position: "absolute",
                width: "100%",

                flex: 1,
                height: 300,
                bottom: 50,

                alignSelf: "center",
                justifyContent: "center",
              }}
            >
              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  width: "88%",
                  marginBottom: 0,
                  // marginLeft: -6,
                }}
              >
                <MaterialIcons
                  name="alternate-email"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Email"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoFocus={false}
                    value={email}
                    onChangeText={(text) => setEmail(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 15,
                  width: "88%",
                  marginBottom: 0,
                }}
              >
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Create a new password"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password}
                    onChangeText={(text) => setPassword(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 15,
                  width: "88%",
                  marginBottom: 10,
                }}
              >
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Confirm password"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password2}
                    onChangeText={(text) => setPassword2(text)}
                  />
                </View>
              </View>

              <Pressable
                onPress={() => {
                  if (
                    password !== password2 ||
                    password == "" ||
                    password2 == ""
                  ) {
                    alert("Passwords don't match");
                  } else {
                    setSignUpType("email");
                    toggleModal2();
                    // console.log(password !== password2);
                  }
                }}
                style={({ pressed }) => ({
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  marginTop: 25,
                  width: "90%",
                  alignItems: "center",
                  backgroundColor: red,
                  borderRadius: 10,
                  height: 47,
                  justifyContent: "center",
                  alignSelf: "center",
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    color: "white",
                    fontSize: 16,
                    // fontFamily: "Roboto", // apply the font here
                    marginTop: 2,
                  }}
                >
                  Sign Up
                </Text>
              </Pressable>

              <View
                style={{
                  marginTop: 17,
                  alignSelf: "center",
                  width: "92%",
                  flexDirection: "row",
                  justifyContent: "space-around",
                }}
              >
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
                <Text
                  style={{
                    fontWeight: "400",
                    fontSize: 13,
                    color: "rgba(0,0,0,0.5)",
                  }}
                >
                  OR
                </Text>
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
              </View>

              <View
                style={{
                  flexDirection: "row",
                  marginTop: 17,
                  width: "90%",
                  justifyContent: "space-between",
                  alignSelf: "center",
                  // backgroundColor:'red'
                }}
              >
                <Pressable
                  onPress={() => {
                    setSignUpType("google");
                    toggleModal2();
                  }}
                  style={({ pressed }) => ({
                    transform: [{ scale: pressed ? 0.99 : 1 }],
                    width: "46%",

                    alignItems: "center",
                    backgroundColor: "rgba(0,0,0,0.04)",
                    borderRadius: 10,
                    height: 50,
                    justifyContent: "center",
                    alignSelf: "center",
                  })}
                >
                  <Text
                    style={{
                      fontWeight: "600",
                      color: "rgba(0,0,0,0.7)",
                      fontSize: 14,
                      // fontFamily: "Avenir-Light",
                      marginTop: 2,
                      left: 10,
                    }}
                  >
                    Google
                  </Text>
                  <Image
                    style={{
                      height: 27,
                      width: 27,
                      position: "absolute",
                      left: 15,
                    }}
                    source={require("../images/google.png")}
                  />
                </Pressable>

                <AppleAuthentication.AppleAuthenticationButton
                  buttonStyle={
                    AppleAuthentication.AppleAuthenticationButtonStyle
                      .WHITE_OUTLINE
                  }
                  buttonType={
                    AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN
                  }
                  cornerRadius={5}
                  style={{ width: "46%", height: 45 }}
                  onPress={() => {
                    setSignUpType("apple");
                    toggleModal2();
                  }}
                />
              </View>

              <View
                style={{
                  marginTop: 25,
                  flexDirection: "row",
                  alignItems: "center",
                  alignSelf: "center",
                }}
              >
                <Text
                  style={{
                    color: "rgba(0,0,0,0.4)",
                    fontWeight: "600",
                    fontSize: 14,
                  }}
                >
                  Already have an account?
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                  <Text
                    style={{
                      color: red,
                      fontWeight: "600",
                      fontSize: 14,
                      marginLeft: 7,
                    }}
                  >
                    Log In
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}
