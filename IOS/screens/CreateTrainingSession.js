//screens/team/CreateTrainingSession.js
import React, {
  useState,
  useRef,
  useMemo,
  useEffect,
  useCallback,
} from "react";
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Pressable,
  Switch,
  FlatList,
  Modal,
  Dimensions,
  Animated,
  Easing,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { useRoute, useNavigation } from "@react-navigation/native";
import { useSelector } from "react-redux";
import DateTimePicker from "@react-native-community/datetimepicker";
import {
  Feather,
  MaterialIcons,
  Ionicons,
  AntDesign,
} from "@expo/vector-icons";
import { red } from "./colors";
import DateTimePickerModal from "react-native-modal-datetime-picker";

import { LinearGradient } from "expo-linear-gradient";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import BottomSheet from "@gorhom/bottom-sheet";

import { BlurView } from "expo-blur";

import * as Haptics from "expo-haptics";
import { Entypo } from "@expo/vector-icons";
import FastImage from "react-native-fast-image";

import { useSessionCreation } from "../hooks/Teams/useSessionCreation";

const { height } = Dimensions.get("window");

const preloadImages = (locations) => {
  const imagesToLoad = locations
    .filter((location) => location.image)
    .map((location) => ({ uri: location.image }));

  FastImage.preload(imagesToLoad);
};

const LocationModal = ({
  isVisible,
  onClose,
  locations,
  onSelectLocation,
  language,
}) => {
  const slideAnim = useRef(new Animated.Value(height * 0.08)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Preload images when the component mounts
    preloadImages(locations);
  }, [locations]);

  useEffect(() => {
    if (isVisible) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
          easing: Easing.bezier(0.25, 0.1, 0.25, 1),
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: height * 0.08,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  const renderLocationItem = ({ item }) => (
    <Pressable
      style={({ pressed }) => ({
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        padding: 15,
        marginBottom: 15,
        borderRadius: 15,
        backgroundColor: pressed ? "rgba(0, 0, 0, 0.05)" : "white",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
        borderColor: "rgba(0,0,0,0.2)",
        borderWidth: 0.5,
      })}
      onPress={() => {
        // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onSelectLocation(item);
        onClose();
      }}
    >
      <View style={{ flexDirection: "row", alignItems: "center", flex: 1 }}>
        {item.image ? (
          <FastImage
            source={{ uri: item.image }}
            style={{ width: 60, height: 60, borderRadius: 10, marginRight: 15 }}
            resizeMode={FastImage.resizeMode.cover}
          />
        ) : (
          <View
            style={{
              width: 60,
              height: 60,
              // backgroundColor: "green",
              marginRight: 15,
              justifyContent: "center",
              alignItems: "center",
              paddingLeft: 10,
              borderColor: "rgba(0,0,0,0.4)",
              borderWidth: 0.5,
              borderRadius: 10,
            }}
          >
            <View
              style={{
                position: "relative",
                width: 40,
                height: 40,
                // marginRight: 20,
                // backgroundColor: "green",
                // alignSelf: "center",
              }}
            >
              <FastImage
                source={require("./../images/qq2.png")}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 100,
                  position: "absolute",
                  zIndex: 100,
                  top: -3,
                  left: -5,
                }}
              />
              <FastImage
                source={require("./../images/elipse.png")}
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 100,
                  position: "absolute",
                  zIndex: 99,
                  bottom: -36,
                  left: -4,
                }}
              />
            </View>
          </View>

          // <View
          //   style={{
          //     width: 60,
          //     height: 60,
          //     // backgroundColor: "green",
          //     justifyContent: "center",
          //     marginRight: 15,
          //   }}
          // >
          //   <FastImage
          //     source={require("../images/standard.jpg")}
          //     style={{
          //       width: 60,
          //       height: 60,
          //       borderRadius: 10,
          //       marginRight: 15,
          //     }}
          //     resizeMode={FastImage.resizeMode.cover}
          //   />
          //   <Entypo
          //     name="location-pin"
          //     size={35}
          //     color={red}
          //     style={{ position: "absolute", alignSelf: "center" }}
          //   />
          // </View>
        )}
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: "600", marginBottom: 5 }}>
            {item.name}
          </Text>
          {/* <Text style={{ fontSize: 14, color: "gray" }} numberOfLines={2}>
            {item.address}
          </Text> */}
        </View>
      </View>
      <MaterialIcons name="chevron-right" size={24} color={red} />
    </Pressable>
  );

  return (
    <View
      style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <Animated.View style={{ flex: 1, opacity: opacityAnim }}>
        <BlurView style={{ flex: 1 }} intensity={25} tint="dark">
          <Pressable onPress={onClose} style={{ flex: 1 }} />
        </BlurView>
      </Animated.View>
      <Animated.View
        style={{
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: "white",
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
          padding: 20,
          maxHeight: height * 0.8,
          transform: [{ translateY: slideAnim }],
        }}
      >
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 30,
          }}
        >
          <Text style={{ fontSize: 22, fontWeight: "bold" }}>
            {language.selectLocation}
          </Text>
          <TouchableOpacity onPress={onClose}>
            <MaterialIcons name="close" size={24} color="black" />
          </TouchableOpacity>
        </View>
        <FlatList
          data={locations}
          keyExtractor={(item, index) => index.toString()}
          renderItem={renderLocationItem}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      </Animated.View>
    </View>
  );
};

const CreateTrainingSession = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { language } = useSelector((state) => state.language);
  const { team, session } = route.params;

  // Time picker states
  const [showStartTimePicker, setShowStartTimePicker] = useState(false);
  const [showEndTimePicker, setShowEndTimePicker] = useState(false);
  const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);

  // Custom hook for form logic
  const {
    isEditMode,
    sessionName,
    setSessionName,
    description,
    setDescription,
    location,
    setLocation,
    isCreating,
    startTime,
    setStartTime,
    endTime,
    setEndTime,
    selectedDays,
    setSelectedDays,
    hasChanges,
    checkSessionName,
    validateAndSubmit,
  } = useSessionCreation(team, session, language, navigation);

  // Helper functions for UI
  const isWeekend = (dayKey) => [5, 6].includes(dayKey);

  const toggleDay = (dayKey) => {
    setSelectedDays((prev) =>
      prev.includes(dayKey)
        ? prev.filter((d) => d !== dayKey)
        : [...prev, dayKey]
    );
  };

  const handleSelectLocation = (selectedLocation) => {
    setLocation(selectedLocation);
    setIsLocationModalVisible(false);
  };

  const days = {
    EN: [
      { key: 0, label: "Mo" },
      { key: 1, label: "Tu" },
      { key: 2, label: "We" },
      { key: 3, label: "Th" },
      { key: 4, label: "Fr" },
      { key: 5, label: "Sa" },
      { key: 6, label: "Su" },
    ],
    CZ: [
      { key: 0, label: "Po" },
      { key: 1, label: "Út" },
      { key: 2, label: "St" },
      { key: 3, label: "Čt" },
      { key: 4, label: "Pá" },
      { key: 5, label: "So" },
      { key: 6, label: "Ne" },
    ],
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <>
        <KeyboardAwareScrollView
          style={{ flex: 1, backgroundColor: "white" }}
          contentContainerStyle={{ paddingTop: insets.top }}
          resetScrollToCoords={{ x: 0, y: 0 }}
          scrollEnabled={true}
          enableOnAndroid={true}
          enableAutomaticScroll={true}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <View style={{ width: "90%", alignSelf: "center" }}>
            {/* Header */}
            <View style={styles.headerContainer}>
              {/* <TouchableOpacity
                onPress={() => navigation.goBack()}
                style={styles.backButton}
              >
                <Feather name="arrow-left" size={24} color={red} />
              </TouchableOpacity> */}
              <Pressable
                style={styles.backButton}
                onPress={() => navigation.goBack()}
              >
                <AntDesign
                  name="arrowleft"
                  size={24}
                  color="black"
                  style={styles.backIcon}
                />
              </Pressable>
              <Text style={styles.headerTitle}>
                {isEditMode
                  ? language.editTrainingSession
                  : language.createTrainingSession}
              </Text>
            </View>

            {/* Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{language.sessionName}</Text>
              <TextInput
                style={styles.input}
                placeholder={language.enterSessionName}
                value={sessionName}
                onChangeText={(text) => {
                  setSessionName(text);
                  checkSessionName(text);
                }}
              />
            </View>

            {/* Description Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>{language.description}</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                placeholder={language.describeSession}
                value={description}
                onChangeText={setDescription}
                multiline
              />
            </View>

            {/* Time Selection */}
            <View style={styles.timeContainer}>
              <Text style={[styles.inputLabel, { marginBottom: 15 }]}>
                {language.time}
              </Text>

              <View style={styles.timeSelectionRow}>
                {/* Start Time */}
                <TouchableOpacity
                  style={styles.timeBox}
                  onPress={() => setShowStartTimePicker(true)}
                >
                  <Text style={styles.timeLabel}>{language.startTime}</Text>
                  <View style={styles.timeValueContainer}>
                    <Feather
                      name="clock"
                      size={16}
                      color={red}
                      style={styles.timeIcon}
                    />
                    <Text style={styles.timeValue}>
                      {startTime.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      })}
                    </Text>
                  </View>
                </TouchableOpacity>

                {/* End Time */}
                <TouchableOpacity
                  style={styles.timeBox}
                  onPress={() => setShowEndTimePicker(true)}
                >
                  <Text style={styles.timeLabel}>{language.endTime}</Text>
                  <View style={styles.timeValueContainer}>
                    <Feather
                      name="clock"
                      size={16}
                      color={red}
                      style={styles.timeIcon}
                    />
                    <Text style={styles.timeValue}>
                      {endTime.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      })}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Days Selection */}
            <View style={styles.daysContainer}>
              <Text style={[styles.inputLabel, { marginBottom: 15 }]}>
                {language.repeatsOn}
              </Text>
              <View style={styles.daysGrid}>
                {days[language.languageCode].map((day) => (
                  <Pressable
                    key={day.key}
                    style={[
                      styles.dayButton,
                      isWeekend(day.key) && styles.weekendButton,
                      selectedDays.includes(day.key) && styles.dayButtonActive,
                    ]}
                    onPress={() => toggleDay(day.key)}
                  >
                    <Text
                      style={[
                        styles.dayButtonText,
                        selectedDays.includes(day.key) &&
                          styles.dayButtonTextActive,
                      ]}
                    >
                      {day.label}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>

            {/* Location Selection */}
            <View style={styles.locationContainer}>
              <Text style={styles.inputLabel}>{language.location}</Text>
              <TouchableOpacity
                style={styles.locationButton}
                onPress={() => setIsLocationModalVisible(true)}
              >
                {location && location.image ? (
                  <FastImage
                    source={{ uri: location.image }}
                    style={styles.locationImage}
                  />
                ) : (
                  <View style={styles.locationPlaceholder}>
                    <MaterialIcons name="location-on" size={24} color={red} />
                  </View>
                )}
                <Text style={styles.locationText}>
                  {location ? location.name : language.chooseLocation}
                </Text>
                <MaterialIcons name="arrow-drop-down" size={24} color={red} />
              </TouchableOpacity>
            </View>

            {/* Create/Edit Button */}
            <TouchableOpacity
              style={[
                styles.createButton,
                isEditMode && !hasChanges && styles.editButtonInactive,
              ]}
              onPress={validateAndSubmit}
              disabled={isCreating || (isEditMode && !hasChanges)}
            >
              {isCreating ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text
                  style={[
                    styles.createButtonText,
                    isEditMode && !hasChanges && styles.editButtonInactiveText,
                  ]}
                >
                  {isEditMode ? language.edit : language.createSessionButton}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </KeyboardAwareScrollView>

        {/* Time Pickers */}
        <DateTimePickerModal
          isVisible={showStartTimePicker}
          mode="time"
          onConfirm={(date) => {
            // This now uses our custom time handler that manages date sync
            setStartTime(date);
            setShowStartTimePicker(false);
          }}
          onCancel={() => setShowStartTimePicker(false)}
          is24Hour={true}
        />
        <DateTimePickerModal
          isVisible={showEndTimePicker}
          mode="time"
          onConfirm={(date) => {
            // This now uses our custom handler that validates time
            setEndTime(date);
            setShowEndTimePicker(false);
          }}
          onCancel={() => setShowEndTimePicker(false)}
          is24Hour={true}
        />

        {/* Location Modal */}
        <LocationModal
          isVisible={isLocationModalVisible}
          onClose={() => setIsLocationModalVisible(false)}
          locations={team.locations}
          onSelectLocation={handleSelectLocation}
          language={language}
        />
      </>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 30,
    marginTop: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 50,
    backgroundColor: "rgba(242,242,242,0.9)",
    justifyContent: "center",
    marginRight: 15,
  },
  backIcon: {
    alignSelf: "center",
    marginTop: 2,
    marginLeft: 2,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: "#333",
    marginBottom: 8,
    fontWeight: "600",
  },
  input: {
    backgroundColor: "rgba(0,0,0,0.0)",
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 10,
    fontSize: 16,
    borderColor: "rgba(0,0,0,0.2)",
    borderWidth: 1,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  timeContainer: {
    marginBottom: 20,
  },
  timeSelectionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 15,
  },
  timeBox: {
    flex: 1,
    backgroundColor: "white",
    borderRadius: 12,
    paddingHorizontal: 12,
    padding: 8,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.15)",
  },
  timeLabel: {
    fontSize: 13,
    color: "#666",
    marginBottom: 4,
    fontWeight: "500",
  },
  timeValueContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeIcon: {
    marginRight: 6,
  },
  timeValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  daysContainer: {
    marginBottom: 20,
  },
  daysGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  dayButton: {
    flex: 1,
    marginHorizontal: 2.5,
    aspectRatio: 1,
    borderRadius: 10,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: `${red}50`,
    justifyContent: "center",
    alignItems: "center",
  },

  weekendButton: {
    backgroundColor: `${red}15`,
  },
  dayButtonActive: {
    backgroundColor: red,
    borderColor: red,
  },
  dayButtonText: {
    color: red,
    fontSize: 14,
    fontWeight: "700",
  },
  dayButtonTextActive: {
    color: "white",
  },
  locationContainer: {
    marginBottom: 20,
  },
  locationButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.0)",
    borderColor: "rgba(0,0,0,0.3)",
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 10,
    borderWidth: 0.5,
  },
  locationImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10,
  },
  locationPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10,
    backgroundColor: `${red}15`,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: `${red}30`,
  },
  locationText: {
    flex: 1,
    fontSize: 16,
    color: "#333",
    marginRight: 10,
  },
  createButton: {
    backgroundColor: red,
    height: 50,
    borderRadius: 10,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
    marginBottom: 20,
  },
  editButtonInactive: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: red,
  },
  editButtonInactiveText: {
    color: red,
  },
  createButtonDisabled: {
    backgroundColor: "#ccc",
    opacity: 0.6,
  },
  createButtonText: {
    color: "white",
    fontSize: 17,
    fontWeight: "700",
  },
});

export default CreateTrainingSession;
