import React from "react";
import { View, Text, StyleSheet, Pressable } from "react-native";

import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { red } from "./colors";

import TeamProfileIcon from "./../assets/TeamProfileIcon";

const SkillLevelIndicator = ({ skillLevels }) => {
  const levels = ["beginner", "intermediate", "advanced", "professional"];
  const maxLevel = levels.reduce((max, level) => {
    return skillLevels.includes(level)
      ? Math.max(max, levels.indexOf(level))
      : max;
  }, -1);

  const BAR_HEIGHTS = [4, 7, 10, 13];

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "flex-end",
        height: 15,
        width: 20,
      }}
    >
      {levels.map((_, index) => (
        <View
          key={index}
          style={{
            width: 3.5,
            height: BAR_HEIGHTS[index],
            backgroundColor:
              index <= maxLevel
                ? `rgba(${parseInt(red.slice(1, 3), 16)}, ${parseInt(
                    red.slice(3, 5),
                    16
                  )}, ${parseInt(red.slice(5, 7), 16)}, ${0.6 + index * 0.1})` // Starting at 0.6, incrementing by 0.1
                : "#ddd",
            marginRight: 2,
            borderRadius: 1.5,
          }}
        />
      ))}
    </View>
  );
};

const TeamItem = ({ team, navigation }) => {
  const location = team.locations?.[0]?.name || "No location set";
  const weekdays = ["Mo", "Tu", "We", "Th", "Fr", "Sa", "Su"];

  // Get all training days from recurring sessions
  const trainingDays =
    team.recurringTrainingSessions?.reduce((days, session) => {
      if (session.days) {
        days.push(...session.days);
      }
      return days;
    }, []) || [];

  return (
    <Pressable
      onPress={() => {
        // console.log(navigation);
        navigation.navigate("TeamDetails", { teamId: team.id });
      }}
      style={({ pressed }) => [
        styles.teamItem,
        { transform: [{ scale: pressed ? 0.98 : 1 }] },
      ]}
    >
      {/* Header Section stays the same */}
      <View style={styles.header}>
        <TeamProfileIcon team={team} size={70} />
        <View style={styles.headerText}>
          <Text style={styles.teamName}>{team.name}</Text>
          <Text numberOfLines={2} ellipsizeMode="tail" style={styles.location}>
            {location}
          </Text>
        </View>
      </View>

      {/* Days Section */}
      <View style={styles.daysSection}>
        {weekdays.map((day, index) => (
          <View key={index} style={styles.dayContainer}>
            <Text style={styles.dayText}>{day}</Text>
            <View
              style={[
                styles.dayIndicator,
                trainingDays.includes(index) && styles.activeDayIndicator,
              ]}
            />
          </View>
        ))}
      </View>

      {/* Body Section */}
      <View style={styles.body}>
        {/* Left Column - Team Characteristics */}
        <View style={styles.leftColumn}>
          <View style={styles.infoRow}>
            <MaterialIcons name="people" size={20} color="#666" />
            <Text style={styles.infoValue}>
              {team.ageGroups
                .map((age) => age.charAt(0).toUpperCase() + age.slice(1))
                .join(" & ")}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <View style={{ width: 20, alignItems: "center" }}>
              <SkillLevelIndicator skillLevels={team.skillLevels} />
            </View>
            <Text style={styles.infoValue}>
              {team.skillLevels
                .map((level) => level.charAt(0).toUpperCase() + level.slice(1))
                .join(" & ")}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <MaterialIcons name="sports-basketball" size={20} color="#666" />
            <Text style={styles.infoValue}>
              {team.teamType.charAt(0).toUpperCase() + team.teamType.slice(1)}
            </Text>
          </View>
        </View>

        {/* Right Column - Members */}
        <View style={styles.rightColumn}>
          <View style={styles.membersContainer}>
            <MaterialIcons name="group" size={24} color="#666" />
            <Text style={styles.membersCount}>{team.members.length}</Text>
            <Text style={styles.membersLabel}>members</Text>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  teamItem: {
    backgroundColor: "white",
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 15,
    padding: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    // height: 220,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  headerText: {
    marginLeft: 15,
    flex: 1,
  },
  teamName: {
    fontSize: 21,
    fontWeight: "bold",
    color: "#333",
  },
  location: {
    fontSize: 14,
    color: "#666",
    marginTop: 5,
  },

  daysSection: {
    flexDirection: "row",
    justifyContent: "space-around",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  dayContainer: {
    alignItems: "center",
    width: 30,
  },
  dayText: {
    fontSize: 13,
    color: "#666",
    marginBottom: 5,
  },
  dayIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#ddd",
  },
  activeDayIndicator: {
    backgroundColor: red, // Changed to red (imported color)
  },

  body: {
    paddingTop: 15,
    flex: 1,
    flexDirection: "row",
  },
  leftColumn: {
    flex: 2,
    justifyContent: "space-around",
    borderRightWidth: 1,
    borderRightColor: "rgba(0,0,0,0.1)",
    paddingRight: 15,
  },
  rightColumn: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingLeft: 15,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  infoValue: {
    fontSize: 14,
    color: "#333",
    marginLeft: 12,
    flex: 1,
  },
  membersContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  membersCount: {
    fontSize: 22,
    fontWeight: "bold",
    color: "#333",
    marginTop: 5,
  },
  membersLabel: {
    fontSize: 14,
    color: "#666",
    marginTop: 2,
  },
});

export default TeamItem;
