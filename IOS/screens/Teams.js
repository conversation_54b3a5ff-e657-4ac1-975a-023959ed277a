import React, {
  useEffect,
  useLayoutEffect,
  useState,
  create<PERSON>ontext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { FontAwesome5 } from "@expo/vector-icons";
import { Octicons } from "@expo/vector-icons";
import ScreenHeader from "../components/common/ScreenHeader";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

import TeamProfileIcon from "./../assets/TeamProfileIcon";
import ExploreTeams from "./ExploreTeams";

// import ProfileIcon from "./../assets/comps/ProfileIcon";
const { width } = Dimensions.get("window");
const { height } = Dimensions.get("window");

const Tab = createMaterialTopTabNavigator();

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

// ProfileIcon component removed as we're now using ScreenHeader with ProfileImage

const FILTER_OPTIONS = {
  genderPolicy: ["menOnly", "womenOnly", "mixed"],
  skillLevels: ["beginner", "intermediate", "advanced"],
  teamType: ["casual", "competitive", "social"],
  ageGroups: ["youth", "adult", "senior"],
};

const TeamItem = ({ team, onPress }) => (
  <Pressable
    onPress={() => onPress(team)}
    style={({ pressed }) => ({
      width: "95%",
      height: 250,
      backgroundColor: "#fff",
      borderRadius: 15,
      marginBottom: 25,
      padding: 15,
      alignSelf: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1.5 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      transform: [{ scale: pressed ? 0.98 : 1 }],
      borderColor: "rgba(0,0,0,0.1)",
      borderWidth: 1,
    })}
  >
    <View
      style={{ flexDirection: "row", alignItems: "center", marginBottom: 15 }}
    >
      {/* <View
        style={{
          width: 70,
          height: 70,
          borderRadius: 15,
          backgroundColor: team.color,
          justifyContent: "center",
          alignItems: "center",
          marginRight: 15,
        }}
      >
        <Text style={{ fontSize: 32, color: "#fff", fontWeight: "bold" }}>
          {team.name.charAt(0).toUpperCase()}
        </Text>
      </View> */}
      <TeamProfileIcon team={team} size={70} />
      <View>
        <Text
          style={{
            fontSize: 24,
            fontWeight: "bold",
            color: "#333",
            marginLeft: 10,
          }}
        >
          {team.name}
        </Text>
        <Text
          style={{ fontSize: 16, color: "#666", marginTop: 5, marginLeft: 10 }}
        >
          {team.teamType}
        </Text>
      </View>
    </View>
    <View style={{ flex: 1, justifyContent: "space-around" }}>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <AntDesign
          name="team"
          size={16}
          color="#666"
          style={{ marginRight: 10 }}
        />
        <Text
          style={{ fontSize: 16, color: "#333" }}
        >{`${team.members.length} members`}</Text>
      </View>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <AntDesign
          name="Trophy"
          size={16}
          color="#666"
          style={{ marginRight: 10 }}
        />
        <Text style={{ fontSize: 16, color: "#333" }}>
          {team.skillLevels.join(", ")}
        </Text>
      </View>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <AntDesign
          name="calendar"
          size={16}
          color="#666"
          style={{ marginRight: 10 }}
        />
        <Text style={{ fontSize: 16, color: "#333" }}>
          {team.ageGroups.join(", ")}
        </Text>
      </View>
    </View>
  </Pressable>
);

const MyTeams = ({ navigation }) => {
  const { teams, isLoading, error } = useSelector((state) => state.teams);

  if (isLoading) {
    return (
      <Text
        style={{
          fontSize: 18,
          color: "#666",
          textAlign: "center",
          marginTop: 20,
        }}
      >
        Loading teams...
      </Text>
    );
  }

  if (error) {
    return (
      <Text
        style={{
          fontSize: 18,
          color: "#666",
          textAlign: "center",
          marginTop: 20,
        }}
      >
        Error: {error}
      </Text>
    );
  }

  const handleTeamPress = (team) => {
    navigation.navigate("TeamDetails", { teamId: team.id });
  };

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      <FlatList
        data={teams}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TeamItem team={item} onPress={handleTeamPress} />
        )}
        contentContainerStyle={{ paddingVertical: 10 }}
      />
      <Pressable
        style={({ pressed }) => ({
          position: "absolute",
          right: 20,
          bottom: 20,
          backgroundColor: red,
          width: 60,
          height: 60,
          borderRadius: 30,
          justifyContent: "center",
          alignItems: "center",
          elevation: 5,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          transform: [{ scale: pressed ? 0.95 : 1 }],
        })}
        onPress={() => navigation.navigate("ChooseTeamType")}
      >
        <AntDesign name="plus" size={30} color="white" />
      </Pressable>
    </View>
  );
};

export default function Teams() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const chats = useSelector((state) => state.socials.chats);
  const user = useSelector((state) => state.socials.userData);

  const notifications = useSelector((state) => state.socials.notifications);
  const { language } = useSelector((state) => state.language);

  const [profileImageRef, setProfileImageRef] = useState(null);
  const [profileColor, setProfileColor] = useState(null);
  const [userName, setUserName] = useState(null);

  const [chatNotifsCount, setChatNotifsCount] = useState(null);

  useEffect(() => {
    const count = chats.filter((chat) => {
      return chat[auth.currentUser.uid + "_um"] > 0;
    }).length;
    setChatNotifsCount(count);
  }, [chats]);

  if (
    !language ||
    notifications == null ||
    chats == null ||
    chatNotifsCount == null ||
    !user
  ) {
    return;
  }

  return (
    <>
      <View
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "white",
          paddingTop: insets.top,
        }}
        // onStartShouldSetResponder={() => true}
      >
        <ScreenHeader title="Teams" />

        <Tab.Navigator
          screenOptions={{
            // swipeEnabled: false,
            tabBarLabelStyle: {
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              // backgroundColor: "red",
              fontSize: 14,
              fontWeight: "600",
              textTransform: "none",
            },
            tabBarIndicatorStyle: {
              backgroundColor: red,
            },
            tabBarStyle: {
              marginTop: 5,
              height: 46,
              borderBottomColor: "rgba(0,0,0,0.2)",
              borderBottomWidth: 0.5,
              // backgroundColor: "red",
            },

            lazy: false,
            // tabBarActiveTintColor: red,
            // tabBarInactiveTintColor: "rgba(0,0,0,0.6)",
          }}
        >
          <Tab.Screen name={"My Teams"} component={MyTeams} />
          <Tab.Screen name={"Explore Teams"} component={ExploreTeams} />
        </Tab.Navigator>
      </View>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      ></View>
    </>
  );
}
