// screens/Explore.js
import React, { useCallback, useState, useEffect, useRef } from "react";
import {
  View,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  AppState,
} from "react-native";
import { useIsFocused, useFocusEffect } from "@react-navigation/native";
import { useSelector } from "react-redux";

// Components
import EventItemExplore from "../components/event/EventItemExplore";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { red } from "./colors";

// Custom hooks
import { useExplore } from "../hooks/useExplore";

// Extracted components
import ExploreHeader from "../components/explore/ExploreHeader";
import EmptyGamesList from "../components/explore/EmptyGamesList";
import MapPreviewModal from "../components/map/MapPreviewModal";
import CreateGameFab from "../components/common/CreateGameFab";

// Utils
import { calculateMapDeltaForRadius } from "../utils/mapUtils";

const Explore = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const { language } = useSelector((state) => state.language);
  const {
    games,
    loading,
    error,
    refreshing,
    fetchGames,
    refreshGames,
    setFilterRadius,
    filterRadius,
    centerCoords,
    setCenterCoords,
    fetchMoreGames,
    hasMoreGames,
    loadingMore,
  } = useExplore();
  
  // App state tracking
  const appState = useRef(AppState.currentState);
  const [appStateVisible, setAppStateVisible] = useState(appState.current);
  
  // Add state for tracking scroll position
  const [scrollPosition, setScrollPosition] = useState(0);
  const [listHeight, setListHeight] = useState(1); // Default to 1 to avoid division by zero
  const [contentHeight, setContentHeight] = useState(1);

  // Maintain UI state separate from hook to avoid display issues
  const [displayRadius, setDisplayRadius] = useState(filterRadius);
  
  // Add state for the preview map modal
  const [isMapPreviewVisible, setIsMapPreviewVisible] = useState(false);
  const [mapDelta, setMapDelta] = useState({
    latitudeDelta: 0.15,
    longitudeDelta: 0.15
  });

  // Update display radius when filterRadius changes
  useEffect(() => {
    setDisplayRadius(filterRadius);
    
    // Update map zoom based on radius for preview
    if (filterRadius) {
      const newDelta = calculateMapDeltaForRadius(filterRadius, centerCoords?.latitude);
      setMapDelta(newDelta);
    }
  }, [filterRadius, centerCoords]);

  // Set up AppState listener to detect when app comes to foreground
  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      console.log('App state changed from', appState.current, 'to', nextAppState);
      
      if (
        appState.current.match(/inactive|background/) && 
        nextAppState === 'active'
      ) {
        console.log('App has come to the foreground! Fetching fresh games data...');
        fetchGames();
      }
      
      appState.current = nextAppState;
      setAppStateVisible(nextAppState);
    });

    return () => {
      subscription.remove();
    };
  }, [fetchGames]);

  const isFocused = useIsFocused();

  // Refresh games when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log("Screen focused, fetching games...");
      fetchGames();
    }, [isFocused, fetchGames])
  );

  const renderItem = ({ item }) => (
    <EventItemExplore
      value={item}
      language={language}
      navigation={navigation}
    />
  );

  const handleFilterPress = () => {
    navigation.navigate("EditCoordsUpdated", {
      language,
      onReturn: (newRadius, newCoords) => {
        console.log("Received new radius from EditCoords:", newRadius);
        console.log("Received new coordinates from EditCoords:", newCoords);

        if (newRadius) {
          // Explicitly convert to number and update
          const numericRadius = Number(newRadius);
          console.log("Setting filter radius to:", numericRadius);

          // Update UI state immediately for a responsive feel
          setDisplayRadius(numericRadius);

          // Update hook state with slight delay to avoid race conditions
          setTimeout(() => {
            setFilterRadius(numericRadius);
          }, 50);
        }

        if (newCoords) {
          // Make sure coordinates are serializable plain objects
          const serializedCoords = {
            latitude: Number(newCoords.latitude),
            longitude: Number(newCoords.longitude),
          };
          console.log("Setting center coordinates to:", serializedCoords);

          // Update hook state
          setCenterCoords(serializedCoords);

          // fetchGames will be called automatically due to state changes
        }
      },
    });
  };
  
  // Show map preview with current radius
  const showMapPreview = () => {
    if (centerCoords) {
      // Make sure we have the latest zoom level
      const newDelta = calculateMapDeltaForRadius(displayRadius, centerCoords.latitude);
      setMapDelta(newDelta);
      setIsMapPreviewVisible(true);
    } else {
      // If no coordinates, direct to filter screen
      handleFilterPress();
    }
  };

  // Handle edit radius from map preview
  const handleEditRadiusFromPreview = () => {
    setIsMapPreviewVisible(false);
    setTimeout(() => handleFilterPress(), 300); // Slight delay for better UX
  };

  return (
    <View style={styles.container}>
      <View style={styles.listContainer}>
        <FlatList
          data={games}
          renderItem={renderItem}
          keyExtractor={(item) => item.eventID}
          contentContainerStyle={styles.listContent}
          ListHeaderComponent={
            <ExploreHeader
              games={games}
              hasMoreGames={hasMoreGames}
              loading={loading}
              displayRadius={displayRadius}
              language={language}
              onShowMapPreview={showMapPreview}
              onFilterPress={handleFilterPress}
              accentColor={red}
            />
          }
          ListEmptyComponent={!loading && 
            <EmptyGamesList error={error} language={language} />
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={refreshGames}
              colors={[red]}
              tintColor={red}
            />
          }
          showsVerticalScrollIndicator={false}
          onEndReached={() => {
            // Load more games when user reaches end of list
            if (hasMoreGames && !loadingMore) {
              fetchMoreGames();
            }
          }}
          onEndReachedThreshold={0.5} // Start loading when user is halfway through the list
          ListFooterComponent={() => (
            // Show loading indicator at bottom when loading more games
            loadingMore ? (
              <View style={styles.loadMoreContainer}>
                <ActivityIndicator size="small" color={red} />
              </View>
            ) : null
          )}
          // Track scroll position for custom scroll indicator
          onScroll={(event) => {
            const offsetY = event.nativeEvent.contentOffset.y;
            setScrollPosition(offsetY);
          }}
          scrollEventThrottle={16} // Update at 60fps for smooth animation
          onLayout={(event) => {
            setListHeight(event.nativeEvent.layout.height);
          }}
          onContentSizeChange={(width, height) => {
            setContentHeight(height);
          }}
        />
        
        {/* Subtle scroll indicator */}
        {games.length > 0 && contentHeight > listHeight && (
          <View style={styles.scrollTrackContainer}>
            <View style={styles.scrollTrack}>
              <View 
                style={[
                  styles.scrollThumb, 
                  { 
                    height: Math.max(40, (listHeight / contentHeight) * listHeight),
                    top: (scrollPosition / (contentHeight - listHeight)) * (listHeight - Math.max(40, (listHeight / contentHeight) * listHeight))
                  }
                ]}
              />
            </View>
          </View>
        )}
      </View>

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={red} />
        </View>
      )}

      {/* Map Preview Modal */}
      <MapPreviewModal
        isVisible={isMapPreviewVisible}
        onClose={() => setIsMapPreviewVisible(false)}
        onEditRadius={handleEditRadiusFromPreview}
        centerCoords={centerCoords}
        radius={displayRadius}
        mapDelta={mapDelta}
        language={language}
        accentColor={red}
      />

      {/* Floating action button for creating a new game */}
      <CreateGameFab 
        navigation={navigation}
        insets={insets}
        language={language}
        color={red}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  listContainer: {
    flex: 1,
    position: 'relative',
  },
  listContent: {
    paddingBottom: 100,
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
  },
  loadMoreContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
    backgroundColor: "transparent",
  },
  scrollTrackContainer: {
    position: 'absolute',
    right: 2,
    top: 10,
    bottom: 10,
    width: 4,
    alignItems: 'center',
    justifyContent: 'flex-start',
    pointerEvents: 'none',
  },
  scrollTrack: {
    width: 2,
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    borderRadius: 1,
    overflow: 'hidden',
    position: 'relative',
  },
  scrollThumb: {
    width: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    borderRadius: 1,
    position: 'absolute',
    right: 0,
  },
});

export default Explore;