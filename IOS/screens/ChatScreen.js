import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  // Vibration,
  Keyboard,
  Pressable,
  Modal,
  TouchableHighlight,
  ViewBase,
  LayoutAnimation,
  UIManager,
} from "react-native";

import { auth, db, functions, storage } from "../config/firebase";

import { httpsCallable } from "@react-native-firebase/functions";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import * as Notifications from "expo-notifications";

import {
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
} from "@react-native-firebase/storage";
import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  limit,
  addDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  increment,
  endBefore,
  Timestamp,
} from "@react-native-firebase/firestore";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
import { Ionicons } from "@expo/vector-icons";
import FontAwesome from "react-native-vector-icons/FontAwesome";

import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import {
  GiftedChat,
  Bubble,
  InputToolbar,
  Composer,
  Send,
  Message,
  MessageText,
} from "react-native-gifted-chat";

import { useRoute } from "@react-navigation/native";

import { MaterialCommunityIcons } from "@expo/vector-icons";
import { EvilIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Entypo } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome5 } from "@expo/vector-icons";

import * as Haptics from "expo-haptics";

import { PanGestureHandler, State } from "react-native-gesture-handler";

import { BlurView } from "expo-blur";

import { Octicons } from "@expo/vector-icons";
import { useSelector, useDispatch } from "react-redux";

import * as ImagePicker from "expo-image-picker";
import _ from "lodash";

// import Animated, {
//   useSharedValue,
//   useAnimatedStyle,
//   withTiming,
//   withSpring,
// } from "react-native-reanimated";

import Reanimated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  ceil,
} from "react-native-reanimated";

import * as ImageManipulator from "expo-image-manipulator";

import FastImage from "react-native-fast-image";

// import * as Clipboard from "expo-clipboard";

import * as FileSystem from "expo-file-system";

import * as MediaLibrary from "expo-media-library";

// import Share from "react-native-share";

import ImageViewer from "react-native-image-zoom-viewer";

import {
  setupMessageListener,
  updateMessages,
  loadOlderMessages,
  setMessagesLoading,
  leaveChat,
} from "../slices/socialsSlice";

import TeamProfileIcon from "./../assets/TeamProfileIcon";

async function getMediaLibraryPermissions() {
  const { status } = await MediaLibrary.requestPermissionsAsync();
  return status === "granted";
}

const saveImage = async (uri) => {
  try {
    // Request permission
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== "granted") {
      alert("Sorry, we need gallery permissions to make this work!");
      return;
    }

    console.log(uri);

    let finalUri = uri;

    // Check if the URI is a remote URL or a local file URI
    if (uri.startsWith("http://") || uri.startsWith("https://")) {
      // It's a remote URL, process as before
      let imageName = uri.split("/").pop().split("?")[0];
      const fileExtension = ".jpg"; // Change this based on your expected file type
      imageName += fileExtension;
      const fileUri = FileSystem.documentDirectory + imageName;
      const downloadedFile = await FileSystem.downloadAsync(uri, fileUri);
      finalUri = downloadedFile.uri;
    }
    // No need to download if it's a local file URI, use it directly

    // Save the file to the gallery
    const asset = await MediaLibrary.createAssetAsync(finalUri);
    await MediaLibrary.createAlbumAsync("Download", asset, false);

    alert("Image saved to gallery!");
  } catch (error) {
    console.error("Error processing the image: ", error);
    alert("Failed to process image.");
  }
};

const shareImage = async (imageUrl, setShareLoading) => {
  try {
    let localImageUri = imageUrl;

    // Check if the image URL is a remote URL
    if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
      // It's a remote URL, download the image
      const imageName = imageUrl.split("/").pop().split("?")[0];
      const fileExtension = ".jpg";
      const localUri = FileSystem.cacheDirectory + imageName + fileExtension;
      const downloadedFile = await FileSystem.downloadAsync(imageUrl, localUri);
      localImageUri = downloadedFile.uri;
    }
    // If it's a local file URI, use it directly

    // Share the image
    await Share.open({
      url: localImageUri,
      type: "image/jpeg", // Adjust the type based on your image format
    });
    setShareLoading(false);
  } catch (error) {
    setShareLoading(false);
    // console.error("Error sharing the image: ", error);
    // alert("Failed to share image.");
  }
};

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

function formatDate(dateString) {
  const messageDate = new Date(dateString);
  const currentDate = new Date();

  // Calculate the difference in days
  const diffTime = Math.abs(currentDate - messageDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Format the time if the message was sent today
  if (
    diffDays === 1 &&
    messageDate.toDateString() === currentDate.toDateString()
  ) {
    return messageDate.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // If the message was sent within the last 7 days
  if (diffDays < 7) {
    return `${diffDays} days ago`;
  }

  // If the message was sent more than 7 days ago
  return messageDate.toLocaleDateString([], {
    day: "2-digit",
    month: "2-digit",
  });
}

if (
  Platform.OS === "android" &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(false);
}

const sentFriendRequestNotificationV2 = httpsCallable(
  functions,
  "sentFriendRequestNotificationV2"
);

const createChatRoom = httpsCallable(functions, "createChatRoom");

const mesid = "ea6031db-11da-49f8-bc0b-35d7044abbfb";

const AnimatedBubble = ({ props }) => {
  const translateX = useRef(new Animated.Value(0)).current;

  const isHighlighted = useState(true);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      // Check if translation has reached the limit
      if (event.nativeEvent.translationX >= 50) {
        // Trigger vibration
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        // Slide the bubble back to its original position
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      } else {
        // If not reached the limit, just slide back to original position without vibration
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
      maxPointers={1}
      minDeltaX={10}
    >
      <Animated.View
        style={{
          // zIndex: isHighlighted ? 100 : 1,
          transform: [
            {
              translateX: translateX.interpolate({
                inputRange: [0, 50],
                outputRange: [0, 50],
                extrapolate: "clamp",
              }),
            },
          ],
        }}
      >
        <Bubble
          {...props}
          wrapperStyle={{
            right: {
              backgroundColor: red,
              borderRadius: 10,
              borderTopRightRadius: 4,
              paddingRight: 10,
              opacity: props.currentMessage._id == mesid ? 1 : 0.3,
            },
            left: {
              backgroundColor: "#f0f0f0",
              borderRadius: 10,
              borderTopLeftRadius: 3,
              opacity: props.currentMessage._id == mesid ? 1 : 0.3,
              // paddingRight: 10,
              // marginLeft: -40,
            },
          }}
          onLongPress={() => {
            // setSelectedMessageId(currentMessage._id);
            // setShowDeleteButton(true);
          }}
          onPress={() => {
            // setSelectedMessageId(null);
            // setShowDeleteButton(false);
          }}
        />
        {/* </BlurView> */}
      </Animated.View>
    </PanGestureHandler>
  );
};

const MessageImage =
  //  React.memo(
  ({ avatar, profileColor, userName }) => {
    return avatar ? (
      <>
        <View style={{ width: 40 }}></View>
        <FastImage
          source={{ uri: avatar }}
          style={{
            width: 30,
            height: 30,
            borderRadius: 50,
            marginRight: 10,
            position: "absolute",
            bottom: 5,
          }}
        />
      </>
    ) : (
      <View
        style={{
          width: 100,
          height: 100,
          borderRadius: 100,
          backgroundColor: profileColor,
        }}
      >
        <Text
          style={{
            alignSelf: "center",
            fontSize: 50,
            color: "white",
            paddingTop: 20,
            paddingLeft: 2,
          }}
        >
          {userName[0].toUpperCase()}
        </Text>
      </View>
    );
  };

///////////////////////////////////////////////////////////
// const areEqual = (prevProps, nextProps) => {
//   // Assuming 'message' object contains all relevant data
//   if (prevProps.message._id !== nextProps.message._id) {
//     // console.log(
//     //   "Relaoded because of id",
//     //   prevProps.message._id,
//     //   nextProps.message._id
//     // );
//     return false; // Different messages, re-render
//   }

//   if (prevProps.message.status !== nextProps.message.status) {
//     // console.log("Relaoded because of status");
//     return false; // Status changed, re-render
//   }
//   if (prevProps.message.read !== nextProps.message.read) {
//     // console.log("Relaoded because of read");
//     return false; // Read status changed, re-render
//   }

//   if (prevProps.message.text !== nextProps.message.text) {
//     // console.log("Relaoded because of text");
//     return false; // Read status changed, re-render
//   }
//   if (prevProps.message?.image !== nextProps.message?.image) {
//     // console.log(
//     //   "Relaoded because of image",
//     //   prevProps.message?.image,
//     //   nextProps.message?.image
//     // );
//     return false; // Read status changed, re-render
//   }

//   if (prevProps.message?.imageSize !== nextProps.message?.imageSize) {
//     // console.log("Relaoded because of imageSize", prevProps.message?._id);
//     return false; // Read status changed, re-render
//   }

//   // if (prevProps.message.index !== nextProps.message.index) {
//   //   return false; // Read status changed, re-render
//   // }

//   if (prevProps.isNextFromSameUser !== nextProps.isNextFromSameUser) {
//     // console.log("Relaoded because of isNextFromSameUser");
//     return false; // Read status changed, re-render
//   }
//   if (prevProps.isPreviousFromSameUser !== nextProps.isPreviousFromSameUser) {
//     // console.log("Relaoded because of isPreviousFromSameUser");
//     return false; // Read status changed, re-render
//   }

//   if (prevProps.scrollToMessage !== nextProps.scrollToMessage) {
//     // console.log("Relaoded because of isPreviousFromSameUser");
//     return false; // Read status changed, re-render
//   }

//   //////////////////////////////r

//   // if (prevProps.message.replyMessage !== nextProps.message.replyMessage) {
//   //   return false; // Read status changed, re-render
//   // }

//   // if (prevProps.messages !== nextProps.messages) {
//   //   return false; // Read status changed, re-render
//   // }

//   // if (prevProps.setSelectedMessage !== nextProps.setSelectedMessage) {
//   //   return false; // Read status changed, re-render
//   // }

//   // Add other comparisons here if there are more relevant props

//   // If no changes are detected in the relevant props, do not re-render
//   return true;
// };

// const RenderMessage = React.memo((props) => {
//   const message = props.message;

//   const index = props.index;
//   // const messages = props.messages;

//   // const selectedMessage = props.selectedMessage;

//   const messageRefs = props.messageRefs;

//   const setOverlayPosition = props.setOverlayPosition;

//   const setSelectedMessage = props.setSelectedMessage;

//   const setReplyMessage = props.setReplyMessage;

//   const isCurrentUser = message.user._id === auth.currentUser.uid;

//   const isNextFromSameUser = props.isNextFromSameUser;
//   const isPreviousFromSameUser = props.isPreviousFromSameUser;

//   // Format the time
//   const formattedTime = new Date(message.createdAt).toLocaleTimeString([], {
//     hour: "2-digit",
//     minute: "2-digit",
//   });

//   const isShortMessage = message.text.length <= 20;

//   const key = `message-${index}`;

//   const tickOpacity = useSharedValue(0.65); // Initial opacity

//   useEffect(() => {
//     if (message.read === true) {
//       tickOpacity.value = withTiming(1, { duration: 100 });
//     }
//   }, [message.read]);

//   const tickStyle = useAnimatedStyle(() => {
//     return {
//       opacity: tickOpacity.value,
//       marginLeft: 4,
//     };
//   });

//   ///////
//   /////////
//   ///////
//   /////

//   // console.log("RenderMessage");
//   //
//   const translateX = useRef(new Animated.Value(0)).current;
//   const hapticTriggeredRef = useRef(false);

//   const onGestureEvent = Animated.event(
//     [{ nativeEvent: { translationX: translateX } }],
//     {
//       useNativeDriver: true,
//       listener: (event) => {
//         const { translationX: currentTranslationX } = event.nativeEvent;
//         let hapticTriggerThreshold = 40;
//         if (
//           !hapticTriggeredRef.current &&
//           ((isCurrentUser &&
//             currentTranslationX >= hapticTriggerThreshold - 15) ||
//             (isCurrentUser && currentTranslationX <= -hapticTriggerThreshold) ||
//             // ||
//             (!isCurrentUser && currentTranslationX >= hapticTriggerThreshold))
//         ) {
//           setReplyMessage(message);

//           Haptics.impactAsync();
//           hapticTriggeredRef.current = true;
//         }
//       },
//     }
//   );

//   const onHandlerStateChange = (event) => {
//     const { state } = event.nativeEvent;
//     if (state === State.END) {
//       hapticTriggeredRef.current = false;
//       Animated.spring(translateX, {
//         toValue: 0,
//         speed: 100,
//         useNativeDriver: true,
//       }).start();
//     }
//   };

//   // const translateY = useSharedValue(100); // Start from -100

//   // useEffect(() => {
//   //   translateY.value = withTiming(0, { duration: 400 }); // Animate to 0 over 300 milliseconds
//   // }, []);

//   // const animatedStyle = useAnimatedStyle(() => {
//   //   return {
//   //     transform: [{ translateY: translateY.value }],
//   //   };
//   // });

//   const translateY = useState(new Animated.Value(50))[0];

//   useEffect(() => {
//     // console.log("asdfasdfasdfasdfs");
//     // Slide in animation
//     // setTimeout(() => {
//     Animated.timing(translateY, {
//       toValue: 0, // Animate to final position
//       duration: 150, // Duration of the animation
//       useNativeDriver: true,
//     }).start();
//     // }, 500);
//   }, []);

//   const [savedPhoneUrl, setSavedPhoneUrl] = useState(message.image);

//   const [switchImage, setSwitchImage] = useState(false);

//   const [increaseZIndex, setIncreaseZIndex] = useState(false);

//   useEffect(() => {
//     if (message.image && message.image.startsWith("https://")) {
//       setSwitchImage(true);
//     }
//   }, [message.image]);

//   // if (message?.image) {
//   //   console.log("message?.image", message?.image);
//   // }

//   const aspectRatio = message?.imageSize?.aspectRatio;

//   const maxWidth = screenWidth * 0.73;
//   const maxHeight = screenHeight * 0.35;

//   let calculatedWidth = maxWidth;
//   let calculatedHeight = maxWidth / aspectRatio;

//   // Check if the calculated height is greater than the maximum allowed height
//   if (calculatedHeight > maxHeight) {
//     // Recalculate both width and height based on the maxHeight
//     calculatedHeight = maxHeight;
//     calculatedWidth = maxHeight * aspectRatio;
//   }

//   const aspectRatioRe = message?.replyMessage?.imageSize?.aspectRatio;

//   const maxWidthRe = screenWidth * 0.73;
//   const maxHeightRe = screenHeight * 0.35;

//   let calculatedWidthRe = maxWidthRe;
//   let calculatedHeightRe = maxWidthRe / aspectRatioRe;

//   // Check if the calculated height is greater than the maximum allowed height
//   if (calculatedHeightRe > maxHeightRe) {
//     // Recalculate both width and height based on the maxHeight
//     calculatedHeightRe = maxHeightRe;
//     calculatedWidthRe = maxHeightRe * aspectRatioRe;
//   }

//   return (
//     <PanGestureHandler
//       onGestureEvent={onGestureEvent}
//       onHandlerStateChange={onHandlerStateChange}
//       maxPointers={1}
//       // minDeltaX={30} // Slightly less than previously set, but still more than the default
//       // activeOffsetX={[-100, 100]} // Keep the threshold for horizontal drag activation
//       // activeOffsetY={[-11, 11]} // Reduce the threshold for vertical movements
//       // failOffsetY={[-30, 30]} // Slightly less than previously set
//       failOffsetY={[-20, 20]}
//       activeOffsetX={[-8, 8]}
//     >
//       <Animated.View
//         style={{
//           transform: [
//             {
//               translateX: translateX.interpolate({
//                 inputRange: isCurrentUser ? [-200, 200] : [0, 200],
//                 outputRange: isCurrentUser ? [-100, 100] : [0, 100],
//                 extrapolate: "clamp",
//               }),
//             },
//             { translateY: translateY },
//           ],
//           marginTop: message?.replyMessage ? 5 : 0,
//           // ...[additional styles]...
//         }}
//       >
//         {message.replyMessage && (
//           <>
//             {message.replyMessage.imageSize ? (
//               <Pressable
//                 style={{
//                   width: calculatedWidthRe * 0.8,
//                   height: calculatedHeightRe * 0.8,
//                   // height: message.imageSize.height ,
//                   // maxWidth: screenWidth * 0.75,
//                   // aspectRatio: message.replyMessage.imageSize.aspectRatio,

//                   borderRadius: 13,
//                   overflow: "hidden", // This clips the children inside the wrapper
//                   alignSelf: "center",
//                   // Apply additional styles like margin if needed
//                   backgroundColor: "rgba(240,240,240)",
//                   marginTop: 2,
//                   justifyContent: "center",
//                   borderWidth: 0.6,
//                   borderColor: "rgba(0,0,0,0.2)",
//                   zIndex: 10,
//                   alignSelf: isCurrentUser ? "flex-end" : "flex-start",
//                   marginBottom: -12, // Overlaps with the main message

//                   maxWidth: "80%",
//                   marginLeft: !isCurrentUser && !isNextFromSameUser ? 50 : 50,
//                   marginRight: isCurrentUser && 9,
//                 }}
//                 onPress={() => {
//                   // console.log(message.replyMessage._id);
//                   props.scrollToMessage(message.replyMessage._id);
//                 }}
//               >
//                 <ActivityIndicator
//                   style={{
//                     alignSelf: "center",
//                     position: "absolute",
//                     zIndex: 1,
//                   }}
//                 ></ActivityIndicator>
//                 {message.replyMessage.image && (
//                   <>
//                     <FastImage
//                       source={{ uri: message.replyMessage.image }}
//                       style={{
//                         width: calculatedWidthRe * 0.8,
//                         height: calculatedHeightRe * 0.8,
//                         resizeMode: "contain",
//                         zIndex: increaseZIndex ? 3 : 1,
//                         position: "absolute",
//                         // left: -100,
//                         // opacity: 0.2,
//                       }}
//                     />

//                     <View
//                       style={{
//                         backgroundColor: "white",
//                         position: "absolute",
//                         width: calculatedWidthRe,
//                         height: calculatedHeightRe,
//                         zIndex: 100,
//                         opacity: 0.3,
//                       }}
//                     ></View>
//                   </>
//                 )}
//                 {/*
//                 <LinearGradient
//                   colors={[
//                     "rgba(0,0,0,0.2)",
//                     "rgba(0,0,0,0.2)",
//                     // "rgba(0,0,0,0.1)",
//                     "transparent",
//                   ]}
//                   start={{ x: 0.9, y: 1 }} // Start from bottom-right corner
//                   end={{ x: 0.8, y: 0.4 }}
//                   style={{
//                     position: "absolute",
//                     bottom: 0,
//                     right: 0,
//                     padding: 5,
//                     paddingTop: 10,
//                     paddingLeft: 20,
//                     borderRadius: 13,
//                     zIndex: 5,
//                     // marginBottom: 2,
//                     // marginRight: 2,
//                     flexDirection: "row",
//                     height: 50,
//                     width: 100,
//                   }}
//                 >
//                   <View
//                     style={{
//                       position: "absolute",
//                       flexDirection: "row",
//                       bottom: 3,
//                       right: 3,
//                     }}
//                   >
//                     <Text
//                       style={{
//                         fontSize: 11,
//                         color: "white",
//                         fontWeight: "500",
//                         paddingTop: 2,
//                       }}
//                     >
//                       {formattedTime}
//                     </Text>
//                     {isCurrentUser && (
//                       <Reanimated.View style={tickStyle}>
//                         {message.replyMessage.status === "sent" && (
//                           <Ionicons
//                             name="checkmark-done-sharp"
//                             size={14}
//                             color="white"
//                           />
//                         )}
//                         {message.replyMessage.status === "sending" && (
//                           <Ionicons
//                             name="checkmark-sharp"
//                             size={14}
//                             color="white"
//                           />
//                         )}
//                       </Reanimated.View>
//                     )}
//                   </View>
//                 </LinearGradient> */}
//               </Pressable>
//             ) : (
//               <Pressable
//                 style={{
//                   backgroundColor: "rgba(255, 255, 255, 0.8)",
//                   borderColor: "rgba(0, 0, 0, 0.2)",
//                   borderWidth: 0.8,
//                   padding: 6,
//                   borderRadius: 6,
//                   marginBottom: -5, // Overlaps with the main message
//                   alignSelf: isCurrentUser ? "flex-end" : "flex-start",
//                   maxWidth: "80%",
//                   opacity: 0.7,
//                   marginLeft: !isCurrentUser && !isNextFromSameUser ? 50 : 50,
//                   marginRight: isCurrentUser && 9,
//                   paddingBottom: 10,
//                 }}
//                 onPress={() => {
//                   // console.log(
//                   //   message.replyMessage._id,
//                   //   props.scrollToMessage,
//                   //   message._id
//                   // );
//                   props.scrollToMessage(message.replyMessage._id);
//                 }}
//                 // onLongPress={() => {
//                 //   Haptics.notificationAsync(Haptics.impactAsync());
//                 //   messageRefs.current[key].measure(
//                 //     (x, y, width, height, pageX, pageY) => {
//                 //       setOverlayPosition({ x: pageX, y: pageY, width, height });
//                 //       setSelectedMessage({ item: message, index });
//                 //     }
//                 //   );
//                 // }}
//               >
//                 <Text
//                   style={{
//                     fontWeight: "500",
//                     fontSize: 10,
//                     color: "rgba(0, 0, 0, 0.7)",
//                     marginBottom: 3,
//                   }}
//                 >
//                   {/* Reply: {message.replyMessage.user.name} */}
//                   Replying to
//                   {message.replyMessage.user._id == auth.currentUser.uid
//                     ? ` ${isCurrentUser ? "yourself" : "you"}`
//                     : `: ${message.replyMessage.user.name}`}
//                 </Text>
//                 <Text
//                   style={{
//                     fontSize: 12,
//                     color: "rgba(0, 0, 0, 0.85)",
//                   }}
//                   numberOfLines={5}
//                 >
//                   {message.replyMessage.text}
//                 </Text>
//               </Pressable>
//             )}
//           </>
//         )}

//         <Pressable
//           style={{
//             marginBottom: isNextFromSameUser ? 1 : 5,
//             marginTop: isPreviousFromSameUser ? 1 : 5,
//             // marginHorizontal:  !isNextFromSameUser ? 10 : 50,
//             marginLeft: !isCurrentUser && !isNextFromSameUser ? 10 : 50,
//             marginRight: isCurrentUser && 9,

//             alignSelf: isCurrentUser ? "flex-end" : "flex-start",
//             flexDirection: "row",
//             alignItems: "center",
//             zIndex: 10,
//           }}
//           ref={(el) => (messageRefs.current[key] = el)}
//           // onLongPress={() => {

//           //   setSelectedMessage({ item: message, index });
//           // }}
//           onLongPress={() => {
//             Haptics.notificationAsync(Haptics.impactAsync());
//             messageRefs.current[key].measure(
//               (x, y, width, height, pageX, pageY) => {
//                 setOverlayPosition({ x: pageX, y: pageY, width, height });
//                 setSelectedMessage({ item: message, index });
//               }
//             );
//           }}
//         >
//           {!isNextFromSameUser && !isCurrentUser && (
//             <MessageImage
//               avatar={message.user.avatar}
//               profileColor={message.user.profileColor}
//               userName={message.user.name}
//             />
//           )}

//           {message.imageSize ? (
//             <View
//               style={{
//                 width: calculatedWidth,
//                 // height: message.imageSize.height ,
//                 // maxWidth: screenWidth * 0.75,
//                 aspectRatio: message.imageSize.aspectRatio,

//                 borderRadius: 13,
//                 overflow: "hidden", // This clips the children inside the wrapper
//                 alignSelf: "center",
//                 // Apply additional styles like margin if needed
//                 backgroundColor: "rgba(240,240,240)",
//                 marginTop: 2,
//                 justifyContent: "center",
//                 borderWidth: 0.6,
//                 borderColor: "rgba(0,0,0,0.2)",
//                 zIndex: 10,
//               }}
//             >
//               <ActivityIndicator
//                 style={{
//                   alignSelf: "center",
//                   position: "absolute",
//                   zIndex: 1,
//                 }}
//               ></ActivityIndicator>
//               {message.image && (
//                 <Pressable
//                   onPress={() => {
//                     console.log("pressed");
//                     props.setSelectedImage({
//                       image: message.image,
//                       imageSize: message.imageSize,
//                       message: message,
//                     });
//                     props.toggleImageModal();
//                   }}
//                   onLongPress={() => {
//                     Haptics.notificationAsync(Haptics.impactAsync());
//                     messageRefs.current[key].measure(
//                       (x, y, width, height, pageX, pageY) => {
//                         setOverlayPosition({
//                           x: pageX,
//                           y: pageY,
//                           width,
//                           height,
//                         });
//                         setSelectedMessage({ item: message, index });
//                       }
//                     );
//                   }}
//                   style={{ flex: 1, zIndex: 2 }}
//                 >
//                   {!increaseZIndex && (
//                     <Image
//                       source={{ uri: message.image }}
//                       style={{
//                         width: calculatedWidth,
//                         height: calculatedHeight,
//                         resizeMode: "contain",
//                         zIndex: 2,
//                         position: "absolute",
//                       }}
//                     />
//                   )}

//                   {message.image.startsWith("https://") && (
//                     <FastImage
//                       source={{ uri: message.image }}
//                       style={{
//                         width: calculatedWidth,
//                         height: calculatedHeight,
//                         resizeMode: "contain",
//                         zIndex: increaseZIndex ? 3 : 1,
//                         position: "absolute",
//                         // left: -100,
//                       }}
//                       onLoad={() => {
//                         // setTimeout(() => {
//                         setIncreaseZIndex(true);
//                         // }, 2000);
//                       }}
//                     />
//                   )}
//                 </Pressable>
//               )}

//               <LinearGradient
//                 colors={[
//                   "rgba(0,0,0,0.2)",
//                   "rgba(0,0,0,0.2)",
//                   // "rgba(0,0,0,0.1)",
//                   "transparent",
//                 ]}
//                 start={{ x: 0.9, y: 1 }} // Start from bottom-right corner
//                 end={{ x: 0.8, y: 0.4 }}
//                 style={{
//                   position: "absolute",
//                   bottom: 0,
//                   right: 0,
//                   padding: 5,
//                   paddingTop: 10,
//                   paddingLeft: 20,
//                   borderRadius: 13,
//                   zIndex: 5,
//                   // marginBottom: 2,
//                   // marginRight: 2,
//                   flexDirection: "row",
//                   height: 50,
//                   width: 100,
//                 }}
//               >
//                 <View
//                   style={{
//                     position: "absolute",
//                     flexDirection: "row",
//                     bottom: 3,
//                     right: 3,
//                   }}
//                 >
//                   <Text
//                     style={{
//                       fontSize: 11,
//                       color: "white",
//                       fontWeight: "500",
//                       paddingTop: 2,
//                     }}
//                   >
//                     {formattedTime}
//                   </Text>
//                   {isCurrentUser && (
//                     <Reanimated.View style={tickStyle}>
//                       {message.status === "sent" && (
//                         <Ionicons
//                           name="checkmark-done-sharp"
//                           size={14}
//                           color="white"
//                         />
//                       )}
//                       {message.status === "sending" && (
//                         <Ionicons
//                           name="checkmark-sharp"
//                           size={14}
//                           color="white"
//                         />
//                       )}
//                     </Reanimated.View>
//                   )}
//                 </View>
//               </LinearGradient>
//             </View>
//           ) : (
//             <View
//               style={{
//                 backgroundColor: isCurrentUser ? red : "rgba(245,245,245,1)",
//                 borderRadius: 13,
//                 // marginBottom: message.replyMessage ? 10 : 0,
//                 // For the current user, apply the border radius to the right side
//                 borderTopRightRadius: !isPreviousFromSameUser
//                   ? 13
//                   : !isCurrentUser
//                   ? 13
//                   : 4,
//                 borderBottomRightRadius:
//                   isCurrentUser && !isNextFromSameUser
//                     ? 13
//                     : !isCurrentUser
//                     ? 13
//                     : 4,
//                 // For the opposite user, apply the border radius to the left side
//                 borderTopLeftRadius:
//                   !isCurrentUser && !isPreviousFromSameUser
//                     ? 13
//                     : isCurrentUser
//                     ? 13
//                     : 4,
//                 borderBottomLeftRadius:
//                   !isCurrentUser && !isNextFromSameUser
//                     ? 13
//                     : isCurrentUser
//                     ? 13
//                     : 4,
//                 maxWidth: "85%", // Ensures the message bubble doesn't span the entire width
//                 flexDirection: isShortMessage ? "row" : "column", // Set flexDirection based on message length
//                 alignItems: "center", // Align items center for short messages
//                 justifyContent: "space-between", // Justify content space between for short messages
//                 paddingHorizontal: 10,
//                 paddingVertical: 10,
//               }}
//             >
//               <Text
//                 style={{
//                   color: isCurrentUser ? "white" : "black",
//                   flexShrink: 1,
//                   fontSize: 16,
//                 }}
//               >
//                 {message.text}
//               </Text>
//               <View style={{ alignSelf: "flex-end", flexDirection: "row" }}>
//                 <Text
//                   style={{
//                     marginLeft: isShortMessage ? 10 : 0,
//                     fontSize: 10,
//                     color: isCurrentUser
//                       ? "rgba(255,255,255,0.8)"
//                       : "rgba(0,0,0,0.3)",
//                     alignSelf: "flex-end",
//                     marginBottom: isShortMessage ? 2 : 0,
//                     fontWeight: "500",
//                   }}
//                 >
//                   {formattedTime}
//                 </Text>
//                 {isCurrentUser && (
//                   <Reanimated.View style={tickStyle}>
//                     {message.status === "sent" && (
//                       <Ionicons
//                         name="checkmark-done-sharp"
//                         size={14}
//                         color="white"
//                       />
//                     )}
//                     {message.status === "sending" && (
//                       <Ionicons
//                         name="checkmark-sharp"
//                         size={14}
//                         color="white"
//                       />
//                     )}
//                   </Reanimated.View>
//                 )}
//               </View>
//             </View>
//           )}
//         </Pressable>
//       </Animated.View>
//     </PanGestureHandler>
//   );
// }, areEqual);
////////////////////////////////////////////////////////////

const arePropsEqual = (prevProps, nextProps) => {
  // console.log("arePropsEqual is running for message:", prevProps.message._id);

  const logChange = (propName, prevValue, nextValue) => {
    console.log(
      `RenderMessage re-render caused by ${propName}:`,
      `Previous: ${prevValue}`,
      `New: ${nextValue}`
    );
    return false;
  };

  // Message ID and index
  if (prevProps.message._id !== nextProps.message._id)
    return logChange(
      "message._id",
      prevProps.message._id,
      nextProps.message._id
    );
  if (prevProps.index !== nextProps.index)
    return logChange("index", prevProps.index, nextProps.index);

  // User relationship flags
  if (prevProps.isNextFromSameUser !== nextProps.isNextFromSameUser)
    return logChange(
      "isNextFromSameUser",
      prevProps.isNextFromSameUser,
      nextProps.isNextFromSameUser
    );
  if (prevProps.isPreviousFromSameUser !== nextProps.isPreviousFromSameUser)
    return logChange(
      "isPreviousFromSameUser",
      prevProps.isPreviousFromSameUser,
      nextProps.isPreviousFromSameUser
    );

  // Message content
  if (prevProps.message.text !== nextProps.message.text)
    return logChange(
      "message.text",
      prevProps.message.text,
      nextProps.message.text
    );
  if (prevProps.message.image !== nextProps.message.image)
    return logChange(
      "message.image",
      prevProps.message.image,
      nextProps.message.image
    );
  if (prevProps.message.status !== nextProps.message.status)
    return logChange(
      "message.status",
      prevProps.message.status,
      nextProps.message.status
    );
  if (prevProps.message.read !== nextProps.message.read)
    return logChange(
      "message.read",
      prevProps.message.read,
      nextProps.message.read
    );

  // Reply message
  if (prevProps.message.replyMessage || nextProps.message.replyMessage) {
    if (
      prevProps.message.replyMessage?._id !==
      nextProps.message.replyMessage?._id
    )
      return logChange(
        "message.replyMessage._id",
        prevProps.message.replyMessage?._id,
        nextProps.message.replyMessage?._id
      );
    if (
      prevProps.message.replyMessage?.text !==
      nextProps.message.replyMessage?.text
    )
      return logChange(
        "message.replyMessage.text",
        prevProps.message.replyMessage?.text,
        nextProps.message.replyMessage?.text
      );
    if (
      prevProps.message.replyMessage?.image !==
      nextProps.message.replyMessage?.image
    )
      return logChange(
        "message.replyMessage.image",
        prevProps.message.replyMessage?.image,
        nextProps.message.replyMessage?.image
      );
  }

  // Function references
  const functionProps = [
    "setOverlayPosition",
    "setSelectedMessage",
    "setReplyMessage",
    "setSelectedImage",
    "toggleImageModal",
    "scrollToMessage",
  ];
  for (let prop of functionProps) {
    if (prevProps[prop] !== nextProps[prop])
      return logChange(prop, "[function]", "[new function]");
  }

  // If all checks pass, consider the props equal
  return true;
};

const RenderMessage = React.memo((props) => {
  const message = props.message;

  const index = props.index;
  // const messages = props.messages;

  // const selectedMessage = props.selectedMessage;

  const messageRefs = props.messageRefs;

  const setOverlayPosition = props.setOverlayPosition;

  const setSelectedMessage = props.setSelectedMessage;

  const setReplyMessage = props.setReplyMessage;

  const isCurrentUser = message.user._id === auth.currentUser.uid;

  const isNextFromSameUser = props.isNextFromSameUser;
  const isPreviousFromSameUser = props.isPreviousFromSameUser;

  // Format the time
  const formattedTime = new Date(message.createdAt).toLocaleTimeString([], {
    hour: "2-digit",
    minute: "2-digit",
  });

  const isShortMessage = message.text.length <= 20;

  const key = `message-${index}`;

  const tickOpacity = useSharedValue(0.65); // Initial opacity

  useEffect(() => {
    if (message.read === true) {
      tickOpacity.value = withTiming(1, { duration: 100 });
    }
  }, [message.read]);

  const tickStyle = useAnimatedStyle(() => {
    return {
      opacity: tickOpacity.value,
      marginLeft: 4,
    };
  });

  ///////
  /////////
  ///////
  /////

  // console.log("RenderMessage");
  //
  const translateX = useRef(new Animated.Value(0)).current;
  const hapticTriggeredRef = useRef(false);

  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX } }],
    {
      useNativeDriver: true,
      listener: (event) => {
        const { translationX: currentTranslationX } = event.nativeEvent;
        let hapticTriggerThreshold = 40;
        if (
          !hapticTriggeredRef.current &&
          ((isCurrentUser &&
            currentTranslationX >= hapticTriggerThreshold - 15) ||
            (isCurrentUser && currentTranslationX <= -hapticTriggerThreshold) ||
            // ||
            (!isCurrentUser && currentTranslationX >= hapticTriggerThreshold))
        ) {
          setReplyMessage(message);

          Haptics.impactAsync();
          hapticTriggeredRef.current = true;
        }
      },
    }
  );

  const onHandlerStateChange = (event) => {
    const { state } = event.nativeEvent;
    if (state === State.END) {
      hapticTriggeredRef.current = false;
      Animated.spring(translateX, {
        toValue: 0,
        speed: 100,
        useNativeDriver: true,
      }).start();
    }
  };

  // const translateY = useSharedValue(100); // Start from -100

  // useEffect(() => {
  //   translateY.value = withTiming(0, { duration: 400 }); // Animate to 0 over 300 milliseconds
  // }, []);

  // const animatedStyle = useAnimatedStyle(() => {
  //   return {
  //     transform: [{ translateY: translateY.value }],
  //   };
  // });

  const translateY = useState(new Animated.Value(50))[0];

  useEffect(() => {
    const messageAge = Date.now() - message.createdAt.getTime();
    const isRecentMessage = messageAge < 3000; // 3 seconds in milliseconds

    if (isRecentMessage) {
      Animated.timing(translateY, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start();
    } else {
      // Immediately set the position for older messages
      translateY.setValue(0);
    }
  }, [message.createdAt]);

  const [savedPhoneUrl, setSavedPhoneUrl] = useState(message.image);

  const [switchImage, setSwitchImage] = useState(false);

  const [increaseZIndex, setIncreaseZIndex] = useState(false);

  useEffect(() => {
    if (message.image && message.image.startsWith("https://")) {
      setSwitchImage(true);
    }
  }, [message.image]);

  // if (message?.image) {
  //   console.log("message?.image", message?.image);
  // }

  const aspectRatio = message?.imageSize?.aspectRatio;

  const maxWidth = screenWidth * 0.73;
  const maxHeight = screenHeight * 0.35;

  let calculatedWidth = maxWidth;
  let calculatedHeight = maxWidth / aspectRatio;

  // Check if the calculated height is greater than the maximum allowed height
  if (calculatedHeight > maxHeight) {
    // Recalculate both width and height based on the maxHeight
    calculatedHeight = maxHeight;
    calculatedWidth = maxHeight * aspectRatio;
  }

  const aspectRatioRe = message?.replyMessage?.imageSize?.aspectRatio;

  const maxWidthRe = screenWidth * 0.73;
  const maxHeightRe = screenHeight * 0.35;

  let calculatedWidthRe = maxWidthRe;
  let calculatedHeightRe = maxWidthRe / aspectRatioRe;

  // Check if the calculated height is greater than the maximum allowed height
  if (calculatedHeightRe > maxHeightRe) {
    // Recalculate both width and height based on the maxHeight
    calculatedHeightRe = maxHeightRe;
    calculatedWidthRe = maxHeightRe * aspectRatioRe;
  }

  console.log("RenderMessage", message._id);

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
      maxPointers={1}
      // minDeltaX={30} // Slightly less than previously set, but still more than the default
      // activeOffsetX={[-100, 100]} // Keep the threshold for horizontal drag activation
      // activeOffsetY={[-11, 11]} // Reduce the threshold for vertical movements
      // failOffsetY={[-30, 30]} // Slightly less than previously set
      failOffsetY={[-20, 20]}
      activeOffsetX={[-8, 8]}
    >
      <Animated.View
        style={{
          transform: [
            {
              translateX: translateX.interpolate({
                inputRange: isCurrentUser ? [-200, 200] : [0, 200],
                outputRange: isCurrentUser ? [-100, 100] : [0, 100],
                extrapolate: "clamp",
              }),
            },
            { translateY: translateY },
          ],
          marginTop: message?.replyMessage ? 5 : 0,
          // ...[additional styles]...
        }}
      >
        {message.replyMessage && (
          <>
            {message.replyMessage.imageSize ? (
              <Pressable
                style={{
                  width: calculatedWidthRe * 0.8,
                  height: calculatedHeightRe * 0.8,
                  // height: message.imageSize.height ,
                  // maxWidth: screenWidth * 0.75,
                  // aspectRatio: message.replyMessage.imageSize.aspectRatio,

                  borderRadius: 13,
                  overflow: "hidden", // This clips the children inside the wrapper
                  alignSelf: "center",
                  // Apply additional styles like margin if needed
                  backgroundColor: "rgba(240,240,240)",
                  marginTop: 2,
                  justifyContent: "center",
                  borderWidth: 0.6,
                  borderColor: "rgba(0,0,0,0.2)",
                  zIndex: 10,
                  alignSelf: isCurrentUser ? "flex-end" : "flex-start",
                  marginBottom: -12, // Overlaps with the main message

                  maxWidth: "80%",
                  marginLeft: !isCurrentUser && !isNextFromSameUser ? 50 : 50,
                  marginRight: isCurrentUser && 9,
                }}
                onPress={() => {
                  // console.log(message.replyMessage._id);
                  // props.scrollToMessage(message.replyMessage._id);
                }}
              >
                <ActivityIndicator
                  style={{
                    alignSelf: "center",
                    position: "absolute",
                    zIndex: 1,
                  }}
                ></ActivityIndicator>
                {message.replyMessage.image && (
                  <>
                    <FastImage
                      source={{ uri: message.replyMessage.image }}
                      style={{
                        width: calculatedWidthRe * 0.8,
                        height: calculatedHeightRe * 0.8,
                        resizeMode: "contain",
                        zIndex: increaseZIndex ? 3 : 1,
                        position: "absolute",
                        // left: -100,
                        // opacity: 0.2,
                      }}
                    />

                    <View
                      style={{
                        backgroundColor: "white",
                        position: "absolute",
                        width: calculatedWidthRe,
                        height: calculatedHeightRe,
                        zIndex: 100,
                        opacity: 0.3,
                      }}
                    ></View>
                  </>
                )}
                {/*
                <LinearGradient
                  colors={[
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                    // "rgba(0,0,0,0.1)",
                    "transparent",
                  ]}
                  start={{ x: 0.9, y: 1 }} // Start from bottom-right corner
                  end={{ x: 0.8, y: 0.4 }}
                  style={{
                    position: "absolute",
                    bottom: 0,
                    right: 0,
                    padding: 5,
                    paddingTop: 10,
                    paddingLeft: 20,
                    borderRadius: 13,
                    zIndex: 5,
                    // marginBottom: 2,
                    // marginRight: 2,
                    flexDirection: "row",
                    height: 50,
                    width: 100,
                  }}
                >
                  <View
                    style={{
                      position: "absolute",
                      flexDirection: "row",
                      bottom: 3,
                      right: 3,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 11,
                        color: "white",
                        fontWeight: "500",
                        paddingTop: 2,
                      }}
                    >
                      {formattedTime}
                    </Text>
                    {isCurrentUser && (
                      <Reanimated.View style={tickStyle}>
                        {message.replyMessage.status === "sent" && (
                          <Ionicons
                            name="checkmark-done-sharp"
                            size={14}
                            color="white"
                          />
                        )}
                        {message.replyMessage.status === "sending" && (
                          <Ionicons
                            name="checkmark-sharp"
                            size={14}
                            color="white"
                          />
                        )}
                      </Reanimated.View>
                    )}
                  </View>
                </LinearGradient> */}
              </Pressable>
            ) : (
              <Pressable
                style={{
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                  borderColor: "rgba(0, 0, 0, 0.2)",
                  borderWidth: 0.8,
                  padding: 6,
                  borderRadius: 6,
                  marginBottom: -5, // Overlaps with the main message
                  alignSelf: isCurrentUser ? "flex-end" : "flex-start",
                  maxWidth: "80%",
                  opacity: 0.7,
                  marginLeft: !isCurrentUser && !isNextFromSameUser ? 50 : 50,
                  marginRight: isCurrentUser && 9,
                  paddingBottom: 10,
                }}
                onPress={() => {
                  // console.log(
                  //   message.replyMessage._id,
                  //   props.scrollToMessage,
                  //   message._id
                  // );
                  // props.scrollToMessage(message.replyMessage._id);
                }}
                // onLongPress={() => {
                //   Haptics.notificationAsync(Haptics.impactAsync());
                //   messageRefs.current[key].measure(
                //     (x, y, width, height, pageX, pageY) => {
                //       setOverlayPosition({ x: pageX, y: pageY, width, height });
                //       setSelectedMessage({ item: message, index });
                //     }
                //   );
                // }}
              >
                <Text
                  style={{
                    fontWeight: "500",
                    fontSize: 10,
                    color: "rgba(0, 0, 0, 0.7)",
                    marginBottom: 3,
                  }}
                >
                  {/* Reply: {message.replyMessage.user.name} */}
                  Replying to
                  {message.replyMessage.user._id == auth.currentUser.uid
                    ? ` ${isCurrentUser ? "yourself" : "you"}`
                    : `: ${message.replyMessage.user.name}`}
                </Text>
                <Text
                  style={{
                    fontSize: 12,
                    color: "rgba(0, 0, 0, 0.85)",
                  }}
                  numberOfLines={5}
                >
                  {message.replyMessage.text}
                </Text>
              </Pressable>
            )}
          </>
        )}

        <Pressable
          style={{
            marginBottom: isNextFromSameUser ? 1 : 5,
            marginTop: isPreviousFromSameUser ? 1 : 5,
            // marginHorizontal:  !isNextFromSameUser ? 10 : 50,
            marginLeft: !isCurrentUser && !isNextFromSameUser ? 10 : 50,
            marginRight: isCurrentUser && 9,

            alignSelf: isCurrentUser ? "flex-end" : "flex-start",
            flexDirection: "row",
            alignItems: "center",
            zIndex: 10,
          }}
          ref={(el) => (messageRefs.current[key] = el)}
          // onLongPress={() => {

          //   setSelectedMessage({ item: message, index });
          // }}
          onLongPress={() => {
            Haptics.notificationAsync(Haptics.impactAsync());
            messageRefs.current[key].measure(
              (x, y, width, height, pageX, pageY) => {
                setOverlayPosition({ x: pageX, y: pageY, width, height });
                setSelectedMessage({ item: message, index });
              }
            );
          }}
        >
          {!isNextFromSameUser && !isCurrentUser && (
            <MessageImage
              avatar={message.user.avatar}
              profileColor={message.user.profileColor}
              userName={message.user.name}
            />
          )}

          {message.imageSize ? (
            <View
              style={{
                width: calculatedWidth,
                // height: message.imageSize.height ,
                // maxWidth: screenWidth * 0.75,
                aspectRatio: message.imageSize.aspectRatio,

                borderRadius: 13,
                overflow: "hidden", // This clips the children inside the wrapper
                alignSelf: "center",
                // Apply additional styles like margin if needed
                backgroundColor: "rgba(240,240,240)",
                marginTop: 2,
                justifyContent: "center",
                borderWidth: 0.6,
                borderColor: "rgba(0,0,0,0.2)",
                zIndex: 10,
              }}
            >
              <ActivityIndicator
                style={{
                  alignSelf: "center",
                  position: "absolute",
                  zIndex: 1,
                }}
              ></ActivityIndicator>
              {message.image && (
                <Pressable
                  onPress={() => {
                    console.log("pressed");
                    props.setSelectedImage({
                      image: message.image,
                      imageSize: message.imageSize,
                      message: message,
                    });
                    props.toggleImageModal();
                  }}
                  onLongPress={() => {
                    Haptics.notificationAsync(Haptics.impactAsync());
                    messageRefs.current[key].measure(
                      (x, y, width, height, pageX, pageY) => {
                        setOverlayPosition({
                          x: pageX,
                          y: pageY,
                          width,
                          height,
                        });
                        setSelectedMessage({ item: message, index });
                      }
                    );
                  }}
                  style={{ flex: 1, zIndex: 2 }}
                >
                  {!increaseZIndex && (
                    <Image
                      source={{ uri: message.image }}
                      style={{
                        width: calculatedWidth,
                        height: calculatedHeight,
                        resizeMode: "contain",
                        zIndex: 2,
                        position: "absolute",
                      }}
                    />
                  )}

                  {message.image.startsWith("https://") && (
                    <FastImage
                      source={{ uri: message.image }}
                      style={{
                        width: calculatedWidth,
                        height: calculatedHeight,
                        resizeMode: "contain",
                        zIndex: increaseZIndex ? 3 : 1,
                        position: "absolute",
                        // left: -100,
                      }}
                      onLoad={() => {
                        // setTimeout(() => {
                        setIncreaseZIndex(true);
                        // }, 2000);
                      }}
                    />
                  )}
                </Pressable>
              )}

              <LinearGradient
                colors={[
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  // "rgba(0,0,0,0.1)",
                  "transparent",
                ]}
                start={{ x: 0.9, y: 1 }} // Start from bottom-right corner
                end={{ x: 0.8, y: 0.4 }}
                style={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  padding: 5,
                  paddingTop: 10,
                  paddingLeft: 20,
                  borderRadius: 13,
                  zIndex: 5,
                  // marginBottom: 2,
                  // marginRight: 2,
                  flexDirection: "row",
                  height: 50,
                  width: 100,
                }}
              >
                <View
                  style={{
                    position: "absolute",
                    flexDirection: "row",
                    bottom: 3,
                    right: 3,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 11,
                      color: "white",
                      fontWeight: "500",
                      paddingTop: 2,
                    }}
                  >
                    {formattedTime}
                  </Text>
                  {isCurrentUser && (
                    <Reanimated.View style={tickStyle}>
                      {message.status === "sent" && (
                        <Ionicons
                          name="checkmark-done-sharp"
                          size={14}
                          color="white"
                        />
                      )}
                      {message.status === "sending" && (
                        <Ionicons
                          name="checkmark-sharp"
                          size={14}
                          color="white"
                        />
                      )}
                    </Reanimated.View>
                  )}
                </View>
              </LinearGradient>
            </View>
          ) : (
            <View
              style={{
                backgroundColor: isCurrentUser ? red : "rgba(245,245,245,1)",
                borderRadius: 13,
                // marginBottom: message.replyMessage ? 10 : 0,
                // For the current user, apply the border radius to the right side
                borderTopRightRadius: !isPreviousFromSameUser
                  ? 13
                  : !isCurrentUser
                  ? 13
                  : 4,
                borderBottomRightRadius:
                  isCurrentUser && !isNextFromSameUser
                    ? 13
                    : !isCurrentUser
                    ? 13
                    : 4,
                // For the opposite user, apply the border radius to the left side
                borderTopLeftRadius:
                  !isCurrentUser && !isPreviousFromSameUser
                    ? 13
                    : isCurrentUser
                    ? 13
                    : 4,
                borderBottomLeftRadius:
                  !isCurrentUser && !isNextFromSameUser
                    ? 13
                    : isCurrentUser
                    ? 13
                    : 4,
                maxWidth: "85%", // Ensures the message bubble doesn't span the entire width
                flexDirection: isShortMessage ? "row" : "column", // Set flexDirection based on message length
                alignItems: "center", // Align items center for short messages
                justifyContent: "space-between", // Justify content space between for short messages
                paddingHorizontal: 10,
                paddingVertical: 10,
              }}
            >
              <Text
                style={{
                  color: isCurrentUser ? "white" : "black",
                  flexShrink: 1,
                  fontSize: 16,
                }}
              >
                {message.text}
              </Text>
              <View style={{ alignSelf: "flex-end", flexDirection: "row" }}>
                <Text
                  style={{
                    marginLeft: isShortMessage ? 10 : 0,
                    fontSize: 10,
                    color: isCurrentUser
                      ? "rgba(255,255,255,0.8)"
                      : "rgba(0,0,0,0.3)",
                    alignSelf: "flex-end",
                    marginBottom: isShortMessage ? 2 : 0,
                    fontWeight: "500",
                  }}
                >
                  {formattedTime}
                </Text>
                {isCurrentUser && (
                  <Reanimated.View style={tickStyle}>
                    {message.status === "sent" && (
                      <Ionicons
                        name="checkmark-done-sharp"
                        size={14}
                        color="white"
                      />
                    )}
                    {message.status === "sending" && (
                      <Ionicons
                        name="checkmark-sharp"
                        size={14}
                        color="white"
                      />
                    )}
                  </Reanimated.View>
                )}
              </View>
            </View>
          )}
        </Pressable>
      </Animated.View>
    </PanGestureHandler>
  );
}, arePropsEqual);

const deleteMessage = async (chatId, message, messages) => {
  console.log(chatId, message, messages.length);
  const messageId = message._id;
  try {
    const batch = writeBatch(db);

    if (message.image && message.imageSize) {
      const imageStoragePath = `chat_images/${chatId}/${auth.currentUser.uid}/${messageId}`;
      const storageRef = ref(storage, imageStoragePath);

      // Delete the image from Firebase Storage
      await deleteObject(storageRef);
    }

    // Get a reference to the specific message document
    const messageDocRef = doc(db, "chats", chatId, "messages", messageId);

    // Delete the document
    batch.delete(messageDocRef);

    // Update the lastUnreadMessage if deleting the last message
    if (messages.length > 1 && messages[0]._id === messageId) {
      const chatDocRef = doc(db, "chats", chatId);

      batch.update(chatDocRef, {
        lastUnreadMessage: messages[1],
        lastUnreadMessageDate: messages[1].createdAt,
      });
    }

    await batch.commit();
  } catch (error) {
    alert("Error deleting message: ", error);
  }
};

const MAX_IMAGES = 10; // Maximum number of images allowed

const pickImage = async (images, setImages) => {
  const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
  if (status !== "granted") {
    const response = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (response.status !== "granted") {
      alert("Sorry, we need media library permissions to make this work.");
      return;
    }
  }

  let result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    quality: 0,
    allowsMultipleSelection: true,
  });

  if (!result.canceled) {
    // Limit the number of images selected
    const totalImagesCount = images.length + result.assets.length;
    if (totalImagesCount > MAX_IMAGES) {
      alert(`You can only select up to ${MAX_IMAGES} images.`);
      return;
    }

    // Resize and compress the selected images
    const imagePromises = result.assets.map(async (asset) => {
      return resizeAndCompressImage(asset.uri);
    });

    const resizedImages = await Promise.all(imagePromises);
    setImages([...images, ...resizedImages.filter((image) => image)]);
  }
};

const takePhoto = async (images, setImages) => {
  const { status } = await ImagePicker.getCameraPermissionsAsync();
  if (status !== "granted") {
    const response = await ImagePicker.requestCameraPermissionsAsync();
    if (response.status !== "granted") {
      alert("Sorry, we need camera permissions to make this work.");
      return;
    }
  }

  let result = await ImagePicker.launchCameraAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    quality: 0,
  });

  if (!result.canceled) {
    const resizedImage = await resizeAndCompressImage(result.uri);
    if (resizedImage) {
      setImages([...images, resizedImage]);
    }
  }
};

const resizeAndCompressImage = async (uri) => {
  try {
    // Get the original image dimensions
    const { width: origWidth, height: origHeight } = await new Promise(
      (resolve, reject) => {
        Image.getSize(
          uri,
          (width, height) => {
            resolve({ width, height });
          },
          (error) => {
            reject(error);
          }
        );
      }
    );

    // Set a maximum dimension (width/height)
    const MAX_DIMENSION = 800;

    // Calculate the resize ratio
    const resizeRatio = MAX_DIMENSION / Math.max(origWidth, origHeight);

    // Resize dimensions
    const newWidth = origWidth * resizeRatio;
    const newHeight = origHeight * resizeRatio;

    // Resize and compress the image
    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: newWidth, height: newHeight } }],
      { compress: 0.5, format: ImageManipulator.SaveFormat.JPEG }
    );

    return {
      uri: manipResult.uri,
      width: manipResult.width,
      height: manipResult.height,
      aspectRatio: manipResult.width / manipResult.height,
    };
  } catch (error) {
    console.error("Error resizing and compressing image:", error);
    return null;
  }
};

const InputBar =
  // React.memo(
  ({
    user,
    chatId,
    replyMessage,
    setReplyMessage,
    userOpp,
    // setMessages,
    flatListRef,
  }) => {
    const [messageTextIn, setMessageTextIn] = useState("");

    const [images, setImages] = useState([]);

    useEffect(() => {
      if (images.length > 0) {
        onSendImage(messageTextIn, images);
      }
    }, [images]);

    const inputRef = useRef(null);

    // console.log("InputBar", chatId);

    useEffect(() => {
      if (replyMessage && inputRef.current) {
        inputRef.current.focus();
      }
    }, [replyMessage]);

    const scaleSendButton = useRef(new Animated.Value(0.9)).current;
    const opacitySendButton = useRef(new Animated.Value(0)).current;
    const scaleImageCamera = useRef(new Animated.Value(1)).current;
    const opacityImageCamera = useRef(new Animated.Value(1)).current;

    const dispatch = useDispatch();

    const onSend = async (messageText) => {
      const trimmedText = messageText.trim();
      setReplyMessage(null);

      flatListRef.current?.scrollToOffset({ animated: false, offset: -10 });

      setTimeout(
        async () => {
          if (trimmedText) {
            const chatDocRef = doc(db, "chats", chatId);
            const messageCollectionRef = collection(chatDocRef, "messages");
            const newMessageDocRef = doc(messageCollectionRef);

            const now = new Date();

            const newMessage = {
              _id: newMessageDocRef.id,
              createdAt: now,
              text: trimmedText,
              user: {
                _id: auth.currentUser.uid,
                avatar: user.profileImageRefSmall,
                name: user.name,
                profileColor: user.profileColor,
              },
              replyMessage: replyMessage ? replyMessage : null,
              status: "sending",
              read: false,
              image: null,
              imageSize: null,
            };

            // const slideUpAnimation = {
            //   duration: 150,
            //   create: {
            //     type: LayoutAnimation.Types.easeInEaseOut,
            //     property: LayoutAnimation.Properties.opacity,
            //     springDamping: 1,
            //     initialVelocity: 0,
            //   },
            //   update: {
            //     type: LayoutAnimation.Types.easeInEaseOut,
            //     property: LayoutAnimation.Properties.translateY,
            //     springDamping: 1,
            //     initialVelocity: 0,
            //   },
            // };

            // LayoutAnimation.configureNext(slideUpAnimation);

            const customConfig = {
              ...LayoutAnimation.Presets.linear,
              duration: 150,
            };

            LayoutAnimation.configureNext(customConfig);

            // Update the Redux store with the new message
            dispatch(
              updateMessages({
                chatId: chatId,
                messages: [newMessage], // Add as a single-item array
              })
            );

            const batch = writeBatch(db);
            batch.set(newMessageDocRef, {
              _id: newMessage._id,
              createdAt: Timestamp.fromDate(now), // Convert Date to Firestore Timestamp
              text: trimmedText,
              user: newMessage.user,
              userId: newMessage.user._id,
              replyMessage: replyMessage ? replyMessage : null,
              image: null,
              imageSize: null,
              read: false,
            });

            batch.update(chatDocRef, {
              lastUnreadMessage: {
                _id: newMessage._id,
                createdAt: Timestamp.fromDate(now), // Convert Date to Firestore Timestamp
                text: trimmedText,
                user: newMessage.user,
                userId: newMessage.user._id,
                replyMessage: replyMessage ? replyMessage : null,
                read: false,
                image: null,
                imageSize: null,

                // Convert Date to Firestore Timestamp
              },

              lastUnreadMessageDate: Timestamp.fromDate(now),
            });
            try {
              setTimeout(() => {
                batch.commit();
              }, 100);

              // The Firestore listener will update the Redux store with the confirmed message
            } catch (error) {
              console.error(error.message);
              // If you want to handle failed messages, you could update the message status here
              // For example:
              // dispatch(
              //   updateMessages({
              //     chatId: route.params.docId,
              //     messages: [{ ...newMessage, status: "failed" }],
              //   })
              // );
            }
          }
        },
        replyMessage ? 50 : 0
      );
    };

    const onSendImage = async (messageText, images) => {
      const trimmedText = messageText.trim();
      const chatDocRef = doc(db, "chats", chatId);
      const messageCollectionRef = collection(chatDocRef, "messages");

      flatListRef.current?.scrollToOffset({ animated: false, offset: -10 });
      setReplyMessage(null);

      const batch = writeBatch(db);

      // Clear the images state after initiating upload
      setImages([]);

      // Loop through the images array to upload each image
      const uploadPromises = images.map(async (imageObject) => {
        const { uri, width, height, aspectRatio } = imageObject;

        const newMessageDocRef = doc(messageCollectionRef);

        console.log("id", newMessageDocRef.id);

        // Create a new message with the image URL and dimensions
        const newMessage = {
          _id: newMessageDocRef.id,
          createdAt: new Date(Timestamp.now().seconds * 1000),
          text: trimmedText,
          user: {
            _id: auth.currentUser.uid,
            avatar: user.profileImageRefSmall,
            name: user.name,
            profileColor: user.profileColor,
          },
          userId: auth.currentUser.uid,
          image: uri,
          imageSize: { width, height, aspectRatio },
          imageID: null,
          read: false,
          replyMessage: replyMessage ? replyMessage : null,
          status: "sending",
        };

        const customConfig = {
          ...LayoutAnimation.Presets.linear,
          duration: 150, // Set your desired duration in milliseconds
        };

        LayoutAnimation.configureNext(customConfig);

        dispatch(
          updateMessages({
            chatId: chatId,
            messages: [newMessage], // Add as a single-item array
          })
        );

        let finalImageUri = uri;

        // Convert URI schema if needed
        // if (
        //   finalImageUri.startsWith("content://") ||
        //   finalImageUri.startsWith("assets-library://")
        // ) {
        //   const asset = await MediaLibrary.getAssetInfoAsync(finalImageUri);
        //   finalImageUri = asset.localUri;
        // }

        // Compress and convert the image
        const compressedImage = await ImageManipulator.manipulateAsync(
          finalImageUri,
          [],
          { compress: 1, format: ImageManipulator.SaveFormat.JPEG }
        );

        // console.log(finalImageUri);
        // console.log(compressedImage.uri);

        const metadata = {
          contentType: "image/jpeg", // or the appropriate MIME type
          customMetadata: {
            uploader: auth.currentUser.uid,
          },
        };

        const response = await fetch(compressedImage.uri);

        const blob = await response.blob();

        //

        const storageRef = ref(
          storage,
          `chat_images/${chatId}/${auth.currentUser.uid}/${newMessageDocRef.id}`
        );

        // Upload the image to Firebase Storage using uploadBytesResumable
        const uploadTask = uploadBytesResumable(storageRef, blob, metadata);
        await uploadTask;

        const imageUrl = await getDownloadURL(uploadTask.snapshot.ref);

        // Set the new image message document
        batch.set(newMessageDocRef, {
          _id: newMessageDocRef.id,

          createdAt: serverTimestamp(),
          text: trimmedText,
          user: {
            _id: auth.currentUser.uid,
            avatar: user.profileImageRefSmall,
            name: user.name,
            profileColor: user.profileColor,
          },
          userId: auth.currentUser.uid,
          replyMessage: replyMessage ? replyMessage : null,
          image: imageUrl,
          imageSize: { width, height, aspectRatio },
          read: false,
        });

        // Update the chat document with last unread message details
        batch.update(chatDocRef, {
          // [userOpp.uid + "_um"]: increment(1),
          lastUnreadMessage: {
            _id: newMessageDocRef.id,
            createdAt: serverTimestamp(),
            text: trimmedText,
            user: {
              _id: auth.currentUser.uid,
              avatar: user.profileImageRefSmall,
              name: user.name,
              profileColor: user.profileColor,
            },
            userId: auth.currentUser.uid,
            image: imageUrl,
            imageSize: { width, height, aspectRatio },

            read: false,
            replyMessage: replyMessage ? replyMessage : null,
            text: null,
          },
          lastUnreadMessageDate: serverTimestamp(),
        });
      });

      try {
        await Promise.all(uploadPromises);
        await batch.commit();
      } catch (error) {
        console.error(error.message);
        // handle any errors here
      }
    };

    // Don't forget to handle the replyMessage state accordingly

    // const onSendImage = async (messageText, imageUri) => {
    //   const trimmedText = messageText.trim();
    //   // Generate a new document reference for the image message
    //   const chatDocRef = doc(db, "chats", route.params.docId);
    //   const messageCollectionRef = collection(chatDocRef, "messages");
    //   const newMessageDocRef = doc(messageCollectionRef);

    //   // First, upload the image to Firebase Storage
    //   const storageRef = ref(
    //     storage,
    //     `chat_images/${route.params.docId}/${newMessageDocRef.id}`
    //   );
    //   const response = await fetch(imageUri);
    //   const blob = await response.blob();

    //   await uploadBytes(storageRef, blob);
    //   const imageUrl = await getDownloadURL(storageRef);

    //   // Then, create a new message with the image URL
    //   const newMessage = {
    //     _id: newMessageDocRef.id,
    //     createdAt: serverTimestamp(),
    //     user: {
    //       _id: auth.currentUser.uid,
    //       avatar: user.profileImageRefSmall,
    //       name: user.name,
    //       profileColor: user.profileColor,
    //     },
    //     image: imageUrl,
    //     text: trimmedText,
    //   };

    //   const batch = writeBatch(db);

    //   // Set the new image message document
    //   batch.set(newMessageDocRef, newMessage);

    //   // Update the chat document with last unread message details
    //   batch.update(chatDocRef, {
    //     [userOpp.uid + "_um"]: increment(1),
    //     lastUnreadMessage: {
    //       ...newMessage,
    //       text: "Image", // Since this is an image, we might set a default text
    //     },
    //     lastUnreadMessageDate: serverTimestamp(),
    //   });

    //   await batch.commit();
    // };

    //   ,
    //   [
    //     messageText,
    //     userOpp,
    //     db,
    //     route.params.docId,
    //     replyMessage,
    //     user.name,
    //     user.profileColor,
    //     user.profileImageRefSmall,
    //   ]
    // );

    // useEffect(() => {
    //   setMessageTextIn(messageTextIn);
    // }, [messageTextIn]);

    const scaleUp = () => {
      // Hide the Icons
      Animated.timing(scaleImageCamera, {
        toValue: 0.9,
        duration: 85,
        useNativeDriver: true,
      }).start();
      Animated.timing(opacityImageCamera, {
        toValue: 0,
        duration: 85,
        useNativeDriver: true,
      }).start();

      // Show the Send Button
      Animated.spring(scaleSendButton, {
        toValue: 1,
        useNativeDriver: true,
        speed: 50,
        bounciness: 13,
      }).start();
      Animated.timing(opacitySendButton, {
        toValue: 1,
        duration: 85,
        useNativeDriver: true,
      }).start();
    };

    const scaleDown = () => {
      // Show the Icons
      Animated.spring(scaleImageCamera, {
        toValue: 1,
        useNativeDriver: true,
        speed: 50,
        bounciness: 13,
      }).start();
      Animated.timing(opacityImageCamera, {
        toValue: 1,
        duration: 85,
        useNativeDriver: true,
      }).start();

      // Hide the Send Button
      Animated.timing(scaleSendButton, {
        toValue: 0.9,
        duration: 85,
        useNativeDriver: true,
      }).start();
      Animated.timing(opacitySendButton, {
        toValue: 0,
        duration: 85,
        useNativeDriver: true,
      }).start();
    };

    const textLengthRef = useRef(messageTextIn.length);

    useEffect(() => {
      const currentLength = messageTextIn.trim().length;
      const prevLength = textLengthRef.current;

      if (currentLength === 0 && prevLength > 0) {
        scaleDown();
      } else if (currentLength > 0 && prevLength === 0) {
        scaleUp();
      }

      textLengthRef.current = currentLength;
    }, [messageTextIn]);

    return (
      <View
        style={{
          width: "90%",
          borderRadius: 25,
          backgroundColor: "white",
          borderWidth: 0.5,
          borderColor: "rgba(0,0,0,0.2)",
          alignItems: "flex-end",
          flexDirection: "row",
          alignItems: "center",
          alignSelf: "center",
          zIndex: 2,
        }}
      >
        <TextInput
          ref={inputRef}
          style={{
            minHeight: 20,
            maxHeight: 120,
            paddingLeft: 15,
            marginTop: 2,
            flex: 1,
            paddingVertical: 5,
            fontSize: 15,
          }}
          placeholder="Message..."
          multiline={true}
          value={messageTextIn}
          onChangeText={setMessageTextIn}
        />

        <View
          style={{
            width: 80,
            height: 40,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Animated.View
            style={{
              transform: [{ scale: scaleImageCamera }],
              opacity: opacityImageCamera,
              flexDirection: "row",
              position: "absolute",
              zIndex: messageTextIn.trim() === "" ? 10 : 0,
            }}
          >
            <Pressable
              style={{
                marginRight: 10,
                marginTop: 1.5,
                paddingHorizontal: 5,
                paddingVertical: 5,
              }}
              onPress={() => {
                if (messageTextIn.trim() === "") pickImage(images, setImages);
              }}
            >
              <Ionicons name="images" size={27} color={red} />
            </Pressable>

            <Pressable
              style={{
                marginRight: 20,
                marginTop: 2,
                paddingHorizontal: 7,
                paddingVertical: 5,
                zIndex: 10,
              }}
              onPress={() => {
                if (messageTextIn.trim() === "") takePhoto(images, setImages);
              }}
            >
              <Entypo name="camera" size={25} color={red} />
            </Pressable>
          </Animated.View>

          <Animated.View
            style={{
              transform: [{ scale: scaleSendButton }],
              opacity: opacitySendButton,
              flexDirection: "row",
              position: "absolute",
              right: 2,
              zIndex: messageTextIn.trim() === "" ? 0 : 10,
            }}
          >
            <TouchableOpacity
              onPress={() => {
                setMessageTextIn("");
                scaleDown();
                onSend(messageTextIn);
              }}
              activeOpacity={0.8}
              style={{ marginRight: 3 }}
            >
              <LinearGradient
                colors={["#fc6285", red]}
                start={[0.2, 1]}
                end={[0.5, 0.4]}
                style={{
                  marginLeft: 5,
                  width: 44,
                  height: 32,
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: 20,
                }}
              >
                <FontAwesome
                  name="send"
                  size={16}
                  color="white"
                  style={{ marginRight: 2 }}
                />
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    );
  };
// ,
// (prevProps, nextProps) => {
//   // Implement your custom logic here to decide whether to re-render or not
//   // Return true if passing nextProps would render the same output as prevProps, otherwise return false.

//   // Example: If you have other props that should trigger a re-render, compare them here.
//   // If there are no other props, you can simply return true to prevent re-renders.

//   return true; // this will prevent the component from re-rendering.
// }
// );

export default function ChatScreen() {
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const route = useRoute();

  const dispatch = useDispatch();

  const [userOpp] = useState(route.params.user);
  const user = useSelector((state) => state.socials.userData);
  const { language } = useSelector((state) => state.language);

  const chat = useSelector((state) =>
    state.socials.chats.find((chat) => {
      // console.log("Chat", chat.id, route.params.chat.id);
      return (
        // chat.users.includes(userOpp.uid) &&
        // chat.users.includes(auth.currentUser.uid)
        chat.id === route.params.chat.id
      );
    })
  );

  const messages = useSelector((state) =>
    chat ? state.socials.messages[chat.id] : null
  );
  const isMessagesLoading = useSelector(
    (state) => state.socials.isMessagesLoading
  );

  const [chatCreated, setChatCreated] = useState(false);
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);

  const flatListRef = useRef();
  const mainOpacity = useRef(new Animated.Value(0)).current;
  const [showChat, setShowChat] = useState(false);

  useEffect(() => {
    if (chat) {
      console.log("Joining chat room");
      dispatch(setupMessageListener(chat.id));
      setChatCreated(true);
    } else {
      console.log("Creating chat room");
      createChatRoom({
        user1: auth.currentUser.uid,
        user2: userOpp.uid,
      })
        .then((result) => {
          setChatCreated(true);
        })
        .catch((error) => {
          console.error("Error creating chat room:", error);
        });
    }

    return () => {
      console.log("Leaving chat room");
      dispatch(leaveChat(chat.id));
    };
  }, [chat, dispatch]);

  useEffect(() => {
    if (messages !== null && chatCreated && !isMessagesLoading && language) {
      setShowChat(true);
      Animated.timing(mainOpacity, {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }).start();
    }
  }, [messages, chatCreated, isMessagesLoading, language]);

  const handleLoadMore = useCallback(() => {
    if (messages.length > 0 && !isMessagesLoading) {
      setLoadingMoreMessages(true);
      const oldestMessage = messages[messages.length - 1];
      dispatch(loadOlderMessages(chat.id, oldestMessage.createdAt))
        .then((result) => {
          console.log("Messages loaded successfully");
          setLoadingMoreMessages(false);
          if (result === true) {
            console.log("No more messages to load");
            setHasMoreMessages(false);
          }
        })
        .catch((error) => {
          console.error("Error loading older messages:", error);
          setLoadingMoreMessages(false);
        });
    }
  }, [messages, isMessagesLoading, chat, dispatch]);

  const [selectedImage, setSelectedImage] = useState(null);
  const [imageModalVisible, setImageModal] = useState(false);
  const imageModalValue = useRef(new Animated.Value(0)).current;

  const toggleImageModal = useCallback(() => {
    if (imageModalVisible) {
      Animated.timing(imageModalValue, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setImageModal(false));
    } else {
      setImageModal(true);
      Animated.timing(imageModalValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [imageModalVisible, imageModalValue]);

  const [shareLoading, setShareLoading] = useState(false);
  const [showZoomImage, setShowZoomImage] = useState(false);

  const [selectedMessage, setSelectedMessage] = useState(null);
  const [overlayPosition, setOverlayPosition] = useState(null);

  const messageRefs = useRef({});

  const scrollToMessage = useCallback(
    (messageId) => {
      const index = messages.findIndex((msg) => msg._id === messageId);
      if (index !== -1) {
        flatListRef.current.scrollToIndex({ animated: false, index: index });

        setTimeout(() => {
          if (flatListRef.current && flatListRef.current._listRef) {
            const currentOffset =
              flatListRef.current._listRef._scrollMetrics.offset;
            flatListRef.current.scrollToOffset({
              animated: true,
              offset: currentOffset - 200,
            });
          }
        }, 300);
      } else {
        console.log("Message not found", messageId);
        setTargetMessageId(messageId);
      }
    },
    [messages]
  );

  const [replyMessage, setReplyMessage] = useState(null);

  useFocusEffect(
    useCallback(() => {
      const checkAndUpdateUnreadMessages = async () => {
        if (
          chat &&
          chat.unreadMessages &&
          chat.unreadMessages[auth.currentUser.uid] > 0
        ) {
          // console.warn(
          //   chat.unreadMessages[auth.currentUser.uid],
          //   "unread messages"
          // );
          try {
            const chatDocRef = doc(db, "chats", chat.id);
            await updateDoc(chatDocRef, {
              [`unreadMessages.${auth.currentUser.uid}`]: 0,
            });
            console.log("Unread messages count reset to 0");
          } catch (error) {
            console.error("Error updating unread messages count:", error);
          }
        }
      };

      checkAndUpdateUnreadMessages();
    }, [chat])
  );
  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.replyMessage) {
        console.warn("Reply message found", route.params.replyMessage);
        setTimeout(() => {
          setReplyMessage(route.params.replyMessage);
        }, 350);

        // Clear the parameter after using it
        navigation.setParams({ replyMessage: undefined });
      }
    }, [route.params?.replyMessage])
  );

  const [fadeAnim] = useState(new Animated.Value(0));
  const [hideBlurryView, setHideBlurryView] = useState(false);

  useEffect(() => {
    if (selectedMessage && overlayPosition) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 80,
        useNativeDriver: true,
      }).start();
    }
    if (hideBlurryView) {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 80,
        useNativeDriver: true,
      }).start();
    }
  }, [selectedMessage, overlayPosition, fadeAnim, hideBlurryView]);

  const slideAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (replyMessage) {
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 175,
        useNativeDriver: true,
      }).start();
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 175,
        useNativeDriver: true,
      }).start();
    }
    if (!replyMessage) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }).start();
      Animated.timing(opacityAnim, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }).start();
    }
  }, [replyMessage]);

  /// This section is to prevent each message rerendering
  const setOverlayPositionCallback = useCallback((position) => {
    setOverlayPosition(position);
  }, []);

  const setSelectedMessageCallback = useCallback((message) => {
    setSelectedMessage(message);
  }, []);

  const setReplyMessageCallback = useCallback((message) => {
    setReplyMessage(message);
  }, []);

  const setSelectedImageCallback = useCallback((image) => {
    setSelectedImage(image);
  }, []);

  const renderSelectedMessage = useCallback(
    (item, index, overlayPosition) => {
      const message = item;
      const isCurrentUser = message.user._id === auth.currentUser.uid;

      const previousMessage =
        index + 1 < messages.length ? messages[index + 1] : null;
      const nextMessage = index - 1 >= 0 ? messages[index - 1] : null;

      const isNextFromSameUser =
        nextMessage && nextMessage.user._id === message.user._id;
      const isPreviousFromSameUser =
        previousMessage && previousMessage.user._id === message.user._id;

      // Format the time
      const formattedTime = new Date(message.createdAt).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });

      const isShortMessage = message.text.length <= 20;

      const key = `message-${index}`;

      // Calculate max width based on the position and screen width

      let maxWidth = screenWidth - overlayPosition.x;
      maxWidth = maxWidth < 100 ? 100 : maxWidth; // Set a minimum width

      // Adjust the position if the message overflows the screen
      let adjustedLeft = overlayPosition.x;
      if (overlayPosition.x + maxWidth > screenWidth) {
        adjustedLeft = screenWidth - maxWidth; // Adjust to fit within screen
      }

      const aspectRatio = message?.imageSize?.aspectRatio;

      const maxWidthImage = screenWidth * 0.73;
      const maxHeightImage = screenHeight * 0.35;

      let calculatedWidth = maxWidthImage;
      let calculatedHeight = maxWidthImage / aspectRatio;

      // Check if the calculated height is greater than the maximum allowed height
      if (calculatedHeight > maxHeightImage) {
        // Recalculate both width and height based on the maxHeight
        calculatedHeight = maxHeightImage;
        calculatedWidth = maxHeightImage * aspectRatio;
      }

      // Now apply these dimensions to the image
      const imageStyle = {
        width: calculatedWidth,
        height: calculatedHeight,
        resizeMode: "contain",
        zIndex: 2,
        // opacity: 0.1,
        borderTopWidth: 0.5,
        borderColor: "rgba(0,0,0,0.2)",
      };

      return (
        <Pressable
          style={{
            // marginBottom: isNextFromSameUser ? 1 : 5,
            // marginTop: isPreviousFromSameUser ? 1 : 5,
            // // marginHorizontal:  !isNextFromSameUser ? 10 : 50,
            // marginLeft: !isCurrentUser && !isNextFromSameUser ? 10 : 50,
            // marginRight: isCurrentUser && 9,

            // alignSelf: isCurrentUser ? "flex-end" : "flex-start",
            // flexDirection: "row",
            // alignItems: "center",
            // marginBottom: index == 0 ? 100 : 10,
            marginLeft: !isNextFromSameUser && !isCurrentUser && 40,

            zIndex: 50,
            position: "absolute",
            left: overlayPosition.x,
            top: overlayPosition.y,
          }}
          // ref={(el) => (messageRefs.current[key] = el)}
          // onLongPress={() => {

          //   setSelectedMessage({ item: message, index });
          // }}
          // onLongPress={() => {
          //   Haptics.notificationAsync(Haptics.impactAsync());
          //   handleMessageLongPress(message, key, index);
          // }}
        >
          {/* {!isNextFromSameUser && !isCurrentUser && (
            <MessageImage
              avatar={message.user.avatar}
              profileColor={message.user.profileColor}
              userName={message.user.name}
            />
          )} */}

          {message.imageSize ? (
            <View
              style={{
                width: calculatedWidth,
                // height: message.imageSize.height ,
                // maxWidth: screenWidth * 0.75,
                aspectRatio: message.imageSize.aspectRatio,

                borderRadius: 13,
                overflow: "hidden", // This clips the children inside the wrapper
                alignSelf: "center",
                // Apply additional styles like margin if needed
                backgroundColor: "rgba(240,240,240)",
                marginTop: 2,
                justifyContent: "center",
                borderWidth: 0.6,
                borderColor: "rgba(0,0,0,0.2)",
                zIndex: 10,
              }}
            >
              <ActivityIndicator
                style={{
                  alignSelf: "center",
                  position: "absolute",
                  zIndex: 1,
                }}
              ></ActivityIndicator>
              {message.image && (
                <FastImage
                  source={{ uri: message.image }}
                  style={imageStyle}
                ></FastImage>
              )}

              <LinearGradient
                colors={[
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  // "rgba(0,0,0,0.1)",
                  "transparent",
                ]}
                start={{ x: 0.9, y: 1 }} // Start from bottom-right corner
                end={{ x: 0.8, y: 0.4 }}
                style={{
                  position: "absolute",
                  bottom: 0,
                  right: 0,
                  padding: 5,
                  paddingTop: 10,
                  paddingLeft: 20,
                  borderRadius: 13,
                  zIndex: 2,
                  // marginBottom: 2,
                  // marginRight: 2,
                  flexDirection: "row",
                  height: 50,
                  width: 100,
                }}
              >
                <View
                  style={{
                    position: "absolute",
                    flexDirection: "row",
                    bottom: 3,
                    right: 3,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 11,
                      color: "white",
                      fontWeight: "500",
                      paddingTop: 2,
                    }}
                  >
                    {formattedTime}
                  </Text>
                  {isCurrentUser && (
                    <View
                      style={{
                        opacity: message.read === true ? 1 : 0.65,
                        marginLeft: 4,
                      }}
                    >
                      {message.status === "sent" && (
                        <Ionicons
                          name="checkmark-done-sharp"
                          size={14}
                          color="white"
                        />
                      )}
                      {message.status === "sending" && (
                        <Ionicons
                          name="checkmark-sharp"
                          size={14}
                          color="white"
                        />
                      )}
                    </View>
                  )}
                </View>
              </LinearGradient>
            </View>
          ) : (
            <View
              style={{
                backgroundColor: isCurrentUser ? red : "rgba(245,245,245,1)",
                borderRadius: 13,
                // marginBottom: message.replyMessage ? 10 : 0,
                // For the current user, apply the border radius to the right side
                borderTopRightRadius: !isPreviousFromSameUser
                  ? 13
                  : !isCurrentUser
                  ? 13
                  : 4,
                borderBottomRightRadius:
                  isCurrentUser && !isNextFromSameUser
                    ? 13
                    : !isCurrentUser
                    ? 13
                    : 4,
                // For the opposite user, apply the border radius to the left side
                borderTopLeftRadius:
                  !isCurrentUser && !isPreviousFromSameUser
                    ? 13
                    : isCurrentUser
                    ? 13
                    : 4,
                borderBottomLeftRadius:
                  !isCurrentUser && !isNextFromSameUser
                    ? 13
                    : isCurrentUser
                    ? 13
                    : 4,
                maxWidth: "100%", // Ensures the message bubble doesn't span the entire width
                flexDirection: isShortMessage ? "row" : "column", // Set flexDirection based on message length
                alignItems: "center", // Align items center for short messages
                justifyContent: "space-between", // Justify content space between for short messages
                paddingHorizontal: 10,
                paddingVertical: 10,
              }}
            >
              <Text
                style={{
                  color: isCurrentUser ? "white" : "black",
                  flexShrink: 1,
                  fontSize: 16,
                }}
              >
                {message.text}
              </Text>
              <View style={{ alignSelf: "flex-end", flexDirection: "row" }}>
                <Text
                  style={{
                    marginLeft: isShortMessage ? 10 : 0,
                    fontSize: 10,
                    color: isCurrentUser
                      ? "rgba(255,255,255,0.8)"
                      : "rgba(0,0,0,0.3)",
                    alignSelf: "flex-end",
                    marginBottom: isShortMessage ? 2 : 0,
                    fontWeight: "500",
                  }}
                >
                  {formattedTime}
                </Text>
                {isCurrentUser && (
                  <View
                    style={{
                      opacity: message.read === true ? 1 : 0.65,
                      marginLeft: 4,
                    }}
                  >
                    {message.status === "sent" && (
                      <Ionicons
                        name="checkmark-done-sharp"
                        size={14}
                        color="white"
                      />
                    )}
                    {message.status === "sending" && (
                      <Ionicons
                        name="checkmark-sharp"
                        size={14}
                        color="white"
                      />
                    )}
                  </View>
                )}
              </View>
            </View>
          )}
        </Pressable>
      );
    },
    [messages, auth]
  );

  const BlurryComp = () => {
    const windowWidth = screenWidth * 0.4; // As you set width to 40%
    const windowHeight = 100; // Assuming a fixed height for your window

    let topPosition, leftPosition;

    if (selectedMessage && overlayPosition) {
      // Calculate additional offset based on message height
      const offset = Math.max(20, overlayPosition.height / 10); // Adjust 20 and 1/4 factor as needed

      // Check space availability
      const spaceBelow =
        screenHeight - (overlayPosition.y + overlayPosition.height);
      const spaceAbove = overlayPosition.y;

      const hasSpaceBelow = spaceBelow >= windowHeight + offset;
      const hasSpaceAbove = spaceAbove >= windowHeight + offset;

      if (hasSpaceBelow) {
        topPosition = overlayPosition.y + overlayPosition.height + offset;
      } else if (hasSpaceAbove) {
        topPosition = overlayPosition.y - windowHeight - offset;
      } else {
        // For very long messages, render the window partially overlapping the message
        if (
          overlayPosition.y + overlayPosition.height + windowHeight >
          screenHeight
        ) {
          // Bottom of the message extends beyond the screen
          topPosition = screenHeight - windowHeight - 10; // 10 is a buffer from screen bottom
        } else {
          // Message is large but fits within the screen, or it's cut off at the top
          topPosition =
            overlayPosition.y + overlayPosition.height - windowHeight / 2;
        }
      }

      leftPosition = overlayPosition.x;
      if (leftPosition + windowWidth > screenWidth) {
        leftPosition = screenWidth - windowWidth - 10;
      }
      if (leftPosition < 0) {
        leftPosition = 0;
      }
    }

    return (
      <>
        <Animated.View
          style={{
            alignSelf: "center",
            position: "absolute",
            width: "40%",
            top: topPosition,
            left: leftPosition,
            zIndex: 100,
            backgroundColor: "rgba(245,245,245,0.95)",
            borderRadius: 15,
            opacity: fadeAnim,
          }}
        >
          <Pressable
            style={({ pressed }) => ({
              flexDirection: "row",
              justifyContent: "space-between",
              padding: 12,
              backgroundColor: !pressed
                ? "rgba(245,245,245,0.95)"
                : "rgba(230,230,230,0.95)",
              borderTopEndRadius: 15,
              borderTopStartRadius: 15,
              // borderBottomStartRadius: 15,
              // borderBottomEndRadius: 15,
            })}
            onPress={() => {
              setHideBlurryView(true);

              setTimeout(() => {
                setSelectedMessage(null);
                setOverlayPosition(null);
                setHideBlurryView(false);
                setReplyMessage(selectedMessage.item);
              }, 20);
            }}
          >
            <Text style={{ fontSize: 18 }}>Reply</Text>
            <Octicons name="reply" size={22} color="black" />
          </Pressable>
          {selectedMessage.item.user._id == auth.currentUser.uid && (
            <>
              <View
                style={{
                  width: "100%",
                  height: 0.4,
                  backgroundColor: "rgba(0,0,0,0.4)",
                }}
              />
              <Pressable
                style={({ pressed }) => ({
                  flexDirection: "row",
                  justifyContent: "space-between",
                  padding: 12,
                  backgroundColor: !pressed
                    ? "rgba(245,245,245,0.95)"
                    : "rgba(230,230,230,0.95)",
                  borderBottomEndRadius: 15,
                  borderBottomStartRadius: 15,
                })}
                onPress={() => {
                  setHideBlurryView(true);

                  setTimeout(() => {
                    setSelectedMessage(null);
                    setOverlayPosition(null);
                    setHideBlurryView(false);
                    deleteMessage(chat.id, selectedMessage.item, messages);
                  }, 20);
                }}
              >
                <Text style={{ fontSize: 18, color: red }}>Delete</Text>
                <MaterialIcons name="delete" size={24} color={red} />
              </Pressable>
            </>
          )}

          {/* <TouchableOpacity
            style={{
              // height: 20,
              flexDirection: "row",
              justifyContent: "space-between",
              padding: 10,
            }}
          >
            <Text style={{ fontSize: 18, color: red }}>Delete</Text>
          </TouchableOpacity> */}
        </Animated.View>
        <Pressable
          style={{
            width: "100%",
            height: "100%",
            position: "absolute",
            // backgroundColor: "red",
            zIndex: 3,
          }}
          onPress={() => {
            setHideBlurryView(true);
            setTimeout(() => {
              setSelectedMessage(null);
              setOverlayPosition(null);
              setHideBlurryView(false);
            }, 80);
          }}
        >
          <Animated.View
            style={{
              position: "absolute", // Position the BlurView absolutely
              width: "100%",
              height: "120%",
              // flex: 1,
              zIndex: 5,
              opacity: fadeAnim,
            }}
          >
            <BlurView
              style={{
                position: "absolute", // Position the BlurView absolutely
                width: "100%",
                height: "120%",
                // flex: 1,
                zIndex: 5,
              }}
              intensity={50} // you can adjust intensity
              tint="dark" // or 'light' or 'dark'
            ></BlurView>
          </Animated.View>

          <View
            style={{
              // position: "absolute",
              // left: overlayPosition.x,
              // top: overlayPosition.y,
              zIndex: 10,
              width: "100%",
              height: "100%",
              alignSelf: "center",
            }}
          >
            {selectedMessage &&
              renderSelectedMessage(
                selectedMessage.item,
                selectedMessage.index,
                overlayPosition
              )}
          </View>
        </Pressable>
      </>
    );
  };

  if (!showChat) {
    return (
      <View style={{ backgroundColor: "white", flex: 1 }}>
        <ActivityIndicator
          size="small"
          style={{ marginTop: 200, alignSelf: "center" }}
        ></ActivityIndicator>
      </View>
    );
  }

  return (
    <Animated.View
      style={{
        width: "100%",
        height: "100%",
        backgroundColor: "white",
        paddingTop: insets.top - 10,
        zIndex: 2,
      }}
    >
      {selectedMessage && overlayPosition && <BlurryComp />}

      <View
        style={{
          height: 60,
          alignSelf: "center",
          width: "100%",
          flexDirection: "row",
          alignItems: "center",
          // justifyContent: "center",
          borderBottomColor: "rgba(0,0,0,0.3)",
          borderBottomWidth: 0.4,
          backgroundColor: "white",
          zIndex: 2,
          // paddingTop: insets.top,
        }}
      >
        {chat.type === "private" ? (
          <>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 60,
                // backgroundColor: "red",
                padding: 3,
              }}
              onPress={() => navigation.navigate("User", { user: userOpp })}
              // onPress={() => {
              //   console.warn("Profile", userOpp);
              // }}
            >
              <FastImage
                source={{ uri: userOpp.profileImageRef }}
                style={{
                  height: 35,
                  width: 35,
                  borderRadius: 100,
                  marginRight: 0,
                }}
              ></FastImage>
            </Pressable>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "flex-start",
                alignItems: "center",
                marginLeft: 5,
                // backgroundColor: "green",
                padding: 3,
                width: "60%",
              }}
              onPress={() =>
                navigation.navigate("ChatMenu", { chat: chat, user: userOpp })
              }
            >
              <View
                style={{
                  // backgroundColor: "red",
                  // paddingHorizontal: 15,
                  paddingVertical: 5,
                  flexDirection: "row",
                }}
              >
                <Text
                  style={{
                    // fontFamily: "Nunito-Bold",
                    fontSize: 16,
                    color: "rgba(0,0,0,0.87)",
                    paddingLeft: 5,
                    fontWeight: "600",
                    paddingTop: 2,
                  }}
                >
                  {userOpp.name}
                </Text>
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={13}
                  color="black"
                  style={{
                    alignSelf: "center",
                    marginTop: 2,
                    marginLeft: 3,
                    color: "rgba(0,0,0,0.7)",
                  }}
                />
              </View>
            </Pressable>
          </>
        ) : chat.type === "game" ? (
          <>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 60,
                // backgroundColor: "red",
                padding: 3,
              }}
              onPress={() => {
                // console.log(chat.gameId);
                navigation.navigate("Game", {
                  gameId: chat.gameId,
                });
              }}
            >
              <FastImage
                source={{ uri: chat.gameInfo.courtInfo.imageRefs[0] }}
                style={{
                  height: 38,
                  width: 48,
                  borderRadius: 12,
                  marginRight: 0,
                }}
              ></FastImage>
            </Pressable>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "flex-start",
                alignItems: "center",
                marginLeft: 5,
                // backgroundColor: "green",
                padding: 3,
                width: "60%",
              }}
              onPress={() =>
                navigation.navigate("ChatMenu", { chat: chat, user: null })
              }
            >
              <View
                style={{
                  // backgroundColor: "red",
                  // paddingHorizontal: 15,
                  paddingVertical: 5,
                  flexDirection: "row",
                }}
              >
                <Text
                  style={{
                    // fontFamily: "Nunito-Bold",
                    fontSize: 16,
                    color: "rgba(0,0,0,0.87)",
                    paddingLeft: 5,
                    fontWeight: "600",
                    paddingTop: 2,
                  }}
                >
                  {chat.gameInfo.name}
                </Text>
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={13}
                  color="black"
                  style={{
                    alignSelf: "center",
                    marginTop: 2,
                    marginLeft: 3,
                    color: "rgba(0,0,0,0.7)",
                  }}
                />
              </View>
            </Pressable>

            {/* <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 60,
                // backgroundColor: "red",
                padding: 3,
              }}
              onPress={() => {
                // console.log(chat.gameId);
                navigation.navigate("Game", {
                  gameId: chat.gameId,
                });
              }}
            >
              <FastImage
                source={{ uri: chat.gameInfo.courtInfo.imageRefs[0] }}
                style={{
                  height: 40,
                  width: 50,
                  borderRadius: 12,
                  marginRight: 0,
                }}
              ></FastImage>
              <View
                style={{
                  // backgroundColor: "red",
                  // paddingHorizontal: 15,
                  paddingVertical: 5,
                  marginLeft: 10,
                }}
              >
                <Text
                  style={{
                    // fontFamily: "Nunito-Bold",
                    fontSize: 16,
                    color: "rgba(0,0,0,0.87)",
                    paddingLeft: 5,
                    fontWeight: "600",
                    paddingTop: 2,
                  }}
                >
                  {chat.gameInfo.name}
                </Text>
              </View>
            </Pressable> */}
          </>
        ) : (
          <>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "center",
                alignItems: "center",
                marginLeft: 60,
                padding: 3,
              }}
              onPress={() => {
                navigation.navigate("TeamDetails", {
                  teamId: chat.teamId,
                });
              }}
            >
              {/* <View
                style={{
                  width: 35,
                  height: 35,
                  borderRadius: 8,
                  backgroundColor: chat.teamInfo.color,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{ fontSize: 15, color: "white", fontWeight: "bold" }}
                >
                  {chat.teamInfo.name[0].toUpperCase()}
                </Text>
              </View> */}
              <TeamProfileIcon team={chat.teamInfo} size={35}></TeamProfileIcon>
            </Pressable>
            <Pressable
              style={{
                flexDirection: "row",
                justifyContent: "flex-start",
                alignItems: "center",
                marginLeft: 5,
                padding: 3,
                width: "60%",
              }}
              onPress={() =>
                navigation.navigate("ChatMenu", { chat: chat, user: null })
              }
            >
              <View style={{ paddingVertical: 5, flexDirection: "row" }}>
                <Text
                  style={{
                    fontSize: 16,
                    color: "rgba(0,0,0,0.87)",
                    paddingLeft: 5,
                    fontWeight: "600",
                    paddingTop: 2,
                  }}
                >
                  {chat.teamInfo.name}
                </Text>
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={13}
                  color="black"
                  style={{
                    alignSelf: "center",
                    marginTop: 2,
                    marginLeft: 3,
                    color: "rgba(0,0,0,0.7)",
                  }}
                />
              </View>
            </Pressable>
          </>
        )}

        <Pressable
          style={{
            position: "absolute",
            left: 15,
            // backgroundColor: "red",
            paddingVertical: 5,
            paddingLeft: 5,
          }}
          onPress={() => {
            navigation.goBack();
          }}
        >
          {/* <AntDesign
            name="arrowleft"
            size={30}
            color="black"
            style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
          /> */}

          <MaterialIcons
            name="arrow-back-ios"
            size={25}
            color="black"
            style={{ alignSelf: "center", marginTop: 2, marginLeft: 3 }}
          />
        </Pressable>
      </View>

      {!showChat && (
        <View
          style={{
            position: "absolute",
            backgroundColor: "white",
            marginTop: insets.top + 50,
            height: "100%",
            width: "100%",
            zIndex: 100,

            alignItems: "center",
          }}
        >
          <View
            style={{
              marginTop: "30%",

              alignItems: "center",
            }}
          >
            {!chatCreated && (
              <Text style={{ marginBottom: 15, color: "rgba(0,0,0,0.8)" }}>
                Creating Chat Room
              </Text>
            )}

            <ActivityIndicator></ActivityIndicator>
          </View>
        </View>
      )}

      {userOpp && messages && chatCreated && (
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
          keyboardVerticalOffset={Platform.OS === "ios" ? -20 : -20}
        >
          <View
            style={{
              zIndex: 10,
              height: "100%",
              width: 25,
              backgroundColor: "transparent",
              position: "absolute",
            }}
          ></View>
          <Animated.View
            style={{ flex: 1, backgroundColor: "white", opacity: mainOpacity }}
          >
            <Animated.View
              style={{
                // flex: 1,
                width: "100%",
                height: "100%",
                transform: [
                  {
                    translateY: slideAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -58], // Adjust this value as needed
                    }),
                  },
                ],
              }}
            >
              <FlatList
                ref={flatListRef}
                style={{
                  backgroundColor: "white",
                  flex: 1,
                }}
                data={messages}
                renderItem={({ item, index }) => {
                  const nextMessage = index > 0 ? messages[index - 1] : null;
                  const previousMessage =
                    index + 1 < messages.length ? messages[index + 1] : null;

                  const isNextFromSameUser =
                    nextMessage && nextMessage.user._id === item.user._id;
                  const isPreviousFromSameUser =
                    previousMessage &&
                    previousMessage.user._id === item.user._id;

                  return (
                    <RenderMessage
                      message={item}
                      index={index}
                      isNextFromSameUser={isNextFromSameUser}
                      isPreviousFromSameUser={isPreviousFromSameUser}
                      messageRefs={messageRefs}
                      setOverlayPosition={setOverlayPositionCallback}
                      setSelectedMessage={setSelectedMessageCallback}
                      setReplyMessage={setReplyMessageCallback}
                      setSelectedImage={setSelectedImageCallback}
                      toggleImageModal={toggleImageModal}
                      // key={`${item._id}-${item.createdAt.getTime()}`}
                      // scrollToMessage={scrollToMessage}
                    />
                  );
                }}
                keyExtractor={(item) => `${item._id}`}
                inverted={true}
                contentContainerStyle={{ paddingBottom: 20 }}
                showsVerticalScrollIndicator={false}
                // on={() => {
                //   // console.log("onEndReached", hasMoreMessages);
                //   // if (hasMoreMessages) {
                //   //   setMessagesLimit((prev, next) => prev + 30);
                //   // }
                //   console.warn("aaaaaaaaaaaaaaaaa");
                //   // handleLoadMore();
                // }}
                onEndReached={() => {
                  if (hasMoreMessages && messages.length >= 19) {
                    handleLoadMore();
                  }
                }}
                // onEndReachedThreshold={0.5}
                ListFooterComponent={() =>
                  loadingMoreMessages ? (
                    <ActivityIndicator
                      style={{ paddingTop: 10, paddingBottom: 20 }}
                    />
                  ) : null
                }
                ListHeaderComponent={
                  // <Animated.View
                  //   style={{
                  //     height: 90,
                  //     backgroundColor: "white",
                  //     width: "100%",
                  //     // opacity: 0.8,
                  //     transform: [
                  //       {
                  //         translateY: slideAnim.interpolate({
                  //           inputRange: [0, 1],
                  //           outputRange: [0, -60], // Adjusts the vertical position
                  //         }),
                  //       },
                  //     ],
                  //   }}
                  // ></Animated.View>
                  /////////////
                  <View
                    style={{
                      height: 85,
                      backgroundColor: "white",
                      width: "100%",
                    }}
                  ></View>
                }
              />
            </Animated.View>

            {replyMessage && (
              <Animated.View
                style={{
                  position: "absolute",
                  bottom: 80,
                  width: "100%",
                  // transform: [
                  //   {
                  //     translateY: slideAnim.interpolate({
                  //       inputRange: [0, 1],
                  //       outputRange: [60, 0], // Adjust as needed
                  //     }),
                  //   },
                  // ],
                  borderTopWidth: 0.2,
                  borderTopColor: "rgba(0,0,0,0.3)",
                  zIndex: 1,
                  justifyContent: "center",
                  opacity: opacityAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                    // Adjust as needed for opacity
                  }),
                  // backgroundColor: "red",
                }}
              >
                <BlurView
                  style={{
                    flexDirection: "row",
                    height: 60,
                    width: "100%",
                    alignSelf: "center",
                    // backgroundColor: "green",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                  intensity={100}
                  tint="default"
                >
                  <View
                    style={{
                      position: "absolute",
                      width: "100%",
                      height: 60,
                      backgroundColor: "white",
                      opacity: 0.6,
                    }}
                  />
                  <View
                    style={{
                      alignSelf: "center",
                      width: replyMessage.imageSize ? "75%" : "85%",
                      // backgroundColor: "red",
                      marginBottom: 3,
                    }}
                  >
                    <View
                      style={{
                        flexDirection: "row",
                        flexWrap: "wrap",
                        marginBottom: 2,
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "400",
                          color: "rgba(60,60,60,1)",
                        }}
                      >
                        Replying to
                      </Text>

                      <Text
                        style={{
                          fontSize: 12,
                          fontWeight: "400",
                          // marginLeft: 5,
                          color: "rgba(60,60,60,1)",
                        }}
                      >
                        {replyMessage.user._id == auth.currentUser.uid
                          ? ` yourself`
                          : `: ${replyMessage.user.name}`}
                      </Text>
                    </View>
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      {replyMessage.imageSize && (
                        <Entypo
                          name="camera"
                          size={15}
                          color={"rgba(0,0,0,0.45)"}
                          style={{
                            marginRight: 7,
                            paddingTop: 4,
                            paddingLeft: 2,
                          }}
                        />
                      )}

                      <Text
                        style={{
                          fontSize: 13,
                          marginTop: 5,

                          // backgroundColor: "red",
                          maxWidth: "92%",
                        }}
                        numberOfLines={1}
                      >
                        {replyMessage.imageSize ? "Photo" : replyMessage.text}
                      </Text>
                    </View>
                  </View>
                  {replyMessage.imageSize && (
                    <FastImage
                      source={{ uri: replyMessage.image }}
                      style={{
                        width: 43,
                        height: 43,
                        resizeMode: "contain",
                        zIndex: 2,
                        borderRadius: 5,
                      }}
                    />
                  )}

                  <Pressable
                    onPress={() => {
                      // setHideReplyMessage(true);
                      setReplyMessage(null);
                    }}
                    style={{
                      // backgroundColor: "red",
                      paddingVertical: 10,
                      paddingHorizontal: 5,
                    }}
                  >
                    {/* <AntDesign name="close" size={28} color="rgba(0,0,0,0.5)" /> */}
                    <EvilIcons
                      name="close"
                      size={28}
                      color="rgba(0,0,0,1)"
                      style={{ marginBottom: 5 }}
                    />
                  </Pressable>
                </BlurView>
              </Animated.View>
            )}

            <BlurView
              style={{
                position: "absolute", // Position the BlurView absolutely
                // top: 0, // Position from the top
                left: 0, // Position from the left
                right: 0, // Position from the right
                bottom: 0, // Position from the bottom
                // paddingTop: !replyMessage ? 10 : 0,
                // borderColor: !replyMessage ? "rgba(0,0,0,0.3)" : 0,
                // borderTopWidth: !replyMessage ? 0.2 : 0,
                ///////////////////

                paddingTop: 12,
                borderColor: "rgba(0,0,0,0.3)",
                borderTopWidth: 0.2,

                ///////////
                paddingBottom: 30,
                zIndex: 2,
              }}
              intensity={100} // you can adjust intensity
              tint="default" // or 'light' or 'dark'
            >
              <View
                style={{
                  width: "100%",
                  height: 150,
                  backgroundColor: "white",
                  position: "absolute",
                  opacity: 0.6,
                  // zIndex: 10,
                }}
              ></View>

              <InputBar
                // messageText={messageText}
                // setMessageText={setMessageText}
                user={user}
                chatId={chat.id}
                replyMessage={replyMessage}
                // setHideReplyMessage={setHideReplyMessage}
                setReplyMessage={setReplyMessage}
                userOpp={userOpp}
                // setMessages={setMessages}
                flatListRef={flatListRef}
              />
            </BlurView>
          </Animated.View>
        </KeyboardAvoidingView>
      )}

      <Modal
        animationType="none"
        transparent={true}
        visible={imageModalVisible}
        onRequestClose={toggleImageModal}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          // onPressOut={toggleImageModal}
        >
          <Animated.View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0,0,0,0.5)",
              opacity: imageModalValue,
            }}
          >
            {selectedImage && (
              <>
                <View
                  style={{
                    position: "absolute",
                    height: "100%",
                    width: "100%",
                    backgroundColor: "white",
                    zIndex: 9,
                    opacity: 0.7,
                  }}
                />
                <BlurView
                  style={{
                    height: "100%",
                    width: "100%",
                    position: "absolute",
                    zIndex: 10,
                  }}
                  intensity={30}
                  tint="light"
                >
                  <View
                    style={{
                      position: "absolute",
                      height: "100%",
                      width: "100%",
                      backgroundColor: "white",
                      opacity: 0.4,
                    }}
                  />
                  <View
                    style={{
                      height: 60 + insets.top,
                      alignSelf: "center",
                      width: "100%",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: "transparent",
                      zIndex: 2,
                      paddingTop: insets.top - 10,
                      position: "absolute",
                      top: 0,
                    }}
                  >
                    <View
                      style={{
                        alignSelf: "flex-start",
                        flexDirection: "row",
                        marginTop: 19,
                      }}
                    >
                      <Pressable
                        style={{
                          // position: "absolute",
                          // left: 15,
                          // top: insets.top + 5,
                          marginRight: 20,
                          marginLeft: 15,
                          marginTop: 3,
                        }}
                        onPress={() => {
                          // console.log(selectedImage);
                          toggleImageModal();
                        }}
                      >
                        <AntDesign name="close" size={35} color="black" />
                      </Pressable>
                      <FastImage
                        source={{ uri: selectedImage.message.user.avatar }}
                        style={{ height: 40, width: 40, borderRadius: 100 }}
                      ></FastImage>
                      <View
                        style={{
                          // backgroundColor: "green",
                          paddingTop: 2,
                          marginLeft: 3,
                        }}
                      >
                        <Text
                          style={{
                            // fontFamily: "Nunito-Bold",
                            fontSize: 16,
                            color: "rgba(0,0,0,0.87)",
                            paddingLeft: 5,
                            fontWeight: "600",
                            // paddingTop: 3,
                          }}
                        >
                          {selectedImage.message.user.name}
                        </Text>
                        <Text
                          style={{
                            // fontFamily: "Nunito-Bold",
                            fontSize: 13,
                            color: "rgba(0,0,0,0.6)",
                            paddingLeft: 5,
                            fontWeight: "500",
                            // paddingTop: 3,
                          }}
                        >
                          {formatDate(selectedImage.message.createdAt)}
                        </Text>
                      </View>

                      {/* <Ionicons name="share-outline" size={24} color="black" /> */}
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        marginRight: 20,
                        // justifyContent: "center",
                        alignItems: "center",
                        paddingTop: 5,
                      }}
                    >
                      <Pressable
                        style={{
                          // backgroundColor: "red",
                          padding: 4,
                          marginRight: 20,
                        }}
                        onPress={() => {
                          console.log("save image");
                          saveImage(selectedImage.image);
                        }}
                      >
                        <MaterialIcons
                          name="save-alt"
                          size={29}
                          color="black"
                          style={{}}
                        />
                      </Pressable>
                      <Pressable
                        onPress={() => {
                          console.log("share image");
                          setShareLoading(true);
                          shareImage(selectedImage.image, setShareLoading);
                        }}
                      >
                        {shareLoading ? (
                          <ActivityIndicator
                            style={{ marginRight: 17 }}
                          ></ActivityIndicator>
                        ) : (
                          <MaterialIcons
                            name="ios-share"
                            size={29}
                            color="black"
                            style={{
                              // backgroundColor: "red",
                              padding: 4,
                              // marginRight: 30,
                            }}
                          />
                        )}
                      </Pressable>
                    </View>
                  </View>
                  <Pressable
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                      // backgroundColor: "red",
                    }}
                    onPress={() => {
                      console.log("click");
                      // setShowZoomImage(true);
                      toggleImageModal();
                    }}
                  >
                    {selectedImage?.image &&
                      (() => {
                        const { aspectRatio, height, width } =
                          selectedImage.imageSize;

                        const maxWidth = screenWidth * 0.9;
                        const maxHeight = screenHeight * 0.62;

                        let calculatedWidth = maxWidth;
                        let calculatedHeight = maxWidth / aspectRatio;

                        if (calculatedHeight > maxHeight) {
                          calculatedHeight = maxHeight;
                          calculatedWidth = maxHeight * aspectRatio;
                        }

                        return (
                          <Pressable
                            style={{
                              borderWidth: 0.4,
                              borderColor: "rgba(0,0,0,0.35)",
                              borderRadius: 14,
                            }}
                            onPress={() => {
                              setShowZoomImage(true);
                            }}
                          >
                            <FastImage
                              source={{ uri: selectedImage.image }}
                              style={{
                                alignSelf: "center",
                                height: calculatedHeight,
                                width: calculatedWidth,
                                aspectRatio: aspectRatio,
                                resizeMode: "contain",
                                zIndex: 1,
                                borderRadius: 15,
                                // borderWidth: 0.4,
                                // borderColor: "rgba(0,0,0,0.5)",
                              }}
                            />
                          </Pressable>
                        );
                      })()}

                    <TouchableOpacity
                      style={{
                        // backgroundColor: "green",
                        position: "absolute",
                        bottom: insets.bottom + 5,
                        flexDirection: "row",
                        paddingVertical: 10,
                        // paddingHorizontal: 30,
                        width: "50%",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: 10,
                        borderWidth: 1,
                        borderColor: "rgba(0,0,0,0.3)",
                      }}
                      activeOpacity={0.4}
                      onPress={() => {
                        toggleImageModal();
                        setTimeout(() => {
                          setReplyMessage(selectedImage.message);
                        }, 200);
                      }}
                    >
                      <Text
                        style={{
                          fontWeight: "600",
                          // paddingTop: 2,
                          marginRight: 10,
                          fontSize: 15,
                        }}
                      >
                        Reply
                      </Text>
                      {/* <MaterialIcons
                      name="save-alt"
                      size={22}
                      color="black"
                      style={{ position: "absolute", right: 10 }}
                    /> */}

                      <Octicons
                        name="reply"
                        size={22}
                        color="black"
                        style={{ position: "absolute", right: 10 }}
                      />
                    </TouchableOpacity>
                  </Pressable>
                  {showZoomImage && (
                    <>
                      <Pressable
                        style={{
                          position: "absolute",
                          width: "100%",
                          zIndex: 200,
                          top: insets.top + 11,
                          left: 15,
                        }}
                        onPress={() => {
                          setShowZoomImage(false);
                        }}
                      >
                        <AntDesign name="close" size={35} color="white" />
                      </Pressable>

                      <ImageViewer
                        imageUrls={[{ url: selectedImage.image }]}
                        onSwipeDown={() => {
                          setShowZoomImage(false);
                        }}
                        enableSwipeDown={true}
                        style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          // flex: 1,
                          backgroundColor: "",
                          zIndex: 100,
                        }}
                      />
                    </>
                  )}
                </BlurView>
              </>
            )}

            {/* <BlurView
              style={{
                height: "100%",
                width: "100%",
                position: "absolute",
                zIndex: 10,
                // alignItems: "center",
                // justifyContent: "center",
              }}
              intensity={50} // you can adjust intensity
              tint="light" // or 'light' or 'dark'
            >
              <View
                style={{
                  position: "absolute",
                  height: "100%",
                  width: "100%",
                  backgroundColor: "white",
                  opacity: 0.5,
                }}
              ></View>
              <View
                style={{
                  height: 60 + insets.top,
                  alignSelf: "center",
                  width: "100%",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  // borderBottomColor: "rgba(0,0,0,0.3)",
                  // borderBottomWidth: 0.4,
                  backgroundColor: "transparent",
                  zIndex: 2,
                  paddingTop: insets.top - 10,
                  // paddingTop: insets.top,
                  position: "absolute",
                }}
              ></View>
              {selectedImage.imageSize && (
                <FastImage
                  source={{ uri: selectedImage.image }}
                  style={{
                    // width: calculatedWidthRe * 0.8,
                    alignSelf: "center",
                    height: 50,
                    maxHeight: "100%",
                    aspectRatio: selectedImage.imageSize.aspectRatio,
                    resizeMode: "contain",
                    zIndex: 3,
                    // position: "absolute",
                    // left: -100,
                    // opacity: 0.2,
                  }}
                />
              )}
            </BlurView> */}
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </Animated.View>
    // </KeyboardAvoidingView>
  );
}
