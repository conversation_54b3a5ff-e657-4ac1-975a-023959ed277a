import React, { createContext, useState, useMemo } from "react";

export const AuthenticatedUserContext = createContext({});

export const AuthenticatedUserProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [test, setTest] = useState(true);
  const [eventInfo, setEventInfo] = useState(null);
  const [events, setEvents] = useState([]);

  const value = useMemo(() => {
    return {
      test,
      setTest,
      eventInfo,
      setEventInfo,
      events,
      setEvents,
    };
  }, [test, eventInfo, events]);

  return (
    <AuthenticatedUserContext.Provider value={value}>
      {children}
    </AuthenticatedUserContext.Provider>
  );
};
