import React from "react";
import { View, Pressable, StyleSheet } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import TeamItem from "./TeamItem";
import { red } from "./colors";

const TeamInfoComp = ({ selectedMarker, setSelectedMarker }) => {
  if (!selectedMarker) return null;

  return (
    <View style={styles.container}>
      {/* Close Button */}
      <Pressable onPress={() => setSelectedMarker(null)} style={styles.closeButton}>
        <AntDesign name="close" size={24} color="white" />
      </Pressable>

      {/* Team Preview */}
      <TeamItem team={selectedMarker} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "95%",
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 0.6,
    borderColor: "rgba(0,0,0,0.4)",
    position: "absolute",
    bottom: 15,
    alignSelf: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  closeButton: {
    position: "absolute",
    right: 10,
    top: 10,
    zIndex: 10,
    backgroundColor: red,
    borderRadius: 15,
    padding: 5,
  },
});

export default TeamInfoComp;
