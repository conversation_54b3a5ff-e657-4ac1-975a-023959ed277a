import React, {
  useEffect,
  useLayoutEffect,
  useState,
  create<PERSON>ontext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  TouchableWithoutFeedback,
  SafeAreaView,
  Easing,
} from "react-native";

import { auth, db, functions } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "@react-native-firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { httpsCallable } from "@react-native-firebase/functions";

import { Ionicons } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

import { useSelector } from "react-redux";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import Spinner from "react-native-spinkit";
import { BlurView } from "expo-blur";
import { Octicons, Entypo } from "@expo/vector-icons";

import ImageViewer from "react-native-image-zoom-viewer";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;
const imageSize = (screenWidth - 4) / 3;

function formatDate(dateString) {
  const messageDate = new Date(dateString.seconds * 1000);
  const currentDate = new Date();

  // Calculate the difference in days
  const diffTime = Math.abs(currentDate - messageDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Format the time if the message was sent today
  if (
    diffDays === 1 &&
    messageDate.toDateString() === currentDate.toDateString()
  ) {
    return messageDate.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  }

  // If the message was sent within the last 7 days
  if (diffDays < 7) {
    return `${diffDays} days ago`;
  }

  // If the message was sent more than 7 days ago
  return messageDate.toLocaleDateString([], {
    day: "2-digit",
    month: "2-digit",
  });
}

const ChatMedia = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const { images } = route.params;

  const { language } = useSelector((state) => state.language);

  const [selectedImage, setSelectedImage] = useState(null);

  const [imageModalVisible, setImageModal] = useState(false);
  const imageModalValue = useRef(new Animated.Value(0)).current;
  const [showZoomImage, setShowZoomImage] = useState(false);
  const [shareLoading, setShareLoading] = useState(false);

  const toggleImageModal = useCallback(() => {
    if (imageModalVisible) {
      Animated.timing(imageModalValue, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setImageModal(false));
    } else {
      setImageModal(true);
      Animated.timing(imageModalValue, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  }, [imageModalVisible, imageModalValue]);

  const RenderImageItem = ({ item }) => (
    <FastImage
      source={{ uri: item.image }}
      style={styles.image}
      resizeMode={FastImage.resizeMode.cover}
    />
  );

  const renderItem = useCallback(
    ({ item }) => (
      <Pressable
        onPress={() => {
          setSelectedImage(item);
          toggleImageModal();
          console.log("selected image", item);
        }}
      >
        <RenderImageItem item={item} />
      </Pressable>
    ),
    []
  );

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <Pressable
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <MaterialIcons name="arrow-back-ios" size={24} color="black" />
        </Pressable>
        <Text style={styles.headerTitle}>Media</Text>
      </View>

      {images.length === 0 ? (
        <View style={styles.noImagesContainer}>
          <Text style={styles.noImagesText}>No images in this chat yet</Text>
        </View>
      ) : (
        <FlatList
          data={images}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          numColumns={3}
          columnWrapperStyle={styles.columnWrapper}
          contentContainerStyle={styles.listContainer}
        />
      )}

      <Modal
        animationType="none"
        transparent={true}
        visible={imageModalVisible}
        onRequestClose={toggleImageModal}
      >
        <TouchableOpacity style={{ flex: 1 }} activeOpacity={1}>
          <Animated.View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0,0,0,0.5)",
              opacity: imageModalValue,
            }}
          >
            {selectedImage && (
              <>
                <View
                  style={{
                    position: "absolute",
                    height: "100%",
                    width: "100%",
                    backgroundColor: "white",
                    zIndex: 9,
                    opacity: 0.7,
                  }}
                />
                <BlurView
                  style={{
                    height: "100%",
                    width: "100%",
                    position: "absolute",
                    zIndex: 10,
                  }}
                  intensity={30}
                  tint="light"
                >
                  <View
                    style={{
                      position: "absolute",
                      height: "100%",
                      width: "100%",
                      backgroundColor: "white",
                      opacity: 0.4,
                    }}
                  />
                  <View
                    style={{
                      height: 60 + insets.top,
                      alignSelf: "center",
                      width: "100%",
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                      backgroundColor: "transparent",
                      zIndex: 2,
                      paddingTop: insets.top - 10,
                      position: "absolute",
                      top: 0,
                    }}
                  >
                    <View
                      style={{
                        alignSelf: "flex-start",
                        flexDirection: "row",
                        marginTop: 19,
                      }}
                    >
                      <Pressable
                        style={{
                          marginRight: 20,
                          marginLeft: 15,
                          marginTop: 3,
                        }}
                        onPress={toggleImageModal}
                      >
                        <AntDesign name="close" size={35} color="black" />
                      </Pressable>
                      <FastImage
                        source={{ uri: selectedImage.user.avatar }}
                        style={{ height: 40, width: 40, borderRadius: 100 }}
                      />
                      <View
                        style={{
                          paddingTop: 2,
                          marginLeft: 3,
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 16,
                            color: "rgba(0,0,0,0.87)",
                            paddingLeft: 5,
                            fontWeight: "600",
                          }}
                        >
                          {selectedImage.user.name}
                        </Text>
                        <Text
                          style={{
                            fontSize: 13,
                            color: "rgba(0,0,0,0.6)",
                            paddingLeft: 5,
                            fontWeight: "500",
                          }}
                        >
                          {formatDate(selectedImage.createdAt)}
                        </Text>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        marginRight: 20,
                        alignItems: "center",
                        paddingTop: 5,
                      }}
                    >
                      <Pressable
                        style={{
                          padding: 4,
                          marginRight: 20,
                        }}
                        onPress={() => {
                          console.log("save image");
                          saveImage(selectedImage.image);
                        }}
                      >
                        <MaterialIcons
                          name="save-alt"
                          size={29}
                          color="black"
                        />
                      </Pressable>
                      <Pressable
                        onPress={() => {
                          console.log("share image");
                          setShareLoading(true);
                          shareImage(selectedImage.image, setShareLoading);
                        }}
                      >
                        {shareLoading ? (
                          <ActivityIndicator style={{ marginRight: 17 }} />
                        ) : (
                          <MaterialIcons
                            name="ios-share"
                            size={29}
                            color="black"
                            style={{
                              padding: 4,
                            }}
                          />
                        )}
                      </Pressable>
                    </View>
                  </View>
                  <Pressable
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onPress={toggleImageModal}
                  >
                    {selectedImage?.image &&
                      (() => {
                        const { aspectRatio } = selectedImage.imageSize;

                        const maxWidth = screenWidth * 0.9;
                        const maxHeight = screenHeight * 0.62;

                        let calculatedWidth = maxWidth;
                        let calculatedHeight = maxWidth / aspectRatio;

                        if (calculatedHeight > maxHeight) {
                          calculatedHeight = maxHeight;
                          calculatedWidth = maxHeight * aspectRatio;
                        }

                        return (
                          <Pressable
                            style={{
                              borderWidth: 0.4,
                              borderColor: "rgba(0,0,0,0.35)",
                              borderRadius: 14,
                            }}
                            onPress={() => {
                              setShowZoomImage(true);
                            }}
                          >
                            <FastImage
                              source={{ uri: selectedImage.image }}
                              style={{
                                alignSelf: "center",
                                height: calculatedHeight,
                                width: calculatedWidth,
                                aspectRatio: aspectRatio,
                                resizeMode: "contain",
                                zIndex: 1,
                                borderRadius: 15,
                              }}
                            />
                          </Pressable>
                        );
                      })()}

                    <TouchableOpacity
                      style={{
                        position: "absolute",
                        bottom: insets.bottom + 5,
                        flexDirection: "row",
                        paddingVertical: 10,
                        width: "50%",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: 10,
                        borderWidth: 1,
                        borderColor: "rgba(0,0,0,0.3)",
                      }}
                      activeOpacity={0.4}
                      onPress={() => {
                        toggleImageModal();
                        // console.log("reply to image", route.params.chat);
                        navigation.navigate("ChatScreen", {
                          chat: route.params.chat,
                          replyMessage: selectedImage,
                        });
                      }}
                    >
                      <Text
                        style={{
                          fontWeight: "600",
                          marginRight: 10,
                          fontSize: 15,
                        }}
                      >
                        {language.reply}
                      </Text>
                      <Octicons
                        name="reply"
                        size={22}
                        color="black"
                        style={{ position: "absolute", right: 10 }}
                      />
                    </TouchableOpacity>
                  </Pressable>
                  {showZoomImage && (
                    <>
                      <Pressable
                        style={{
                          position: "absolute",
                          width: "100%",
                          zIndex: 200,
                          top: insets.top + 11,
                          left: 15,
                        }}
                        onPress={() => {
                          setShowZoomImage(false);
                        }}
                      >
                        <AntDesign name="close" size={35} color="white" />
                      </Pressable>

                      <ImageViewer
                        imageUrls={[{ url: selectedImage.image }]}
                        onSwipeDown={() => {
                          setShowZoomImage(false);
                        }}
                        enableSwipeDown={true}
                        style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          backgroundColor: "",
                          zIndex: 100,
                        }}
                      />
                    </>
                  )}
                </BlurView>
              </>
            )}
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 15,
    height: 50,
    borderBottomWidth: 0.5,
    borderBottomColor: "rgba(0,0,0,0.1)",
    position: "relative",
  },
  backButton: {
    padding: 5,
    position: "absolute",
    left: 15,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  noImagesContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  noImagesText: {
    fontSize: 16,
    color: "gray",
  },
  listContainer: {
    top: -1,
  },
  columnWrapper: {
    justifyContent: "flex-start",
  },
  image: {
    width: imageSize,
    height: imageSize,
    margin: 0.5,
  },
});

export default ChatMedia;
