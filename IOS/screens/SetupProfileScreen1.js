import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  TouchableOpacity,
  Keyboard,
  Dimensions,
  Animated,
  Platform,
} from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import * as ImagePicker from "expo-image-picker";
import {
  MaterialIcons,
  Entypo,
  FontAwesome5,
  Feather,
} from "@expo/vector-icons";
import { auth, db } from "../config/firebase";
import { red } from "../screens/colors";
import { EN, CZ } from "./../assets/strings";
import * as Haptics from "expo-haptics";
import MonthYearPicker from "react-native-month-year-picker";
import {
  collection,
  collectionGroup,
  query,
  where,
  onSnapshot,
} from "@react-native-firebase/firestore";
// Components
import ProfileAvatar from "../components/ProfileAvatar";
import NavigationButton from "../components/NavigationButton";
import { useSetupProfile } from "./SetupProfileContext";
import { PROFILE_COLORS } from "../constants";
import { useFocusEffect } from "@react-navigation/native";

const { width } = Dimensions.get("window");

// Username validation function
const validateUsername = (username) => {
  const MIN_LENGTH = 3;
  const MAX_LENGTH = 18;

  // Trim whitespace
  const trimmed = username.trim();

  // Check for minimum length
  if (trimmed.length < MIN_LENGTH) {
    return {
      isValid: false,
      errorType: "tooShort",
      sanitized: trimmed,
    };
  }

  // Check for maximum length
  if (trimmed.length > MAX_LENGTH) {
    return {
      isValid: false,
      errorType: "tooLong",
      sanitized: trimmed.substring(0, MAX_LENGTH),
    };
  }

  // Check for valid characters (letters from any language, numbers, and some special chars)
  // Allowed: Letters, numbers, underscore, period, hyphen
  // Not allowed: Spaces and other special characters
  const validPattern = /^[\p{L}\p{N}_.-]+$/u;
  if (!validPattern.test(trimmed)) {
    return {
      isValid: false,
      errorType: "invalidChars",
      sanitized: trimmed,
    };
  }

  return {
    isValid: true,
    errorType: null,
    sanitized: trimmed,
  };
};

const SetupProfileScreen1 = ({ navigation }) => {
  const {
    name,
    setName,
    profileImage,
    setProfileImage,
    gender,
    setGender,
    profileColor,
    setProfileColor,
    nameFree,
    setNameFree,
    date,
    setDate,
    dateChanged,
    setDateChanged,
    languageCode,
    setCurrentStep,
  } = useSetupProfile();

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [isCheckingUsername, setIsCheckingUsername] = useState(false);
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: true,
    errorType: null,
  });
  const [usernameBlurred, setUsernameBlurred] = useState(false);
  const [usernameTouched, setUsernameTouched] = useState(false);

  const languageStrings = languageCode === "CZ" ? CZ : EN;
  const scrollViewRef = useRef(null);
  const debounceTimeout = useRef(null);

  // Set current step when component mounts
  useFocusEffect(() => {
    setCurrentStep(0);
  });

  // Animation for fields
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Start fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  // Handle username changes with validation
  const handleUsernameChange = (text) => {
    // Mark as touched when user starts typing
    if (!usernameTouched && text.length > 0) {
      setUsernameTouched(true);
    }

    // Validate username
    const validation = validateUsername(text);

    // Update validation state
    setUsernameValidation(validation);

    // Update the name with sanitized version
    setName(validation.sanitized);
  };

  const shouldShowUsernameError = () => {
    // Only show validation errors if:
    // 1. The field has been blurred (user clicked away) OR
    // 2. User has typed at least 3 characters
    return (
      (usernameBlurred || (usernameTouched && name.length >= 3)) &&
      (!usernameValidation.isValid || showUsernameError)
    );
  };

  // Simpler username checking with debounce
  useEffect(() => {
    // Don't do anything for empty usernames
    if (!name || name.trim() === "") {
      setNameFree(null);
      return;
    }

    // Don't check availability if the username is invalid
    if (!usernameValidation.isValid) {
      return;
    }

    // Clear any existing timeout
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    // Wait a bit before checking
    debounceTimeout.current = setTimeout(() => {
      // Only check if the username is actually different
      const trimmedName = name.trim();

      const q = query(
        collectionGroup(db, "public"),
        where("name", "==", trimmedName)
      );

      setIsCheckingUsername(true);

      const unsubscribe = onSnapshot(
        q,
        (querySnapshot) => {
          setIsCheckingUsername(false);
          if (
            querySnapshot.empty ||
            querySnapshot?.docs[0]?.data()?.uid === auth.currentUser.uid
          ) {
            setNameFree(true);
          } else {
            setNameFree(false);
          }
        },
        (error) => {
          setIsCheckingUsername(false);
          console.error("Error checking username:", error);
          // Default to allowing the username if there's an error checking
          setNameFree(true);
        }
      );

      return unsubscribe;
    }, 400);

    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }
    };
  }, [name, usernameValidation.isValid]);

  // Calculate age from date
  const calculateAge = (dob) => {
    const diff_ms = Date.now() - dob.getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  };

  // Month-Year picker functions
  const showDatePicker = () => {
    Keyboard.dismiss();
    setDatePickerVisibility(true);
  };
  const hideDatePicker = () => setDatePickerVisibility(false);

  const handleConfirm = (event, selectedDate) => {
    // Only update the date if a valid selection was made
    if (selectedDate) {
      setDate(selectedDate);
      setDateChanged(true);
    }
    hideDatePicker();
  };

  // Image picker functions
  const pickImage = async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          languageStrings.permissionRequired,
          languageStrings.mediaPermissionMessage
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.log("Error picking image:", error);
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          languageStrings.permissionRequired,
          languageStrings.cameraPermissionMessage
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 1,
      });

      if (!result.canceled) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.log("Error taking photo:", error);
    }
  };

  const changeProfileColor = () => {
    const randomIndex = Math.floor(Math.random() * PROFILE_COLORS.length);
    setProfileColor(PROFILE_COLORS[randomIndex]);
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const removeProfileImage = () => {
    setProfileImage(null);
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  // Check for validation errors
  const showUsernameError =
    name && name.trim() !== "" && !isCheckingUsername && nameFree === false;

  // Determine username validation error message
  const getUsernameErrorMessage = () => {
    if (!usernameValidation.isValid) {
      switch (usernameValidation.errorType) {
        case "tooShort":
          return languageStrings.usernameTooShort;
        case "tooLong":
          return languageStrings.usernameTooLong;
        case "invalidChars":
          return languageStrings.usernameInvalidChars;
        default:
          return null;
      }
    }

    if (showUsernameError) {
      return languageStrings.usernameTaken;
    }

    return null;
  };

  const usernameErrorMessage = getUsernameErrorMessage();

  // Format the date string to show month and year only
  const formatMonthYear = (date) => {
    return date.toLocaleDateString(languageStrings.dateLocale, {
      month: "long",
      year: "numeric",
    });
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={Platform.OS === "ios" ? 30 : 80}
        extraHeight={100}
      >
        {/* Profile Avatar Section */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
              ],
            },
          ]}
        >
          <View style={styles.avatarContainer}>
            <ProfileAvatar
              name={name}
              profileImage={profileImage}
              size={width * 0.35}
              profileColor={profileColor}
              onChangeColor={changeProfileColor}
              onAddImage={pickImage}
              onDeleteImage={removeProfileImage}
            />
          </View>

          {/* Photo Options */}
          <View style={styles.photoOptionsContainer}>
            <TouchableOpacity
              style={styles.photoOptionButton}
              onPress={pickImage}
            >
              <MaterialIcons name="photo-library" size={18} color="#666" />
              <Text style={styles.photoOptionText}>
                {languageStrings.chooseImage}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.photoOptionButton}
              onPress={takePhoto}
            >
              <Entypo name="camera" size={18} color="#666" />
              <Text style={styles.photoOptionText}>
                {languageStrings.takeImage}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Profile Information Form */}
        <Animated.View
          style={[
            styles.section,
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: fadeAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [40, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {/* Name Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.username}</Text>
            <TextInput
              style={[
                styles.textInput,
                shouldShowUsernameError() && styles.inputError,
              ]}
              value={name}
              onChangeText={handleUsernameChange}
              onBlur={() => setUsernameBlurred(true)}
              placeholder={languageStrings.enterYourName}
              placeholderTextColor="rgba(0,0,0,0.3)"
              maxLength={18}
            />
            <Text style={styles.usernameHelperText}>
              {languageStrings.usernameHelper}
            </Text>
            {shouldShowUsernameError() && (
              <Text style={styles.errorText}>{getUsernameErrorMessage()}</Text>
            )}
          </View>

          {/* Date of Birth - Using Month Year Picker instead of full date */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.dateOfBirth}</Text>
            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={showDatePicker}
            >
              <Text style={styles.dateText}>{formatMonthYear(date)}</Text>
              <View style={styles.dateButtonContainer}>
                <MaterialIcons name="date-range" size={20} color={red} />
                <Text style={styles.changeDateText}>
                  {languageStrings.change}
                </Text>
              </View>
            </TouchableOpacity>

            {dateChanged && calculateAge(date) < 15 && (
              <Text style={styles.errorText}>
                {languageStrings.ageRestriction}
              </Text>
            )}
          </View>

          {/* Gender Selection */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>{languageStrings.gender}</Text>
            <View style={styles.genderContainer}>
              <TouchableOpacity
                style={[
                  styles.genderButton,
                  gender === "male" && styles.genderButtonActive,
                ]}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setGender("male");
                }}
              >
                <MaterialIcons
                  name="male"
                  size={25}
                  color={gender === "male" ? red : "#666"}
                  style={styles.genderIcon}
                />
                <Text
                  style={[
                    styles.genderText,
                    gender === "male" && styles.genderTextActive,
                  ]}
                >
                  {languageStrings.male}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.genderButton,
                  gender === "female" && styles.genderButtonActive,
                ]}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setGender("female");
                }}
              >
                <MaterialIcons
                  name="female"
                  size={25}
                  color={gender === "female" ? red : "#666"}
                  style={styles.genderIcon}
                />

                <Text
                  style={[
                    styles.genderText,
                    gender === "female" && styles.genderTextActive,
                  ]}
                >
                  {languageStrings.female}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Extra padding at the bottom to ensure everything is visible when keyboard appears */}
          <View style={styles.keyboardPadding} />
        </Animated.View>
      </KeyboardAwareScrollView>
      {isDatePickerVisible && (
        <MonthYearPicker
          onChange={handleConfirm}
          value={date}
          minimumDate={new Date(1900, 0)} // Set minimum date to 1900-01
          maximumDate={new Date()} // Set max date to current date
          locale={languageStrings.dateLocale}
          okButton={languageStrings.ok}
          cancelButton={languageStrings.cancel}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 90,
  },
  section: {
    marginTop: 15,
    marginBottom: 15,
  },
  avatarContainer: {
    alignItems: "center",
    position: "relative",
  },
  colorChangeButton: {
    position: "absolute",
    bottom: 5,
    right: "36%",
    backgroundColor: red,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  removeImageButton: {
    position: "absolute",
    bottom: 5,
    right: "36%",
    backgroundColor: "#e74c3c",
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  photoOptionsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
    marginBottom: 5,
  },
  photoOptionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.02)",
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 6,
    borderWidth: 0.5,
    borderColor: "rgba(0,0,0,0.06)",
  },
  photoOptionText: {
    color: "#666",
    fontWeight: "600",
    marginLeft: 6,
    fontSize: 13,
  },
  inputContainer: {
    marginBottom: 15,
    paddingHorizontal: 17,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: "600",
    color: "#333",
    marginBottom: 6,
    paddingLeft: 2,
  },
  textInput: {
    height: 45,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    fontSize: 15,
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  inputError: {
    borderColor: "#e74c3c",
    backgroundColor: "rgba(231, 76, 60, 0.03)",
  },
  errorText: {
    color: "#e74c3c",
    fontSize: 13,
    marginTop: 4,
  },
  usernameHelperText: {
    fontSize: 12,
    color: "#888",
    marginTop: 4,
    paddingLeft: 3,
  },
  // Date picker styles
  datePickerButton: {
    height: 45,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.06)",
    borderRadius: 12,
    paddingHorizontal: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.01)",
  },
  dateText: {
    fontSize: 15,
    color: "#333",
  },
  dateButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  changeDateText: {
    color: red,
    marginLeft: 5,
    fontSize: 13,
    fontWeight: "500",
  },
  ageIndicator: {
    fontSize: 13,
    color: "#666",
    marginTop: 5,
  },
  ageNumber: {
    fontWeight: "bold",
    color: "#333",
  },
  // Gender styles
  genderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 6,
  },
  genderButton: {
    flex: 0.48,
    height: 45,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.02)",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.08)",
  },
  genderButtonActive: {
    backgroundColor: "rgba(197, 42, 71, 0.06)",
    borderColor: "rgba(197, 42, 71, 0.3)",
  },
  genderIcon: {
    marginRight: 8,
  },
  genderText: {
    fontSize: 15,
    fontWeight: "500",
    color: "#666",
  },
  genderTextActive: {
    color: red,
    fontWeight: "600",
  },
  keyboardPadding: {
    height: 100, // Extra padding at the bottom
  },
});

export default SetupProfileScreen1;
