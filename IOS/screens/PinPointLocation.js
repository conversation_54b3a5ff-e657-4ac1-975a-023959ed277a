import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Platform,
  ActivityIndicator,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { useSelector } from "react-redux";
import { Feather, MaterialCommunityIcons } from "@expo/vector-icons";
import { red } from "./colors";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";
import { LinearGradient } from "expo-linear-gradient";
import { original } from "@reduxjs/toolkit";

const { width } = Dimensions.get("window");

const PinpointLocation = ({ navigation, route }) => {
  const { selectedPlace } = route.params;
  const [pinpointedLocation, setPinpointedLocation] = useState(selectedPlace);
  const { language } = useSelector((state) => state.language);
  const mapRef = useRef(null);
  const insets = useSafeAreaInsets();
  const [mapType, setMapType] = useState("standard");

  const [isAdding, setIsAdding] = useState(false);

  const toggleMapType = useCallback(() => {
    setMapType((prevType) =>
      prevType === "standard" ? "satellite" : "standard"
    );
  }, []);

  const mapTypeImages = useMemo(
    () => ({
      standard: require("./../images/standard.jpg"),
      satellite: require("./../images/satellite.jpg"),
    }),
    []
  );

  useEffect(() => {
    mapRef.current?.animateToRegion({
      latitude: selectedPlace.latitude,
      longitude: selectedPlace.longitude,
      latitudeDelta: 0.005,
      longitudeDelta: 0.005,
    });
  }, [selectedPlace]);

  const handleLocationSelect = useCallback(
    (event) => {
      const { latitude, longitude } = event.nativeEvent.coordinate;
      // try {
      setPinpointedLocation({
        ...selectedPlace,
        latitude,
        longitude,
      });
      // } catch (error) {
      //   console.log("error", error);
      // }
    },
    [selectedPlace]
  );

  const confirmLocation = async () => {
    if (route.params.isFromLocationList) {
      setIsAdding(true);
      try {
        await route.params.addLocation(pinpointedLocation, selectedPlace);
        navigation.navigate("LocationsList", { teamId: route.params.teamId });
      } catch (error) {
        console.error("Error saving location:", error);
        setIsAdding(false);
      }
    } else {
      route.params.onLocationSelected(pinpointedLocation, {
        longitude: selectedPlace.longitude,
        latitude: selectedPlace.latitude,
      });
      navigation.navigate("CreateTeam");
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          padding: 20,
          marginTop: insets.top,
        }}
      >
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Feather name="arrow-left" size={24} color={red} />
        </TouchableOpacity>
        <Text style={{ fontSize: 20, fontWeight: "bold", marginLeft: 20 }}>
          {language.pinpointLocation}
        </Text>
      </View>

      <View
        style={{
          paddingHorizontal: 20,
          paddingBottom: 15,
          borderBottomWidth: 1,
          borderBottomColor: "rgba(0,0,0,0.1)",
        }}
      >
        <Text style={{ fontSize: 16, color: "#333", marginBottom: 5 }}>
          {selectedPlace.name}
        </Text>
        <Text
          style={{
            fontSize: 14,
            color: "#666",
            fontStyle: "italic",
            marginTop: 7,
          }}
        >
          {language.tapMapToPinpoint}
        </Text>
      </View>

      <View style={{ flex: 1, position: "relative" }}>
        <MapView
          ref={mapRef}
          style={{ flex: 1 }}
          onPress={handleLocationSelect}
          mapType={mapType}
        >
          {pinpointedLocation && (
            <Marker
              coordinate={{
                latitude: pinpointedLocation.latitude,
                longitude: pinpointedLocation.longitude,
              }}
            >
              <FastImage
                source={require("../images/qq-empty.png")}
                style={{ width: 55, height: 55, bottom: 22.5 }}
                resizeMode={FastImage.resizeMode.contain}
              />
            </Marker>
          )}
        </MapView>

        <TouchableOpacity
          style={{
            position: "absolute",
            bottom: insets.bottom + 80,
            right: 10,
            backgroundColor: "white",
            borderRadius: 10,
            width: 65,
            height: 65,
            alignItems: "center",
            justifyContent: "center",
            ...Platform.select({
              ios: {
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
              },
              android: {
                elevation: 5,
              },
            }),
          }}
          onPress={toggleMapType}
        >
          <FastImage
            source={
              mapTypeImages[mapType === "standard" ? "satellite" : "standard"]
            }
            style={{ height: 60, width: 60, borderRadius: 8 }}
          />
          <MaterialCommunityIcons
            name="layers-outline"
            size={19}
            color={mapType !== "standard" ? "rgba(0,0,0,0.6)" : "white"}
            style={{ position: "absolute", bottom: 5, left: 5 }}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={{
            position: "absolute",
            bottom: insets.bottom + 5,
            left: 20,
            right: 20,
            backgroundColor: red,
            borderRadius: 30,
            overflow: "hidden",
            opacity: isAdding ? 0.8 : 1,
            ...Platform.select({
              ios: {
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 3.84,
              },
              android: {
                elevation: 5,
              },
            }),
            height: 50,
            justifyContent: "center",
          }}
          onPress={confirmLocation}
          disabled={isAdding}
        >
          <LinearGradient
            colors={[red, "#fa5757"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={{
              // paddingVertical: 15,
              alignItems: "center",
              flex: 1,
              justifyContent: "center",
            }}
          >
            {route.params.isFromLocationList && isAdding ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text
                style={{ color: "white", fontSize: 18, fontWeight: "bold" }}
              >
                {language.confirm}
              </Text>
            )}
          </LinearGradient>
        </TouchableOpacity>

        {/* <TouchableOpacity
          style={{
            opacity: selectedPlace ? 1 : 0.5,
            overflow: "hidden",
            borderRadius: 25,
          }}
          disabled={!selectedPlace}
          onPress={confirmLocation}
        ></TouchableOpacity> */}
      </View>
    </View>
  );
};

export default PinpointLocation;
