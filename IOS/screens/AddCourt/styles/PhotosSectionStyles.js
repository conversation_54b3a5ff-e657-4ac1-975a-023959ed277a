// File: screens/AddCourt/styles/PhotosSectionStyles.js
import { StyleSheet, Dimensions } from 'react-native';
import { red } from '../../../screens/colors';

const screenWidth = Dimensions.get('window').width;

const PhotosSectionStyles = StyleSheet.create({
  // Photos section styles
  photoSectionTitle: {
    fontSize: 17,
    fontWeight: "700",
    color: "#333",
  },
  photoSectionSubtitle: {
    fontSize: 14,
    color: "rgba(0,0,0,0.6)",
    marginTop: 5,
  },
  photoButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginVertical: 16,
  },
  photoButton: {
    backgroundColor: "rgba(255,255,255,0.9)",
    borderWidth: 1,
    borderColor: red,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 25,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  photoButtonText: {
    color: red,
    fontWeight: "600",
    marginLeft: 8,
    fontSize: 14,
  },
  imageScrollView: { 
    marginTop: 16 
  },
  imageScrollViewContent: { 
    paddingHorizontal: 8 
  },
  imageContainer: {
    marginHorizontal: 8,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#fff",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    position: "relative",
  },
  courtImage: {
    width: screenWidth * 0.65,
    aspectRatio: 1.33,
    borderRadius: 12,
  },
  selectedImageOverlay: {
    position: "absolute",
    top: 0, left: 0, right: 0, bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
  },
  deleteImageButton: {
    backgroundColor: "rgba(200,0,0,0.6)",
    padding: 12,
    borderRadius: 30,
  },
  imageNumberBadge: {
    position: "absolute",
    top: 10, right: 10,
    backgroundColor: "rgba(0,0,0,0.7)",
    width: 24, height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  imageNumberText: {
    color: "white",
    fontSize: 12,
    fontWeight: "bold",
  },
  emptyImagesContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "rgba(0,0,0,0.03)",
    borderRadius: 12,
    marginTop: 16,
  },
  emptyImagesText: {
    marginTop: 10,
    color: "rgba(0,0,0,0.5)",
    fontSize: 15,
  },
  photosHelp: {
    marginTop: 16,
    padding: 10,
    backgroundColor: "rgba(255, 247, 230, 0.5)",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "rgba(255, 165, 0, 0.7)",
  },
  photosHelpText: {
    fontSize: 13,
    color: "rgba(0,0,0,0.7)",
  },
});

export default PhotosSectionStyles;