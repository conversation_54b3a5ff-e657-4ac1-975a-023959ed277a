// File: screens/AddCourt/styles/FeaturesSectionStyles.js
import { StyleSheet } from 'react-native';
import { red } from '../../../screens/colors';

const FeaturesSectionStyles = StyleSheet.create({
  sectionContent: { 
    padding: 16 
  },
  sectionDescription: {
    backgroundColor: "rgba(255, 247, 230, 0.5)",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "rgba(255, 0, 0, 0.5)",
    padding: 12,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: "rgba(0,0,0,0.6)",
  },
  
  // Feature group styles
  featureGroup: {
    marginBottom: 20,
    backgroundColor: "rgba(255,255,255,0.9)",
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  featureGroupTitle: {
    fontSize: 17,
    fontWeight: "700",
    marginBottom: 15,
    color: "#333",
  },
  
  // Feature item styles
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
  },
  featureIconContainer: {
    width: 30,
    alignItems: "center",
    marginRight: 10,
  },
  featureLabel: {
    flex: 1,
    fontSize: 15,
    color: "#333",
  },
  featureControl: {
    width: "40%",
  },
  
  // Required indicator
  requiredIndicator: {
    color: red,
    marginLeft: 2,
  }
});

export default FeaturesSectionStyles;