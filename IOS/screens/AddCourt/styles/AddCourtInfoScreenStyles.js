// File: screens/AddCourt/styles/AddCourtInfoScreenStyles.js
import { StyleSheet, Dimensions } from 'react-native';
import { red } from '../../../screens/colors';

const screenWidth = Dimensions.get('window').width;

const AddCourtInfoScreenStyles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: "white" 
  },
  scrollView: { 
    flex: 1, 
    backgroundColor: "white" 
  },
  scrollViewContent: { 
    flexGrow: 1, 
    paddingBottom: 30 
  },
  sectionContent: { 
    padding: 16 
  },
  
  // Input styles
  inputGroup: { 
    marginBottom: 16 
  },
  rowInputGroup: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  inputGroupHalf: { 
    width: "48%" 
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 8,
    color: "#333",
  },
  textInput: {
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.2)",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 15,
    backgroundColor: "white",
  },
  textAreaInput: { 
    height: 100, 
    paddingTop: 12 
  },
  numericInput: {
    textAlign: "center",
    fontSize: 16,
    fontWeight: "600",
  }
});

export default AddCourtInfoScreenStyles;