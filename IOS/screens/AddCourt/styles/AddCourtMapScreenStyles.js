// File: screens/AddCourt/styles/AddCourtMapScreenStyles.js
import { StyleSheet } from 'react-native';
import { red } from '../../../screens/colors';

const AddCourtMapScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  instructionsContainer: {
    position: "absolute",
    alignSelf: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  instructionsText: {
    color: "white",
    fontWeight: "500",
    fontSize: 14,
  },
  mapTypeButton: {
    position: "absolute",
    bottom: 30,
    right: 16,
    backgroundColor: red,
    borderRadius: 8,
    padding: 10,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  mapTypeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  mapTypeText: {
    color: "white",
    fontWeight: "600",
    marginLeft: 5,
  },
  markerContainer: {
    backgroundColor: red,
    borderWidth: 3,
    borderColor: "white",
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  markerIcon: {
    marginLeft: 1,
  },
});

export default AddCourtMapScreenStyles;