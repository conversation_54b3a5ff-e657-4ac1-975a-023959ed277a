// File: screens/AddCourt/styles/ModalStyles.js
import { StyleSheet } from 'react-native';
import { red } from '../../../screens/colors';

const ModalStyles = StyleSheet.create({
  // Modal styles
  modalContainer: {
    flex: 1,
    justifyContent: "center", 
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  modalAnimatedContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  modalContent: {
    width: "90%",
    maxHeight: "80%",
    backgroundColor: "white",
    borderRadius: 16,
    padding: 20,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: 16,
    color: red,
  },
  modalScrollView: { 
    maxHeight: "70%" 
  },
  modalText: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 20,
  },
  modalSubtitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 10,
  },
  exampleImage: {
    width: "100%",
    aspectRatio: 1.33,
    borderRadius: 8,
    marginBottom: 20,
  },
  modalButton: {
    backgroundColor: red,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignSelf: "center",
    marginTop: 10,
  },
  modalButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});

export default ModalStyles;