// File: screens/AddCourt/AddCourtInfoScreen.js
import React, { useState, useCallback } from "react";
import { View, Alert, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useNavigation, useRoute } from "@react-navigation/native";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";

import { useCourtData } from "../../contexts/CourtDataContext";
import { red } from "../../screens/colors";
import { ScreenHeader } from "../../components/CustomComponents";

// Import component sections
import BasicInfoSection from "./components/BasicInfoSection";
import FeaturesSection from "./components/FeaturesSection";
import PhotosSection from "./components/PhotosSection";

// Import wizard components
import SwipeableContainer from "./components/SwipeableContainer";
import StepIndicator from "./components/StepIndicator";
import WizardFooter from "./components/WizardFooter";

/**
 * Court information input screen with wizard flow
 */
const AddCourtInfoScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const language = route.params || {};
  const courtData = useCourtData();
  
  // Wizard step state
  const [currentStep, setCurrentStep] = useState(0);
  
  // Step definitions
  const steps = [
    {
      id: 'basic',
      label: language.basicInfo || "Basic Info",
      icon: <MaterialCommunityIcons 
              name="basketball" 
              size={18} 
              color={currentStep === 0 ? "white" : red} 
            />,
      validate: () => {
        // Validate basic info fields
        if (!courtData.courtName.trim()) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.enterCourtName || "Please enter a court name."
          );
          return false;
        }
        if (!courtData.courtDescription.trim()) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.enterDescription || "Please enter a court description."
          );
          return false;
        }
        if (!courtData.basketsCount) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.enterBasketsCount || "Please enter the number of baskets."
          );
          return false;
        }
        if (courtData.surface === null) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.selectSurface || "Please select a surface type."
          );
          return false;
        }
        if (courtData.surface === 3 && !courtData.differentSurface.trim()) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.specifyOtherSurface || "Please specify the other surface type."
          );
          return false;
        }
        if (courtData.indoorOutdoor === null) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.selectType || "Please select indoor or outdoor."
          );
          return false;
        }
        if (courtData.publicPrivate === null) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.selectAccess || "Please select public or private."
          );
          return false;
        }
        return true;
      }
    },
    {
      id: 'features',
      label: language.features || "Features",
      icon: <MaterialIcons 
              name="featured-play-list" 
              size={18} 
              color={currentStep === 1 ? "white" : red} 
            />,
      validate: () => {
        // Validate all feature selections are made
        if (!courtData.validateFeatures()) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.selectAllFeatures || "Please select an option for each feature."
          );
          return false;
        }
        return true;
      }
    },
    {
      id: 'photos',
      label: language.photos || "Photos",
      icon: <MaterialIcons 
              name="photo-library" 
              size={18} 
              color={currentStep === 2 ? "white" : red} 
            />,
      validate: () => {
        // Validate photos
        if (courtData.images.length === 0) {
          Alert.alert(
            language.missingInformation || "Missing Information",
            language.addAtLeastOnePhoto || "Please add at least one photo of the court."
          );
          return false;
        }
        return true;
      }
    }
  ];
  
  // Check if the current step is valid to enable/disable the next button
  const isCurrentStepValid = useCallback(() => {
    switch (currentStep) {
      case 0: // Basic Info
        return courtData.courtName.trim() !== "" && 
               courtData.courtDescription.trim() !== "" && 
               courtData.basketsCount !== "" &&
               courtData.surface !== null &&
               (courtData.surface !== 3 || courtData.differentSurface.trim() !== "") &&
               courtData.indoorOutdoor !== null &&
               courtData.publicPrivate !== null;
      case 1: // Features
        return courtData.validateFeatures();
      case 2: // Photos
        return courtData.images.length > 0;
      default:
        return false;
    }
  }, [currentStep, courtData]);
  
  // Handle next button press
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      // Validate current step
      if (steps[currentStep].validate()) {
        // Move to next step
        setCurrentStep(currentStep + 1);
      }
    } else {
      // Last step, proceed to map screen
      navigation.navigate("AddCourtMap");
    }
  };
  
  // Handle back button press
  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  // Handle step indicator press
  const handleStepPress = (stepIndex) => {
    // Only allow going to previous steps or current step
    if (stepIndex <= currentStep) {
      setCurrentStep(stepIndex);
    }
  };

  const canSwipeToNextStep = useCallback(() => {
    return isCurrentStepValid();
  }, [currentStep, courtData]);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <ScreenHeader
        title={language.addCourt || "Add Court"}
        onBack={() => navigation.goBack()}
      />
      
      {/* Step Indicator */}
      <StepIndicator 
        currentStep={currentStep}
        steps={steps}
        onStepPress={handleStepPress}
        allMandatory={true}
      />
      
      {/* Swipeable Content */}
      <SwipeableContainer
      currentStep={currentStep}
      onChangeStep={setCurrentStep}
      canSwipeNext={true} // canSwipeToNextStep()
    >
      <BasicInfoSection language={language} />
      <FeaturesSection language={language} />
      <PhotosSection language={language} />
    </SwipeableContainer>
      
      {/* Footer Navigation */}
      <WizardFooter
        currentStep={currentStep}
        totalSteps={steps.length}
        onBack={handleBack}
        onNext={handleNext}
        isNextDisabled={!isCurrentStepValid()}
        language={language}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "white",
  }
});

export default AddCourtInfoScreen;