// File: screens/AddCourt/AddCourtMapScreen.js
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  Alert,
  ActivityIndicator,
  Animated,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useNavigation, useRoute, CommonActions } from "@react-navigation/native";
import MapView from "react-native-maps";

import { useCourtData } from "../../contexts/CourtDataContext";
import { UserLocationService, CourtService } from "../../services/courtServices";
import { MapHeader } from "../../components/CustomComponents";
import { red } from "../../screens/colors";

// Import components
import MapTypeButton from "./components/MapTypeButton";
import CourtMarker from "./components/CourtMarker";

// Import styles
import styles from "./styles/AddCourtMapScreenStyles";

/**
 * Court location selection screen component with improved flow
 */
const AddCourtMapScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const insets = useSafeAreaInsets();
  const language = route.params || {};
  const courtData = useCourtData();
  
  // Map refs and state
  const mapRef = useRef(null);
  const [mapType, setMapType] = useState("standard");
  const [uploading, setUploading] = useState(false);
  const [mapIsLoading, setMapIsLoading] = useState(true);
  
  // Animation values
  const opacity = useRef(new Animated.Value(0)).current;

  // Fetch user's location on component mount
  useEffect(() => {
    const fetchUserLocation = async () => {
      try {
        const { centerCoordinate } = await UserLocationService.getUserLocationPreferences();
        
        if (centerCoordinate) {
          courtData.setCoordinates(centerCoordinate);
        } else {
          // Default coordinates if not available
          courtData.setCoordinates({
            latitude: 37.7749,
            longitude: -122.4194
          });
        }
      } catch (error) {
        console.error("Error fetching user location:", error);
        // Set default coordinates on error
        courtData.setCoordinates({
          latitude: 37.7749,
          longitude: -122.4194
        });
      }
    };
    
    fetchUserLocation();
  }, []);

  // Fade in animation when map is ready
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setMapIsLoading(false);
      Animated.timing(opacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }, 700);

    return () => clearTimeout(timeoutId);
  }, [opacity]);

  // Toggle map type between standard and satellite
  const toggleMapType = () => {
    setMapType(mapType === "standard" ? "satellite" : "standard");
  };

  // Upload court data to Firebase and navigate back to main map
  const uploadCourt = async () => {
    if (!courtData.validateLocation()) {
      Alert.alert(
        language.error || "Error",
        language.noLocation || "Please select a location for the court"
      );
      return;
    }

    setUploading(true);

    try {
      // Get complete court data object
      const courtDataToUpload = courtData.getCourtData();
      
      // Upload court through service
      const courtId = await CourtService.uploadCourt(courtDataToUpload);
      
      // Store coordinates for use in navigation
      const addedCourtCoordinates = {
        latitude: courtData.coordinates.latitude,
        longitude: courtData.coordinates.longitude,
      };
      
      Alert.alert(
        language.success || "Success!",
        language.addedCourt || "Your court has been submitted for review.",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate back to Map screen and pass the coordinates
              // to zoom into the newly added court
              navigation.dispatch(
                CommonActions.navigate({
                  name: "Map",
                  params: {
                    zoomToCoordinates: addedCourtCoordinates,
                    newCourtId: courtId,
                    showSuccessMessage: true,
                  },
                })
              );
            },
          },
        ],
        { cancelable: false }
      );
    } catch (error) {
      console.error("Error adding court:", error);
      Alert.alert(
        language.error || "Error",
        language.uploadError || "There was an error submitting your court. Please try again."
      );
    } finally {
      setUploading(false);
    }
  };

  if (!courtData.coordinates) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={red} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {mapIsLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={red} />
        </View>
      )}
      
      <Animated.View style={[styles.mapContainer, { opacity }]}>
        <MapView
          ref={mapRef}
          style={styles.map}
          mapType={mapType}
          onRegionChange={(region) => {
            courtData.setCoordinates({
              latitude: region.latitude,
              longitude: region.longitude,
            });
          }}
          initialRegion={{
            latitude: courtData.coordinates.latitude,
            longitude: courtData.coordinates.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}
        >
          <CourtMarker coordinate={courtData.coordinates} />
        </MapView>

        {/* Map type toggle button */}
        <MapTypeButton 
          mapType={mapType} 
          onToggle={toggleMapType} 
          language={language}
        />

        {/* Header */}
        <MapHeader
          title={language.chooseLocation || "Set Location"}
          onBack={() => {
            if (!uploading) {
              navigation.goBack();
            }
          }}
          onSubmit={uploadCourt}
          uploading={uploading}
          submitText={language.submit || "Submit"}
        />

        {/* Instructions */}
        <View style={[styles.instructionsContainer, { top: 90 + insets.top }]}>
          <Text style={styles.instructionsText}>
            {language.dragToAdjust || "Drag the map to set court location"}
          </Text>
        </View>
      </Animated.View>
    </View>
  );
};

export default AddCourtMapScreen;