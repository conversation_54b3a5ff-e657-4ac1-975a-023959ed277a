// File: screens/AddCourt/components/MapTypeButton.js
import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import styles from '../styles/AddCourtMapScreenStyles';

/**
 * Button to toggle between standard and satellite map views
 */
const MapTypeButton = ({ mapType, onToggle, language }) => {
  return (
    <TouchableOpacity
      style={styles.mapTypeButton}
      onPress={onToggle}
    >
      <View style={styles.mapTypeContainer}>
        <MaterialCommunityIcons
          name={mapType === "standard" ? "satellite-variant" : "map"}
          size={20}
          color="white"
        />
        <Text style={styles.mapTypeText}>
          {mapType === "standard" ? 
            (language.satellite || "Satellite") : 
            (language.standard || "Standard")}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export default MapTypeButton;