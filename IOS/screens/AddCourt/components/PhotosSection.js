// File: screens/AddCourt/components/PhotosSection.js
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  Pressable, 
  ScrollView, 
  Alert, 
  ActivityIndicator 
} from 'react-native';
import { MaterialIcons, Entypo } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import { useCourtData } from '../../../contexts/CourtDataContext';
import ImagePreview from './ImagePreview';
import PhotoGuidelines from './PhotoGuidelines';
import { red } from '../../../screens/colors';

/**
 * Court photos input section with improved header styling and loading indicator
 */
const PhotosSection = ({ language }) => {
  const courtData = useCourtData();
  
  // UI state
  const [isModalVisible, setModalVisible] = useState(false);
  const [infoShown, setInfoShown] = useState(false);
  const [chosenAction, setChosenAction] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Image action handlers
  const handleImageAction = (action) => {
    setChosenAction(action);
    if (!infoShown) {
      setModalVisible(true);
    } else {
      action === 1 ? pickImage() : takePhoto();
    }
  };

  // Handle image picking from gallery
  const pickImage = async () => {
    try {
      setIsLoading(true);
      
      const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        const response = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (response.status !== "granted") {
          Alert.alert(
            language.permissionRequired || "Permission Required",
            language.mediaLibraryPermission || "Please enable media library permissions in your settings."
          );
          setIsLoading(false);
          return;
        }
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
        allowsMultipleSelection: true,
        selectionLimit: 5,
      });

      if (!result.canceled) {
        const newImages = [...courtData.images, ...result.assets.map(item => item.uri)];
        courtData.setImages(newImages.slice(0, 10));
        
        if (newImages.length > 10) {
          Alert.alert(
            language.tooManyImages || "Too Many Images",
            language.maxTenImages || "Maximum 10 images allowed."
          );
        }
      }
    } catch (error) {
      console.error("Error picking images:", error);
      Alert.alert(language.error || "Error", language.errorPickingImages || "Error selecting images.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle taking photos with camera
  const takePhoto = async () => {
    try {
      setIsLoading(true);
      
      const { status } = await ImagePicker.getCameraPermissionsAsync();
      if (status !== "granted") {
        const response = await ImagePicker.requestCameraPermissionsAsync();
        if (response.status !== "granted") {
          Alert.alert(
            language.permissionRequired || "Permission Required",
            language.cameraPermission || "Please enable camera permissions."
          );
          setIsLoading(false);
          return;
        }
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
      });

      if (!result.canceled) {
        courtData.setImages([...courtData.images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert(language.error || "Error", language.errorTakingPhoto || "Error taking photo.");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle modal confirmation
  const handleModalConfirm = () => {
    setModalVisible(false);
    setInfoShown(true);
    
    // Add a slight delay to improve UX
    setTimeout(() => {
      if (chosenAction === 1) {
        pickImage();
      } else if (chosenAction === 2) {
        takePhoto();
      }
    }, 300);
  };

  return (
    <View style={styles.sectionContent}>
      {/* Improved header section */}
      <View style={styles.headerContainer}>
        <View style={styles.titleContainer}>
          <MaterialIcons name="photo-library" size={24} color={red} style={styles.headerIcon} />
          <View>
            <Text style={styles.photoSectionTitle}>
              {language.courtPhotos || "Court Photos"}
              <Text style={styles.requiredStar}>*</Text>
            </Text>
            <Text style={styles.photoSectionSubtitle}>
              {language.addAtLeastOne || "Add at least one photo of the court"}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.photoButtonsContainer}>
        <Pressable
          onPress={() => !isLoading && handleImageAction(1)}
          style={[styles.photoButton, isLoading && styles.disabledButton]}
          disabled={isLoading}
        >
          <MaterialIcons name="photo-library" size={22} color={red} />
          <Text style={styles.photoButtonText}>{language.gallery || "Gallery"}</Text>
        </Pressable>
        
        <Pressable
          onPress={() => !isLoading && handleImageAction(2)}
          style={[styles.photoButton, isLoading && styles.disabledButton]}
          disabled={isLoading}
        >
          <Entypo name="camera" size={22} color={red} />
          <Text style={styles.photoButtonText}>{language.camera || "Camera"}</Text>
        </Pressable>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={red} />
          <Text style={styles.loadingText}>
            {language.processingImages || "Processing images..."}
          </Text>
        </View>
      ) : courtData.images.length > 0 ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.imageScrollView}
          contentContainerStyle={styles.imageScrollViewContent}
        >
          {courtData.images.map((item, index) => (
            <ImagePreview 
              key={index} 
              uri={item} 
              index={index}
              onDelete={(idx) => {
                const newImages = [...courtData.images];
                newImages.splice(idx, 1);
                courtData.setImages(newImages);
              }}
            />
          ))}
        </ScrollView>
      ) : (
        <View style={styles.emptyImagesContainer}>
          <MaterialIcons name="photo-camera" size={60} color="rgba(0,0,0,0.2)" />
          <Text style={styles.emptyImagesText}>{language.noImages || "No images added yet"}</Text>
        </View>
      )}

      <View style={styles.photosHelp}>
        <MaterialIcons name="lightbulb" size={20} color="rgba(255, 165, 0, 0.8)" style={styles.tipIcon} />
        <Text style={styles.photosHelpText}>
          {language.photosTip || "Take clear photos of the court, hoops, and surrounding area"}
        </Text>
      </View>

      {/* Photo Guidelines Modal */}
      <PhotoGuidelines
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={handleModalConfirm}
        language={language}
      />
    </View>
  );
};

// Improved styles with better visual hierarchy, spacing, and loading states
const styles = {
  sectionContent: {
    padding: 16,
  },
  headerContainer: {
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerIcon: {
    marginRight: 12,
  },
  photoSectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#333",
    marginBottom: 6,
  },
  requiredStar: {
    color: red,
    fontSize: 18,
    fontWeight: "700",
    marginLeft: 4,
  },
  photoSectionSubtitle: {
    fontSize: 14,
    color: "rgba(0,0,0,0.6)",
    lineHeight: 18,
  },
  photoButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginVertical: 20,
  },
  photoButton: {
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: red,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 30,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  disabledButton: {
    opacity: 0.6,
    borderColor: 'rgba(255, 0, 0, 0.4)',
  },
  photoButtonText: {
    color: red,
    fontWeight: "600",
    marginLeft: 10,
    fontSize: 15,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0,0,0,0.03)",
    borderRadius: 12,
    padding: 30,
    marginVertical: 16,
  },
  loadingText: {
    marginTop: 12,
    color: "#555",
    fontSize: 14,
    fontWeight: "500",
  },
  imageScrollView: { 
    marginTop: 16,
    marginBottom: 16,
  },
  imageScrollViewContent: { 
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  emptyImagesContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
    backgroundColor: "rgba(0,0,0,0.03)",
    borderRadius: 12,
    marginVertical: 16,
  },
  emptyImagesText: {
    marginTop: 12,
    color: "rgba(0,0,0,0.5)",
    fontSize: 15,
  },
  photosHelp: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    padding: 12,
    backgroundColor: "rgba(255, 247, 230, 0.7)",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "rgba(255, 165, 0, 0.7)",
  },
  tipIcon: {
    marginRight: 10,
  },
  photosHelpText: {
    flex: 1,
    fontSize: 14,
    color: "rgba(0,0,0,0.7)",
    lineHeight: 18,
  }
};

export default PhotosSection;