// File: screens/AddCourt/components/FeaturesSection.js
import React from 'react';
import { ScrollView, Text, View } from 'react-native';
import { 
  MaterialCommunityIcons,
  FontAwesome5,
  Ionicons
} from "@expo/vector-icons";
import { FeatureItem, FeatureGroup } from '../../../components/CustomComponents';
import { useCourtData } from '../../../contexts/CourtDataContext';
import styles from '../styles/FeaturesSectionStyles';
import { red } from '../../../screens/colors';
import CustomSwitch from '../../../components/CustomSwitch';

/**
 * Court features input section
 */
const FeaturesSection = ({ language }) => {
  const courtData = useCourtData();

  // Yes/No options for binary choices
  const yesNoOptions = [
    { label: language.yes || "Yes", value: 1 },
    { label: language.no || "No", value: 2 }
  ];

  // Board size options
  const boardSizeOptions = [
    { label: language.regular || "Regular", value: 1 },
    { label: language.small || "Small", value: 3 }
  ];

  return (
    <ScrollView style={styles.sectionContent}>
      <View style={styles.sectionDescription}>
        <Text style={styles.sectionTitle}>{language.mandatoryFeatures || "All features are mandatory"}</Text>
        <Text style={styles.sectionSubtitle}>{language.selectAllFeatures || "Please select an option for each feature below"}</Text>
      </View>
      
      <FeatureGroup title={language.courtFeatures || "Court Features"}>
        <FeatureItem
          icon={<FontAwesome5 name="ruler" size={16} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.fullCourt || "Full Court"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.fullCourt}
              onValueChange={courtData.setFullCourt}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<MaterialCommunityIcons name="basketball-hoop" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.boardSize || "Board Size"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={boardSizeOptions}
              selectedValue={courtData.boardSize}
              onValueChange={courtData.setBoardSize}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<MaterialCommunityIcons name="border-all" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.lines || "Court Lines"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.lines}
              onValueChange={courtData.setLines}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<Ionicons name="flashlight" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.lighting || "Lighting"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.lighting}
              onValueChange={courtData.setLighting}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />
      </FeatureGroup>

      <FeatureGroup title={language.amenities || "Amenities"}>
        <FeatureItem
          icon={<MaterialCommunityIcons name="water" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.waterFountain || "Water Fountain"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.waterFountain}
              onValueChange={courtData.setWaterFountain}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<MaterialCommunityIcons name="seat" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.seating || "Seating"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.seating}
              onValueChange={courtData.setSeating}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<MaterialCommunityIcons name="fence" size={18} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.fencing || "Fencing"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.fencing}
              onValueChange={courtData.setFencing}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />

        <FeatureItem
          icon={<FontAwesome5 name="wheelchair" size={16} color={red} />}
          label={
            <View style={{flexDirection: 'row'}}>
              <Text>{language.accessible || "Accessible"}</Text>
              <Text style={styles.requiredIndicator}>*</Text>
            </View>
          }
          control={
            <CustomSwitch
              options={yesNoOptions}
              selectedValue={courtData.accessible}
              onValueChange={courtData.setAccessible}
              mandatory={true}
              placeholderText={language.select || "Select"}
            />
          }
        />
      </FeatureGroup>
    </ScrollView>
  );
};

export default FeaturesSection;