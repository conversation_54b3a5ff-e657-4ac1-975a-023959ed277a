// File: screens/AddCourt/components/SwipeableContainer.js
import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Dimensions, ScrollView } from 'react-native';

const { width } = Dimensions.get('window');

/**
 * A simplified swipeable container using ScrollView instead of Animated
 * 
 * @param {Array} children - React components to render as steps
 * @param {number} currentStep - Index of the current active step
 * @param {Function} onChangeStep - Callback when step changes, receives new index
 * @param {boolean} canSwipeNext - Whether swiping to next step is allowed
 */
const SwipeableContainer = ({ 
  children, 
  currentStep, 
  onChangeStep,
  canSwipeNext = true 
}) => {
  const scrollViewRef = useRef(null);
  
  // Scroll to the current step when it changes
  useEffect(() => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: currentStep * width,
        animated: true
      });
    }
  }, [currentStep]);

  // Handle scroll end to detect page changes
  const handleScrollEnd = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newStep = Math.round(offsetX / width);
    
    // Only allow moving to previous steps or current step if canSwipeNext is false
    if (newStep !== currentStep) {
      if (newStep > currentStep && !canSwipeNext) {
        // Prevent moving forward by scrolling back to current step
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            x: currentStep * width,
            animated: true
          });
        }, 10);
      } else {
        // Allow the change
        onChangeStep(newStep);
      }
    }
  };

  // Handle scroll to prevent moving to next step in real-time
  const handleScroll = (event) => {
    if (!canSwipeNext) {
      const offsetX = event.nativeEvent.contentOffset.x;
      // If trying to scroll past current step, prevent it
      if (offsetX > currentStep * width) {
        scrollViewRef.current?.scrollTo({
          x: currentStep * width,
          animated: false
        });
      }
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        onScroll={handleScroll}
        onMomentumScrollEnd={handleScrollEnd}
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
      >
        {React.Children.map(children, (child, index) => (
          <View key={index} style={[styles.step, { width }]}>
            {child}
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  step: {
    flex: 1,
  },
});

export default SwipeableContainer;