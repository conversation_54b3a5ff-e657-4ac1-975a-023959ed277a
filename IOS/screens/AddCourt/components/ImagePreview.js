// File: screens/AddCourt/components/ImagePreview.js
import React, { useState } from 'react';
import { View, Image, Text, Pressable } from 'react-native';
import { MaterialIcons } from "@expo/vector-icons";
import styles from '../styles/PhotosSectionStyles';

/**
 * Court image preview component with delete functionality
 */
const ImagePreview = ({ uri, index, onDelete }) => {
  const [selected, setSelected] = useState(false);
  
  return (
    <Pressable
      onPress={() => setSelected(!selected)}
      style={styles.imageContainer}
    >
      <Image source={{ uri }} style={styles.courtImage} />
      {selected && (
        <View style={styles.selectedImageOverlay}>
          <Pressable
            style={styles.deleteImageButton}
            onPress={() => {
              onDelete(index);
              setSelected(false);
            }}
          >
            <MaterialIcons name="delete" size={32} color="white" />
          </Pressable>
        </View>
      )}
      <View style={styles.imageNumberBadge}>
        <Text style={styles.imageNumberText}>{index + 1}</Text>
      </View>
    </Pressable>
  );
};

export default ImagePreview;