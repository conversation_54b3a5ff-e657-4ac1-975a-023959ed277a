// File: screens/AddCourt/components/SurfaceTypeDropdown.js
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, FlatList, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { red } from '../../../screens/colors';

/**
 * Dropdown component for selecting surface types
 * 
 * @param {number} value - Currently selected value
 * @param {Array} options - Array of options with shape { label: string, value: number }
 * @param {Function} onSelect - Callback when an option is selected
 * @param {Object} language - Language strings
 * @param {string} placeholder - Placeholder text when no option is selected
 */
const SurfaceTypeDropdown = ({ 
  value, 
  options, 
  onSelect, 
  language,
  placeholder = "Select an option"
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  
  // Find the currently selected option label
  const selectedLabel = value !== null 
    ? options.find(opt => opt.value === value)?.label 
    : null;
  
  return (
    <View>
      <TouchableOpacity 
        style={[
          styles.dropdownButton,
          value === null && styles.unselectedDropdown
        ]}
        onPress={() => setModalVisible(true)}
      >
        {value === null ? (
          <>
            <Text style={styles.placeholderText}>{placeholder}</Text>
            <MaterialIcons name="arrow-drop-down" size={24} color={red} />
          </>
        ) : (
          <>
            <Text style={styles.dropdownButtonText}>{selectedLabel}</Text>
            <MaterialIcons name="arrow-drop-down" size={24} color="rgba(0,0,0,0.6)" />
          </>
        )}
      </TouchableOpacity>
      
      <Modal
        transparent={true}
        visible={modalVisible}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {language.surfaceType || "Surface Type"}
            </Text>
            <FlatList
              data={options}
              keyExtractor={(item) => item.value.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.optionItem,
                    value === item.value && styles.selectedOption
                  ]}
                  onPress={() => {
                    onSelect(item.value);
                    setModalVisible(false);
                  }}
                >
                  <Text style={[
                    styles.optionText,
                    value === item.value && styles.selectedOptionText
                  ]}>
                    {item.label}
                  </Text>
                  {value === item.value && (
                    <MaterialIcons name="check" size={20} color={red} />
                  )}
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Styles for the dropdown component
const styles = StyleSheet.create({
  dropdownButton: {
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.2)",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "white",
  },
  unselectedDropdown: {
    borderColor: red,
    borderStyle: "dashed",
  },
  dropdownButtonText: {
    fontSize: 15,
    color: "#333",
  },
  placeholderText: {
    fontSize: 15,
    color: "rgba(0,0,0,0.4)",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "white",
    width: "80%",
    borderRadius: 10,
    padding: 20,
    maxHeight: "70%",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 15,
    color: red,
    textAlign: "center",
  },
  optionItem: {
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.05)",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  selectedOption: {
    backgroundColor: "rgba(255, 50, 50, 0.05)",
  },
  optionText: {
    fontSize: 16,
    color: "#333",
  },
  selectedOptionText: {
    fontWeight: "600",
    color: red,
  },
});

export default SurfaceTypeDropdown;