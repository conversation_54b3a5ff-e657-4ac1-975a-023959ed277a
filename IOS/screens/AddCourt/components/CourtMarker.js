// File: screens/AddCourt/components/CourtMarker.js
import React from 'react';
import { View } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Marker } from 'react-native-maps';
import styles from '../styles/AddCourtMapScreenStyles';

/**
 * Custom marker component for the court location on the map
 */
const CourtMarker = ({ coordinate }) => {
  return (
    <Marker coordinate={coordinate} draggable={false}>
      <View style={styles.markerContainer}>
        <MaterialCommunityIcons 
          name="basketball" 
          size={24} 
          color="white" 
          style={styles.markerIcon} 
        />
      </View>
    </Marker>
  );
};

export default CourtMarker;