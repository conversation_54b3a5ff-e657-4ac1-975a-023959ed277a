// File: screens/AddCourt/components/PhotoGuidelines.js
import React, { useRef } from 'react';
import { 
  Modal, 
  View, 
  Text, 
  Image, 
  TouchableOpacity, 
  ScrollView,
  Animated,
  TouchableWithoutFeedback,
  Dimensions,
  StyleSheet
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { red } from '../../../screens/colors';

const { width, height } = Dimensions.get('window');

/**
 * Improved photo guidelines modal component that ensures the example image is fully visible
 */
const PhotoGuidelines = ({ 
  isVisible, 
  onClose, 
  onConfirm, 
  language 
}) => {
  const modalAnimatedValue = useRef(new Animated.Value(isVisible ? 1 : 0)).current;
  
  // Update animation value when visibility changes
  React.useEffect(() => {
    Animated.timing(modalAnimatedValue, {
      toValue: isVisible ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isVisible]);

  return (
    <Modal
      animationType="none"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={styles.modalContainer} 
        activeOpacity={1} 
        onPressOut={onClose}
      >
        <Animated.View 
          style={[
            styles.modalAnimatedContainer, 
            { opacity: modalAnimatedValue }
          ]}
        >
          <TouchableWithoutFeedback>
            <View style={styles.modalContent}>
              {/* Header with close button */}
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {language.howTo || "Photo Guidelines"}
                </Text>
                <TouchableOpacity 
                  onPress={onClose}
                  style={styles.closeButton}
                >
                  <MaterialIcons name="close" size={24} color="rgba(0,0,0,0.5)" />
                </TouchableOpacity>
              </View>

              {/* Scrollable content */}
              <ScrollView 
                style={styles.modalScrollView}
                contentContainerStyle={styles.scrollViewContent}
                showsVerticalScrollIndicator={true}
              >
                <Text style={styles.modalText}>
                  {language.howToText || 
                  "When taking photos of basketball courts, please follow these guidelines for the best results:\n\n• Take clear, well-lit photos\n• Include the entire court if possible\n• Show the baskets and court markings\n• Include unique features of the court\n• Avoid including people's faces"}
                </Text>

                <Text style={styles.modalSubtitle}>
                  {language.examplePhoto || "Example Photo"}
                </Text>
                
                {/* Example image container with proper sizing */}
                <View style={styles.imageContainer}>
                  <Image
                    style={styles.exampleImage}
                    source={require("../../../images/court-image-example.jpg")}
                    resizeMode="contain"
                  />
                </View>
              </ScrollView>

              {/* Action button */}
              <TouchableOpacity
                onPress={onConfirm}
                style={styles.modalButton}
                activeOpacity={0.7}
              >
                <Text style={styles.modalButtonText}>
                  {language.understand || "Got It"}
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center", 
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  modalAnimatedContainer: {
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  modalContent: {
    width: width * 0.9,
    maxHeight: height * 0.85, // Make modal taller
    backgroundColor: "white",
    borderRadius: 16,
    padding: 0, // Remove padding for better content space
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: red,
  },
  closeButton: {
    padding: 5,
  },
  modalScrollView: { 
    width: '100%'
  },
  scrollViewContent: {
    padding: 20,
    paddingBottom: 30,
  },
  modalText: {
    fontSize: 15,
    lineHeight: 24,
    marginBottom: 20,
    color: '#444',
  },
  modalSubtitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 12,
    color: '#333',
  },
  imageContainer: {
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 20,
  },
  exampleImage: {
    width: '100%',
    height: undefined,
    aspectRatio: 4/3, // Maintain image aspect ratio
    borderRadius: 8, 
  },
  modalButton: {
    backgroundColor: red,
    margin: 20,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  modalButtonText: {
    color: "white",
    fontWeight: "700",
    fontSize: 16,
  },
});

export default PhotoGuidelines;