// File: screens/AddCourt/components/BasicInfoSection.js
import React from 'react';
import { useSelector } from "react-redux";
import { View, Text, TextInput, StyleSheet } from 'react-native';
import { useCourtData } from '../../../contexts/CourtDataContext';
import SurfaceTypeDropdown from './SurfaceTypeDropdown';
import baseStyles from '../styles/AddCourtInfoScreenStyles';
import CustomSwitch from '../../../components/CustomSwitch';

/**
 * Basic court information input section with updated UI
 */
const BasicInfoSection = () => {
  const { language } = useSelector((state) => state.language);
  const courtData = useCourtData();

  // Surface type options
  const surfaceOptions = [
    { label: language.artificial || "Artificial", value: 1 },
    { label: language.concrete || "Concrete", value: 2 },
    { label: language.other || "Other", value: 3 }
  ];

  // Indoor/Outdoor options
  const indoorOutdoorOptions = [
    { label: language.outdoor || "Outdoor", value: 1 },
    { label: language.indoor || "Indoor", value: 3 }
  ];

  // Public/Private options
  const publicPrivateOptions = [
    { label: language.public || "Public", value: 1 },
    { label: language.private || "Private", value: 3 }
  ];

  return (
    <View style={baseStyles.sectionContent}>
      <View style={baseStyles.inputGroup}>
        <Text style={baseStyles.inputLabel}>
          {language.describeCourt || "Court Description"}*
        </Text>
        <TextInput
          style={[baseStyles.textInput, baseStyles.textAreaInput]}
          value={courtData.courtDescription}
          onChangeText={courtData.setCourtDescription}
          placeholder={language.describeCourtPlaceholder || "Describe the court (condition, popularity, etc.)"}
          placeholderTextColor="rgba(0,0,0,0.4)"
          multiline
          numberOfLines={4}
          textAlignVertical="top"
        />
      </View>

      <View style={baseStyles.rowInputGroup}>
        <View style={styles.narrowInputGroup}>
          <Text style={baseStyles.inputLabel}>
            {language.basketsCount || "Baskets"}*
          </Text>
          <TextInput
            style={[baseStyles.textInput, baseStyles.numericInput]}
            value={courtData.basketsCount}
            onChangeText={(text) => {
              // Allow only numbers
              const numericRegex = /^[0-9]*$/;
              if (numericRegex.test(text)) {
                courtData.setBasketsCount(text);
              }
            }}
            keyboardType="numeric"
            placeholder="0"
            placeholderTextColor="rgba(0,0,0,0.4)"
          />
        </View>

        <View style={styles.wideInputGroup}>
          <Text style={baseStyles.inputLabel}>
            {language.surfaceType || "Surface Type"}*
          </Text>
          <SurfaceTypeDropdown
            value={courtData.surface}
            options={surfaceOptions}
            onSelect={courtData.setSurface}
            language={language}
            placeholder={language.selectSurface || "Select surface type"}
          />
        </View>
      </View>

      {courtData.surface === 3 && (
        <View style={baseStyles.inputGroup}>
          <Text style={baseStyles.inputLabel}>
            {language.otherSurface || "Specify Surface Type"}*
          </Text>
          <TextInput
            style={baseStyles.textInput}
            value={courtData.differentSurface}
            onChangeText={courtData.setDifferentSurface}
            placeholder={language.otherSurfacePlaceholder || "e.g., Rubber, Asphalt, etc."}
            placeholderTextColor="rgba(0,0,0,0.4)"
          />
        </View>
      )}

      <View style={baseStyles.rowInputGroup}>
        <View style={baseStyles.inputGroupHalf}>
          <Text style={baseStyles.inputLabel}>
            {language.type || "Type"}*
          </Text>
          <CustomSwitch
            options={indoorOutdoorOptions}
            selectedValue={courtData.indoorOutdoor}
            onValueChange={courtData.setIndoorOutdoor}
            mandatory={true}
            placeholderText={language.selectType || "Select type"}
          />
        </View>

        <View style={baseStyles.inputGroupHalf}>
          <Text style={baseStyles.inputLabel}>
            {language.access || "Access"}*
          </Text>
          <CustomSwitch
            options={publicPrivateOptions}
            selectedValue={courtData.publicPrivate}
            onValueChange={courtData.setPublicPrivate}
            mandatory={true}
            placeholderText={language.selectAccess || "Select access"}
          />
        </View>
      </View>
    </View>
  );
};

// Additional styles for the updated input layout
const styles = StyleSheet.create({
  narrowInputGroup: {
    width: "30%",
  },
  wideInputGroup: {
    width: "65%",
  }
});

export default BasicInfoSection;