// File: screens/AddCourt/components/StepIndicator.js
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { red } from '../../../screens/colors';

/**
 * Step indicator component that shows progress in the wizard
 * 
 * @param {number} currentStep - Index of the current active step
 * @param {Array} steps - Array of step information objects
 * @param {Function} onStepPress - Callback when a step indicator is pressed
 * @param {boolean} allMandatory - Whether all steps are mandatory
 */
const StepIndicator = ({ currentStep, steps, onStepPress }) => {
  return (
    <View style={styles.container}>
      {/* Connector line that spans across all steps */}
      <View style={styles.connectorContainer}>
        <View style={styles.connector} />
        <View 
          style={[
            styles.activeConnector, 
            { 
              width: `${(100 / (steps.length - 1)) * currentStep}%` 
            }
          ]} 
        />
      </View>
      
      {/* Steps indicators */}
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => {
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;
          
          return (
            <View key={index} style={styles.stepWrapper}>
              {/* Step indicator */}
              <TouchableOpacity 
                onPress={() => onStepPress(index)}
                disabled={!isCompleted && !isActive}
                style={[
                  styles.step,
                  isActive && styles.activeStep,
                  isCompleted && styles.completedStep
                ]}
              >
                {isCompleted ? (
                  <MaterialIcons name="check" size={18} color="white" />
                ) : (
                  step.icon
                )}
              </TouchableOpacity>
              
              {/* Step label */}
              <Text style={[
                styles.stepLabel,
                isActive && styles.activeStepLabel
              ]}>
                {step.label}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  // Container for the connector line that spans all steps
  connectorContainer: {
    position: 'absolute',
    top: 34, // Position to align with the center of step indicators
    left: 50,
    right: 50,
    height: 2,
    flexDirection: 'row',
  },
  // Background connector line (inactive)
  connector: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 2,
    backgroundColor: '#f0f0f0',
    zIndex: 1,
  },
  // Active connector line that grows based on the current step
  activeConnector: {
    position: 'absolute',
    top: 0,
    left: 0,
    height: 2,
    backgroundColor: '#4CAF50',
    zIndex: 2,
  },
  // Container for step indicators
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 3, // Ensure steps are above the connector line
  },
  stepWrapper: {
    alignItems: 'center',
  },
  step: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    zIndex: 3,
  },
  activeStep: {
    backgroundColor: red,
    borderColor: red,
  },
  completedStep: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  stepLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  activeStepLabel: {
    color: '#333',
    fontWeight: '600',
  },
});

export default StepIndicator;