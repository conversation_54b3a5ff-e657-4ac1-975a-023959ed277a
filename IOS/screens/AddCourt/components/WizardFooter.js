// File: screens/AddCourt/components/WizardFooter.js
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { red } from '../../../screens/colors';

/**
 * Footer component with back and next buttons for the wizard
 * 
 * @param {number} currentStep - Index of the current active step
 * @param {number} totalSteps - Total number of steps in the wizard
 * @param {Function} onBack - Callback when back button is pressed
 * @param {Function} onNext - Callback when next button is pressed
 * @param {boolean} isNextDisabled - Whether the next button should be disabled
 * @param {Object} language - Language strings for the component
 */
const WizardFooter = ({ 
  currentStep, 
  totalSteps, 
  onBack, 
  onNext, 
  isNextDisabled,
  language
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;
  
  return (
    <View style={styles.container}>
      {/* Back button */}
      {!isFirstStep ? (
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={onBack}
        >
          <MaterialIcons name="arrow-back" size={20} color="#666" />
          <Text style={styles.backButtonText}>
            {language.back || "Back"}
          </Text>
        </TouchableOpacity>
      ) : (
        <View style={styles.spacer} />
      )}
      
      {/* Step indicator dots */}
      <View style={styles.dotsContainer}>
        {Array.from({ length: totalSteps }).map((_, index) => (
          <View 
            key={index}
            style={[
              styles.dot,
              currentStep === index && styles.activeDot
            ]}
          />
        ))}
      </View>
      
      {/* Next/Submit button */}
      <TouchableOpacity 
        style={[
          styles.nextButton,
          isNextDisabled && styles.disabledButton
        ]} 
        onPress={onNext}
        disabled={isNextDisabled}
      >
        <Text style={styles.nextButtonText}>
          {isLastStep 
            ? (language.setLocation || "Set Location") 
            : (language.next || "Next")}
        </Text>
        <MaterialIcons 
          name="arrow-forward" 
          size={20} 
          color="white" 
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    backgroundColor: 'white',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  backButtonText: {
    marginLeft: 4,
    color: '#666',
    fontSize: 16,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: red,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  nextButtonText: {
    color: 'white',
    fontWeight: '600',
    marginRight: 6,
    fontSize: 16,
  },
  spacer: {
    width: 80,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#ccc',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: red,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
});

export default WizardFooter;