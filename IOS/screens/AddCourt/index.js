// File: screens/AddCourt/index.js
import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import AddCourtInfoScreen from "./AddCourtInfoScreen";
import AddCourtMapScreen from "./AddCourtMapScreen";
import { CourtDataProvider } from "../../contexts/CourtDataContext";

const Stack = createStackNavigator();

/**
 * Main AddCourt component that manages navigation between court creation screens
 * Uses CourtDataContext to share data between screens
 * 
 * This is the main entry point for the AddCourt feature.
 * The component structure is:
 * 
 * AddCourt/                  - Main navigation container
 * ├─ AddCourtInfoScreen      - First screen (tabbed interface)
 * │  ├─ BasicInfoSection     - Court name, description, count, surface
 * │  ├─ FeaturesSection      - Court features and amenities
 * │  └─ PhotosSection        - Court photos with camera/gallery selection
 * └─ AddCourtMapScreen       - Second screen (location selection)
 */
const AddCourt = () => {
  return (
    <CourtDataProvider>
      <Stack.Navigator
        screenOptions={{
          unmountInactiveRoutes: false,
          gestureEnabled: false,
          cardStyle: { backgroundColor: 'white' }
        }}
      >
        <Stack.Screen
          name="AddCourtInfoInput"
          component={AddCourtInfoScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="AddCourtMap"
          component={AddCourtMapScreen}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    </CourtDataProvider>
  );
};

export default AddCourt;