import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { GooglePlacesAutocomplete } from "react-native-google-places-autocomplete";
import { useSelector } from "react-redux";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Feather, MaterialIcons } from "@expo/vector-icons";
import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";
import { useToast } from "../hooks/toast/useToast"; // Make sure this path is correct

const SearchLocation = ({ navigation, route }) => {
  const [selectedPlace, setSelectedPlace] = useState(null);
  const { language } = useSelector((state) => state.language);
  const insets = useSafeAreaInsets();
  const showToast = useToast();

  const { onLocationSelected, isFromLocationList, teamId, addLocation } =
    route.params;

  // Get locations to check against (different sources depending on origin)
  const locationsToCheck = isFromLocationList
    ? useSelector((state) => state.teams.teams.find((t) => t.id === teamId))
        ?.locations || []
    : route.params.existingLocations || [];

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  const handlePlaceSelect = (data, details) => {
    const newPlace = {
      name: data.description,
      place_id: data.place_id,
      latitude: details.geometry.location.lat,
      longitude: details.geometry.location.lng,
      originalLatitude: details.geometry.location.lat,
      originalLongitude: details.geometry.location.lng,
    };

    // Check if this place already exists (works for both CreateTeam and LocationsList cases)
    const locationExists = locationsToCheck.some(
      (location) => location.place_id === data.place_id
    );

    if (locationExists) {
      showToast(
        language.locationAlreadyExists ||
          "This location already exists in your team",
        "error"
      );
      return;
    }

    setSelectedPlace(newPlace);
    dismissKeyboard();
  };

  return (
    <TouchableWithoutFeedback onPress={dismissKeyboard}>
      <View
        style={{
          flex: 1,
          padding: 20,
          backgroundColor: "white",
          paddingTop: insets.top,
        }}
      >
        <TouchableOpacity
          style={{ marginBottom: 20 }}
          onPress={() => navigation.goBack()}
        >
          <Feather name="arrow-left" size={24} color={red} />
        </TouchableOpacity>

        <Text
          style={{
            fontSize: 28,
            fontWeight: "bold",
            marginBottom: 20,
            color: "#333",
          }}
        >
          {language.searchLocation}
        </Text>

        <GooglePlacesAutocomplete
          placeholder={language.searchPlaceholder}
          onPress={(data, details = null) => handlePlaceSelect(data, details)}
          query={{
            key: "AIzaSyDeoJjMlI6YhcEWyWFLSe0r5w---lMiehk",
            language: "cs",
            components: "country:cz",
          }}
          styles={{
            container: { flex: 0, marginBottom: 20 },
            textInput: {
              fontSize: 16,
              backgroundColor: "rgba(245,245,245,1)",
              borderRadius: 10,
              paddingHorizontal: 15,
              paddingVertical: 10,
              paddingLeft: 40,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.06,
              shadowRadius: 4,
              elevation: 3,
              borderWidth: 1,
              borderColor: "rgba(0,0,0,0.05)",
            },
            listView: {
              backgroundColor: "#fff",
              borderRadius: 10,
              marginTop: 5,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 3,
            },
          }}
          renderLeftButton={() => (
            <View
              style={{ position: "absolute", left: 12, top: 12, zIndex: 1 }}
            >
              <Feather name="search" size={20} color="rgba(0,0,0,0.5)" />
            </View>
          )}
          fetchDetails={true}
        />

        <View
          style={{
            position: "absolute",
            bottom: insets.bottom + 15,
            width: "100%",
            alignSelf: "center",
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: 20,
            }}
          >
            <MaterialIcons
              name="map"
              size={24}
              color={selectedPlace ? red : "rgba(0,0,0,0.3)"}
            />
            <Text
              style={{
                marginLeft: 10,
                fontSize: 16,
                color: selectedPlace ? red : "rgba(0,0,0,0.3)",
              }}
            >
              {language.nextStepMap}
            </Text>
          </View>
          <TouchableOpacity
            style={{
              opacity: selectedPlace ? 1 : 0.5,
              overflow: "hidden",
              borderRadius: 25,
            }}
            disabled={!selectedPlace}
            onPress={() =>
              navigation.navigate("PinpointLocation", {
                selectedPlace,
                onLocationSelected,
                isFromLocationList,
                teamId,
                addLocation,
              })
            }
          >
            <LinearGradient
              colors={[red, "#fa5757"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={{
                paddingVertical: 15,
                alignItems: "center",
              }}
            >
              <Text
                style={{ color: "white", fontSize: 18, fontWeight: "bold" }}
              >
                {language.continueToMap}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default SearchLocation;
