// File: contexts/CourtDataContext.js
import React, { createContext, useState, useContext } from 'react';

// Create context
const CourtDataContext = createContext(null);

/**
 * Provider component for managing court data state throughout the Add Court flow
 */
export const CourtDataProvider = ({ children }) => {
  // Basic court info
  const [courtName, setCourtName] = useState("");
  const [courtDescription, setCourtDescription] = useState("");
  const [basketsCount, setBasketsCount] = useState("");
  
  // Surface properties
  const [surface, setSurface] = useState(null);
  const [differentSurface, setDifferentSurface] = useState("");
  
  // Court features - initialized as null (not selected)
  const [boardSize, setBoardSize] = useState(null);
  const [lines, setLines] = useState(null);
  const [lighting, setLighting] = useState(null);
  const [fullCourt, setFullCourt] = useState(null);
  const [indoorOutdoor, setIndoorOutdoor] = useState(null);
  const [publicPrivate, setPublicPrivate] = useState(null);
  
  // Additional amenities - initialized as null (not selected)
  const [waterFountain, setWaterFountain] = useState(null);
  const [seating, setSeating] = useState(null);
  const [fencing, setFencing] = useState(null);
  const [accessible, setAccessible] = useState(null);
  
  // Images and location
  const [images, setImages] = useState([]);
  const [coordinates, setCoordinates] = useState(null);

  // Court data validation
  const validateBasicInfo = () => {
    return (
      courtName.trim() !== "" &&
      basketsCount !== "" &&
      courtDescription.trim() !== "" &&
      surface !== null
    );
  };

  const validateFeatures = () => {
    return (
      boardSize !== null &&
      lines !== null &&
      lighting !== null &&
      fullCourt !== null &&
      indoorOutdoor !== null &&
      publicPrivate !== null &&
      waterFountain !== null &&
      seating !== null &&
      fencing !== null &&
      accessible !== null
    );
  };

  const validateImages = () => {
    return images.length > 0;
  };

  const validateLocation = () => {
    return coordinates !== null;
  };

  // Complete court validation
  const isFormValid = () => {
    return validateBasicInfo() && validateFeatures() && validateImages();
  };

  // Get court data as an object
  const getCourtData = () => ({
    courtName,
    courtDescription,
    basketsCount,
    surface,
    differentSurface,
    boardSize,
    lines,
    lighting,
    fullCourt,
    indoorOutdoor,
    publicPrivate,
    waterFountain,
    seating,
    fencing,
    accessible,
    images,
    coordinates
  });

  // All the state values and functions to update them
  const value = {
    // Basic court info
    courtName, setCourtName,
    courtDescription, setCourtDescription,
    basketsCount, setBasketsCount,
    
    // Surface properties
    surface, setSurface,
    differentSurface, setDifferentSurface,
    
    // Court features
    boardSize, setBoardSize,
    lines, setLines,
    lighting, setLighting,
    fullCourt, setFullCourt,
    indoorOutdoor, setIndoorOutdoor,
    publicPrivate, setPublicPrivate,
    
    // Additional amenities
    waterFountain, setWaterFountain,
    seating, setSeating,
    fencing, setFencing,
    accessible, setAccessible,
    
    // Images and location
    images, setImages,
    coordinates, setCoordinates,
    
    // Validation methods
    validateBasicInfo,
    validateFeatures,
    validateImages,
    validateLocation,
    isFormValid,
    
    // Helper methods
    getCourtData
  };

  return (
    <CourtDataContext.Provider value={value}>
      {children}
    </CourtDataContext.Provider>
  );
};

// Custom hook for using the court data context
export const useCourtData = () => {
  const context = useContext(CourtDataContext);
  if (context === null) {
    throw new Error('useCourtData must be used within a CourtDataProvider');
  }
  return context;
};