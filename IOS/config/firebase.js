import { initializeApp, getApps, getApp } from "@react-native-firebase/app";
import { getAuth } from "@react-native-firebase/auth";
import { getFirestore } from "@react-native-firebase/firestore";
import { getStorage } from "@react-native-firebase/storage";
import { getFunctions } from "@react-native-firebase/functions";
import {
  getCrashlytics,
  setCrashlyticsCollectionEnabled,
} from "@react-native-firebase/crashlytics";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Your Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAhyxUBmf4X44gLDlx7uceL0aKvgn4IqHE",
  authDomain: "ballin-project.firebaseapp.com",
  projectId: "ballin-project",
  storageBucket: "ballin-project.appspot.com",
  messagingSenderId: "99572682741",
  appId: "1:99572682741:web:29aa2243615bb650f15abc",
  measurementId: "G-7V2H843YE1",
};

globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;

// Initialize Firebase only if no apps have been initialized
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
// const app = getApp();

// const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);
const storage = getStorage(app);
const functions = getFunctions(app);
const crashlytics = getCrashlytics(app);

// Enable Crashlytics collection
setCrashlyticsCollectionEnabled(crashlytics, true);

// Set AsyncStorage for Auth persistence

// auth.setPersistence(getReactNativePersistence(AsyncStorage));

export { app, auth, db, storage, functions, crashlytics };
