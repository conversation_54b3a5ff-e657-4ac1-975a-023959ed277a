import {
  recordError,
  setAttributes,
  setAttribute,
  log,
} from "@react-native-firebase/crashlytics";
import { crashlytics } from "../config/firebase";

export const recordCrashlyticsError = (error, screenName) => {
  // Error location from stack trace
  const errorLocation = error?.stack.split("\n")[1];

  // Set error context attributes
  setAttribute(crashlytics, "screenName", screenName);
  setAttribute(crashlytics, "errorLocation", errorLocation);
  setAttribute(crashlytics, "errorType", error.name);
  setAttribute(crashlytics, "errorMessage", error.message);

  // Log additional error details
  log(crashlytics, `Error occurred in: ${screenName}`);
  log(crashlytics, `Full stack trace: ${error.stack}`);

  // Add timestamp
  setAttribute(crashlytics, "errorTimestamp", new Date().toISOString());

  // Record the error
  recordError(crashlytics, error);
};
