import { TextEncoder, TextDecoder } from "text-encoding";

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

import React, {
  useState,
  createContext,
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useCallback,
} from "react";
import { NavigationContainer, useNavigation } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import {
  StyleSheet,
  Text,
  View,
  Button,
  TextInput,
  Image,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
  // Keyboard,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  Modal,
  Linking,
} from "react-native";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "./config/firebase";
import { db } from "./config/firebase";

import {
  CollectionReference,
  doc,
  getDoc,
  Firestore,
  setDoc,
  collection,
  onSnapshot,
} from "firebase/firestore";

import Login from "./screens/Login";
import Signup from "./screens/Signup";
import Home from "./screens/Home";
import MyGames from "./screens/MyGames";
import Map from "./screens/Map";

import Profile from "./screens/Profile";
import Event from "./screens/Event";
import MyGamesStack from "./screens/MyGamesStack";
import EditEvent from "./screens/EditEvent";
import ChooseCoords from "./screens/ChooseCoords";
import EditCoords from "./screens/EditCoords";
import CourtInfo from "./screens/CourtInfo";
import AddCourt from "./screens/AddCourt";
import SetupProfile from "./screens/SetupProfile";
import Socials from "./screens/Socials";

import Ionicons from "@expo/vector-icons/Ionicons";
import { AntDesign } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";

import {
  AuthenticatedUserContext,
  AuthenticatedUserProvider,
} from "./screens/Provider";
// import { red } from "./screens/colors";
import { useFonts } from "expo-font";

import * as Notifications from "expo-notifications";

import { useSafeAreaInsets } from "react-native-safe-area-context";

import { MaterialCommunityIcons } from "@expo/vector-icons";
import Constants from "expo-constants";

import { AppLoading } from "expo";

import { red } from "./screens/colors";

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const CustomTabBarIcon = ({ focused, iconName }) => {
  // let color = focused ? "rgba(197,42,71,1)" : "black";
  let color = focused ? `${red}` : "black";

  if (iconName == "user") {
    return <Feather name={iconName} size={27} color={color} />;
  }

  if (iconName == "home") {
    return <AntDesign name={iconName} size={27} color={color} />;
  }

  if (iconName == "map") {
    return <Feather name={iconName} size={27} color={color} />;
  }

  if (iconName == "socials") {
    <MaterialCommunityIcons
      name="account-group-outline"
      size={24}
      color="black"
    />;
  }

  return <Ionicons name={iconName} size={27} color={color} />;
};

const ModalComponent = ({ hideModal }) => {
  // const [language, setLanguage] = useState(null);
  const [language2, setLanguage2] = useState(null);
  let unsubscribe;

  const [strings, setStrings] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const docRef = doc(db, "users", auth?.currentUser?.uid);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          setLanguage2(docSnap.data().language);
        }

        const infoRef = doc(db, "info", "communityTeaser");
        const infoSnap = await getDoc(infoRef);

        if (infoSnap.exists()) {
          setStrings(infoSnap.data());
        }
      } catch (error) {
        console.error("An error occurred:", error);
      }
    }

    fetchData();
  }, []);

  // useEffect(() => {
  //   if (language2 === "CZ") {
  //   } else {
  //     if (language !== EN) {
  //       setLanguage(EN);
  //     }
  //   }
  // }, [language2]);

  if (!strings) {
    return;
  }

  return (
    <TouchableOpacity
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.2)",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1,
      }}
      onPress={hideModal}
      activeOpacity={1}
    >
      <View
        style={{
          backgroundColor: "#fff",
          padding: 20,
          borderRadius: 10,
          alignItems: "center",
          position: "absolute",
          bottom: 110,
          width: "90%",
          // borderColor: "rgba(0,0,0,0.5)",
          // borderWidth: 1,
        }}
      >
        <Text style={{ fontSize: 16, marginBottom: 10, fontWeight: "500" }}>
          {language2 == "CZ" ? strings.czHeader : strings.enHeader}
        </Text>

        <Text style={{ fontSize: 14, marginBottom: 10, textAlign: "center" }}>
          {language2 == "CZ" ? strings.czBody : strings.enBody}
        </Text>
        <View
          style={{
            position: "absolute",
            bottom: -10,
            left: "50%",
            marginLeft: 10,
            width: 0,
            height: 0,
            borderLeftWidth: 10,
            borderLeftColor: "transparent",
            borderRightWidth: 10,
            borderRightColor: "transparent",
            borderTopWidth: 10,
            borderTopColor: "#fff",
          }}
        />
      </View>
    </TouchableOpacity>
  );
};

function ChatStack() {
  const insets = useSafeAreaInsets();

  const [showModal, setShowModal] = useState(false);

  return (
    // <SafeAreaView2
    //   style={{ flex: 1, backgroundColor: "white" }}
    //   edges={["bottom"]}
    // >
    <>
      <Tab.Navigator
        screenOptions={{
          headerStyle: {},
          tabBarInactiveTintColor: "black",
          tabBarShowLabel: false,

          tabBarStyle: {
            height: 55 + insets.bottom * 0.9,
            // marginBottom: insets.bottom,

            // paddingTop: 10,
            // paddingBottom: 10,
            backgroundColor: "white",

            // position: "absolute",
            borderTopWidth: 0,
          },
        }}
      >
        <Tab.Screen
          name="Home"
          component={Home}
          options={{
            headerShown: false,

            tabBarActiveTintColor: red,
            tabBarIcon: ({ focused }) => (
              <CustomTabBarIcon focused={focused} iconName="home" />
            ),
          }}
        />
        <Tab.Screen
          name="My Games"
          component={MyGames}
          options={{
            // tabBarShowLabel: false,
            headerShown: false,
            lazy: false,
            tabBarActiveTintColor: red,

            tabBarIcon: ({ focused }) => (
              <CustomTabBarIcon
                focused={focused}
                iconName="basketball-outline"
              />
            ),
            // tabBarOptions: { activeTintColor: "red" },
          }}
        />
        <Tab.Screen
          name="Socials"
          component={Socials}
          options={{
            headerShown: false,
            tabBarActiveTintColor: "red",
            tabBarIcon: ({ focused }) => (
              <MaterialCommunityIcons
                name="account-group-outline"
                size={32}
                color={focused ? "red" : "black"}
              />
            ),
          }}
          listeners={({ navigation }) => ({
            tabPress: (e) => {
              e.preventDefault();

              console.log("test");

              setShowModal(true);
            },
          })}
        />
        <Tab.Screen
          name="Map"
          component={Map}
          options={{
            headerShown: false,
            lazy: false,
            tabBarActiveTintColor: red,
            tabBarIcon: ({ focused }) => (
              <CustomTabBarIcon focused={focused} iconName="map" />
            ),
          }}
        />

        <Tab.Screen
          name="Profile"
          options={{
            lazy: false,
            headerShown: false,
            tabBarActiveTintColor: red,
            tabBarIcon: () => <Feather name="user" size={24} color="black)" />,
            tabBarIcon: ({ focused }) => (
              <CustomTabBarIcon focused={focused} iconName="user" />
            ),
          }}
          component={Profile}
        />
      </Tab.Navigator>
      {showModal && <ModalComponent hideModal={() => setShowModal(false)} />}
    </>

    // </SafeAreaView2>

    // </AuthenticatedUserProvider>
  );
}

function AppRoot({ user }) {
  if (user === "hasNoProfile") {
    console.log("Has no profile");
    return (
      <NavigationContainer independent={true}>
        <Stack.Navigator
          options={{ headerShown: false }}
          screenOptions={{
            gestureEnabled: false,
            unmountInactiveRoutes: true,
          }}
          // screenOptions={{}}
        >
          <Stack.Screen
            name="SetupProfile"
            component={SetupProfile}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ChooseCoords"
            component={ChooseCoords}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Root"
            component={ChatStack}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name="Event"
            component={Event}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="CourtInfo"
            component={CourtInfo}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="MyGamesStack"
            component={MyGamesStack}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="EditEvent"
            component={EditEvent}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="AddCourt"
            component={AddCourt}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="EditCoords"
            component={EditCoords}
            options={{ headerShown: false }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }

  if (user === "hasProfile") {
    console.log("Has profile");
    return (
      <NavigationContainer independent={true}>
        <Stack.Navigator
          options={{ headerShown: false }}
          screenOptions={
            {
              // gestureEnabled: false,
            }
          }
          // screenOptions={{
          //   unmountInactiveRoutes: false,
          // }}
        >
          <Stack.Screen
            name="Root"
            component={ChatStack}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name="ChooseCoords"
            component={ChooseCoords}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name="Event"
            component={Event}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="CourtInfo"
            component={CourtInfo}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="MyGamesStack"
            component={MyGamesStack}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="EditEvent"
            component={EditEvent}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="AddCourt"
            component={AddCourt}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="EditCoords"
            component={EditCoords}
            options={{ headerShown: false }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }
}

function RootNavigator() {
  // useEffect(() => {
  //   SplashScreen.preventAutoHideAsync();
  // }, []);

  const [fontsLoaded] = useFonts({
    "BungeeInline-Regular": require("./assets/fonts/Bungee_Inline/BungeeInline-Regular.ttf"),
    "Nunito-Bold": require("./assets/fonts/Nunito/static/Nunito-Bold.ttf"),
  });

  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);

  const checkIfUserExists = async () => {
    // console.log("called");
    const docRef = doc(db, "users2/", auth.currentUser.uid);
    const docSnap = await getDoc(docRef);
    // console.log(docSnap.exists());

    if (docSnap.exists()) {
      return true;
    } else {
      return false;
    }
  };

  useLayoutEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (authenticateUser) => {
      if (authenticateUser) {
        setUser(authenticateUser);
        const userExists = await checkIfUserExists();
        if (userExists) {
          setUser("hasProfile");
        } else {
          setUser("hasNoProfile");
        }
      } else {
        setUser("noUser");
      }

      setTimeout(() => {
        setLoading(false);
      }, 500);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1 }}>
        <Image
          source={require("./images/newlogo.png")}
          style={{
            width: 400,
            height: 400,
            alignSelf: "center",
            marginTop: "41%",
          }}
        ></Image>
      </View>
    );
  }

  if (user === "hasProfile" || user === "hasNoProfile") {
    return <AppRoot user={user} />;
  }

  if (user === "noUser") {
    return (
      <NavigationContainer independent={true}>
        <Stack.Navigator
          options={{ headerShown: false }}
          screenOptions={{
            gestureEnabled: false,
          }}
          // screenOptions={{
          //   unmountInactiveRoutes: false,
          // }}
        >
          <Stack.Screen
            name="Login"
            component={Login}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="Signup"
            component={Signup}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ChooseCoords"
            component={ChooseCoords}
            options={{ headerShown: false }}
          />

          <Stack.Screen
            name="SetupProfile"
            component={SetupProfile}
            options={{ headerShown: false }}
          />
        </Stack.Navigator>
      </NavigationContainer>
    );
  }
}

function VersionCheck({ children }) {
  const [needsUpdate, setNeedsUpdate] = useState(false);

  const [message, setMessage] = useState(null);

  useEffect(() => {
    const currentVersion = Constants.expoConfig.version;
    const platform = Platform.OS;
    console.warn(`Current app version for ${platform}:`, currentVersion);

    const docRef = doc(db, "info", "version");

    const unsubscribe = onSnapshot(
      docRef,
      (docSnap) => {
        if (docSnap.exists()) {
          setMessage(docSnap.data().message);
          const versionData = docSnap.data();
          console.warn("Latest versions:", versionData);

          const latestVersion =
            platform === "ios" ? versionData.ios : versionData.and;

          console.warn(`Comparing versions:`, currentVersion, latestVersion);

          if (currentVersion < latestVersion) {
            setNeedsUpdate(true);
          } else {
            setNeedsUpdate(false);
          }
        }
      },
      (error) => {
        console.error("Error listening to app version changes:", error);
      }
    );

    // Cleanup function to unsubscribe from the listener when the component unmounts
    return () => unsubscribe();
  }, []);

  const goToStore = () => {
    const storeUrl =
      Platform.OS === "ios"
        ? "https://apps.apple.com/cz/app/lets-hoop/id6451425446?l=cs"
        : "https://play.google.com/store/apps/details?id=com.matyasnemec.letshoop";
    Linking.openURL(storeUrl);
  };

  if (needsUpdate) {
    return (
      <Modal transparent={true} animationType="slide" visible={needsUpdate}>
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0,0,0,0.5)",
          }}
        >
          <View
            style={{
              backgroundColor: "white",
              padding: 20,
              borderRadius: 10,
              alignItems: "center",
              marginHorizontal: 25,
            }}
          >
            <Text
              style={{ fontSize: 18, fontWeight: "bold", marginBottom: 20 }}
            >
              Update Required
            </Text>
            <Text style={{ textAlign: "center", marginBottom: 20 }}>
              A new version of the app is available. Please update to continue
              using the app.
            </Text>
            {message && (
              <Text style={{ textAlign: "center", marginBottom: 20 }}>
                {message}
              </Text>
            )}

            <TouchableOpacity
              onPress={goToStore}
              style={{ backgroundColor: red, padding: 10, borderRadius: 5 }}
            >
              <Text style={{ color: "white" }}>Update Now</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }

  return children;
}

export default function App() {
  return (
    <VersionCheck>
      <AuthenticatedUserProvider>
        <RootNavigator />
      </AuthenticatedUserProvider>
    </VersionCheck>
  );
}
