{"expo": {"name": "Let's Hoop", "slug": "lets-hoop", "version": "1.0.2", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": false, "buildNumber": "1.0.2", "bundleIdentifier": "com.lerty.ballinapp2", "infoPlist": {"NSCameraUsageDescription": "This app needs access to the camera to take photos.", "NSPhotoLibraryUsageDescription": "This app needs access to the library to choose photos.", "CFBundleURLTypes": [{"CFBundleURLSchemes": ["com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n"]}]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-build-properties", {"ios": {"useFrameworks": "static"}}]], "extra": {"eas": {"projectId": "601fa968-e69a-4e71-9c37-bd4de7bd5f96"}}, "owner": "lerty"}}