export const EN = {
  communityTeaseTitle: "Community",

  communityTeaseBody:
    "Adding friends, creating teams and training sessions, joining tournaments and challanging other teams to matches comming this spring!",

  weekdays: [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ],

  deletedEvent: "This event has been deleted",

  noGames: "There are currently no games\n in your area",

  feedbackSentHome: "Message Sent",

  eventFull: "This game is already full",

  writeUsTut:
    "Have a question, idea, or something to share with us? You can express yourself here",

  writeUs: "Share your thoughts",

  start: "Start :)",

  welcome: "Welcome to our new app! 👋",

  welcomeString:
    "We're glad you've joined our basketball community. Since the app is new, we'll be grateful if you help us expand the community by sharing the app. It will help us create a community across the whole country in the future. \n\nWe are students from Prague, and so far, we've documented over 60 courts just in Prague. If you live outside of Prague or can't see your favorite court, you can easily add it to the map. \n\nIf you have a question, an idea for improvement, or anything else, be sure to tell us",

  areaUpdated: "Region updated",

  addedCourt:
    "Thank you for adding a new court. We will verify it as soon as possible and let you know :)",

  usernameTaken: "This username is already taken :(",

  editEvent: "Edit game",

  noUsername: "Please choose a username",
  noDate: "Please choose your date of birth",
  lowAge: "You have to be at least 15 years old to sign up",

  save: "Save",

  areaInfo:
    "Please choose an area that is relevant for you. This will allow us to show you events that are near you",

  changeLater: "You will be able to change this later",

  dateLocale: "en-En",

  setNotif: "Set up notifications",

  //Setup profile
  userInfoGuide:
    "Here you can describe how long and how often do you play basketball and what type of players you want to play with",
  continue: "Continue",

  passwordInput: "Password",
  confirmPassword: "Confirm Password",
  emailInput: "Email",
  login: "Login",
  signUp: "Sign up",
  haveAcc: "Already have an account?",
  noAcc: "Don't have an account yet?",

  hi: "Hi",
  changeRegion: "Change region",
  liveEvents: "Live Events",
  plannedEvents: "Planned Events",
  endsAt: "Ends at",
  yourGames: "Your games",
  favCourts: "Favourite courts",
  noFavCourts:
    "Here you can add your favourite courts and we will notify you whenever someone creates a new game on one of them",
  exploreCourts: "Explore courts",
  createGame: "Create a new game",
  moreCourtInfo: "More...",
  openingHours: "Open hours",
  artificial: "Artificial",
  concrete: "Concrete",
  otherSurface: "Other...",

  //Add a court
  describeCourt:
    "Describe the courts condition, especially the hoops and the surface. Also, if you see any opening hours, be sure to write them down here!",
  basketsCount: "Baskets count",
  surfaceType: "Surface type",
  addCourt: "Add a court",
  chooseImage: "Choose a photo",
  takeImage: "Take a photo",
  send: "Send",
  howTo: "What images of the court are required",
  howToText:
    "-  Hold the camera HORIZONTALLY \n\n-  At least one photo for every hoop \n-  Without people. In case you meet any, ask them to move for a moment",
  examplePhoto: "Example",
  understand: "I understand",
  images: "Images",
  chooseLocation: "Pinpoint the location",

  //CourtInfo
  games: "Games",

  //Creating a game
  nameOfEvent: "Name of your event",
  eventInfoInput:
    "Give some information about your event. Mainly the level of basketball at which you want to play. (beginner/advanced)",
  chooseCourt: "Choose a court",
  chooseThisCourt: "Choose court",
  date: "Date",
  from: "From",
  to: "To",
  public: "Public",
  ageRestricted: "Age restricted",
  usersLimit: "Limit of players",
  defaultUserLimit: "Default no limit",
  publishGame: "Slide to publish",
  minAge: "Min age",
  maxAge: "Max age",
  notifExplanation:
    "We would like to send you important notfications, like when you recieve a new chat message or when someone creates an event on one of your favourite courts. \n\n You can easily change this later in the settings of this app",
  allow: "Allow",
  deny: "Deny",

  //Profile
  age: "Age",
  name: "Username",
  dateOfBirth: "Date of birth",
  userDescription: "Description",
  settings: "Settings",
  logOut: "Log out",
  editProfile: "Edit profile",
  saveChanges: "Save",
  password: "Password",
  description: "Description",

  save: "Save",

  //Settings
  notifications: "Notifications",
  favCourtNotif: "Someone has created a game on one of your favourite courts",
  newChatNotif: "New message in chat",
  eventChangeNotif: "The setting of an event that you are in has changed",
  usersChangeNotif: "Someone has joined/left your game",
  languageLabel: "Language",

  other: "Others",
  feedback: "Send us feedback",
  feedbackSent: "Feedback sent!",

  reportBug: "Report a bug",
  reportBugSent: "Information about the bug was sent, thank you!",
  reportGuide: "Please tell us what exactly you did that lead to the bug.",

  deleteAccount: "Delete account",
  deleteAccountConfirm: `Type "Delete" to confirm`,
  deleteAccountDone: "Account was deleted",
  deleteBtn: "DELETE",
  deleteInfo: "This action will permanently delte your account",
  accountNotDeleted: "Account was not deleted",
  signOutTitle:
    "Please sign out and sign in again in order to be able to delete your account",
  singOutText:
    "For security reasons, you need to sign out and sign in again before deleting your account.",

  signOutText2:
    "You migh have to sign out and sign in again in order to be able to delete your account",

  signOutTitleEmail: "Please sign out and sign in again",

  signOutTitleEmail2:
    "For security reasons, you need to sign out and sign in again before updating your email",

  cancel: "Cancel",

  information: "Information",
  deleteChat: "Delete",
  joinGame: "Join game",
  leaveGame: "Leave game",
  users: "Users",
  yearsOld: "years old",
  reportUser: "Report",
  reportText: "Tell us why do you want to report this user",
  //   send: "Send",
  deleteEvent: "Delete event",
  editEvent: "Edit event",
  confirm: "Confirm",

  gamesLimit: "You can only have 4 games at once",

  removeUserEnsure: "Are you sure you want to remove",
  yes: "Yes",
  no: "No",

  logOutEnsure: "Are you sure you want to log out?",

  regular: "Big(Regular)",
  small: "Small",

  boardSize: "Board size",

  lines: "Lines",

  yes: "Yes",
  no: "No",

  lighting: "Lighting",
  fullCourt: "Full court",

  type: "Type",
  indoor: "Indoor",
  outdoor: "Outdoor",

  access: "Access",
  public: "Public",
  private: "Private",
};

export const CZ = {
  communityTeaseTitle: "Komunita",

  communityTeaseBody:
    "Přidávání přátel, vytváření týmů a opakujících se tréninků, otevřené turnaje a vyzývání ostatních týmu na zápas. Tento update přidáme na jaře!",

  weekdays: [
    "neděle",
    "pondělí",
    "úterý",
    "středa",
    "čtvrtek",
    "pátek",
    "sobota",
  ],
  deletedEvent: "Tato hra byla smazána",

  eventFull: "Tato hra je plná",
  noGames: "V současnosti se v tvé oblasti\n nekonají žádné hry",

  feedbackSentHome: "Zpráva odeslána",

  writeUsTut:
    "Máš dotaz, nápad nebo nám chceš něco sdělit? Tady se můžeš vyjádřit",

  writeUs: "Napiš nám",

  start: "Začít :)",

  welcome: "Vítej v naší nové aplikaci! 👋",

  welcomeString:
    "Jsme rádi, že ses přidal do naší komunity basketbalistů. Jelikož je tato aplikace nová, budeme vděčni, když nám pomůžes rozšířit komunitu a aplikaci sdílíš například na stories. Pomůže nám to  do budoucna vytvořit komunitu po celé republice. \n\nJsme studenti z Prahy a zatím jsme zdokumentovali přes 60 hřišť pouze v Praze. Pokud ale bydlíš mimo Prahu, nebo nevidíš své oblíbené hřiště, můžeš ho na mapě jednoduše přidat. \n\nKdybys měl dotaz, nápad na vylepšení, nebo cokoliv jiného, určitě nám napiš",

  areaUpdated: "Oblast aktualizována",

  addedCourt:
    "Děkujeme za přidání nového hřiště! Co nejrychleji hřiště ověříme a dáme ti vědět :)",

  usernameTaken: "Toto uživatelské jméno už je zabrané :(",

  editEvent: "Upravit událost",

  noUsername: "Zadejte uživatelské jméno",
  noDate: "Zadejte datum narození",
  lowAge: "Musí vám být aspoň 15 let, abyste mohli používat aplikaci",

  save: "Uložit",

  areaInfo:
    "Vyberte prosím pro vás relevantní oblast. Toto nám umožní zobrazovat hry ve vašem okolí",

  changeLater: "Toto nastavení budete moci později změnit",

  dateLocale: "en-En",

  setNotif: "Nastavení upozornění",

  //Setup Profile
  userInfoGuide:
    "Zde můžete stručně uvést, odkdy a jak často hrajete basketbal, případně s jakými hráči byste chtěli hrát",
  continue: "Pokračovat",

  passwordInput: "Heslo",
  confirmPassword: "Potvrdit heslo",
  emailInput: "Email",
  login: "Přihlásit se",
  signUp: "Registrovat",
  haveAcc: "Už máte účet?",
  noAcc: "Jěště nemáte účet?",

  hi: "Ahoj",
  changeRegion: "Změnit oblast",
  liveEvents: "Živé hry",
  plannedEvents: "Naplánované hry",
  endsAt: "Končí v",
  yourGames: "Tvé hry",
  favCourts: "Oblíbené hřiště",
  noFavCourts:
    "Zde si můžete přidat oblbené hřiště ve vašem okolí a my vás upozorníme, kdykoliv na nich někdo vytvoří novou hru.",
  exploreCourts: "Prozkoumat hřiště",
  createGame: "Vytvořit hru",
  moreCourtInfo: "Více...",
  openingHours: "Otevírací doba",
  artificial: "Umělý",
  concrete: "Beton",
  otherSurface: "Jiný",

  //Add a court
  describeCourt:
    "Popište stav hřiště, zejména povrch a koše. Pokud má hřiště otevírací dobu, zapište ji sem nebo ji vyfoťte.",
  basketsCount: "Počet košů",
  surfaceType: "Typ povrchu",
  addCourt: "Přidat hřiště",
  chooseImage: "Vybrat obrázek",
  takeImage: "Vyfotit obrázek",
  send: "Odeslat",
  howTo: "Jak vyfotit hřiště:",
  howToText:
    "-  Foťte hřiště  NALEŽATO \n\n-  Alespoň jednu fotku pro každý koš \n-  Pokud možno bez lidí \n-  Pokud má hřiště basektbalové čáry, zachyťte je na fotce",
  examplePhoto: "Příklad",
  understand: "Rozumím",
  images: "Fotky",
  chooseLocation: "Zadejte lokaci hřiště",

  //CourtInfo
  games: "Hry",

  //Creating a game
  nameOfEvent: "Název vaší hry",
  eventInfoInput:
    "Uveďte informace o hře. Je dobré napsat, na jaké úrovni basketbalu si chcete zahrát",
  chooseCourt: "Vyber hřiště",
  chooseThisCourt: "Vybrat hřiště",
  date: "Datum",
  from: "Od",
  to: "Do",
  public: "Otevřený",
  ageRestricted: "Věkově omezený",
  usersLimit: "Limit hřáčů",
  defaultUserLimit: "Žádný limit",
  publishGame: "Přejeďte pro zveřejnění",
  minAge: "Min věk",
  maxAge: "Max věk",
  notifExplanation:
    "Rádi bychom ti posílali důležitá oznámení, jako když někdo napíše novou zprávu do chatu nebo založí hru na jednom z vašich oblíbených hřišť.\n\n Jestli a jaké oznámení ti budou chodit si můžete později jednoduše zvolit v nastavení",
  allow: "Povolit",
  deny: "Zakázat",

  //Profile
  age: "Věk",
  name: "Uživatelské jméno",
  dateOfBirth: "Datum narození",
  userDescription: "Popis",
  settings: "Nastavení",
  logOut: "Odhlásit se",
  editProfile: "Upravit profil",
  saveChanges: "Uložit",
  password: "Heslo",
  description: "Popis",

  save: "Uložit",

  //Settings
  notifications: "Oznámení",
  favCourtNotif: "Něko vytvořil hru na jednom z tvých oblíbených hřišť",
  newChatNotif: "Nová zpráva v chatu",
  eventChangeNotif: "Nastavení hry, ke které seš připojen, se změnilo",
  usersChangeNotif: "Někdo se právě připojil k tvé hře / odešel z tvé hry",
  languageLabel: "Jazyk",

  other: "Další",
  feedback: "Pošli nám feedback",
  feedbackSent: "Feedback odeslán!",

  reportBug: "Nahlásit chybu",
  reportBugSent: "Informace o chybě poslány, děkujeme!",
  reportGuide: "Prosím přesně popiště, co jste udělali, aby k chybě došlo.",

  deleteAccount: "Trvale smazat profil",
  deleteAccountConfirm: `Napište "Delete" pro potvrzení`,
  deleteAccountDone: "Účet byl smazán",
  deleteBtn: "SMAZAT",
  deleteInfo: "Váš účet bude nevratně smazán",
  accountNotDeleted: "Účet nebyl smazán",

  signOutTitle:
    "Prosím odhlaste se a znovu se přihlašte, abyste mohli smazat svůj účet",
  singOutText:
    "Z bezpečnostních důvodů se musíte odhlásit a znovu přihlásit, aby jste mohli vymazat svůj účet",

  signOutText2:
    "Zkuste se prosím odhlásit a znovu přihlásit, a pak zkuste svůj účet znovu smazat.",

  cancel: "Cancel",

  information: "Informace",
  deleteChat: "Smazat",
  joinGame: "Přidat se",
  leaveGame: "Opustit hru",
  users: "Hřáči",
  yearsOld: "let",
  reportUser: "Nahlásit uživatele",
  reportText: "Uveďte důvod nahlášení uživatele",
  //   send: "Send",
  deleteEvent: "Smazat hru",
  editEvent: "Upravit událost",
  confirm: "Potvrdit",

  gamesLimit: "Můžete mít najednou jen 4 hry",

  removeUserEnsure: "Opravdu chcete odebrat uživatele",
  yes: "Ano",
  no: "Ne",

  logOutEnsure: "Opravdu se chcete odhlásit?",

  regular: "Velká(regulérní)",
  small: "Malá",

  boardSize: "Velikost desky",

  lines: "Čáry",
  ano: "Ano",
  ne: "Ne",

  lighting: "Osvětlení",
  fullCourt: "Celé hřiště",

  type: "Typ",
  indoor: "Vnitřní",
  outdoor: "Venkovní",

  access: "Přístup",
  public: "Veřejné",
  private: "Soukromé",
};
