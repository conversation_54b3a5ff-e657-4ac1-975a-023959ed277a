{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@config-plugins/android-jsc-intl": "^6.0.0", "@expo/config-plugins": "^7.2.5", "@expo/prebuild-config": "^6.2.6", "@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/datetimepicker": "7.2.0", "@react-native-community/slider": "4.4.2", "@react-navigation/bottom-tabs": "^6.5.4", "@react-navigation/elements": "^1.3.17", "@react-navigation/material-top-tabs": "^6.6.2", "@react-navigation/native": "^6.1.6", "@react-navigation/stack": "^6.3.12", "babel-preset-fbjs": "^3.4.0", "dotenv": "^16.3.1", "expo": "^49.0.0", "expo-asset": "~8.10.1", "expo-auth-session": "~5.0.2", "expo-build-properties": "~0.8.3", "expo-constants": "~14.4.2", "expo-crypto": "~12.4.1", "expo-dev-client": "~2.4.6", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-image-manipulator": "~11.3.0", "expo-image-picker": "~14.3.2", "expo-linear-gradient": "~12.3.0", "expo-location": "~16.1.0", "expo-media-library": "~15.4.1", "expo-notifications": "~0.20.1", "expo-permissions": "~14.2.1", "expo-random": "~13.2.0", "expo-server-sdk": "^3.7.0", "expo-status-bar": "~1.6.0", "firebase": "^10.0.0", "geolib": "^3.3.4", "jsc-android": "^294992.0.0", "metro-react-native-babel-transformer": "^0.66.0", "prop-types": "^15.8.1", "react": "18.2.0", "react-devtools": "^4.27.4", "react-dom": "18.2.0", "react-native": "0.72.3", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-gifted-chat": "^2.4.0", "react-native-image-picker": "^5.0.1", "react-native-image-resizer": "^1.4.5", "react-native-image-slider-box": "^2.0.7", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-maps": "1.7.1", "react-native-modal-datetime-picker": "^15.0.0", "react-native-pager-view": "6.2.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^3.5.2", "react-native-typing-animation": "0.1.7", "react-native-vector-icons": "^9.2.0", "react-native-web": "~0.19.6", "text-encoding": "^0.7.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.22.9"}, "private": true}