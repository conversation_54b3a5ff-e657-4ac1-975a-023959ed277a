import React, { useEffect, useState, useRef } from "react";
// import { Buffer } from "buffer";

import {
  StyleSheet,
  Text,
  View,
  Button,
  TextInput,
  Image,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  ScrollView,
  Dimensions,
  Pressable,
  Animated,
  TouchableWithoutFeedback,
  Keyboard,
  Modal,
  Linking,
} from "react-native";
import {
  createUserWithEmailAndPassword,
  signInWithCredential,
  GoogleAuthProvider,
} from "firebase/auth";
import { auth } from "../config/firebase";
import { db } from "../config/firebase";

import {
  collection,
  CollectionReference,
  doc,
  Firestore,
  getDocs,
  setDoc,
  updateDoc,
  increment,
} from "firebase/firestore";

import { red } from "./colors";
import { MaterialIcons } from "@expo/vector-icons";
import * as Font from "expo-font";

import * as Google from "expo-auth-session/providers/google";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

export default function Signup({ navigation }) {
  const [password, setPassword] = useState("");
  const [password2, setPassword2] = useState("");
  const [email, setEmail] = useState("");

  const [clicked, setClicked] = useState(false);
  const [signUpClicked, setSignUpClicked] = useState(false);

  const [imageLoaded, setImageLoaded] = useState(false);

  const [showPage, setShowPage] = useState(false);

  const [urls, setUrls] = useState();

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setShowPage(true);
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const infoCollection = collection(db, "info");
      const infoSnapshot = await getDocs(infoCollection);
      const infoData = infoSnapshot.docs.map((doc) => {
        setUrls(doc.data());
      });
    };

    fetchData();
  }, []);

  let tempImage;

  const ContinueLoadingCircle = () => {
    if (clicked) {
      return <ActivityIndicator size="large" />;
    } else {
      return (
        <Text
          style={{
            fontWeight: "bold",
            color: "rgba(197,42,71,1)",
            fontSize: 50,
          }}
        >
          Continue
        </Text>
      );
    }
  };

  let [fontsLoaded] = Font.useFonts({
    Roboto: require("../assets/fonts/Roboto-Regular.ttf"),
  });

  useEffect(() => {}, [clicked, signUpClicked]);

  const onHandleSignup = async () => {
    if (password !== password2) {
      alert("Hesla se neshodují");
    } else {
      if (email !== "" && password !== "") {
        setClicked(false);

        try {
          // await checkIfUserExists();

          createUserWithEmailAndPassword(auth, email, password)
            .then(async () => {
              navigation.navigate("SetupProfile");
            })
            .catch((err) => {
              setSignUpClicked(false);
              Alert.alert("Login error", err.message);
            });
        } catch (error) {
          alert(error);
        }
      }
    }
  };

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId:
      "99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n.apps.googleusercontent.com",
  });

  useEffect(() => {
    if (response?.type === "success") {
      const { id_token } = response.params;

      const credential = GoogleAuthProvider.credential(id_token);

      signInWithCredential(auth, credential)
        .then(async (userCredential) => {
          const user = userCredential.user;
        })
        .catch((error) => {
          alert("Error signing in with Google", error);
        });
    }
  }, [response]);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 2500,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const [signUpType, setSignUpType] = useState(null);

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={{ flex: 1, backgroundColor: "white" }}>
        {(!imageLoaded || !showPage || !fontsLoaded) && (
          <View
            style={{
              width: "100%",
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <ActivityIndicator></ActivityIndicator>
          </View>
        )}

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "80%",
                    // height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "0%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "400",
                        fontSize: 15,
                        color: "rgba(0,0,0,0.8)",
                        marginTop: 15,
                        lineHeight: 21,
                      }}
                    >
                      Please read the full
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.termsAndConditions}`)
                        }
                      >
                        {" "}
                        Terms and Conditions
                      </Text>
                      ,{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() => Linking.openURL(`${urls.privacyPolicy}`)}
                      >
                        {" "}
                        Privacy Policy
                      </Text>{" "}
                      and{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.copyrightPolicy}`)
                        }
                      >
                        {" "}
                        Copyright Policy
                      </Text>{" "}
                      before proceeding.
                    </Text>
                  </View>

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "9%",
                      marginBottom: "3%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(() => {
                          if (signUpType === "email") {
                            onHandleSignup();
                          }

                          if (signUpType === "google") {
                            promptAsync().catch((error) => {
                              console.error("Google auth error", error);
                            });
                          }
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        // width: "70%",
                        height: 40,
                        backgroundColor: red,
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "500",
                          alignSelf: "center",
                          color: "white",
                          fontSize: 14,
                          // fontFamily: "roboto",
                          paddingHorizontal: 15,
                        }}
                      >
                        I Understand and Agree
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <StatusBar
          translucent={true}
          backgroundColor={"transparent"}
          barStyle="dark-content"
          // hidden={true}
        />

        <KeyboardAvoidingView
          behavior={"height"}
          keyboardVerticalOffset={-200}
          // behavior="height"
          style={{
            width: "100%",
            backgroundColor: "rgba(0,0,0,0)",
            alignSelf: "center",
            // alignItems: "center",
          }}
          // enabled=[]
          // keyboardVerticalOffset={-180}
        >
          <Animated.View style={{ flex: 1, opacity: opacity1 }}></Animated.View>
          <SafeAreaView
            style={{ width: "100%", height: "100%", alignItems: "center" }}
          >
            <View
              style={{
                // position: "absolute",
                width: "100%",

                flex: 1,
                height: 300,
                bottom: 10,

                alignSelf: "center",
                justifyContent: "flex-end",
                // justifyContent: "center",
              }}
            >
              <View
                style={{
                  alignSelf: "center",
                  width: "100%",
                  height: "45%",
                  marginTop: "6%",
                  // top: 0,
                  // position: "absolute",
                  // marginLeft: -screenWidth,
                  // marginRight: 100,
                  // left: screenWidth * 0.047,
                }}
              >
                <Image
                  onLoadEnd={() => {
                    setImageLoaded(true);
                  }}
                  source={require("./../images/newlogo.png")}
                  style={{
                    height: screenWidth * 0.75,
                    width: screenHeight * 0.27,
                    alignSelf: "center",
                    // flex: 1,
                    // resizeMode: "contain",
                  }}
                />
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  width: "88%",
                  marginBottom: 5,
                  // marginLeft: -6,
                }}
              >
                <MaterialIcons
                  name="alternate-email"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Email"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoFocus={false}
                    value={email}
                    onChangeText={(text) => setEmail(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 10,
                  width: "88%",
                  marginBottom: 10,
                }}
              >
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Create a new password"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password}
                    onChangeText={(text) => setPassword(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 10,
                  width: "88%",
                  marginBottom: 15,
                }}
              >
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Confirm password"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password2}
                    onChangeText={(text) => setPassword2(text)}
                  />
                </View>
              </View>

              <Pressable
                onPress={() => {
                  if (
                    password !== password2 ||
                    password == "" ||
                    password2 == ""
                  ) {
                    alert("Passwords don't match");
                  } else {
                    setSignUpType("email");
                    toggleModal2();
                    // console.log(password !== password2);
                  }
                }}
                style={({ pressed }) => ({
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  marginTop: 35,
                  width: "90%",
                  alignItems: "center",
                  backgroundColor: red,
                  borderRadius: 10,
                  height: 45,
                  justifyContent: "center",
                  alignSelf: "center",
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    color: "white",
                    fontSize: 16,
                    marginTop: 2,
                  }}
                >
                  Sign Up
                </Text>
              </Pressable>

              <View
                style={{
                  marginTop: 12,
                  alignSelf: "center",
                  width: "92%",
                  flexDirection: "row",
                  justifyContent: "space-around",
                }}
              >
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
                <Text
                  style={{
                    fontWeight: "400",
                    fontSize: 13,
                    color: "rgba(0,0,0,0.5)",
                  }}
                >
                  OR
                </Text>
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
              </View>

              <Pressable
                onPress={() => {
                  setSignUpType("google");
                  toggleModal2();
                }}
                style={({ pressed }) => ({
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  marginTop: 12,
                  width: "90%",
                  alignItems: "center",
                  backgroundColor: "rgba(0,0,0,0.04)",
                  borderRadius: 10,
                  height: 45,
                  justifyContent: "center",
                  alignSelf: "center",
                })}
              >
                <Text
                  style={{
                    fontWeight: "600",
                    color: "rgba(0,0,0,0.4)",
                    fontSize: 14,
                    // fontFamily: "Avenir-Light",
                    marginTop: 2,
                  }}
                >
                  Continue with Google
                </Text>
                <Image
                  style={{
                    height: 27,
                    width: 27,
                    position: "absolute",
                    left: 20,
                  }}
                  source={require("../images/google.png")}
                />
              </Pressable>

              <View
                style={{
                  marginTop: 25,
                  flexDirection: "row",
                  alignItems: "center",
                  alignSelf: "center",
                }}
              >
                <Text
                  style={{
                    color: "rgba(0,0,0,0.4)",
                    fontWeight: "600",
                    fontSize: 14,
                  }}
                >
                  Already have an account?
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("Login")}>
                  <Text
                    style={{
                      color: red,
                      fontWeight: "600",
                      fontSize: 14,
                      marginLeft: 7,
                    }}
                  >
                    Log In
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </SafeAreaView>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
}
