import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Pressable,
  Platform,
  ScrollView,
  FlatList,
  ImageBackground,
  Animated,
  Dimensions,
  TouchableHighlight,
  Modal,
  Appearance,
  StatusBar,
  // SafeAreaView,
  TouchableWithoutFeedback,
} from "react-native";

// } from "firebase/auth";
import { auth, db } from "../config/firebase";
// import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  collectionGroup,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  where,
  onSnapshot,
  arrayRemove,
  writeBatch,
  deleteDoc,
} from "firebase/firestore";

import { red } from "./colors";
import { LinearGradient } from "expo-linear-gradient";

import { Ionicons } from "@expo/vector-icons";
// import { Feather } from "@expo/vector-icons";
import { FontAwesome } from "@expo/vector-icons";
import { MaterialCommunityIcons } from "@expo/vector-icons";

import * as Device from "expo-device";
import * as Notifications from "expo-notifications";

import { EN, CZ } from "./../assets/strings";

import {
  useSafeAreaInsets,
  SafeAreaView,
} from "react-native-safe-area-context";

import FastImage from "react-native-fast-image";

import Constants from "expo-constants";

const Stack = createStackNavigator();
const MyGamesDataContext = createContext({});

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

// const hristeimg = require("./../images/court2.jpg");

const MyGamesDataContextProvider = ({ children }) => {
  const [creatingGame, setCreatingGame] = useState(false);

  const obj = {
    creatingGame: creatingGame,
    setcreatingGame: setCreatingGame,
  };

  return (
    <MyGamesDataContext.Provider value={obj}>
      {children}
    </MyGamesDataContext.Provider>
  );
};

function EventItem(props) {
  const fadeInOpacity = useRef(new Animated.Value(0)).current;
  // const positionValue = useRef(new Animated.Value(-20)).current;
  const scaleValue = useRef(new Animated.Value(0.95)).current;
  const [imageLoaded, setImageLoaded] = useState(false);

  const widthValue = useRef(new Animated.Value(1)).current;

  const opacityValue = useRef(new Animated.Value(0.95)).current;

  const duration = 1250;

  const date5 = new Date(props.value?.timeEnd?.seconds * 1000);
  const hours = date5.getHours().toString().padStart(2, "0");
  const minutes = date5.getMinutes().toString().padStart(2, "0");

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: duration,
          useNativeDriver: false,
        }),
        Animated.timing(opacityValue, {
          toValue: 0.95,
          duration: duration,
          useNativeDriver: false,
        }),
      ])
    ).start();
  }, []);

  const opacityValue2 = useRef(new Animated.Value(0.1)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(opacityValue2, {
          toValue: 0.7,
          duration: duration,
          useNativeDriver: false,
        }),
        Animated.timing(opacityValue2, {
          toValue: 0.1,
          duration: duration,
          useNativeDriver: false,
        }),
      ])
    ).start();
  }, []);

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(widthValue, {
          toValue: 1.087,
          duration: duration,
          useNativeDriver: false,
        }),
        Animated.timing(widthValue, {
          toValue: 1,
          duration: duration,
          useNativeDriver: false,
        }),
      ])
    ).start();
  }, [props.value.isLive]);

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(fadeInOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
      // Animated.timing(positionValue, {
      //   toValue: 0,
      //   duration: 300,
      //   useNativeDriver: true,
      // }).start();
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);

  Image.getSize(props.value.courtObj.imageRefs[0], (width, height) => {
    const aspectRatio = width / height;

    const fixedWidth =
      props.totalEvents === 1 ? screenWidth * 0.913 : screenWidth * 0.871;
    const calculatedHeight = fixedWidth / aspectRatio;

    setWidth(fixedWidth);
    setHeight(calculatedHeight);
  });

  if (props.value.creator === undefined) {
    return <View style={{ height: 130 }}></View>;
  }

  return (
    <View
      key={props.value.eventID}
      style={{
        width: screenWidth / 0.89,
        height: 200,
        // marginRight: ,
        marginRight:
          // props.totalEvents === props.index + 1
          //   ? -screenWidth / 6
          //   :
          -screenWidth / 4.8,
      }}
    >
      <Animated.View
        key={props.value.eventID + "-animated-view"}
        style={{
          marginTop: 17,
          width: props.totalEvents === 1 ? "89%" : "85%",
          // width: "85%",

          opacity: fadeInOpacity,
          transform: [
            // { translateX: positionValue },
            { scale: scaleValue },
          ],
          // marginRight: -50,
          // justifyContent: "space-around",
          // alignContent: "space-evenly",
          // alignSelf: "center",
        }}
      >
        <Pressable
          style={({ pressed }) => ({
            alignSelf: "center",
            height: imageLoaded ? screenWidth / 1.5 : 160,
            width: "92%",
            height: "auto",
            borderRadius: 17,
            borderColor: "rgba(70,70,70,1)",
            borderWidth: 1.5,
            backgroundColor: "white",
            shadowColor: "grey",
            shadowRadius: 2,
            shadowOpacity: 1,
            shadowOffset: {
              height: 3,
            },
            transform: [{ scale: pressed ? 0.99 : 1 }],
            // opacity: [{ scale: pressed ? 0.995 : 1 }],
          })}
          onPress={() => {
            props.navigation.navigate("Event", {
              eventInfo: props.value,
              language: props.language,
            });
          }}
        >
          <FastImage
            onLoadEnd={() => setImageLoaded(true)}
            style={{
              // width: 320,
              // height: 190,
              width: width,
              height: height,
              borderRadius: 15.2,
              overflow: "hidden",
              borderColor: "black",
            }}
            resizeMode="contain"
            // source={hristeimg}
            source={{ uri: props.value.courtObj.imageRefs[0] }}

            // source={require("./../images/cz-1.5.jpg")}
          >
            {props.value.isLive && (
              <View>
                <Animated.View
                  style={{
                    borderRadius: 8,
                    position: "absolute",
                    // marginLeft: 10,
                    marginTop: 7,
                    alignSelf: "center",
                    // width: widthValue.interpolate({
                    //   inputRange: [50, 100],
                    //   outputRange: ["50%", "100%"],
                    // }),
                    width: "40%",
                    height: 18,
                    backgroundColor: red,
                    shadowColor: red,
                    shadowOpacity: opacityValue2,
                    shadowRadius: 5,
                    shadowOffset: { width: 0, height: 0 },
                    justifyContent: "center",
                    opacity: opacityValue,
                    transform: [
                      {
                        scaleX: widthValue.interpolate({
                          inputRange: [1, 1.087],
                          outputRange: [1, 1.087],
                        }),
                      },
                    ],
                  }}
                ></Animated.View>
                <Text
                  style={{
                    position: "absolute",
                    marginTop: 7,
                    fontWeight: "800",
                    alignSelf: "center",
                    color: "white",
                    fontSize: 13,
                    // fontFamily: "roboto",
                  }}
                >
                  Live
                </Text>
              </View>
            )}
            <View
              style={{
                position: "absolute",
                right: -3,
                top: -2,
                borderTopRightRadius: 19,
                borderTopLeftRadius: 3,
                borderBottomRightRadius: 3,
                borderBottomLeftRadius: 36,
                shadowColor: "rgba(0,0,0,1)",
                shadowOffset: {
                  width: 0,
                  height: 0,
                },
              }}
            >
              <View
                style={{
                  paddingTop: 4,
                  paddingBottom: 4,
                  backgroundColor: "rgba(2,2,2,0.6)",
                  paddingLeft: 16,
                  paddingRight: 10,
                  borderTopRightRadius: 19,
                  borderTopLeftRadius: 3,
                  borderBottomRightRadius: 3,
                  borderBottomLeftRadius: 36,
                }}
              >
                {!props.value.usersLimit ? (
                  <View
                    style={{
                      flexDirection: "row",
                      // alignContent: "space-between",
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "700",
                        color: "white",
                      }}
                    >{`${props.value.users.length}`}</Text>
                    <Ionicons
                      name="people"
                      size={15}
                      color="white"
                      style={{ paddingHorizontal: 4, paddingTop: 1 }}
                    />
                  </View>
                ) : (
                  <View
                    style={{
                      flexDirection: "row",
                      // alignContent: "space-between",
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: "700",
                        color: "white",
                      }}
                    >{`${props.value.users.length}/${props.value.usersLimit}`}</Text>
                    <Ionicons
                      name="people"
                      size={15}
                      color="white"
                      style={{ paddingHorizontal: 4 }}
                    />
                  </View>
                )}
              </View>
            </View>
            {props.value.creator === auth.currentUser.uid && (
              <View
                style={{
                  position: "absolute",
                  left: -3,
                  borderTopLeftRadius: 19,
                  borderTopRightRadius: 3,
                  borderBottomLeftRadius: 3,
                  borderBottomRightRadius: 36,
                  shadowColor: "rgba(0,0,0,1)",
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                }}
              >
                <View
                  style={{
                    paddingTop: 4,
                    paddingBottom: 4,
                    backgroundColor: "rgba(2,2,2,0.6)",
                    paddingLeft: 16,
                    paddingRight: 10,
                    borderTopLeftRadius: 19,
                    borderTopRightRadius: 3,
                    borderBottomLeftRadius: 3,
                    borderBottomRightRadius: 36,
                  }}
                >
                  <MaterialCommunityIcons
                    name="crown"
                    size={22}
                    color="#ffc005"
                    style={{
                      // position: "absolute",
                      alignSelf: "center",
                      marginLeft: -6,
                      marginTop: -2,
                    }}
                  />
                </View>
              </View>
            )}

            <View style={{ flex: 1, justifyContent: "flex-end" }}>
              <LinearGradient
                colors={[
                  "rgba(0,0,0,0)",
                  "rgba(0,0,0,0.1)",
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                  "rgba(0,0,0,0.2)",
                ]}
                style={{
                  flex: 0.23,
                  paddingTop: 10,
                  marginBottom: -1,
                  flexDirection: "row",
                }}
              >
                <View
                  style={{
                    flex: 1,
                    // flexDirection: "row",
                    alignItems: "center",
                    // justifyContent: "space-between",
                    width: "40%",
                    marginLeft: 3,
                    marginBottom: 0,
                    bottom: -13,
                  }}
                >
                  <View
                    style={{
                      alignSelf: "flex-start",
                      // justifyContent: "center",
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 13,
                        fontWeight: "bold",
                        marginLeft: 6,
                        color: "white",
                        maxWidth: "100%",

                        // color: props.value.isLive ? "red" : "blue",
                      }}
                      numberOfLines={1}
                      ellipsizeMode="tail"
                    >
                      {`${props.value.eventName}`}
                    </Text>
                  </View>
                  <View style={{ alignSelf: "flex-start" }}>
                    {props.value.isLive ? (
                      <Text
                        style={{
                          fontSize: 13,
                          marginLeft: 7,
                          color: "white",
                          fontWeight: 400,
                          top: 1,
                        }}
                      >
                        {props.language.endsAt} {hours}:{minutes}
                        {/* {formatDate(
                                  props.value.timeStart,
                                  props.value.timeEnd
                                )} */}
                      </Text>
                    ) : (
                      <Text
                        style={{
                          fontSize: 13,
                          marginLeft: 7,
                          color: "white",
                          fontWeight: 400,
                          // bottom: -3,
                          top: 1,
                        }}
                      >
                        {/* {props.formatDate(
                            props.value.timeStart,
                            props.value.timeEnd
                          )} */}
                        {`${new Date(
                          props.value.timeStart.seconds * 1000
                        ).getDate()}. ${
                          new Date(
                            props.value.timeStart.seconds * 1000
                          ).getMonth() + 1
                        }. ${(
                          "0" +
                          new Date(
                            props.value.timeStart.seconds * 1000
                          ).getHours()
                        ).slice(-2)}:${(
                          "0" +
                          new Date(
                            props.value.timeStart.seconds * 1000
                          ).getMinutes()
                        ).slice(-2)}-${(
                          "0" +
                          new Date(
                            props.value.timeEnd.seconds * 1000
                          ).getHours()
                        ).slice(-2)}:${(
                          "0" +
                          new Date(
                            props.value.timeEnd.seconds * 1000
                          ).getMinutes()
                        ).slice(-2)} ${props.language.weekdays[
                          new Date(props.value.date.seconds * 1000).getDay()
                        ].slice(0, 3)}.`}
                      </Text>
                    )}
                  </View>
                </View>
                <View
                  style={{
                    // height: "100%",
                    height: "100%",
                    width: "5%",
                    // alignSelf: "flex-start",
                    // position: "absolute",
                    // marginLeft: "63.5%",
                    marginTop: 10,
                  }}
                >
                  <Image
                    style={{
                      width: 35,
                      // aspectRatio: 1,
                      height: 35,
                      // height: "100%",
                      // height: 35,
                      // marginLeft: "50%",
                      // marginLeft: "",
                      // position: "absolute",
                      overflow: "visible",
                    }}
                    source={require("./../images/white-marker.png")}
                  />
                </View>

                <View
                  style={{
                    width: "35%",
                    height: 67,
                    // position: "absolute",
                    justifyContent: "center",
                    marginLeft: 5,
                    paddingLeft: 12,
                    // alignSelf: "center",
                  }}
                >
                  <Text
                    style={{
                      // flexShrink: 1,
                      color: "white",
                      marginBottom: 5,
                      maxWidth: "90%",
                      fontWeight: "500",
                      fontSize: 10,
                      // alignSelf: "center",
                      textAlign: "center",
                      // alignSel
                    }}
                    numberOfLines={3}
                    ellipsizeMode="tail"
                  >
                    {props.value.courtObj.address}
                    {/* aaaaaaaaaa */}
                  </Text>
                </View>
              </LinearGradient>
            </View>
          </FastImage>
        </Pressable>
      </Animated.View>
      {!imageLoaded && (
        <View
          key={props.value.eventID}
          style={{
            position: "absolute",
            width: screenWidth / 0.89,
            height: "90%",
            // backgroundColor: "red",
            marginRight:
              props.totalEvents === props.index + 1
                ? -screenWidth / 6
                : -screenWidth / 4.8,
          }}
        >
          <View
            key={props.value.eventID + "-animated-view-indicator"}
            style={{
              marginTop: 17,
              // backgroundColor: "red",
              width: props.totalEvents === 1 ? "88%" : "85%",
            }}
          >
            <View
              style={{
                alignSelf: "center",
                height: screenWidth / 1.5,
                width: "92%",
                borderRadius: 17,

                // backgroundColor: "red",
              }}
            >
              <ActivityIndicator
                style={{ marginTop: "30%" }}
              ></ActivityIndicator>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const CourtItem = React.memo(function CourtItem({
  value,
  courts,
  setCourts,
  index,
  totalCourts,
  navigation,
  removeFromFavourites,
  language,
}) {
  const opacity1 = useRef(new Animated.Value(0)).current;
  // const positionValue = useRef(new Animated.Value(-20)).current;

  const [imageLoaded, setImageLoaded] = useState(false);

  const opacityValue = useRef(new Animated.Value(0.95)).current;

  // const duration = 1250;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const ImageDiv = useMemo(() => {
    return (
      <FastImage
        source={{ uri: value.imageRefs[0] }}
        // source={require("./../images/cz-1.4.jpg")}
        style={{
          height: 125,
          width: "100%",
          borderRadius: 9,
          alignSelf: "center",
        }}
        onLoadEnd={() => {
          setImageLoaded(true);
        }}
      />
    );
  }, [imageLoaded]);

  return (
    <View style={{ width: "100%" }}>
      <View>
        {!imageLoaded && (
          <ActivityIndicator
            style={{
              // bottom: -70,
              // height: 80,
              marginTop: 50,
              marginBottom: -70,
              zIndex: 2,
              opacity: 1,
            }}
          />
        )}
        <Animated.View style={{ opacity: opacity1 }}>
          <Pressable
            onPress={() => {
              const newCourts = courts.filter((court) => court.id !== value.id);
              removeFromFavourites(value);
              setCourts(newCourts);
            }}
            style={{ zIndex: 3 }}
          >
            <FontAwesome
              name="star"
              size={26}
              color="#fcc205"
              style={{
                position: "absolute",
                right: 25,
                top: 20,
              }}
            />
          </Pressable>

          <Pressable
            style={{
              width: "100%",
              marginTop: 10,

              paddingBottom: index == totalCourts - 1 ? 50 : 0,
            }}
            // onPress={() => {
            //   navigation.navigate("CourtInfo", value);
            // }}
            onPress={() => {
              navigation.navigate("CourtInfo", {
                selectedMarker: value,
                language: language,
              });
            }}
          >
            <View
              style={{
                width: "100%",
                alignItems: "center",
              }}
            >
              <View
                style={{
                  // height: 10,
                  width: "92%",
                  borderRadius: 10,
                  overflow: "hidden",
                  position: "relative",
                  borderWidth: 1,
                  borderColor: "rgba(0,0,0,1)",
                }}
              >
                <View>
                  {ImageDiv}

                  <LinearGradient
                    colors={[
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.30)",
                      "rgba(0,0,0,0.2)",
                      // "rgba(0,0,0,0.1)",
                      "rgba(0,0,0,0)",
                    ]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={{
                      position: "absolute",
                      left: 0,
                      top: 0,
                      paddingTop: 5,
                      // height: 135,
                      height: "100%",
                      width: "50%",
                    }}
                  >
                    <Image
                      source={require("./../images/white-marker.png")}
                      style={{
                        width: 55,
                        height: 55,
                        marginTop: 3,
                        marginLeft: "29%",
                        overflow: "visible",
                      }}
                    />
                    <View
                      style={{
                        width: "80%",
                        height: 60,
                        // position: "absolute",
                        justifyContent: "center",
                        marginLeft: 5,
                        paddingLeft: 11,
                        // alignSelf: "center",
                      }}
                    >
                      <Text
                        style={{
                          // flexShrink: 1,
                          color: "white",
                          marginBottom: 5,
                          // maxWidth: "90%",
                          fontWeight: "600",
                          fontSize: 11,
                          // alignSelf: "center",
                          textAlign: "center",
                          // alignSel
                        }}
                        numberOfLines={3}
                        ellipsizeMode="tail"
                      >
                        {value.address}
                      </Text>
                    </View>
                  </LinearGradient>
                </View>
              </View>
            </View>
          </Pressable>
        </Animated.View>
      </View>
    </View>
  );
});

async function registerForPushNotificationsAsync(permission) {
  let token;
  try {
    if (Device.isDevice) {
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== "granted") {
        alert("Failed to get push token for push notification!");
        return;
      }

      // token = (
      //   await Notifications.getExpoPushTokenAsync({
      //     projectId: Constants.expoConfig.extra.eas.projectId,
      //   })
      // ).data;

      //       token = await Notifications.getExpoPushTokenAsync({
      //   projectId: Constants.expoConfig.extra.eas.projectId,
      // });

      token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig.extra.eas.projectId,
      });
    } else {
      alert("Must use physical device for Push Notifications");
    }

    if (Platform.OS === "android") {
      Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }

    await updateDoc(doc(db, "users/", auth.currentUser.uid), {
      notificationsToken: token.data,
      notifications: {
        allowed: permission,
        chat: true,
        eventChange: true,
        favCourt: true,
        usersChange: true,
      },
    });
  } catch (error) {
    alert(error);
  }

  return token;
}

function RootMyGamesStack() {
  const insets = useSafeAreaInsets();

  const MyGames = () => {
    const navigation = useNavigation();

    const [language2, setLanguage2] = useState(null);
    const [language, setLanguage] = useState(null);

    useLayoutEffect(() => {
      navigation.setOptions({
        // headerBackVisible: false,
        headerShown: false,

        //   title: "Choose a court",
        //   headerTintColor: "black",
        headerTitle: () => (
          <Text
            style={{
              fontFamily: "BungeeInline-Regular",
              fontSize: 22,
              // color: "rgba(0,0,0,0.7)",
              color: red,
              // overflow: "visible",
            }}
          >
            My games
          </Text>
        ),

        headerStyle: {
          // backgroundColor: red,
          // height: 90,
          // backgroundColor: "rgba(197,42,71,1)",
        },
      });
    }, [navigation]);

    useEffect(() => {
      const docRef = doc(db, "users", auth.currentUser.uid);

      const unsubscribe = onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists()) {
          const doc = docSnap.data();

          // if (doc.language !== language2) {
          setLanguage2(doc.language);
          // }
        }
      });

      return () => unsubscribe();
    }, []);

    useEffect(() => {
      if (language2 === "CZ") {
        setLanguage(CZ);
      } else {
        setLanguage(EN);
      }
    }, [language2, language]);

    function formatDate(date1, dateEnd) {
      const date = new Date(date1 * 1000);
      const day = date.getDate();
      const month = date.getMonth() + 1;
      const hours = date.getHours();
      const minutes = String(date.getMinutes()).padStart(2, "0");

      const date2 = new Date(dateEnd * 1000);
      const day2 = date2.getDate();
      const month2 = date2.getMonth() + 1;
      const minutes2 = String(date2.getMinutes()).padStart(2, "0");

      const weekday = new Date(date1.seconds).toLocaleDateString("cs-CZ", {
        weekday: "long",
      });

      return `${weekday} ${day}.${month}. ${hours}:${minutes}-${hours2}:${minutes2} `;
    }

    const opacityValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      if (language) {
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 180,
          useNativeDriver: true,
        }).start();
      }
    }, [language]);

    if (!language) {
      return (
        <View
          style={{ backgroundColor: "white", height: "100%", width: "100%" }}
        >
          <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
          <View
            style={{
              width: "100%",
              height: 0.8,
              backgroundColor: "lightgrey",
              position: "absolute",
              bottom: 80,
            }}
          ></View>
        </View>
      );
    }

    const EventsList = () => {
      const [events, setEvents] = useState([]);
      const [initalLoad, setInitialLoad] = useState(false);

      const [userEventsCount, setUserEventsCount] = useState(null);

      useEffect(() => {
        const coll = collection(
          db,
          `events/${auth.currentUser.uid}/user_events`
        );
        const unsubscribe = onSnapshot(coll, (snapshot) => {
          const count = snapshot.docs.length;

          if (count > 0) {
            setImageLoaded(true);
          }
          setTimeout(() => {
            setUserEventsCount(count);
          }, 2000);
        });

        return () => {
          unsubscribe();
        };
      }, []);

      useEffect(() => {
        const q = query(collectionGroup(db, "user_events"));

        const unsubscribe = onSnapshot(q, async (querySnapshot) => {
          let docsPromises;

          if (initalLoad) {
            docsPromises = querySnapshot
              .docChanges()
              .filter((change) => change.type === "modified")
              .map(async (change) => {
                const event = change.doc;
                const userCollectionRef = collection(
                  db,
                  `${event.ref.path}/eventUsers`
                );
                const userEventsSnapshot = await getDocs(userCollectionRef);

                const users = userEventsSnapshot.docs.map(
                  (doc) => doc.data().userID
                );

                if (!users.includes(auth.currentUser.uid)) {
                  return { id: event.id, remove: true };
                } else {
                  return { id: event.id, ...event.data(), users: users };
                }
              });
          } else {
            docsPromises = querySnapshot
              .docChanges()
              .filter((change) => change.type !== 1)
              .map(async (change) => {
                const event = change.doc;
                const userCollectionRef = collection(
                  db,
                  `${event.ref.path}/eventUsers`
                );
                const userEventsSnapshot = await getDocs(userCollectionRef);

                const users = userEventsSnapshot.docs.map(
                  (doc) => doc.data().userID
                );

                if (!users.includes(auth.currentUser.uid)) {
                  return { id: event.id, remove: true };
                } else {
                  return { id: event.id, ...event.data(), users: users };
                }
              });

            setInitialLoad(true);
          }

          const docs = await Promise.all(docsPromises);

          const removedIds = docs
            .filter((doc) => doc.remove)
            .map((doc) => doc.id);

          const addedUpdatedEvents = docs
            .filter((doc) => !doc.remove)
            .sort((a, b) => a.timeStart - b.timeStart);

          setEvents((events) => {
            let currentEvents = events.filter(
              (event) => !removedIds.includes(event.id)
            );

            addedUpdatedEvents.forEach((newEvent) => {
              const index = currentEvents.findIndex(
                (event) => event.id === newEvent.id
              );
              if (index === -1) {
                currentEvents.push(newEvent);
              } else {
                currentEvents[index] = newEvent;
              }
            });

            currentEvents.sort((a, b) => a.timeStart - b.timeStart);

            return currentEvents;
          });
        });

        return unsubscribe;
      }, []);

      const [imageLoaded, setImageLoaded] = useState(null);

      const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

      useEffect(() => {
        if (imageLoaded || userEventsCount !== null) {
          Animated.timing(modalAnimatedValue2, {
            toValue: 1,
            duration: 50,
            useNativeDriver: true,
          }).start();
        }
      }, [imageLoaded, userEventsCount]);

      if (userEventsCount === null) {
        return (
          <View
            style={{
              width: "100%",
              height: "120%",
              // backgroundColor: "red",
            }}
          >
            <ActivityIndicator style={{ marginTop: "50%" }}></ActivityIndicator>
          </View>
        );
      }

      return (
        <View
          style={{
            width: "100%",
            height: "52%",
            // marginTop: 15,
          }}
        >
          <Text
            style={{
              // fontFamily: "HelveticaNeue-Medium",
              // fontFamily: "AvenirNext-DemiBold",
              fontFamily: "Nunito-Bold",

              fontSize: 28,
              color: "rgba(0,0,0,0.87)",
              overflow: "visible",
              // height: "100%",
              paddingLeft: 18,
              // paddingTop: "18%",
              // marginBottom: -7,
              marginTop: "3%",
            }}
          >
            {language.yourGames}
          </Text>
          <TouchableHighlight
            style={{ position: "absolute", marginTop: "3%", right: "6%" }}
            onPress={() =>
              userEventsCount <= 4
                ? navigation.navigate("MyGamesStack")
                : alert(language.gamesLimit)
            }
            underlayColor="rgba(0, 0, 0, 0)"
          >
            <AntDesign name="plus" size={35} color="black" />
          </TouchableHighlight>
          <Animated.View
            style={{
              width: "100%",
              height: "100%",
              marginTop: 0,
              // position: "absolute",
              // bottom: 60,
              opacity: modalAnimatedValue2,
            }}
          >
            {events.length > 0 ? (
              <FlatList
                data={events}
                keyExtractor={(item, index) => item.eventID}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                renderItem={({ item, index }) => {
                  if (item.key === "comp") {
                    return <View style={{ height: 115 }}></View>;
                  } else {
                    return (
                      <EventItem
                        value={item}
                        index={index}
                        totalEvents={events.length}
                        navigation={navigation}
                        formatDate={formatDate}
                        language={language}
                        key={item.eventID}
                      />
                    );
                  }
                }}
                style={{
                  width: "100%",
                  height: "100%",
                  marginTop: "2%",
                  paddingRight: 10,
                }}
                contentContainerStyle={{ paddingRight: "5%" }}
              />
            ) : (
              <FastImage
                source={require("./../images/11.png")}
                style={{
                  height: screenHeight / 3,
                  width: screenWidth * 0.83,
                  alignSelf: "center",
                  marginTop: "5%",
                }}
                resizeMode="cover"
                onLoadEnd={() => {
                  setImageLoaded(true);
                }}
              >
                <Pressable
                  onPress={() => {
                    navigation.navigate("MyGamesStack");
                  }}
                  style={({ pressed }) => ({
                    borderRadius: 8,
                    transform: [{ scale: pressed ? 0.97 : 1 }],
                    width: "60%",
                    height: 40,
                    backgroundColor: red,
                    // shadowColor: "white",
                    // shadowOpacity: 10,
                    // shadowRadius: 10,
                    // shadowOffset: { width: 0, height: 0 },
                    justifyContent: "center",
                    // marginBottom: 30,
                    // bottom: 6,
                    alignSelf: "center",
                    marginTop: "33.5%",
                  })}
                >
                  <Text
                    style={{
                      fontWeight: "600",
                      alignSelf: "center",
                      color: "white",
                      fontSize: 15,
                      // fontFamily: "roboto",
                    }}
                  >
                    {language.createGame}
                  </Text>
                </Pressable>
              </FastImage>
            )}
          </Animated.View>
        </View>
      );
    };

    const FavouriteCourtsList = () => {
      const [courts, setCourts] = useState([]);
      // const { eventInfo, setEventInfo } = useContext(AuthenticatedUserContext);

      const [isModalVisible2, setModalVisible2] = useState(false);

      const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

      const toggleModal2 = () => {
        if (isModalVisible2) {
          Animated.timing(modalAnimatedValue2, {
            toValue: 0,
            duration: 150,
            useNativeDriver: true,
          }).start(() => setModalVisible2(false));
        } else {
          setModalVisible2(true);
          Animated.timing(modalAnimatedValue2, {
            toValue: 1,
            duration: 150,
            useNativeDriver: true,
          }).start();
        }
      };

      useEffect(() => {
        const docRef = doc(db, "users", auth.currentUser.uid);

        const unsubscribe = onSnapshot(docRef, (docSnap) => {
          if (docSnap.exists()) {
            if (!docSnap.data().notificationsToken) {
              toggleModal2();
            }
          } else {
          }
        });

        return () => {
          unsubscribe();
        };
      }, []);

      useEffect(() => {
        const q = query(collection(db, "courts-cz1"));
        const currentUserRef = doc(db, "users", auth.currentUser.uid);

        let userFavouriteCourts = [];

        const fetchCourts = async () => {
          const querySnapshot = await getDocs(q);
          const docs = [];
          querySnapshot.forEach((doc) => {
            if (doc.data().storage_id) {
              const courtData = doc.data();
              const isFavourite = userFavouriteCourts.includes(doc.id);
              if (isFavourite) {
                docs.push({
                  id: doc.id,
                  // pfp: courtData.courtImageRef || "",
                  // nazev: courtData.name || "",
                  coords: [courtData.coords[1], courtData.coords[0]],
                  imageRefs: courtData.imageRefs,
                  address: courtData.address,
                  isFavourite,
                  storage_id: courtData.storage_id,
                  description: courtData.description,
                  baskets: courtData.baskets,
                  surface: courtData.surface,
                });
              }
            }
          });

          setCourts(docs);
        };

        const unsubscribe = onSnapshot(q, fetchCourts);

        const userUnsubscribe = onSnapshot(currentUserRef, (doc) => {
          userFavouriteCourts = doc.data().favouriteCourts || [];

          fetchCourts();
        });

        return () => {
          unsubscribe();
          userUnsubscribe();
        };
      }, []);

      const removeFromFavourites = (value) => {
        const userRef = doc(db, "users", auth.currentUser.uid);

        getDoc(userRef)
          .then((docSnapshot) => {
            if (docSnapshot.exists()) {
              const userData = docSnapshot.data();

              updateDoc(userRef, {
                favouriteCourts: arrayRemove(value.id),
              })
                .then(() => {
                  const subscribedUsersCollection = collection(
                    db,
                    `courts-cz1/${value.id}/subscribedUsers`
                  );
                  const q = query(
                    subscribedUsersCollection,
                    where("userID", "==", auth.currentUser.uid)
                  );

                  getDocs(q)
                    .then((querySnapshot) => {
                      querySnapshot.forEach((document) => {
                        const docToDelete = doc(
                          db,
                          `courts-cz1/${value.id}/subscribedUsers`,
                          document.id
                        );
                        deleteDoc(docToDelete).catch((error) => {
                          Alert.alert(error.message);
                        });
                      });
                    })
                    .catch((error) => {
                      Alert.alert(error.message);
                    });
                })
                .catch((error) => {
                  Alert.alert(error.message);
                });
            } else {
            }
          })
          .catch((error) => {
            Alert.alert(error.message);
          });
      };

      return (
        <View
          style={{
            width: "100%",
            height: "45%",
            marginTop: 15,
            // position: "absolute",
            // bottom: 60,
          }}
        >
          {courts.length > 0 ? (
            <View style={{ width: "100%", height: "100%" }}>
              <ScrollView
                style={{ width: "100%", height: "100%" }}
                horizontal={false}
                showsVerticalScrollIndicator={false}
              >
                {courts.map((court, index) => {
                  return (
                    <CourtItem
                      key={court.id}
                      value={court}
                      index={index}
                      totalCourts={courts.length}
                      courts={courts}
                      setCourts={setCourts}
                      navigation={navigation}
                      removeFromFavourites={removeFromFavourites}
                      language={language}
                    />
                  );
                })}
              </ScrollView>

              <LinearGradient
                colors={[
                  "rgba(255, 255, 255, 0.99)",
                  "rgba(255, 255, 255, 0.97)",
                  "rgba(255, 255, 255, 0.93)",
                  "rgba(255, 255, 255, 0.90)",
                  "rgba(255, 255, 255, 0.87)",
                  "rgba(255, 255, 255, 0.85)",
                  "rgba(255, 255, 255, 0.80)",
                  "rgba(255, 255, 255, 0.75)",
                  "rgba(255, 255, 255, 0.70)",
                  "rgba(255, 255, 255, 0.6)",
                  "rgba(255, 255, 255, 0.5)",
                  // "rgba(255, 255, 255, 0.3)",
                  // "rgba(255, 255, 255, 0.2)",
                  "rgba(255, 255, 255, 0.0)",
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                style={{
                  position: "absolute",
                  left: 0,
                  right: 0,
                  top: 0,
                  height: 5,
                }}
              />
            </View>
          ) : (
            <View
              style={{
                width: "80%",
                height: "100%",

                alignSelf: "center",
              }}
            >
              <Text
                style={{
                  marginTop: "26%",
                  fontSize: 14,
                  fontWeight: 400,
                  textAlign: "center",
                }}
              >
                {language.noFavCourts}
              </Text>

              <Pressable
                onPress={() => {
                  navigation.navigate("Map");
                }}
                style={({ pressed }) => ({
                  borderRadius: 8,
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                  width: "60%",
                  height: 40,
                  backgroundColor: red,
                  shadowColor: red,
                  shadowOpacity: 0.1,
                  shadowRadius: 5,
                  shadowOffset: { width: 0, height: 0 },
                  justifyContent: "center",
                  // marginBottom: 30,
                  // bottom: 6,
                  alignSelf: "center",
                  marginTop: "10%",
                })}
              >
                <Text
                  style={{
                    fontWeight: "600",
                    alignSelf: "center",
                    color: "white",
                    fontSize: 15,
                    // fontFamily: "roboto",
                  }}
                >
                  {language.exploreCourts}
                </Text>
              </Pressable>
            </View>
          )}
          <Modal
            animationType="none"
            transparent={true}
            visible={isModalVisible2}
            onRequestClose={toggleModal2}
          >
            <TouchableOpacity
              style={{ flex: 1 }}
              activeOpacity={1}
              // onPressOut={}
            >
              <Animated.View
                style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "rgba(0,0,0,0.5)",
                  opacity: modalAnimatedValue2,
                }}
              >
                <TouchableWithoutFeedback>
                  <View
                    style={{
                      width: "92%",
                      height: "40%",
                      backgroundColor: "#fff",
                      padding: 20,
                      borderRadius: 10,
                    }}
                  >
                    <View
                      style={{
                        width: "90%",
                        alignSelf: "center",
                        // height: "20%",
                        justifyContent: "center",
                        marginTop: "3%",
                      }}
                    >
                      <Text
                        style={{
                          textAlign: "center",
                          fontWeight: "500",
                          fontSize: 17,
                        }}
                      >{`${language.setNotif}`}</Text>
                    </View>

                    <View
                      style={{
                        width: "95%",
                        alignSelf: "center",
                        // height: "20%",
                        // justifyContent: "center",
                        marginTop: "10%",
                      }}
                    >
                      {/* <Text>{"Pravidla pro focení hřiště"}</Text> */}
                      <Text
                        style={{
                          fontWeight: "400",
                          lineHeight: 18,
                          // fontWeight: "500",
                        }}
                      >
                        {language.notifExplanation}
                      </Text>
                    </View>

                    <View
                      style={{
                        // bottom: -30,
                        flexDirection: "row",
                        justifyContent: "space-between",
                        width: "95%",
                        alignSelf: "center",
                        // alignItems: "center",
                        marginTop: "15%",

                        // flex: 1,
                      }}
                    >
                      <Pressable
                        onPress={() => {
                          toggleModal2();
                          setTimeout(() => {
                            registerForPushNotificationsAsync(false);
                          }, 150);
                        }}
                        style={({ pressed }) => ({
                          borderRadius: 8,
                          width: "45%",
                          height: 40,
                          backgroundColor: "rgba(0,0,0,0.04)",
                          justifyContent: "center",
                          transform: [{ scale: pressed ? 0.97 : 1 }],
                          borderColor: "rgba(0,0,0,0.1)",
                          borderWidth: 0.5,
                          bottom: 6,
                        })}
                      >
                        <Text
                          style={{ fontWeight: "600", alignSelf: "center" }}
                        >
                          {language.deny}
                        </Text>
                      </Pressable>
                      <Pressable
                        onPress={() => {
                          toggleModal2();
                          setTimeout(() => {
                            registerForPushNotificationsAsync(true);
                          }, 150);
                        }}
                        style={({ pressed }) => ({
                          borderRadius: 8,
                          transform: [{ scale: pressed ? 0.97 : 1 }],
                          width: "45%",
                          height: 40,
                          backgroundColor: red,

                          justifyContent: "center",
                          // marginBottom: 30,
                          bottom: 6,
                        })}
                      >
                        <Text
                          style={{
                            fontWeight: "700",
                            alignSelf: "center",
                            color: "white",
                            fontSize: 15,
                            // fontFamily: "roboto",
                          }}
                        >
                          {language.allow}
                        </Text>
                      </Pressable>
                    </View>
                  </View>
                </TouchableWithoutFeedback>
              </Animated.View>
            </TouchableOpacity>
          </Modal>
        </View>
      );
    };

    return (
      <View
        style={{ height: "100%", height: "100%", backgroundColor: "white" }}
      >
        {!language && (
          <ActivityIndicator
            style={{ marginTop: screenHeight / 2 }}
          ></ActivityIndicator>
        )}

        {language && (
          <Animated.View
            style={{
              width: "100%",
              height: "100%",

              opacity: opacityValue,
            }}
            c
          >
            <StatusBar
              translucent={true}
              backgroundColor={"transparent"}
              barStyle="dark-content"
              // hidden={true}
            />
            {/* <View style={{ marginTop: insets.top * 0.5 + 18 }}></View> */}
            <EventsList />

            <Text
              style={{
                fontFamily: "Nunito-Bold",
                fontSize: 22,
                color: "rgba(0,0,0,0.87)",
                overflow: "visible",
                // height: "100%",
                paddingLeft: 18,
                marginTop: "-3%",

                marginBottom: -1,
              }}
            >
              {language.favCourts}
            </Text>

            <FavouriteCourtsList />

            <View
              style={{
                alignItems: "center",
                flex: 1,
                justifyContent: "center",
                marginTop: 0,
              }}
            ></View>
          </Animated.View>
        )}

        <View
          style={{
            width: "100%",
            height: 0.8,
            backgroundColor: "lightgrey",
            position: "absolute",
            bottom: 0,
          }}
        ></View>
      </View>
    );
  };

  return (
    // <NavigationContainer>

    <SafeAreaView
      style={{
        height: "100%",
        backgroundColor: "white",
        // marginTop: -insets.top,
      }}
      edges={["top"]}
    >
      <Stack.Navigator
        defaultSreenOptions={MyGames}
        screenOptions={{
          unmountInactiveRoutes: false,
          gestureEnabled: true,
        }}
      >
        <Stack.Screen name="MyGames" component={MyGames} />
      </Stack.Navigator>
    </SafeAreaView>

    // </NavigationContainer>
  );
}

export default function MyGames() {
  return (
    <MyGamesDataContextProvider>
      <RootMyGamesStack />
    </MyGamesDataContextProvider>
  );
}
