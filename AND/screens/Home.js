import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Pressable,
  AppState,
  Dimensions,
  // AppStateStatus,
  Appearance,
  StatusBar,
  Modal,
  TouchableWithoutFeedback,
} from "react-native";
// import {
//   signOut,
//   verifyBeforeUpdateEmail,
//   updateEmail,
//   reauthenticateWithCredential,
//   sendEmailVerification,
// } from "firebase/auth";
import { auth, db, functions } from "../config/firebase";
// import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  deleteDoc,
  orderBy,
  addDoc,
  GeoPoint,
  where,
  startAfter,
  getCountFromServer,
  limit,
  increment,
} from "firebase/firestore";

import * as Location from "expo-location";

import { Ionicons } from "@expo/vector-icons";

import {
  AuthenticatedUserContext,
  AuthenticatedUserProvider,
} from "./Provider";
import { red } from "./colors";

import { Feather } from "@expo/vector-icons";

// import CachedImage from "react-native-expo-cached-image";

import { LinearGradient } from "expo-linear-gradient";

import { getDistance } from "geolib";

import { MaterialCommunityIcons } from "@expo/vector-icons";

import { EN, CZ } from "./../assets/strings";

import { httpsCallable } from "firebase/functions";
// import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";

import {
  useSafeAreaInsets,
  SafeAreaView,
} from "react-native-safe-area-context";

import { AntDesign } from "@expo/vector-icons";

// import * as Notifications from "expo-notifications";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

// const queryClient = new QueryClient();
let nextId = 1; // Outside your component

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const TempComp = () => {
  const [temperature, setTemperature] = useState("--");
  const [isInitialRender, setIsInitialRender] = useState(true);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const id = nextId++;

  const fetchTemperature = httpsCallable(functions, "fetchTemperature");

  useEffect(() => {
    updateTemperature();

    const intervalId = setInterval(() => {
      updateTemperature();
    }, 4800000); // 4800000ms = 80 minutes

    return () => {
      clearInterval(intervalId);
    };
  }, []);

  async function updateTemperature() {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      return;
    }

    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: 1000,
      });

      const weatherData = await fetchTemperature({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      const currentTemperature = weatherData.data.temperature;
      animateTemperatureChange(currentTemperature);
    } catch (error) {}
  }

  function animateTemperatureChange(newTemperature) {
    if (newTemperature === temperature) {
      // temperature hasn't changed, don't do anything
      return;
    }

    if (isInitialRender) {
      setIsInitialRender(false);
      setTemperature(newTemperature);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setTemperature(newTemperature);
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    }
  }

  return temperature !== null ? (
    <Animated.Text
      style={{
        textAlign: "right",
        color: "white",
        fontSize: 60,
        fontWeight: "200",
        position: "absolute",
        right: 16,
        bottom: 5,
        opacity: fadeAnim,
        // fontFamily: "Roboto",
      }}
    >
      {Math.round(temperature)}°
    </Animated.Text>
  ) : null;
};

const EventItem = React.memo(
  function EventItem(props) {
    const fadeInOpacity = useRef(new Animated.Value(0)).current;
    const scaleValue = useRef(new Animated.Value(0.95)).current;
    const [imageLoaded, setImageLoaded] = useState(false);

    const widthValue = useRef(new Animated.Value(1)).current;

    const opacityValue = useRef(new Animated.Value(0.95)).current;

    const duration = 1250;

    const date5 = new Date(props.value?.timeEnd?.seconds * 1000);
    const hours = date5.getHours().toString().padStart(2, "0");
    const minutes = date5.getMinutes().toString().padStart(2, "0");

    useEffect(() => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(opacityValue, {
            toValue: 1,
            duration: duration,
            useNativeDriver: false,
          }),
          Animated.timing(opacityValue, {
            toValue: 0.95,
            duration: duration,
            useNativeDriver: false,
          }),
        ])
      ).start();
    }, []);

    const opacityValue2 = useRef(new Animated.Value(0.1)).current;

    useEffect(() => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(opacityValue2, {
            toValue: 0.7,
            duration: duration,
            useNativeDriver: false,
          }),
          Animated.timing(opacityValue2, {
            toValue: 0.1,
            duration: duration,
            useNativeDriver: false,
          }),
        ])
      ).start();
    }, []);

    useEffect(() => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(widthValue, {
            toValue: 1.087,
            duration: duration,
            useNativeDriver: false,
          }),
          Animated.timing(widthValue, {
            toValue: 1,
            duration: duration,
            useNativeDriver: false,
          }),
        ])
      ).start();
    }, [props.value.isLive]);

    useEffect(() => {
      if (imageLoaded) {
        Animated.timing(fadeInOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
        // Animated.timing(positionValue, {
        //   toValue: 0,
        //   duration: 300,
        //   useNativeDriver: true,
        // }).start();
        Animated.timing(scaleValue, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }).start();
      }
    }, [imageLoaded]);

    const [width, setWidth] = useState(0);
    const [height, setHeight] = useState(0);

    Image.getSize(props.value.courtObj.imageRefs[0], (width, height) => {
      const aspectRatio = width / height;
      const fixedWidth = screenWidth * 0.913;
      const calculatedHeight = fixedWidth / aspectRatio;

      setWidth(fixedWidth);
      setHeight(calculatedHeight);
    });

    if (props.value.usersCount === undefined) {
      return <View style={{ height: 130 }}></View>;
    }

    return (
      <View key={props.value.eventID}>
        <Animated.View
          key={props.value.eventID + "-animated-view"}
          style={{
            marginTop: 20,
            width: "100%",
            opacity: fadeInOpacity,
            transform: [{ scale: scaleValue }],

            alignSelf: "center",
          }}
        >
          <Pressable
            style={({ pressed }) => ({
              alignSelf: "center",
              height: imageLoaded ? screenWidth / 1.5 : screenWidth / 3,
              width: "92%",
              height: "auto",
              borderRadius: 17,
              borderColor: "rgba(70,70,70,1)",
              borderWidth: 1.7,
              backgroundColor: "white",
              shadowColor: "grey",
              shadowRadius: 2,
              shadowOpacity: 1,
              shadowOffset: {
                height: 3,
              },
              transform: [{ scale: pressed ? 0.99 : 1 }],
              // opacity: [{ scale: pressed ? 0.995 : 1 }],
            })}
            onPress={() => {
              // console.log("Props value:", props.value);
              props.navigation.navigate("Event", {
                eventInfo: props.value,
                language: props.language,
                navigation: props.navigation,
              });
            }}
          >
            <FastImage
              onLoadEnd={() => setImageLoaded(true)}
              style={{
                width: width,
                height: height,

                borderRadius: 15.2,
                overflow: "hidden",
                borderColor: "black",
              }}
              resizeMode="contain"
              source={{ uri: props.value.courtObj.imageRefs[0] }}
            >
              {props.value.isLive && (
                <View>
                  <Animated.View
                    style={{
                      borderRadius: 8,
                      position: "absolute",
                      // marginLeft: 10,
                      marginTop: 7,
                      alignSelf: "center",
                      // width: widthValue.interpolate({
                      //   inputRange: [50, 100],
                      //   outputRange: ["50%", "100%"],
                      // }),
                      width: "40%",
                      height: 18,
                      backgroundColor: red,
                      shadowColor: red,
                      shadowOpacity: opacityValue2,
                      shadowRadius: 5,
                      shadowOffset: { width: 0, height: 0 },
                      justifyContent: "center",
                      opacity: opacityValue,
                      transform: [
                        {
                          scaleX: widthValue.interpolate({
                            inputRange: [1, 1.087],
                            outputRange: [1, 1.087],
                          }),
                        },
                      ],
                    }}
                  ></Animated.View>
                  <Text
                    style={{
                      position: "absolute",
                      marginTop: 6.5,
                      fontWeight: "800",
                      alignSelf: "center",
                      color: "white",
                      fontSize: 13,
                      // fontFamily: "roboto",
                    }}
                  >
                    Live
                  </Text>
                </View>
              )}
              <View
                style={{
                  position: "absolute",
                  right: -3,

                  // borderWidth: 2,
                  borderTopRightRadius: 19,
                  borderTopLeftRadius: 3,
                  borderBottomRightRadius: 3,
                  borderBottomLeftRadius: 36,
                  shadowColor: "rgba(0,0,0,1)",
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                  // zIndex: 100,
                }}
              >
                <View
                  style={{
                    paddingTop: 4,
                    paddingBottom: 4,
                    top: -2,
                    backgroundColor: "rgba(2,2,2,0.6)",
                    paddingLeft: 16,
                    paddingRight: 10,
                    borderTopRightRadius: 19,
                    borderTopLeftRadius: 3,
                    borderBottomRightRadius: 3,
                    borderBottomLeftRadius: 36,
                  }}
                >
                  {!props.value.usersLimit ? (
                    <View
                      style={{
                        flexDirection: "row",
                        // alignContent: "space-between",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "700",
                          color: "white",
                        }}
                      >{`${props.value.usersCount}`}</Text>
                      <Ionicons
                        name="people"
                        size={15}
                        color="white"
                        style={{ paddingHorizontal: 4, paddingTop: 1 }}
                      />
                    </View>
                  ) : (
                    <View
                      style={{
                        flexDirection: "row",
                        // alignContent: "space-between",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 14,
                          fontWeight: "700",
                          color: "white",
                        }}
                      >{`${props.value.usersCount}/${props.value.usersLimit}`}</Text>
                      <Ionicons
                        name="people"
                        size={15}
                        color="white"
                        style={{ paddingHorizontal: 4 }}
                      />
                    </View>
                  )}
                </View>
              </View>
              {props.value.creator === auth.currentUser.uid && (
                <View
                  style={{
                    position: "absolute",
                    left: -3,
                    borderTopLeftRadius: 19,
                    borderTopRightRadius: 3,
                    borderBottomLeftRadius: 3,
                    borderBottomRightRadius: 36,
                    shadowColor: "rgba(0,0,0,1)",
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                  }}
                >
                  <View
                    style={{
                      paddingTop: 4,
                      paddingBottom: 4,
                      backgroundColor: "rgba(2,2,2,0.6)",
                      paddingLeft: 16,
                      paddingRight: 10,
                      borderTopLeftRadius: 19,
                      borderTopRightRadius: 3,
                      borderBottomLeftRadius: 3,
                      borderBottomRightRadius: 36,
                    }}
                  >
                    <MaterialCommunityIcons
                      name="crown"
                      size={22}
                      color="#ffc005"
                      style={{
                        // position: "absolute",
                        alignSelf: "center",
                        marginLeft: -6,
                        marginTop: -2,
                      }}
                    />
                  </View>
                </View>
              )}

              <View style={{ flex: 1, justifyContent: "flex-end" }}>
                <LinearGradient
                  colors={[
                    "rgba(0,0,0,0)",
                    "rgba(0,0,0,0.1)",
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                    "rgba(0,0,0,0.2)",
                  ]}
                  style={{
                    flex: 0.23,
                    paddingTop: 10,
                    marginBottom: -1,
                    flexDirection: "row",
                  }}
                >
                  <View
                    style={{
                      flex: 1,
                      // flexDirection: "row",
                      alignItems: "center",
                      // justifyContent: "space-between",
                      width: "40%",
                      marginLeft: 3,
                      marginBottom: 0,
                      bottom: -13,
                    }}
                  >
                    <View
                      style={{
                        alignSelf: "flex-start",
                        // justifyContent: "center",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: 13,
                          fontWeight: "bold",
                          marginLeft: 6,
                          color: "white",
                          maxWidth: "100%",

                          // color: props.value.isLive ? "red" : "blue",
                        }}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {`${props.value.eventName}`}
                      </Text>
                    </View>
                    <View style={{ alignSelf: "flex-start" }}>
                      {props.value.isLive ? (
                        <Text
                          style={{
                            fontSize: 13,
                            marginLeft: 7,
                            color: "white",
                            fontWeight: 400,
                            top: 1,
                          }}
                        >
                          {props.language.endsAt} {hours}:{minutes}
                          {/* {formatDate(
                                  props.value.timeStart,
                                  props.value.timeEnd
                                )} */}
                        </Text>
                      ) : (
                        <Text
                          style={{
                            fontSize: 13,
                            marginLeft: 7,
                            color: "white",
                            fontWeight: 400,
                            // bottom: -3,
                            top: 1,
                          }}
                        >
                          {/* {props.formatDate(
                            props.value.timeStart,
                            props.value.timeEnd
                          )} */}
                          {`${new Date(
                            props.value.timeStart.seconds * 1000
                          ).getDate()}. ${
                            new Date(
                              props.value.timeStart.seconds * 1000
                            ).getMonth() + 1
                          }. ${(
                            "0" +
                            new Date(
                              props.value.timeStart.seconds * 1000
                            ).getHours()
                          ).slice(-2)}:${(
                            "0" +
                            new Date(
                              props.value.timeStart.seconds * 1000
                            ).getMinutes()
                          ).slice(-2)}-${(
                            "0" +
                            new Date(
                              props.value.timeEnd.seconds * 1000
                            ).getHours()
                          ).slice(-2)}:${(
                            "0" +
                            new Date(
                              props.value.timeEnd.seconds * 1000
                            ).getMinutes()
                          ).slice(-2)} ${props.language.weekdays[
                            new Date(props.value.date.seconds * 1000).getDay()
                          ].slice(0, 3)}.`}
                        </Text>
                      )}
                    </View>
                  </View>
                  <View
                    style={{
                      // height: "100%",
                      height: "100%",
                      width: "5%",
                      // alignSelf: "flex-start",
                      // position: "absolute",
                      // marginLeft: "63.5%",
                      marginTop: 10,
                    }}
                  >
                    <Image
                      style={{
                        width: 35,
                        // aspectRatio: 1,
                        height: 35,
                        // height: "100%",
                        // height: 35,
                        // marginLeft: "50%",
                        // marginLeft: "",
                        // position: "absolute",
                        overflow: "visible",
                      }}
                      source={require("./../images/white-marker.png")}
                    />
                  </View>

                  <View
                    style={{
                      width: "35%",
                      height: 67,
                      // position: "absolute",
                      justifyContent: "center",
                      marginLeft: 5,
                      paddingLeft: 12,
                      // alignSelf: "center",
                    }}
                  >
                    <Text
                      style={{
                        // flexShrink: 1,
                        color: "white",
                        marginBottom: 5,
                        maxWidth: "90%",
                        fontWeight: "500",
                        fontSize: 10,
                        // alignSelf: "center",
                        textAlign: "center",
                        // alignSel
                      }}
                      numberOfLines={3}
                      ellipsizeMode="tail"
                    >
                      {props.value.courtObj.address}
                      {/* aaaaaaaaaa */}
                    </Text>
                  </View>
                </LinearGradient>
              </View>
            </FastImage>
          </Pressable>
        </Animated.View>

        {!imageLoaded && (
          <View
            style={{
              height: screenWidth / 1.5,
              // opacity: imageLoaded ? 0 : 1,
            }}
          >
            <ActivityIndicator />
          </View>
        )}
      </View>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.value === nextProps.value;
  }
);

const Events = ({
  centerCoordinate,
  radius,
  language,
  navigation,
  userDate,
}) => {
  const [initialLoading, setInitialLoading] = useState(false);
  const [events, setEvents] = useState([]);
  const [liveEventsCount, setLiveEventsCount] = useState(null);
  const [plannedEventsCount, setPlannedEventsCount] = useState(null);
  const [loadingEvents, setLoadingEvents] = useState(false);

  const appState = useRef(AppState.currentState);
  const firestoreListener = useRef(null);

  const fetchEvents = async () => {
    const eventsQuery = query(
      collectionGroup(db, "user_events"),
      orderBy("coords"),
      orderBy("date")
    );

    const processSnapshot = async (snapshot) => {
      let validEvents = [];
      let updatedEvents = [...events];

      const docChangesPromises = snapshot.docs.map(async (doc) => {
        const eventDoc = doc.data();
        const eventCoords = {
          latitude: eventDoc.courtObj.coords[1],
          longitude: eventDoc.courtObj.coords[0],
        };
        const centerCoords = {
          latitude: centerCoordinate.latitude,
          longitude: centerCoordinate.longitude,
        };
        const distance = getDistance(centerCoords, eventCoords);

        if (
          distance <= radius &&
          (eventDoc.creator === auth.currentUser.uid ||
            ((eventDoc.ageLimitMin ? eventDoc.ageLimitMin : -1) <=
              calculateAge(userDate) &&
              (eventDoc.ageLimitMax ? eventDoc.ageLimitMax : 100000) >=
                calculateAge(userDate)))
        ) {
          const eventDocRef = collection(db, `${doc.ref.path}/eventUsers`);
          const eventsCount = await getCountFromServer(eventDocRef);
          eventDoc.usersCount = eventsCount.data().count;
          validEvents.push(eventDoc);
        }
      });

      await Promise.all(docChangesPromises);

      updatedEvents = updatedEvents.filter((event) =>
        validEvents.some((e) => e.eventID === event.eventID)
      );

      validEvents.forEach((newEvent) => {
        const existingEventIndex = updatedEvents.findIndex(
          (event) => event.eventID === newEvent.eventID
        );

        if (existingEventIndex !== -1) {
          updatedEvents[existingEventIndex] = newEvent;
        } else {
          updatedEvents.push(newEvent);
        }
      });

      updatedEvents.sort((a, b) => {
        if (
          a.creator === auth.currentUser.uid &&
          b.creator !== auth.currentUser.uid
        ) {
          return -1;
        }
        if (
          a.creator !== auth.currentUser.uid &&
          b.creator === auth.currentUser.uid
        ) {
          return 1;
        }
        return a.date - b.date;
      });

      setEvents(updatedEvents);
      setInitialLoading(true);
      setLoadingEvents(false);
    };

    if (firestoreListener.current) {
      firestoreListener.current();
    }

    firestoreListener.current = onSnapshot(
      eventsQuery,
      { includeMetadataChanges: true },
      async (snapshot) => {
        if (!snapshot.metadata.fromCache) {
          await processSnapshot(snapshot);
        }
      }
    );
  };

  const handleAppStateChange = (nextAppState) => {
    if (
      appState.current.match(/inactive|background/) &&
      nextAppState === "active"
    ) {
      fetchEvents();
    } else {
      if (firestoreListener.current) {
        firestoreListener.current();
      }
      setTimeout(() => {
        setEvents([]);
        setLoadingEvents(true);
      });
    }
    appState.current = nextAppState;
  };

  useEffect(() => {
    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription.remove();
      if (firestoreListener.current) {
        firestoreListener.current();
      }
    };
  }, []);

  useEffect(() => {
    fetchEvents();

    return () => {
      if (firestoreListener.current) {
        firestoreListener.current();
      }
    };
  }, [centerCoordinate, radius]);

  useEffect(() => {
    let liveEvents = 0;
    let plannedEvents = 0;

    events.forEach((event) => {
      if (event.isLive === true) {
        liveEvents += 1;
      }
      if (event.isLive === false) {
        plannedEvents += 1;
      }
    });

    setLiveEventsCount(liveEvents);
    setPlannedEventsCount(plannedEvents);
  }, [events]);

  const CustomHeaderComponent = () => {
    return (
      <Animated.View
        style={{
          flexDirection: "row",
          justifyContent: "space-evenly",
          marginTop: 13,
          marginBottom: -2,
          opacity: 1,
        }}
      >
        <Text style={{ fontSize: 15, fontWeight: "500", color: red }}>
          {`${language.liveEvents}: ${liveEventsCount}`}
        </Text>
        <Text style={{ fontSize: 15, fontWeight: "500" }}>
          {`${language.plannedEvents}: ${plannedEventsCount}`}
        </Text>
      </Animated.View>
    );
  };

  if (!initialLoading) {
    return (
      <ActivityIndicator
        style={{
          // width: "100%",
          // height: "100%",
          paddingTop: 120,
          // backgroundColor: "white",
        }}
      />
    );
  }

  return (
    <View
      key="event-list"
      style={{
        flex: 1,
        backgroundColor: "transparent",
      }}
    >
      {events.length === 0 && (
        <Text
          style={{
            color: "rgba(0,0,0,0.5)",
            fontSize: 15,
            alignSelf: "center",
            marginTop: "35%",
            position: "absolute",
            textAlign: "center",
            lineHeight: 23,
          }}
        >
          {language.noGames}
        </Text>
      )}
      {loadingEvents && (
        <View
          style={{
            height: "100%",
            width: "100%",
            backgroundColor: "white",
            borderRadius: 100,
          }}
        >
          <ActivityIndicator
            style={{ marginTop: 100, alignSelf: "center" }}
          ></ActivityIndicator>
        </View>
      )}

      <FlatList
        data={events}
        keyExtractor={(item, index) => item.eventID}
        ListHeaderComponent={<CustomHeaderComponent />}
        renderItem={({ item, index }) => {
          if (item.key !== "comp") {
            return (
              <EventItem
                key={item.eventID}
                value={item}
                index={index}
                totalEvents={events.length}
                navigation={navigation}
                language={language}
              />
            );
          }
        }}
        contentContainerStyle={{ paddingBottom: "28%" }}
      />
    </View>
  );
};

function RootHomeStack() {
  const navigation = useNavigation();

  const insets = useSafeAreaInsets();

  const HomeSc = () => {
    const [userAreaCoords, setUserAreaCoords] = useState(null);
    const [userDate, setUserDate] = useState(null);
    const [profileImageUrl, setProfileImageUrl] = useState(null);
    const [userName, setUserName] = useState(null);
    const [profileColor, setProfileColor] = useState(null);
    const [centerCoordinate, setCenterCoordinate] = useState(null);
    const [radius, setRadius] = useState(null);

    const [language2, setLanguage2] = useState(null);
    const [language, setLanguage] = useState(null);

    useEffect(() => {
      const docRef = doc(db, "users", auth.currentUser.uid);

      const unsubscribe = onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists()) {
          const doc = docSnap.data();

          if (doc.date?.seconds !== userDate?.seconds) {
            // console.log(doc?.date, userDate?.seconds);
            // console.log("======================1");
            setUserDate(doc.date);
          }

          if (doc.profileImageRef !== profileImageUrl) {
            // console.log("======================2");
            setProfileImageUrl(doc.profileImageRef);
          }

          if (doc.name !== userName) {
            // console.log("======================3");
            setUserName(doc.name);
          }

          if (doc.profileColor !== profileColor) {
            // console.log("======================4");
            setProfileColor(doc.profileColor);
          }

          if (doc.radius !== radius) {
            // console.log(doc.radius, radius);
            // console.log("======================5");
            setRadius(doc.radius);
          }

          if (doc.centerCoord !== centerCoordinate) {
            // console.log(doc.radius, radius);
            // .log("======================6");
            setCenterCoordinate(doc.centerCoord);
          }

          if (doc.language !== language2) {
            // console.log("======================7");
            setLanguage2(doc.language);
          }
        }
      });

      return () => unsubscribe();
    }, []);

    useEffect(() => {
      if (language2 === "CZ") {
        if (language !== CZ) {
          setLanguage(CZ);
        }

        // console.log(CZ);
      } else {
        if (language !== EN) {
          setLanguage(EN);
        }
      }
    }, [language2]);

    const [appState, setAppState] = useState(AppState.currentState);

    useEffect(() => {
      function handleAppStateChange(nextAppState) {
        setAppState(nextAppState);
      }

      // if (mounted == 0) {
      AppState.addEventListener("change", handleAppStateChange);
      // mounted = 1;
      // }

      return () => {
        AppState.removeEventListener("change", handleAppStateChange);
      };
    }, []);

    const fadeInOpacity = useRef(new Animated.Value(0)).current;
    const [profileImageLoaded, setProfileImageLoaded] = useState(false);

    useEffect(() => {
      if (profileImageLoaded) {
        setTimeout(() => {
          Animated.timing(fadeInOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }, 300);
      } else {
        setTimeout(() => {
          Animated.timing(fadeInOpacity, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }).start();
        }, 300);
      }
    }, [profileImageLoaded]);

    const [isModalVisible2, setModalVisible2] = useState(false);

    const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

    const toggleModal2 = () => {
      if (isModalVisible2) {
        Animated.timing(modalAnimatedValue2, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start(() => setModalVisible2(false));
        const docRef = doc(db, "users", auth.currentUser.uid);

        updateDoc(docRef, {
          initialMessage: true,
        });
      } else {
        setModalVisible2(true);
        Animated.timing(modalAnimatedValue2, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    };

    useEffect(() => {
      const docRef = doc(db, "users", auth.currentUser.uid);

      getDoc(docRef)
        .then((docSnap) => {
          if (docSnap.exists()) {
            if (!docSnap.data().initialMessage) {
              toggleModal2();
              // console.log("NO notif");

              // updateDoc(docRef, {
              //   initialMessage: true,
              // });
            }
          } else {
            // console.log("No such document!");
          }
        })
        .catch((error) => {
          // console.log("Error getting document:", error);
        });
    }, []);

    const [feedback, setFeedback] = useState("");

    const uploadFeedback = async () => {
      const coll = collection(db, `feedbackshome-cz/`);

      if (feedback !== "") {
        try {
          const eventDocRef = await addDoc(coll, {
            feedback: feedback,

            user: auth.currentUser.uid,
            date: new Date(),
          });

          alert(language.feedbackSentHome);
          setFeedback("");
        } catch (error) {
          alert(error);
        }
      }
    };

    const [isModalVisible3, setModalVisible3] = useState(false);

    const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

    const toggleModal3 = () => {
      if (isModalVisible3) {
        Animated.timing(modalAnimatedValue3, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start(() => setModalVisible3(false));
      } else {
        setModalVisible3(true);

        Animated.timing(modalAnimatedValue3, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    };

    if (
      !language ||
      !profileColor ||
      userName === null ||
      centerCoordinate === null
    ) {
      return (
        <View
          style={{ backgroundColor: "white", height: "100%", width: "100%" }}
        >
          <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
        </View>
      );
    }

    // console.log(initialMessage, updateMessage);

    return (
      <View
        style={{
          backgroundColor:
            !profileImageLoaded && profileImageUrl ? "white" : red,

          width: "100%",
          height: "100%",
          marginTop: 11 + insets.top * 0.75,
          // marginTop: 45,
        }}
      >
        {/* <StatusBar barStyle="light-content" /> */}
        {!profileImageLoaded && profileImageUrl && (
          <ActivityIndicator
            style={{
              // marginTop: "70%",
              backgroundColor: "white",
              width: "100%",
              height: "100%",
            }}
          ></ActivityIndicator>
        )}
        <Animated.View
          style={{
            flex: 1,
            // alignItems: "center",
            // justifyContent: "center",
            backgroundColor: "white",
            opacity: 1,
            // zIndex: ,
          }}
        >
          <View
            style={{
              flex: 1,
              justifyContent: "flex-end",
              backgroundColor: red,
              opacity: 1,
            }}
          >
            <View
              style={{
                backgroundColor: red,
                flex: 0.23,
              }}
            >
              {/* {(initialMessage !== "showed" || !updateMessage) && ( */}
              <Modal
                animationType="none"
                transparent={true}
                visible={isModalVisible2}
                onRequestClose={toggleModal2}
              >
                <TouchableOpacity
                  style={{ flex: 1 }}
                  activeOpacity={1}
                  onPressOut={toggleModal2}
                >
                  <Animated.View
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: "rgba(0,0,0,0.5)",
                      opacity: modalAnimatedValue2,
                    }}
                  >
                    <TouchableWithoutFeedback>
                      <View
                        style={{
                          width: "90%",
                          // height: "22%",
                          backgroundColor: "#fff",
                          padding: 20,
                          borderRadius: 10,
                        }}
                      >
                        <View
                          style={{
                            width: "90%",
                            alignSelf: "center",
                            // height: "20%",
                            justifyContent: "center",
                            marginTop: "5%",
                          }}
                        >
                          <Text
                            style={{
                              textAlign: "center",

                              fontWeight: "500",
                              fontSize: 17,
                              lineHeight: 18,
                            }}
                          >
                            {language.welcome}
                          </Text>
                        </View>

                        <View
                          style={{
                            width: "95%",
                            alignSelf: "center",

                            justifyContent: "center",
                            marginTop: "9%",
                          }}
                        >
                          <Text
                            style={{
                              textAlign: "justify",
                              fontWeight: "400",
                              fontSize: 15,
                            }}
                          >
                            {language.welcomeString}
                          </Text>
                        </View>

                        <View
                          style={{
                            // bottom: -30,
                            // position: "absolute",
                            flexDirection: "row",
                            justifyContent: "center",
                            width: "95%",
                            alignSelf: "center",
                            marginTop: 35,
                            marginBottom: 5,
                            // bottom: 25,
                          }}
                        >
                          <Pressable
                            onPress={() => {
                              toggleModal2();
                            }}
                            style={({ pressed }) => ({
                              borderRadius: 8,
                              transform: [{ scale: pressed ? 0.97 : 1 }],
                              width: "45%",
                              height: 35,
                              backgroundColor: red,

                              justifyContent: "center",
                            })}
                          >
                            <Text
                              style={{
                                fontWeight: "600",
                                alignSelf: "center",
                                color: "white",
                                fontSize: 15,
                                // fontFamily: "roboto",
                              }}
                            >
                              {language.start}
                            </Text>
                          </Pressable>
                        </View>
                      </View>
                    </TouchableWithoutFeedback>
                  </Animated.View>
                </TouchableOpacity>
              </Modal>

              <Modal
                animationType="none"
                transparent={true}
                visible={isModalVisible3}
                onRequestClose={toggleModal3}
              >
                <TouchableOpacity
                  style={{ flex: 1 }}
                  activeOpacity={1}
                  onPressOut={toggleModal3}
                >
                  <Animated.View
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: "rgba(0,0,0,0.5)",
                      opacity: modalAnimatedValue3,
                    }}
                  >
                    <TouchableWithoutFeedback>
                      <View
                        style={{
                          width: "90%",
                          // height: "35%",
                          backgroundColor: "#fff",
                          padding: 20,
                          borderRadius: 10,
                        }}
                      >
                        <View
                          style={{
                            width: "90%",
                            alignSelf: "center",
                            // height: "20%",
                            justifyContent: "center",
                            marginTop: "3%",
                          }}
                        >
                          <Text
                            style={{
                              textAlign: "center",
                              fontWeight: "500",
                              fontSize: 16,
                            }}
                          >
                            {language.writeUsTut}
                          </Text>
                        </View>

                        <TextInput
                          style={{
                            alignSelf: "center",
                            width: "95%",
                            height: 150,
                            borderWidth: 1,
                            borderColor: "rgba(0,0,0,0.2)",
                            borderRadius: 7,
                            paddingHorizontal: 15,
                            paddingVertical: 15,
                            marginTop: "7%",
                          }}
                          value={feedback}
                          onChangeText={(newText) => {
                            setFeedback(newText);
                          }}
                          placeholder={"..."}
                          multiline={true}
                        />

                        <View
                          style={{
                            // bottom: -30,
                            // position: "absolute",
                            // flexDirection: "row",
                            // justifyContent: "space-between",
                            width: "95%",
                            alignSelf: "center",
                            // bottom: 25,
                            marginTop: "7%",
                          }}
                        >
                          <Pressable
                            onPress={() => {
                              toggleModal3();
                              setTimeout(() => {
                                uploadFeedback();
                              }, 200);
                            }}
                            style={({ pressed }) => ({
                              borderRadius: 8,
                              transform: [{ scale: pressed ? 0.97 : 1 }],
                              width: "70%",
                              height: 40,
                              backgroundColor: red,
                              alignSelf: "center",
                              marginTop: 10,
                              marginBottom: 10,

                              justifyContent: "center",
                            })}
                          >
                            <Text
                              style={{
                                fontWeight: "700",
                                alignSelf: "center",
                                color: "white",
                                fontSize: 15,
                                // fontFamily: "roboto",
                              }}
                            >
                              {language.send}
                            </Text>
                          </Pressable>
                        </View>
                      </View>
                    </TouchableWithoutFeedback>
                  </Animated.View>
                </TouchableOpacity>
              </Modal>

              <View
                style={{
                  flexDirection: "row",
                  marginTop: 18,
                  paddingLeft: 20,
                  width: "100%",
                }}
              >
                {profileImageUrl && (
                  <FastImage
                    style={{ width: 85, height: 85, borderRadius: 100 }}
                    source={{ uri: profileImageUrl }}
                    onLoadEnd={() => {
                      setProfileImageLoaded(true);
                    }}
                  />
                )}

                {!profileImageUrl && (
                  <View
                    style={{
                      width: 85,
                      height: 85,
                      borderRadius: 100,
                      backgroundColor: `${profileColor}`,
                    }}
                  >
                    <Text
                      style={{
                        alignSelf: "center",
                        // fontSize: screenWidth / 7,
                        fontSize: 55,
                        color: "white",
                        paddingTop: 5,
                        paddingLeft: 1,
                      }}
                    >
                      {userName[0].toUpperCase()}
                    </Text>
                  </View>
                )}

                <View
                  style={{
                    height: 90,
                    width: "90%",
                    justifyContent: "space-evenly",
                    marginLeft: 14,
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      fontWeight: "400",
                      fontSize: 16,
                    }}
                  >{`${userName}`}</Text>
                  <Text
                    style={{
                      color: "white",
                      fontWeight: "500",
                      fontSize: 20,
                    }}
                  >
                    {new Date().toLocaleDateString("en-US", {
                      weekday: "short",
                      day: "numeric",
                      month: "short",
                    })}
                  </Text>
                </View>
                <TempComp />
              </View>
              <TouchableOpacity
                style={{
                  right: 15,
                  bottom: 10,
                  position: "absolute",
                  flexDirection: "row",
                }}
                onPress={() => {
                  navigation.navigate("EditCoords", {
                    editting: true,
                    language: language,
                  });
                }}
              >
                <Text style={{ color: "white", marginTop: 6, fontSize: 13 }}>
                  {language.changeRegion}
                </Text>
                <Feather
                  name="map-pin"
                  size={15}
                  color="white"
                  style={{ marginLeft: 3, marginTop: 5 }}
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  left: 17,
                  bottom: 10,
                  position: "absolute",
                  flexDirection: "row",
                }}
                onPress={() => {
                  toggleModal3();
                }}
              >
                <Text style={{ color: "white", marginTop: 6, fontSize: 13 }}>
                  {language.writeUs}
                </Text>
                <AntDesign
                  name="message1"
                  size={15}
                  color="white"
                  style={{ marginLeft: 5, marginTop: 6 }}
                />
              </TouchableOpacity>
            </View>
            <View
              style={{
                backgroundColor: "white",
                flex: 0.81,
                borderTopStartRadius: 15,
                borderTopEndRadius: 15,
                width: "100%",
                // marginTop: -20,
                // height: "100%",
                // alignItems: "center",
              }}
            >
              <Events
                centerCoordinate={centerCoordinate}
                radius={radius}
                language={language}
                navigation={navigation}
                userDate={userDate}
              />
            </View>
          </View>
        </Animated.View>
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      {/* <StatusBar
        translucent={true}
        backgroundColor={"transparent"}
        barStyle="light-content"
        // hidden={true}
      /> */}
      <SafeAreaView
        style={{
          height: "102%",
          backgroundColor: red,
          marginTop: -insets.top,
        }}
        edges={["top"]}
      >
        <HomeSc />
      </SafeAreaView>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      ></View>
    </View>
  );
}

export default function HomeSc() {
  return <RootHomeStack />;
}
