import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Vibration,
  Keyboard,
  Pressable,
} from "react-native";

import { auth, db } from "../config/firebase";
// import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
} from "@react-navigation/native";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  limit,
  addDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
  getCountFromServer,
} from "firebase/firestore";

import { red } from "./colors";

// import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";

import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";

import { useRoute } from "@react-navigation/native";

import { MaterialCommunityIcons } from "@expo/vector-icons";
// import { EvilIcons } from "@expo/vector-icons";
import Swiper from "react-native-swiper";
// import { Asset } from "expo-asset";
import { FontAwesome } from "@expo/vector-icons";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
// import { EN, CZ } from "./../assets/strings";
import { Feather } from "@expo/vector-icons";

import {
  useSafeAreaInsets,
  SafeAreaView,
} from "react-native-safe-area-context";

const Tab = createMaterialTopTabNavigator();

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

export default function CourtInfo() {
  const navigation = useNavigation();

  const insets = useSafeAreaInsets();

  const EventItem = React.memo(
    function EventItem(props) {
      const language = props.language;

      const fadeInOpacity = useRef(new Animated.Value(0)).current;
      const scaleValue = useRef(new Animated.Value(0.95)).current;
      const [imageLoaded, setImageLoaded] = useState(false);

      const widthValue = useRef(new Animated.Value(1)).current;

      const opacityValue = useRef(new Animated.Value(0.95)).current;

      const isLast = props.index === props.totalEvents - 1;

      const duration = 1250;

      const date5 = new Date(props.value?.timeEnd?.seconds * 1000);
      const hours = date5.getHours().toString().padStart(2, "0");
      const minutes = date5.getMinutes().toString().padStart(2, "0");

      useEffect(() => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(opacityValue, {
              toValue: 1,
              duration: duration,
              useNativeDriver: false,
            }),
            Animated.timing(opacityValue, {
              toValue: 0.95,
              duration: duration,
              useNativeDriver: false,
            }),
          ])
        ).start();
      }, []);

      function formatDate(date1, dateEnd) {
        const date = new Date(date1 * 1000);
        const day = date.getDate();
        const month = date.getMonth() + 1;
        const hours = date.getHours();
        const minutes = String(date.getMinutes()).padStart(2, "0");

        const date2 = new Date(dateEnd * 1000);
        const day2 = date2.getDate();
        const month2 = date2.getMonth() + 1;
        const hours2 = date2.getHours();
        const minutes2 = String(date2.getMinutes()).padStart(2, "0");

        const weekday = new Date(date1.seconds).toLocaleDateString("cs-CZ", {
          weekday: "long",
        });

        return `${weekday} ${day}.${month}. ${hours}:${minutes}-${hours2}:${minutes2} `;
      }

      const opacityValue2 = useRef(new Animated.Value(0.1)).current;

      useEffect(() => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(opacityValue2, {
              toValue: 0.7,
              duration: duration,
              useNativeDriver: false,
            }),
            Animated.timing(opacityValue2, {
              toValue: 0.1,
              duration: duration,
              useNativeDriver: false,
            }),
          ])
        ).start();
      }, []);

      useEffect(() => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(widthValue, {
              toValue: 1.087,
              duration: duration,
              useNativeDriver: false,
            }),
            Animated.timing(widthValue, {
              toValue: 1,
              duration: duration,
              useNativeDriver: false,
            }),
          ])
        ).start();
      }, [props.value.isLive]);

      useEffect(() => {
        if (imageLoaded) {
          Animated.timing(fadeInOpacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }).start();

          Animated.timing(scaleValue, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }).start();
        }
      }, [imageLoaded]);

      return (
        <View key={props.value.eventID}>
          <Animated.View
            key={props.value.eventID + "-animated-view"}
            style={{
              marginTop: 20,

              width: "100%",
              opacity: fadeInOpacity,
              transform: [{ scale: scaleValue }],

              alignSelf: "center",
            }}
          >
            <Pressable
              style={({ pressed }) => ({
                alignSelf: "center",
                height: imageLoaded ? screenWidth / 1.5 : screenWidth / 3,
                width: "92%",
                borderRadius: 17,
                borderColor: "black",
                backgroundColor: "white",
                shadowColor: "grey",
                shadowRadius: 2,
                shadowOpacity: 1,
                shadowOffset: {
                  height: 3,
                },
                transform: [{ scale: pressed ? 0.99 : 1 }],
              })}
              onPress={() => {
                navigation.navigate("Event", {
                  eventInfo: props.value,
                  language: language,
                });
              }}
            >
              <ImageBackground
                onLoadEnd={() => setImageLoaded(true)}
                style={{
                  width: "100%",
                  height: "100%",
                  borderRadius: 17,
                  overflow: "hidden",
                  borderColor: "black",
                  borderWidth: 1,
                }}
                source={{ uri: props.value.courtObj.imageRefs[0] }}
              >
                {props.value.isLive && (
                  <View>
                    <Animated.View
                      style={{
                        borderRadius: 8,
                        position: "absolute",

                        marginTop: 7,
                        alignSelf: "center",

                        width: "40%",
                        height: 18,
                        backgroundColor: red,
                        shadowColor: red,
                        shadowOpacity: opacityValue2,
                        shadowRadius: 5,
                        shadowOffset: { width: 0, height: 0 },
                        justifyContent: "center",
                        opacity: opacityValue,
                        transform: [
                          {
                            scaleX: widthValue.interpolate({
                              inputRange: [1, 1.087],
                              outputRange: [1, 1.087],
                            }),
                          },
                        ],
                      }}
                    ></Animated.View>
                    <Text
                      style={{
                        position: "absolute",
                        marginTop: 7,
                        fontWeight: "800",
                        alignSelf: "center",
                        color: "white",
                        fontSize: 13,
                      }}
                    >
                      Live
                    </Text>
                  </View>
                )}
                <View
                  style={{
                    position: "absolute",
                    right: -3,
                    borderTopRightRadius: 19,
                    borderTopLeftRadius: 3,
                    borderBottomRightRadius: 3,
                    borderBottomLeftRadius: 36,
                    shadowColor: "rgba(0,0,0,1)",
                    shadowOffset: {
                      width: 0,
                      height: 0,
                    },
                  }}
                >
                  <View
                    style={{
                      paddingTop: 4,
                      paddingBottom: 4,
                      backgroundColor: "rgba(2,2,2,0.6)",
                      paddingLeft: 16,
                      paddingRight: 10,
                      borderTopRightRadius: 19,
                      borderTopLeftRadius: 3,
                      borderBottomRightRadius: 3,
                      borderBottomLeftRadius: 36,
                    }}
                  >
                    {!props.value.usersLimit ? (
                      <View
                        style={{
                          flexDirection: "row",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 14,
                            fontWeight: "700",
                            color: "white",
                          }}
                        >{`${props.value.users.length}`}</Text>
                        <Ionicons
                          name="people"
                          size={15}
                          color="white"
                          style={{ paddingHorizontal: 4, paddingTop: 1 }}
                        />
                      </View>
                    ) : (
                      <View
                        style={{
                          flexDirection: "row",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 14,
                            fontWeight: "700",
                            color: "white",
                          }}
                        >{`${props.value.users.length}/${props.value.usersLimit}`}</Text>
                        <Ionicons
                          name="people"
                          size={15}
                          color="white"
                          style={{ paddingHorizontal: 4 }}
                        />
                      </View>
                    )}
                  </View>
                </View>
                {props.value.creator === auth.currentUser.uid && (
                  <View
                    style={{
                      position: "absolute",
                      left: -3,
                      borderTopLeftRadius: 19,
                      borderTopRightRadius: 3,
                      borderBottomLeftRadius: 3,
                      borderBottomRightRadius: 36,
                      shadowColor: "rgba(0,0,0,1)",
                      shadowOffset: {
                        width: 0,
                        height: 0,
                      },
                    }}
                  >
                    <View
                      style={{
                        paddingTop: 4,
                        paddingBottom: 4,
                        backgroundColor: "rgba(2,2,2,0.6)",
                        paddingLeft: 16,
                        paddingRight: 10,
                        borderTopLeftRadius: 19,
                        borderTopRightRadius: 3,
                        borderBottomLeftRadius: 3,
                        borderBottomRightRadius: 36,
                      }}
                    >
                      <MaterialCommunityIcons
                        name="crown"
                        size={22}
                        color="#ffc005"
                        style={{
                          alignSelf: "center",
                          marginLeft: -6,
                          marginTop: -2,
                        }}
                      />
                    </View>
                  </View>
                )}

                <View style={{ flex: 1, justifyContent: "flex-end" }}>
                  <LinearGradient
                    colors={[
                      "rgba(0,0,0,0)",
                      "rgba(0,0,0,0.1)",
                      "rgba(0,0,0,0.2)",
                      "rgba(0,0,0,0.2)",
                      "rgba(0,0,0,0.2)",
                      "rgba(0,0,0,0.2)",
                      "rgba(0,0,0,0.2)",
                      "rgba(0,0,0,0.2)",
                    ]}
                    style={{
                      flex: 0.23,
                      paddingTop: 10,
                      marginBottom: -1,
                      flexDirection: "row",
                    }}
                  >
                    <View
                      style={{
                        flex: 1,

                        alignItems: "center",

                        width: "40%",
                        marginLeft: 3,
                        marginBottom: 0,
                        bottom: -13,
                      }}
                    >
                      <View
                        style={{
                          alignSelf: "flex-start",
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 13,
                            fontWeight: "bold",
                            marginLeft: 6,
                            color: "white",
                            maxWidth: "100%",
                          }}
                          numberOfLines={1}
                          ellipsizeMode="tail"
                        >
                          {`${props.value.eventName}`}
                        </Text>
                      </View>
                      <View style={{ alignSelf: "flex-start" }}>
                        {props.value.isLive ? (
                          <Text
                            style={{
                              fontSize: 13,
                              marginLeft: 7,
                              color: "white",
                              fontWeight: 400,
                              top: 1,
                            }}
                          >
                            {props.language.endsAt} {hours}:{minutes}
                          </Text>
                        ) : (
                          <Text
                            style={{
                              fontSize: 13,
                              marginLeft: 7,
                              color: "white",
                              fontWeight: 400,
                              // bottom: -3,
                              top: 1,
                            }}
                          >
                            {`${new Date(
                              props.value.timeStart.seconds * 1000
                            ).getDate()}. ${
                              new Date(
                                props.value.timeStart.seconds * 1000
                              ).getMonth() + 1
                            }. ${(
                              "0" +
                              new Date(
                                props.value.timeStart.seconds * 1000
                              ).getHours()
                            ).slice(-2)}:${(
                              "0" +
                              new Date(
                                props.value.timeStart.seconds * 1000
                              ).getMinutes()
                            ).slice(-2)}-${(
                              "0" +
                              new Date(
                                props.value.timeEnd.seconds * 1000
                              ).getHours()
                            ).slice(-2)}:${(
                              "0" +
                              new Date(
                                props.value.timeEnd.seconds * 1000
                              ).getMinutes()
                            ).slice(-2)} ${props.language.weekdays[
                              new Date(props.value.date.seconds * 1000).getDay()
                            ].slice(0, 3)}.`}
                          </Text>
                        )}
                      </View>
                    </View>
                    <View
                      style={{
                        height: "100%",
                        width: "5%",

                        marginTop: 10,
                      }}
                    >
                      <Image
                        style={{
                          width: 35,

                          height: 35,

                          overflow: "visible",
                        }}
                        source={require("./../images/white-marker.png")}
                      ></Image>
                    </View>

                    <View
                      style={{
                        width: "35%",
                        height: 67,

                        justifyContent: "center",
                        marginLeft: 5,
                        paddingLeft: 12,
                      }}
                    >
                      <Text
                        style={{
                          color: "white",
                          marginBottom: 5,
                          maxWidth: "90%",
                          fontWeight: "500",
                          fontSize: 10,

                          textAlign: "center",
                        }}
                        numberOfLines={3}
                        ellipsizeMode="tail"
                      >
                        {props.value.courtObj.address}
                      </Text>
                    </View>
                  </LinearGradient>
                </View>
              </ImageBackground>
            </Pressable>
          </Animated.View>

          {!imageLoaded && (
            <View
              style={{
                height: screenWidth / 1.5,
              }}
            >
              <ActivityIndicator />
            </View>
          )}
        </View>
      );
    },
    (prevProps, nextProps) => {
      return prevProps.value === nextProps.value;
    }
  );

  const Event = () => {
    const route = useRoute();

    const [language2, setLanguage2] = useState("CZ");
    const [language, setLanguage] = useState(route.params.language);

    const [events, setEvents] = useState([]);

    const navigateToCreateGames = async () => {
      const coll = collection(
        db,
        `events/${auth.currentUser.uid}/user_events/`
      );
      const userEventsCount = await getCountFromServer(coll);
      const count = userEventsCount.data().count;

      if (count >= 3) {
        alert("You can only have 3 events");

        return;
      }

      navigation.navigate("MyGamesStack", route.params.selectedMarker);
    };

    useEffect(() => {
      const q = query(
        collectionGroup(db, "user_events"),
        where("courtID", "==", route.params.selectedMarker.id)
      );

      const unsubscribe = onSnapshot(q, async (querySnapshot) => {
        const docsPromises = querySnapshot.docs.map(async (doc) => {
          const userCollectionRef = collection(
            db,
            `events/${doc.data().creator}/user_events/${doc.id}/eventUsers`
          );
          const userEventsSnapshot = await getDocs(userCollectionRef);

          const users = userEventsSnapshot.docs.map(
            (userDoc) => userDoc.data().userID
          );

          return { id: doc.id, ...doc.data(), users: users };
        });

        const docs = await Promise.all(docsPromises);
        setEvents(docs);
      });

      return unsubscribe;
    }, []);

    const [timeoutDone, setTimeoutDone] = useState(false);
    const [opacityMain] = useState(new Animated.Value(0));

    useEffect(() => {
      const timeoutId = setTimeout(() => {
        setTimeoutDone(true);
      }, 1000);

      return () => clearTimeout(timeoutId);
    }, []);

    useEffect(() => {
      if (timeoutDone) {
        Animated.timing(opacityMain, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }).start();
      }
    }, [timeoutDone]);

    const images = route.params.selectedMarker.imageRefs;

    const [opacity1] = useState(new Animated.Value(0));

    const [loadingStatus, setLoadingStatus] = useState(images.map(() => true));

    const updateLoadingStatus = (index, status) => {
      setLoadingStatus((prevLoadingStatus) => {
        const newLoadingStatus = [...prevLoadingStatus];
        newLoadingStatus[index] = status;
        return newLoadingStatus;
      });
    };

    useEffect(() => {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }).start();
    }, []);

    const [isFavourite, setIsFavourite] = useState(false);

    useEffect(() => {
      const userRef = doc(db, "users", auth.currentUser.uid);

      const unsubscribe = onSnapshot(userRef, (docSnapshot) => {
        if (docSnapshot.exists()) {
          const userData = docSnapshot.data();
          const favouriteCourts = userData.favouriteCourts || [];

          setIsFavourite(
            favouriteCourts.includes(route.params.selectedMarker.id)
          );
        }
      });

      return () => {
        unsubscribe();
      };
    }, [route.params.selectedMarker.id]);

    const addToFavourites = () => {
      const userRef = doc(db, "users", auth.currentUser.uid);
      const courtRef = doc(db, "courts-cz1", route.params.selectedMarker.id);
      const subscribedUsersRef = collection(courtRef, "subscribedUsers");

      getDoc(userRef)
        .then((docSnapshot) => {
          if (docSnapshot.exists()) {
            const userData = docSnapshot.data();
            const favouriteCourts = userData.favouriteCourts || [];

            if (favouriteCourts.includes(route.params.selectedMarker.id)) {
              const updatedFavouriteCourts = favouriteCourts.filter(
                (courtId) => courtId !== route.params.selectedMarker.id
              );

              updateDoc(userRef, {
                favouriteCourts: updatedFavouriteCourts,
              })
                .then(() => {
                  const subscribedUsersCollection = collection(
                    db,
                    `courts-cz1/${route.params.selectedMarker.id}/subscribedUsers`
                  );
                  const q = query(
                    subscribedUsersCollection,
                    where("userID", "==", auth.currentUser.uid)
                  );

                  getDocs(q)
                    .then((querySnapshot) => {
                      querySnapshot.forEach((document) => {
                        const docToDelete = doc(
                          db,
                          `courts-cz1/${route.params.selectedMarker.id}/subscribedUsers`,
                          document.selectedMarker.id
                        );
                        deleteDoc(docToDelete).catch((error) => {
                          Alert.alert(error.message);
                        });
                      });
                    })
                    .catch((error) => {
                      Alert.alert(error.message);
                    });
                })
                .catch((error) => {
                  Alert.alert(error.message);
                });
            } else {
              updateDoc(userRef, {
                favouriteCourts: arrayUnion(route.params.selectedMarker.id),
              })
                .then(() => {
                  addDoc(subscribedUsersRef, {
                    userID: auth.currentUser.uid,
                  }).catch((error) => {
                    Alert.alert(error.message);
                  });
                })
                .catch((error) => {
                  Alert.alert(error.message);
                });
            }
          } else {
            Alert.alert("User not found");
          }
        })
        .catch((error) => {
          Alert.alert("Error fetching user data", error.message);
        });
    };

    const removeFromFavourites = () => {
      const userRef = doc(db, "users", auth.currentUser.uid);

      const value = route.params.selectedMarker;

      getDoc(userRef)
        .then((docSnapshot) => {
          if (docSnapshot.exists()) {
            const userData = docSnapshot.data();

            updateDoc(userRef, {
              favouriteCourts: arrayRemove(value.id),
            })
              .then(() => {
                const subscribedUsersCollection = collection(
                  db,
                  `courts-cz1/${value.id}/subscribedUsers`
                );
                const q = query(
                  subscribedUsersCollection,
                  where("userID", "==", auth.currentUser.uid)
                );

                getDocs(q)
                  .then((querySnapshot) => {
                    querySnapshot.forEach((document) => {
                      const docToDelete = doc(
                        db,
                        `courts-cz1/${value.id}/subscribedUsers`,
                        document.id
                      );
                      deleteDoc(docToDelete).catch((error) => {
                        Alert.alert(error.message);
                      });
                    });
                  })
                  .catch((error) => {
                    Alert.alert(error.message);
                  });
              })
              .catch((error) => {
                Alert.alert(error.message);
              });
          } else {
            Alert.alert("User not found");
          }
        })
        .catch((error) => {
          Alert.alert("Error fetching user data", error.message);
        });
    };

    const [opacityValues] = useState(images.map(() => new Animated.Value(0)));

    const MarkerInfoComp = useMemo(() => {
      return (
        <View
          style={{
            width: "100%",
            height: screenHeight / 2.8,
            backgroundColor: "white",
          }}
        >
          <Pressable
            style={{
              position: "absolute",
              width: 40,
              height: 40,
              borderRadius: 50,
              backgroundColor: "rgba(220,220,220,0.8)",
              top: 45,
              right: 15,
              zIndex: 10,
              justifyContent: "center",
            }}
            onPress={() => {
              navigation.navigate("Map", {
                showMarker: true,
                selectedMarker: route?.params?.selectedMarker,
              });
            }}
          >
            <Feather
              name="map"
              size={23}
              color="black"
              style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
            />
          </Pressable>

          <Pressable
            style={{
              position: "absolute",
              width: 40,
              height: 40,
              borderRadius: 50,
              backgroundColor: "rgba(220,220,220,0.8)",
              top: 45,
              left: 15,
              zIndex: 10,
              justifyContent: "center",
            }}
            onPress={() => {
              navigation.goBack();
            }}
          >
            <AntDesign
              name="arrowleft"
              size={24}
              color="black"
              style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
            />
          </Pressable>
          <Swiper
            loadMinimal
            loadMinimalSize={3}
            dot={
              <View
                style={{
                  backgroundColor: "rgba(255,255,255,0.5)",
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  marginLeft: 3,
                  marginRight: 3,
                  marginTop: 0,

                  bottom: -17,
                }}
              />
            }
            activeDot={
              <View
                style={{
                  backgroundColor: "rgba(255,255,255,0.9)",
                  width: 8,
                  height: 8,
                  borderRadius: 4,
                  marginLeft: 3,
                  marginRight: 3,
                  marginTop: 0,
                  bottom: -17,
                }}
              />
            }
          >
            {images.map((image, index) => (
              <View key={index} style={{ flex: 1, backgroundColor: "#ceefc8" }}>
                {loadingStatus[index] && (
                  <ActivityIndicator
                    style={{
                      position: "absolute",
                      alignSelf: "center",
                      marginTop: 140,
                      zIndex: 1,
                    }}
                  />
                )}
                <Animated.View
                  style={{
                    opacity: opacityValues[index],
                  }}
                >
                  <Image
                    source={{
                      uri: `${image}`,
                    }}
                    style={{
                      width: "100%",
                      height: screenHeight / 2.8,
                      borderTopRightRadius: 9,
                      borderTopLeftRadius: 9,
                    }}
                    onLoadEnd={() => {
                      Animated.timing(opacityValues[index], {
                        toValue: 1,
                        duration: 250,
                        useNativeDriver: true,
                      }).start();
                      updateLoadingStatus(index, false);
                    }}
                  />
                </Animated.View>
              </View>
            ))}
          </Swiper>
        </View>
      );
    });

    if (!language) {
      return (
        <View
          style={{ backgroundColor: "white", height: "100%", width: "100%" }}
        >
          <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
        </View>
      );
    }

    if (!events) {
      return;
    }

    return (
      <View
        style={{
          backgroundColor: "white",
          flex: 1,
        }}
      >
        {!timeoutDone && (
          <ActivityIndicator
            size={"small"}
            style={{ top: screenHeight / 2, alignSelf: "center" }}
          ></ActivityIndicator>
        )}
        <Animated.View
          style={{ width: "100%", opacity: opacityMain, height: "100%" }}
        >
          {MarkerInfoComp}
          <View
            style={{
              width: "100%",
              height: 4,
              backgroundColor: red,

              alignSelf: "center",
            }}
          ></View>
          <ScrollView
            contentContainerStyle={{
              // height: "200%",
              flexGrow: 1,
              width: "100%",
              paddingBottom: "15%",
              // marginBottom: 1000,
            }}
          >
            <View
              style={{
                width: "90%",
                flexDirection: "row",
                marginTop: "8%",
                alignSelf: "center",
                justifyContent: "space-between",
              }}
            >
              <Text
                style={{
                  alignSelf: "flex-start",

                  fontWeight: "600",
                  fontSize: 18,
                  color: "black",
                  overflow: "visible",
                  maxWidth: "85%",
                }}
              >
                {route.params.selectedMarker.address}
              </Text>
              <Pressable
                onPress={() => {
                  if (isFavourite) {
                    removeFromFavourites();
                  } else {
                    addToFavourites();
                  }
                }}
                style={{
                  height: 50,
                  width: 50,
                  marginTop: -15,
                  marginRight: -10,
                  padding: 10,
                }}
              >
                {isFavourite ? (
                  <FontAwesome
                    name="star"
                    size={26}
                    color="#fcc205"
                    style={{ alignSelf: "flex-end" }}
                  />
                ) : (
                  <FontAwesome
                    name="star-o"
                    size={26}
                    color="#fcc205"
                    style={{ alignSelf: "flex-end" }}
                  />
                )}
              </Pressable>
            </View>

            <View
              style={{
                width: "90%",

                marginTop: "12%",
                alignSelf: "center",
                justifyContent: "space-between",
              }}
            >
              <Text
                style={{
                  alignSelf: "flex-start",
                  fontSize: 15,
                  color: "black",
                  overflow: "visible",
                  //   paddingLeft: 2,
                }}
              >
                {route.params.selectedMarker.description}
              </Text>
              {route.params.selectedMarker.open && (
                <Text
                  style={{
                    alignSelf: "flex-start",
                    fontSize: 13,
                    fontWeight: "300",
                    color: "black",
                    overflow: "visible",
                    //   paddingLeft: 2,
                    paddingTop: 30,
                  }}
                >
                  {language.openingHours}: {route.params.selectedMarker.open}
                </Text>
              )}
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "90%",
                alignSelf: "center",
                marginTop: "12%",
              }}
            >
              <View
                style={{
                  borderRadius: 13,

                  width: "40%",
                  height: 30,
                  backgroundColor: "rgba(0,0,0,0.02)",
                  borderWidth: 1,
                  borderColor: "rgba(0,0,0,0.1)",

                  shadowOffset: { width: 0, height: 0 },
                  justifyContent: "center",
                  // marginBottom: 30,
                  bottom: 6,
                }}
              >
                <Text
                  style={{
                    fontWeight: "400",
                    alignSelf: "center",
                    color: "rgba(0,0,0,0.9)",
                    fontSize: 14,
                  }}
                >
                  {language.basketsCount}: {route.params.selectedMarker.baskets}
                </Text>
              </View>
              <View
                style={{
                  borderRadius: 13,

                  width: "50%",
                  height: 30,
                  backgroundColor: "rgba(0,0,0,0.02)",
                  borderWidth: 1,
                  borderColor: "rgba(0,0,0,0.1)",

                  shadowOffset: { width: 0, height: 0 },
                  justifyContent: "center",

                  bottom: 6,
                }}
              >
                <Text
                  style={{
                    fontWeight: "400",
                    alignSelf: "center",
                    color: "rgba(0,0,0,0.9)",
                    fontSize: 14,
                  }}
                >
                  {language.surfaceType}: {route.params.selectedMarker.surface}
                </Text>
              </View>
            </View>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-between",
                width: "90%",
                alignSelf: "center",
                marginTop: "11%",
                marginBottom: "3%",
              }}
            >
              <View
                style={{
                  borderRadius: 13,

                  width: "40%",
                  height: 30,

                  shadowOffset: { width: 0, height: 0 },
                  justifyContent: "center",
                  marginTop: 3,
                  bottom: 6,
                }}
              >
                <Text
                  style={{
                    fontWeight: 600,
                    fontSize: 18,
                    color: "black",
                    overflow: "visible",
                  }}
                >
                  {language.games}:
                </Text>
              </View>
              <Pressable
                onPress={() => {
                  navigateToCreateGames();
                }}
                style={({ pressed }) => ({
                  borderRadius: 8,
                  transform: [{ scale: pressed ? 0.97 : 1 }],
                  width: "55%",
                  height: 35,
                  backgroundColor: red,

                  justifyContent: "center",
                  // marginBottom: 30,
                  bottom: 6,
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    alignSelf: "center",
                    color: "white",
                    fontSize: 15,
                    // fontFamily: "roboto",
                  }}
                >
                  {language.createGame}
                </Text>
              </Pressable>
            </View>

            {events.map((event, index) => {
              if (event.key === "comp") {
                return <View key={event.key} style={{ height: 115 }}></View>;
              } else {
                return (
                  <EventItem
                    key={event.eventID}
                    value={event}
                    index={index}
                    totalEvents={events.length}
                    language={language}
                  />
                );
              }
            })}
          </ScrollView>
        </Animated.View>
      </View>
    );
  };

  return (
    <SafeAreaView
      style={{
        height: "105%",
        backgroundColor: "white",
        marginTop: -insets.top,
      }}
      edges={["top"]}
    >
      <Event />
    </SafeAreaView>
  );
}
