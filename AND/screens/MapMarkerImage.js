import React from "react";
import { Image } from "react-native";

const MapMarkerImage = React.memo(
  ({ source }) => {
    return (
      <Image
        source={source}
        style={{
          width: 70,
          height: 70,
          position: "absolute",
          left: -9,
          bottom: -28,
          overflow: "visible",
          zIndex: 1,
        }}
      />
    );
  },
  (prevProps, nextProps) => prevProps.source === nextProps.source
);

export default MapMarkerImage;
