import React, { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  Button,
  TextInput,
  Image,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  Animated,
  KeyboardAvoidingView,
  Dimensions,
  Pressable,
  TouchableWithoutFeedback,
  Keyboard,
  Modal,
  Linking,
} from "react-native";
import {
  signInWithEmailAndPassword,
  GoogleAuthProvider,
  signInWithCredential,
  sendPasswordResetEmail,
} from "firebase/auth";
import { red } from "./colors";
import {
  doc,
  Firestore,
  setDoc,
  getDoc,
  collection,
  getDocs,
  updateDoc,
  increment,
} from "firebase/firestore";
import { auth } from "../config/firebase";
import { db } from "../config/firebase";
// import { storage } from "../config/firebase";
import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";

import * as Google from "expo-auth-session/providers/google";

// import { styleExtras } from "@rnmapbox/maps/lib/commonjs/utils/styleMap";

const screenHeight = Dimensions.get("window").height;
const screenWidth = Dimensions.get("window").width;

export default function Login({ navigation }) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const [imageLoaded, setImageLoaded] = useState(false);

  const [urls, setUrls] = useState();

  useEffect(() => {
    const fetchData = async () => {
      const infoCollection = collection(db, "info");
      const infoSnapshot = await getDocs(infoCollection);
      const infoData = infoSnapshot.docs.map((doc) => {
        setUrls(doc.data());
      });
    };

    fetchData();
  }, []);

  const checkIfUserExists = async () => {
    const docRef = doc(db, "users/", auth.currentUser.uid);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return true;
    } else {
      return false;
    }
  };

  const onHandleLogin = async () => {
    try {
      if (email !== "" && password !== "") {
        await signInWithEmailAndPassword(auth, email, password)
          .then(async () => {
            const docRef = doc(db, "users", auth.currentUser.uid);

            await updateDoc(docRef, {
              logCounter: increment(1),
            });
          })
          .catch((err) => Alert.alert("Login error", err.message));
      }
    } catch (error) {
      alert(error);
    }
  };

  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId:
      "99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n.apps.googleusercontent.com",
  });

  useEffect(() => {
    if (response?.type === "success") {
      const { id_token } = response.params;

      const credential = GoogleAuthProvider.credential(id_token);

      signInWithCredential(auth, credential)
        .then(async (userCredential) => {
          // Signed in
          const user = userCredential.user;
          const docRef = doc(db, "users", auth.currentUser.uid);

          const docRef2 = doc(db, "users2/", auth.currentUser.uid);
          const docSnap = await getDoc(docRef2);

          if (docSnap.exists()) {
            console.log("exists");
            await updateDoc(docRef, {
              logCounter: increment(1),
            });
          }
        })
        .catch((error) => {
          alert("Error signing in with Google", error);
        });
    }
  }, [response]);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  ////////// Password reset ///////////////
  const [emailReset, setEmailReset] = useState("");

  const onResetPassword = () => {
    sendPasswordResetEmail(auth, emailReset)
      .then(() => {
        alert("Password reset email sent to " + emailReset);
      })
      .catch((error) => {
        var errorCode = error.code;
        var errorMessage = error.message;
        // ..
        alert("Error: " + errorMessage);
      });
  };

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [isModalVisible3, setModalVisible3] = useState(false);

  const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

  const toggleModal3 = () => {
    if (isModalVisible3) {
      Animated.timing(modalAnimatedValue3, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible3(false));
    } else {
      setModalVisible3(true);
      Animated.timing(modalAnimatedValue3, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [showEmail, setShowEmail] = useState(false);

  ////////// Password reset ///////////////

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView style={{ flex: 1, backgroundColor: "white" }}>
        {!imageLoaded && urls && (
          <View
            style={{
              width: "100%",
              height: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <ActivityIndicator></ActivityIndicator>
          </View>
        )}

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible3}
          onRequestClose={toggleModal3}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal3}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue3,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "80%",
                    // height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "0%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "400",
                        fontSize: 15,
                        color: "rgba(0,0,0,0.8)",
                        marginTop: 15,
                        lineHeight: 21,
                      }}
                    >
                      Please read the full
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.termsAndConditions}`)
                        }
                      >
                        {" "}
                        Terms and Conditions
                      </Text>
                      ,{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() => Linking.openURL(`${urls.privacyPolicy}`)}
                      >
                        {" "}
                        Privacy Policy
                      </Text>{" "}
                      and{" "}
                      <Text
                        style={{ color: "#2a99fa" }}
                        onPress={() =>
                          Linking.openURL(`${urls.copyrightPolicy}`)
                        }
                      >
                        {" "}
                        Copyright Policy
                      </Text>{" "}
                      before proceeding.
                    </Text>
                  </View>

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "9%",
                      marginBottom: "3%",
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal3();
                        setTimeout(() => {
                          promptAsync().catch((error) => {
                            alert("Google auth error", error);
                          });
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        // width: "70%",
                        height: 40,
                        backgroundColor: red,
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "500",
                          alignSelf: "center",
                          color: "white",
                          fontSize: 14,
                          // fontFamily: "roboto",
                          paddingHorizontal: 15,
                        }}
                      >
                        I Understand and Agree
                      </Text>
                    </Pressable>

                    <Pressable
                      onPress={() => {
                        toggleModal3();
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        // width: "70%",
                        height: 37,
                        backgroundColor: "rgba(230,230,230,1)",
                        alignSelf: "center",

                        justifyContent: "center",
                        marginTop: 18,
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "500",
                          alignSelf: "center",
                          color: "rgba(0,0,0,0.7)",
                          fontSize: 14,
                          // fontFamily: "roboto",
                          paddingHorizontal: 20,
                        }}
                      >
                        Cancel
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "90%",
                    // height: "35%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <View
                    style={{
                      width: "90%",
                      alignSelf: "center",
                      // height: "20%",
                      justifyContent: "center",
                      marginTop: "3%",
                    }}
                  >
                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "500",
                        fontSize: 16,
                      }}
                    >
                      Reset your password
                    </Text>

                    <Text
                      style={{
                        textAlign: "center",
                        fontWeight: "400",
                        fontSize: 14,
                        marginTop: 15,
                      }}
                    >
                      Enter your email that is associated with your Let's Hoop
                      account
                    </Text>
                  </View>

                  <TextInput
                    style={{
                      alignSelf: "center",
                      width: "95%",
                      // height: "52%",
                      borderWidth: 0.5,
                      borderColor: "rgba(0,0,0,0.2)",
                      borderRadius: 7,
                      paddingHorizontal: 15,
                      paddingTop: 12,
                      paddingBottom: 13,
                      marginTop: "7%",
                    }}
                    value={emailReset}
                    onChangeText={(newText) => {
                      setEmailReset(newText);
                    }}
                    placeholder={"Email"}
                    multiline={true}
                  />

                  <View
                    style={{
                      // bottom: -30,
                      // position: "absolute",
                      // flexDirection: "row",
                      // justifyContent: "space-between",
                      width: "95%",
                      alignSelf: "center",
                      // bottom: 25,
                      marginTop: "9%",
                      marginBottom: 11,
                    }}
                  >
                    <Pressable
                      onPress={() => {
                        toggleModal2();
                        setTimeout(() => {
                          onResetPassword();
                        }, 200);
                      }}
                      style={({ pressed }) => ({
                        borderRadius: 8,
                        transform: [{ scale: pressed ? 0.97 : 1 }],
                        width: "70%",
                        height: 40,
                        backgroundColor: red,
                        alignSelf: "center",

                        justifyContent: "center",
                      })}
                    >
                      <Text
                        style={{
                          fontWeight: "600",
                          alignSelf: "center",
                          color: "white",
                          fontSize: 15,
                          // fontFamily: "roboto",
                        }}
                      >
                        Send email
                      </Text>
                    </Pressable>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>

        <StatusBar
          translucent={true}
          backgroundColor={"transparent"}
          barStyle="dark-content"
          // hidden={true}
        />

        <KeyboardAvoidingView
          // style={{ width: "100%", alignSelf: "center", alignItems: "center" }}
          // behavior="position"
          // behavior={Platform.OS === "ios" ? "position" : "height"}
          behavior={"height"}
          keyboardVerticalOffset={-210}
          // behavior="height"
          style={{
            width: "100%",
            backgroundColor: "rgba(0,0,0,0)",
            alignSelf: "center",
            // alignItems: "center",
          }}
          // enabled=[]
          // keyboardVerticalOffset={-180}
        >
          <Animated.View style={{ flex: 1, opacity: opacity1 }}></Animated.View>
          <View style={{ width: "100%", height: "100%", alignItems: "center" }}>
            {showEmail ? (
              <Text
                style={{ color: "rgba(0,0,0,0.5)", zIndex: 100 }}
                onPress={() => {
                  setShowEmail(false);
                }}
              >
                Contact <NAME_EMAIL>
              </Text>
            ) : (
              <Pressable
                style={{
                  top: 10,
                  left: 20,
                  position: "absolute",
                  // backgroundColor: "red",
                  padding: 3,
                  zIndex: 100,
                }}
                onPress={() => {
                  setShowEmail(true);
                }}
              >
                <AntDesign
                  name="questioncircleo"
                  size={24}
                  color="rgba(0,0,0,0.5)"
                />
              </Pressable>
            )}

            <View
              style={{
                // position: "absolute",
                width: "100%",
                // height: "100%",
                flex: 1,
                height: "100%",
                bottom: 10,

                alignSelf: "center",
                justifyContent: "flex-end",
                // alignItems: "center",

                // flex: 1,
                // marginTop: "100%",
              }}
            >
              <View
                style={{
                  // alignSelf: "center",
                  width: "100%",
                  height: "50%",
                  marginTop: "6%",
                  // marginBottom: "10%",
                  // top: 0,
                  // position: "absolute",
                  // marginLeft: -screenWidth,
                  // marginRight: 100,
                  left: screenWidth * 0.027,
                }}
              >
                <Image
                  onLoadEnd={() => {
                    setImageLoaded(true);
                  }}
                  source={require("./../images/newlogo.png")}
                  style={{
                    height: screenWidth * 0.75,
                    width: screenHeight * 0.27,

                    alignSelf: "center",
                    // flex: 1,
                    // resizeMode: "contain",
                  }}
                />
              </View>
              {/* <Text>Sign Up</Text> */}

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  width: "88%",
                  marginBottom: 5,
                  // marginLeft: -6,
                }}
              >
                <MaterialIcons
                  name="alternate-email"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Email"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoFocus={false}
                    value={email}
                    onChangeText={(text) => setEmail(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 15,
                  width: "88%",
                  // marginBottom: 10,
                }}
              >
                <MaterialIcons
                  name="lock-outline"
                  size={22}
                  color="rgba(0,0,0,0.5)"
                />
                <View
                  style={{
                    flex: 1,
                    borderBottomColor: "rgba(0,0,0,0.1)",
                    borderBottomWidth: 1,
                    marginLeft: 15,
                  }}
                >
                  <TextInput
                    style={{
                      height: 40,
                    }}
                    placeholder="Password"
                    placeholderTextColor={"rgba(0, 0, 0, 0.4)"}
                    autoCapitalize="none"
                    autoCorrect={false}
                    secureTextEntry={true}
                    textContentType="password"
                    value={password}
                    onChangeText={(text) => setPassword(text)}
                  />
                </View>
              </View>

              <View
                style={{
                  alignSelf: "center",
                  flexDirection: "row",
                  alignItems: "center",
                  marginTop: 10,
                  width: "88%",
                  // marginBottom: 10,
                  justifyContent: "flex-end",
                }}
              >
                <TouchableOpacity
                  style={{
                    backgroundColor: "white",
                    padding: 4,
                    paddingHorizontal: 10,
                  }}
                  onPress={toggleModal2}
                >
                  <Text style={{ fontSize: 13, color: "rgba(0,0,0,0.5)" }}>
                    Forgot password?
                  </Text>
                </TouchableOpacity>
              </View>

              <Pressable
                onPress={onHandleLogin}
                style={({ pressed }) => ({
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  marginTop: 25,
                  width: "90%",
                  alignItems: "center",
                  backgroundColor: red,
                  borderRadius: 10,
                  height: 45,
                  justifyContent: "center",
                  alignSelf: "center",
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    color: "white",
                    fontSize: 16,
                    marginTop: 2,
                  }}
                >
                  Log in
                </Text>
              </Pressable>

              <View
                style={{
                  marginTop: 13,
                  alignSelf: "center",
                  width: "92%",
                  flexDirection: "row",
                  justifyContent: "space-around",
                }}
              >
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
                <Text
                  style={{
                    fontWeight: "400",
                    fontSize: 13,
                    color: "rgba(0,0,0,0.5)",
                  }}
                >
                  OR
                </Text>
                <View
                  style={{
                    marginTop: 0,
                    alignSelf: "center",
                    width: "40%",
                    height: 1,
                    backgroundColor: "rgba(0,0,0,0.1)",
                  }}
                ></View>
              </View>

              <Pressable
                onPress={toggleModal3}
                style={({ pressed }) => ({
                  transform: [{ scale: pressed ? 0.99 : 1 }],
                  marginTop: 12,
                  width: "90%",
                  alignItems: "center",
                  backgroundColor: "rgba(0,0,0,0.04)",
                  borderRadius: 10,
                  height: 45,
                  justifyContent: "center",
                  alignSelf: "center",
                })}
              >
                <Text
                  style={{
                    fontWeight: "600",
                    color: "rgba(0,0,0,0.4)",
                    fontSize: 14,
                    // fontFamily: "Avenir-Light",
                    marginTop: 2,
                  }}
                >
                  Continue with Google
                </Text>
                <Image
                  style={{
                    height: 27,
                    width: 27,
                    position: "absolute",
                    left: 20,
                  }}
                  source={require("../images/google.png")}
                />
              </Pressable>

              <View
                style={{
                  marginTop: 30,
                  flexDirection: "row",
                  alignItems: "center",
                  alignSelf: "center",
                }}
              >
                <Text
                  style={{ color: "gray", fontWeight: "600", fontSize: 14 }}
                >
                  Don't have an account yet?{"  "}
                </Text>
                <TouchableOpacity onPress={() => navigation.navigate("Signup")}>
                  <Text style={{ color: red, fontWeight: "600", fontSize: 14 }}>
                    Sign up
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
}
