import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from "react-native-maps";
import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
  Fragment,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Pressable,
  AppState,
  TouchableWithoutFeedback,
  Dimensions,
  StatusBar,
} from "react-native";

import { auth, db } from "../config/firebase";
// import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  getCountFromServer,
  limit,
} from "firebase/firestore";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
// import { Feather } from "@expo/vector-icons";

import { TapGestureHandler } from "react-native-gesture-handler";

import { Entypo } from "@expo/vector-icons";

import { EN, CZ } from "./../assets/strings";
import FastImage from "react-native-fast-image";

import { useIsFocused } from "@react-navigation/native";

import {
  useSafeAreaInsets,
  SafeAreaView,
} from "react-native-safe-area-context";

// // import { SliderBox } from "react-native-image-slider-box";
const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

const AddCourtButton = React.memo(
  ({ navigation, selectedMarker, language }) => {
    // console.log("langagaga", language);
    return (
      <Pressable
        style={{
          position: "absolute",
          width: 60,
          height: 60,
          backgroundColor: "white",
          bottom: 18,
          right: 7,
          borderRadius: 100,
          justifyContent: "center",
          borderColor: "rgba(0,0,0,0.17)",
          borderWidth: 0.6,
          // opacity: selectedMarker ? 0 : 1,
          zIndex: selectedMarker ? -1 : 1,
        }}
        activeOpacity={0.6}
        onPress={() => {
          navigation.navigate("AddCourt", language);
        }}
      >
        <Image
          style={{
            height: 39,
            width: 39,
            alignSelf: "center",
            marginLeft: -12,
            marginBottom: -5,
          }}
          source={require("./../images/qq3.png")}
        ></Image>
        <Entypo
          name="plus"
          size={24}
          color={red}
          style={{ position: "absolute", right: 4, top: 7 }}
        />
      </Pressable>
    );
  }
);

export default function Map() {
  const navigation = useNavigation();

  const route = useRoute();

  const mapRef = useRef(null);

  const insets = useSafeAreaInsets();

  useEffect(() => {
    if (
      route?.params?.selectedMarker &&
      route?.params?.selectedMarker?.coords
    ) {
      setTimeout(() => {
        const selectedMarker = route.params.selectedMarker;
        setSelectedMarker(selectedMarker);

        mapRef.current.animateCamera(
          {
            center: {
              latitude: selectedMarker.coords[1] - 0.001,
              longitude: selectedMarker.coords[0],
            },
            // pitch: 2,
            // heading: 20,
            altitude: 1000,
            zoom: 16,
          },
          { duration: 1000 }
        );
      }, 1000);
    }
  }, [route]);

  const isFocused = useIsFocused();

  const [courts, setCourts] = useState(null);
  const [centerCoordinate, setCenterCoordinate] = useState(null);

  const [language2, setLanguage2] = useState("CZ");
  const [language, setLanguage] = useState(null);

  const [rerender, setRerender] = useState(null);

  useEffect(() => {
    const docRef = doc(db, "users", auth.currentUser.uid);

    const unsubscribe = onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        const doc = docSnap.data();

        if (
          doc?.centerCoord.latitude !== centerCoordinate?.latitude &&
          doc?.centerCoord.longitude !== centerCoordinate?.longitude
        ) {
          setCenterCoordinate(doc.centerCoord);
        }

        setLanguage2(doc.language);
      } else {
        console.log("No such document!");
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (language2 === "CZ") {
      setLanguage(CZ);
      // console.log(CZ);
    } else {
      setLanguage(EN);
    }
  }, [language2]);

  async function fetchCourts() {
    const q = query(collection(db, "courts-cz1"), where("address", "!=", ""));
    const currentUserRef = doc(db, "users", auth.currentUser.uid);

    const querySnapshot = await getDocs(q);

    const userSnap = await getDoc(currentUserRef);
    const userFavouriteCourts = userSnap.data().favouriteCourts || [];

    const docs = await Promise.all(
      querySnapshot.docs.map(async (doc) => {
        const courtData = doc.data();
        const isFavourite = userFavouriteCourts.includes(doc.id);

        const q = query(
          collectionGroup(db, "user_events"),
          where("courtID", "==", doc.id)
        );

        const snapshot = await getCountFromServer(q);

        const count = snapshot.data().count;

        if (courtData?.storage_id) {
          return {
            id: doc.id,
            eventsCount: count,
            coords: [courtData.coords[1], courtData.coords[0]],
            imageRefs: courtData.imageRefs,
            address: courtData.address,
            isFavourite,
            storage_id: courtData.storage_id,
            description: courtData.description,
            baskets: courtData.baskets,
            surface: courtData.surface,
            open: courtData.open ? courtData.open : null,
          };
        }
        return null;
      })
    );

    const filteredDocs = docs.filter((doc) => doc !== null);
    setCourts(filteredDocs);
  }

  useEffect(() => {
    const dbQuery = query(
      collection(db, "courts-cz1"),
      where("address", "!=", "")
    );

    const currentUserRef = doc(db, "users", auth.currentUser.uid);
    const unsubscribe = onSnapshot(dbQuery, async (querySnapshot) => {
      const userSnap = await getDoc(currentUserRef);
      const userFavouriteCourts = userSnap.data().favouriteCourts || [];

      const docs = await Promise.all(
        querySnapshot.docs.map(async (doc) => {
          const courtData = doc.data();

          if (courtData?.storage_id) {
            const isFavourite = userFavouriteCourts.includes(doc.id);

            const q = query(
              collectionGroup(db, "user_events"),
              where("courtID", "==", doc.id)
            );

            const snapshot = await getCountFromServer(q);

            const count = snapshot.data().count;

            return {
              id: doc.id,
              eventsCount: count,
              coords: [courtData.coords[1], courtData.coords[0]],
              imageRefs: courtData.imageRefs,
              address: courtData.address,
              isFavourite,
              storage_id: courtData.storage_id,
              description: courtData.description,
              baskets: courtData.baskets,
              surface: courtData.surface,
              open: courtData.open ? courtData?.open : null,
            };
          }
          return null;
        })
      );

      const filteredDocs = docs.filter((doc) => doc !== null);

      setCourts(filteredDocs);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const q = collectionGroup(db, "user_events");
    const unsubscribe = onSnapshot(q, (snapshot) => {
      if (snapshot.docChanges().length > 0) {
        fetchCourts();
      }
    });

    return unsubscribe;
  }, []);

  const [selectedMarker, setSelectedMarker] = useState(null);

  const [mapIsLoading, setMapIsLoading] = useState(true);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setMapIsLoading(false);
    }, 700);
    return () => clearTimeout(timeoutId);
  }, []);

  const [courtsWithFavorites, setCourtsWithFavorites] = useState([]);

  const [favouriteCourts, setFavouriteCourts] = useState([]);

  const mergeFavourites = useCallback(() => {
    if (courts) {
      const newCourts = courts.map((court) => ({
        ...court,
        isFavourite: favouriteCourts.includes(court.id),
      }));

      setCourtsWithFavorites(newCourts);
    }
  }, [courts, favouriteCourts]);

  useEffect(() => {
    mergeFavourites();
  }, [courts, favouriteCourts, mergeFavourites]);

  useEffect(() => {
    const userRef = doc(db, "users", auth.currentUser.uid);

    const unsubscribe = onSnapshot(
      userRef,
      (doc) => {
        if (doc.exists()) {
          const userData = doc.data();
          setFavouriteCourts(userData.favouriteCourts || []);
        } else {
          console.log("No such document!");
        }
      },
      (error) => {
        console.log("Error fetching document:", error);
      }
    );

    return () => {
      unsubscribe && unsubscribe();
    };
  }, []);

  const MarkerInfoComp = () => {
    const images = selectedMarker.imageRefs;

    const isFavourite = favouriteCourts.includes(selectedMarker.id);

    const [imageLoaded, setImageLoaded] = useState(false);
    const [index, setIndex] = useState(0);
    const [loadingNewImage, setLoadingNewImage] = useState(false);
    const opacity1 = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      if (imageLoaded) {
        setTimeout(() => {
          Animated.timing(opacity1, {
            toValue: 1,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }, 0);
      }
    }, [imageLoaded]);

    return (
      <View
        style={{
          width: "100%",
          height: screenHeight / 2.3,
          backgroundColor: "white",
          borderRadius: 10,
          borderWidth: 0.6,
          borderColor: "rgba(0,0,0,0.4)",
        }}
      >
        <ActivityIndicator
          style={{
            position: "absolute",
            alignSelf: "center",
            alignSelf: "center",
            marginTop: 140,
            opacity: 1,
          }}
        ></ActivityIndicator>
        {loadingNewImage && (
          <ActivityIndicator
            style={{
              position: "absolute",
              alignSelf: "center",
              marginTop: 115,
              opacity: 0.5,
              zIndex: 10,
            }}
            color="white"
          />
        )}
        <Animated.View
          style={{
            opacity: opacity1,
          }}
        >
          <Image
            source={{
              uri: `${images[index]}`,
            }}
            style={{
              width: "100%",
              height: screenHeight / 3,
              borderTopRightRadius: 9,
              borderTopLeftRadius: 9,
            }}
            onLoadEnd={() => {
              setImageLoaded(true);
              setLoadingNewImage(false);
            }}
          ></Image>
          <AntDesign
            name="close"
            size={29}
            color="white"
            style={{
              position: "absolute",
              marginTop: 5,
              right: 4,
              opacity: 1,
            }}
            onPress={() => {
              setSelectedMarker(null);
            }}
          />
          <MaterialIcons
            name="arrow-forward-ios"
            size={24}
            color="white"
            style={{
              position: "absolute",
              marginTop: 115,
              right: 3,
              opacity: 0.8,
            }}
            onPress={() => {
              setLoadingNewImage(true);
              setIndex((index + 1) % images.length);
            }}
          />
          <MaterialIcons
            name="arrow-back-ios"
            size={24}
            color="white"
            style={{
              position: "absolute",
              marginTop: 115,
              left: 13,
              opacity: 0.8,
            }}
            onPress={() => {
              setLoadingNewImage(true);
              setIndex((index - 1 + images.length) % images.length);
            }}
          />
          <View
            style={{
              position: "absolute",
              width: 57,
              height: 42,
              backgroundColor: "white",
              bottom: -5,
              // left: -20,
              borderTopRightRadius: 100,
              overflow: "hidden",
              zIndex: 1,
            }}
          ></View>
          {isFavourite ? (
            <Image
              source={require("./../images/qq-star2.png")}
              style={{
                width: 45,
                height: 45,
                position: "absolute",
                // marginTop: -49,
                left: 1,
                bottom: -12,
                overflow: "visible",
                zIndex: 1,
              }}
            />
          ) : (
            <Image
              source={require("./../images/qq2.png")}
              style={{
                width: 45,
                height: 45,
                position: "absolute",
                // marginTop: -49,
                left: 1,
                bottom: -12,
                overflow: "visible",
                zIndex: 1,
              }}
            />
          )}
          <View
            style={{
              width: "100%",
              height: 25,
              backgroundColor: "rgba(0,0,0,0.3)",
              // opacity: 0.5,
              bottom: 0,
              position: "absolute",
            }}
          >
            {/* <Text>{selectedMarker.nazev}</Text> */}
            <Text
              style={{
                color: "white",
                marginLeft: 62,
                zIndex: 2,
                fontWeight: "600",
                fontSize: 12,
                marginTop: 5,
                maxWidth: 280,
              }}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {selectedMarker.address}
            </Text>
          </View>

          <View
            style={{
              bottom: -30,
              flexDirection: "row",
              justifyContent: "space-between",
              width: "90%",
              alignSelf: "center",
              // alignItems: "center",

              flex: 1,
            }}
          >
            <Pressable
              onPress={() => {
                navigation.navigate("MyGamesStack", selectedMarker);
              }}
              style={({ pressed }) => ({
                borderRadius: 8,
                transform: [{ scale: pressed ? 0.97 : 1 }],
                width: "60%",
                height: 40,
                backgroundColor: red,
                shadowColor: red,
                shadowOpacity: 0.1,
                shadowRadius: 5,
                shadowOffset: { width: 0, height: 0 },
                justifyContent: "center",
                // marginBottom: 30,
                bottom: 8,
              })}
            >
              <Text
                style={{
                  fontWeight: "700",
                  alignSelf: "center",
                  color: "white",
                  fontSize: 15,
                  // fontFamily: "roboto",
                }}
              >
                {language.createGame}
              </Text>
            </Pressable>
            <Pressable
              onPress={() => {
                navigation.navigate("CourtInfo", {
                  selectedMarker: selectedMarker,
                  language: language,
                });
              }}
              style={({ pressed }) => ({
                borderRadius: 8,
                width: "30%",
                height: 40,
                backgroundColor: "rgba(0,0,0,0.04)",
                justifyContent: "center",
                transform: [{ scale: pressed ? 0.97 : 1 }],
                borderColor: "rgba(0,0,0,0.1)",
                borderWidth: 0.5,
                bottom: 8,
              })}
            >
              <Text style={{ fontWeight: "600", alignSelf: "center" }}>
                {language.moreCourtInfo}
              </Text>
            </Pressable>
          </View>
        </Animated.View>
      </View>
    );
  };

  if (!courts || !centerCoordinate || !language) {
    return (
      <View
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "#d8f4d2",
        }}
      >
        <ActivityIndicator
          style={{ marginTop: "60%" }}
          // color={"black"}
        ></ActivityIndicator>
        <View
          style={{
            width: "100%",
            height: 0.8,
            backgroundColor: "lightgrey",
            position: "absolute",
            bottom: 0,
          }}
        ></View>
      </View>
    );
  }

  // console.log(courts);

  const iconSize = 42;
  const iconSizeSelected = 47;

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      <View
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "#d8f4d2",
          // marginTop: -insets.top,
        }}
        onStartShouldSetResponder={() => true}
      >
        {isFocused && (
          <View
            style={{
              width: "100%",
              height: "100%",
              backgroundColor: "#d8f4d2",
            }}
            onStartShouldSetResponder={() => true}
          >
            {mapIsLoading && (
              <View
                style={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#d8f4d2",
                }}
              >
                <ActivityIndicator
                  style={{ marginTop: "60%" }}
                  // color={"black"}
                ></ActivityIndicator>
              </View>
            )}
            {!mapIsLoading && (
              <View
                style={{
                  width: "100%",
                  height: "100%",
                  backgroundColor: "#d8f4d2",
                }}
                onStartShouldSetResponder={() => true}
              >
                <MapView
                  ref={mapRef}
                  attributionEnabled={false}
                  // legalLabelInsets={false}

                  onPress={() => setSelectedMarker(null)}
                  style={{ flex: 1, paddingBottom: insets.top }}
                  customMapStyle={[
                    // {
                    //   featureType: "landscape.man_made",
                    //   stylers: [
                    //     {
                    //       color: "#f0f5ed",
                    //     },
                    //   ],
                    // },
                    {
                      featureType: "landscape.man_made",
                      elementType: "geometry",
                      stylers: [
                        {
                          color: "#f6f4ee",
                        },
                      ],
                    },
                    {
                      featureType: "landscape.natural",
                      stylers: [
                        {
                          color: "#c8ebb5",
                        },
                      ],
                    },
                    {
                      featureType: "poi",
                      stylers: [
                        {
                          visibility: "simplified",
                        },
                      ],
                    },
                    {
                      featureType: "poi.attraction",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.business",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.government",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.medical",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.park",
                      stylers: [
                        {
                          color: "#d0f0bd",
                        },
                      ],
                    },
                    {
                      featureType: "poi.place_of_worship",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.school",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "poi.sports_complex",
                      stylers: [
                        {
                          visibility: "off",
                        },
                      ],
                    },
                    {
                      featureType: "road.highway",
                      stylers: [
                        {
                          color: "#b6b6b8",
                          visibility: "off",
                        },
                        {
                          weight: 1,
                        },
                      ],
                    },

                    {
                      featureType: "water",
                      stylers: [
                        {
                          color: "#ade3f5",
                        },
                      ],
                    },
                  ]}
                  // liteMode={true}
                  // pitchEnabled={false}
                  // rotateEnabled={true}1
                  provider={PROVIDER_GOOGLE}
                  initialRegion={{
                    latitude: centerCoordinate.latitude,
                    longitude: centerCoordinate.longitude,
                    latitudeDelta: 0.922 * 0.6,
                    longitudeDelta: 0.421 * 0.6,
                  }}
                >
                  {courtsWithFavorites.map((court, index) => (
                    <Marker
                      key={index}
                      coordinate={{
                        latitude: court.coords[1],
                        longitude: court.coords[0],
                      }}
                      tracksViewChanges={true}
                      onPress={() => setSelectedMarker(court)}
                      anchor={{ x: 0.5, y: 0.5 }}
                    >
                      {/* <TapGestureHandler
                    onEnded={() => {
                      hristeClick(court);
                    }}
                    style={{
                      // height: 60,
                      // width: 60,
                      backgroundColor: "red",
                      overflow: "visible",
                    }}
                  > */}
                      <View style={{ marginBottom: 30 }}>
                        {court.eventsCount === 0 ? (
                          <View>
                            {court.isFavourite ? (
                              <FastImage
                                source={require("./../images/qq-star5.png")}
                                style={{
                                  width:
                                    selectedMarker === court
                                      ? iconSizeSelected
                                      : iconSize,
                                  height:
                                    selectedMarker === court
                                      ? iconSizeSelected
                                      : iconSize,
                                  // marginTop: -49,
                                  overflow: "visible",
                                  // marginTop: -5,
                                  // position: "absolute",
                                }}
                              />
                            ) : (
                              <FastImage
                                source={require("./../images/qq5.png")}
                                style={{
                                  width:
                                    selectedMarker === court
                                      ? iconSizeSelected
                                      : iconSize,
                                  height:
                                    selectedMarker === court
                                      ? iconSizeSelected
                                      : iconSize,
                                  // marginTop: -49,
                                  // overflow: "visible",
                                  // marginTop: -40,
                                }}
                              />
                            )}
                          </View>
                        ) : (
                          <View>
                            {court.isFavourite ? (
                              <View>
                                <FastImage
                                  source={require("./../images/qq-empty-star5.png")}
                                  style={{
                                    width:
                                      selectedMarker === court
                                        ? iconSizeSelected
                                        : iconSize,
                                    height:
                                      selectedMarker === court
                                        ? iconSizeSelected
                                        : iconSize,
                                    overflow: "visible",
                                  }}
                                />
                                <Text
                                  style={{
                                    position: "absolute",
                                    color: red,
                                    fontWeight: "600",
                                    fontSize:
                                      selectedMarker === court ? 19 : 16,
                                    top: selectedMarker === court ? 6 : 6,
                                    left:
                                      selectedMarker === court ? 17.8 : 16.3,
                                    textAlign: "center",
                                  }}
                                >
                                  {court.eventsCount}
                                </Text>
                              </View>
                            ) : (
                              <View>
                                <FastImage
                                  source={require("./../images/qq-empty5.png")}
                                  style={{
                                    width:
                                      selectedMarker === court
                                        ? iconSizeSelected
                                        : iconSize,
                                    height:
                                      selectedMarker === court
                                        ? iconSizeSelected
                                        : iconSize,
                                    // marginTop: -49,
                                    overflow: "visible",
                                    // marginTop: -5,
                                    // position: "absolute",
                                  }}
                                />
                                <Text
                                  style={{
                                    position: "absolute",
                                    color: red,
                                    fontWeight: "600",
                                    fontSize:
                                      selectedMarker === court ? 19 : 16,
                                    top: selectedMarker === court ? 6 : 6,
                                    left:
                                      selectedMarker === court ? 17.8 : 16.5,
                                    textAlign: "center",
                                  }}
                                >
                                  {court.eventsCount}
                                </Text>
                              </View>
                            )}
                          </View>
                        )}
                      </View>
                      {/* </TapGestureHandler> */}
                    </Marker>
                  ))}
                </MapView>
                {selectedMarker ? (
                  <View
                    style={{
                      position: "absolute",
                      bottom: 15,
                      width: "95%",
                      alignSelf: "center",
                    }}
                  >
                    <MarkerInfoComp />
                  </View>
                ) : null}
              </View>
            )}
            <AddCourtButton
              navigation={navigation}
              selectedMarker={selectedMarker}
              language={language}
            />
          </View>
        )}
      </View>

      <View
        style={{
          width: "100%",
          height: 0.8,
          backgroundColor: "lightgrey",
          position: "absolute",
          bottom: 0,
        }}
      ></View>
    </View>
  );
}
