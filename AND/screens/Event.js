import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TouchableWithoutFeedback,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  // Vibration,
  Keyboard,
  Pressable,
  Modal,
  TouchableHighlight,
  ViewBase,
} from "react-native";

import { auth, db, functions } from "../config/firebase";
// import { storage } from "../config/firebase";
import { httpsCallable } from "firebase/functions";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useIsFocused,
} from "@react-navigation/native";
import * as Notifications from "expo-notifications";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  limit,
  addDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
  writeBatch,
  serverTimestamp,
  Firestore,
} from "firebase/firestore";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
import { Ionicons } from "@expo/vector-icons";

// import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import {
  GiftedChat,
  Bubble,
  InputToolbar,
  Composer,
  Send,
  Message,
  MessageText,
} from "react-native-gifted-chat";

import { useRoute } from "@react-navigation/native";

import { MaterialCommunityIcons } from "@expo/vector-icons";
import { EvilIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Entypo } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";

const Tab = createMaterialTopTabNavigator();

function calculateAge(dob) {
  const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
  const age_dt = new Date(diff_ms);
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

const styles = StyleSheet.create({
  component: {
    backgroundColor: "blue",
  },
});

const removeUserNotification = httpsCallable(
  functions,
  "removeUserNotification"
);

const EventHeaderComp = ({ route, navigation, language }) => {
  const [eventInfo, setEventInfo] = useState(route.params.eventInfo);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    const eventID = eventInfo?.eventID?.split("_")[1];
    const event_userID = eventInfo?.eventID?.split("_")[0];
    const docRef = doc(db, `events/${event_userID}/user_events/${eventID}`);

    const unsubscribe = onSnapshot(docRef, async (docSnapshot) => {
      if (docSnapshot.exists()) {
        const eventData = docSnapshot.data();

        const userCollectionRef = collection(
          db,
          `events/${event_userID}/user_events/${eventID}/eventUsers`
        );
        const userEventsSnapshot = await getDocs(userCollectionRef);

        const users = userEventsSnapshot.docs.map((doc) => doc.data().userID);

        const eventWithUsers = {
          id: docSnapshot.id,
          ...eventData,
          users: users,
        };

        setEventInfo(eventWithUsers);
        // fetchUserDocuments();
      } else {
      }
    });

    return unsubscribe;
  }, []);

  // const navigation = useNavigation();

  const images = eventInfo.courtObj.imageRefs;

  const leaveEvent = async (hideModal) => {
    const [creatorID, eventID] = eventInfo.eventID.split("_");

    const docRef = doc(db, `events/${creatorID}/user_events/${eventID}`);

    hideModal();

    setTimeout(async () => {
      try {
        await deleteDoc(
          doc(
            db,
            `events/${creatorID}/user_events/${eventID}/eventUsers/${auth.currentUser.uid}`
          )
        );

        await updateDoc(docRef, {
          lastUpdated: serverTimestamp(),
          usersChange: "left",
          usersDelete: arrayRemove(auth.currentUser.uid),
        });
      } catch (error) {
        alert(error);
      }
    }, 300);
  };

  const joinEvent = async () => {
    const [creatorID, eventID] = eventInfo.eventID.split("_");

    const docRef = doc(db, `events/${creatorID}/user_events/${eventID}`);

    if (
      eventInfo.usersLimit !== null &&
      eventInfo.users.length >= eventInfo.usersLimit
    ) {
      alert(language.eventFull);
    } else {
      try {
        await setDoc(
          doc(
            db,
            `events/${creatorID}/user_events/${eventID}/eventUsers/${auth.currentUser.uid}`
          ),
          {
            userID: auth.currentUser.uid,
            eventPath: `events/${creatorID}/user_events/${eventID}`,
          }
        );
        await updateDoc(docRef, {
          lastUpdated: serverTimestamp(),
          usersChange: "joined",
          usersDelete: arrayUnion(auth.currentUser.uid),
        });
      } catch (error) {
        alert(error);
      }
    }
  };

  const [imageLoaded, setImageLoaded] = useState(false);
  const [index, setIndex] = useState(0);
  const [loadingNewImage, setLoadingNewImage] = useState(false);

  const opacity1 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  // return;

  // return <Text>{eventInfo.images}</Text>;
  const ModalComp = () => {
    const [modalVisible, setModalVisible] = useState(false);
    const [slideAnimation] = useState(new Animated.Value(screenHeight));
    const fadeAnimation = useState(new Animated.Value(0))[0];

    const [deleteCounter, setDeleteCounter] = useState(0);

    useEffect(() => {}, [deleteCounter]);

    const showModal = () => {
      setModalVisible(true);
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: screenHeight / 1.3,
          duration: 190,
          useNativeDriver: false,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 0.5,
          duration: 160,
          useNativeDriver: false,
        }),
      ]).start();
    };

    const hideModal = () => {
      Animated.parallel([
        Animated.timing(slideAnimation, {
          toValue: screenHeight,
          duration: 190,
          useNativeDriver: false,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 0,
          duration: 160,
          useNativeDriver: false,
        }),
      ]).start(() => setModalVisible(false));

      setTimeout(() => {
        setDeleteCounter(0);
      }, 100);
    };

    const deleteEvent = async () => {
      setDeleteCounter(deleteCounter + 1);
      if (deleteCounter === 3) {
        const [creatorID, docID] = eventInfo.eventID.split("_");

        const docRef = doc(
          db,
          `events/${eventInfo.creator}/user_events/`,
          `${docID}`
        );

        const chatSubcollectionQuery = collection(docRef, "chat");
        const chatQuerySnapshot = await getDocs(chatSubcollectionQuery);

        const batch = writeBatch(db);

        chatQuerySnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        const eventUsersSubcollectionQuery = collection(docRef, "eventUsers");
        const eventUsersQuerySnapshot = await getDocs(
          eventUsersSubcollectionQuery
        );

        eventUsersQuerySnapshot.forEach((doc) => {
          batch.delete(doc.ref);
        });

        await batch.commit();

        await deleteDoc(docRef);

        hideModal();
        setTimeout(() => {
          navigation.goBack();
        }, 200);
      }
    };

    const [editModalVisible, setEditModalVisible] = useState(false);

    if (!eventInfo.users) {
      return;
    }

    return (
      <View style={{}}>
        <Pressable onPress={showModal}>
          <Entypo
            name="dots-three-horizontal"
            size={28}
            color="white"
            style={{ marginRight: 5 }}
          />
        </Pressable>

        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="none"
          onRequestClose={hideModal}
        >
          <TouchableOpacity
            style={{ flex: 1 }}
            activeOpacity={1}
            onPressOut={hideModal}
          >
            <Animated.View
              style={{
                flex: 1,
                backgroundColor: fadeAnimation.interpolate({
                  inputRange: [0, 0.5],
                  outputRange: ["rgba(0,0,0,0)", "rgba(0,0,0,0.5)"],
                }),
              }}
            >
              <Animated.View
                style={{
                  // position: "absolute",
                  bottom:
                    auth.currentUser.uid == eventInfo.creator ? "2%" : "-8%",
                  width: "100%",
                  height:
                    auth.currentUser.uid == eventInfo.creator ? "25%" : "15%",
                  backgroundColor: "white",
                  transform: [{ translateY: slideAnimation }],
                }}
                onStartShouldSetResponder={() => true}
              >
                {eventInfo.users.includes(auth.currentUser.uid) &&
                  eventInfo.creator !== auth.currentUser.uid && (
                    <Pressable
                      style={({ pressed }) => ({
                        position: "absolute",
                        bottom: 39,
                        alignSelf: "center",
                        backgroundColor: pressed
                          ? "rgba(251, 31, 80, 0.3)"
                          : "rgba(251, 31, 80, 0.1)",
                        // opacity: pressed ? 0.4 : 0.2,
                        // borderWidth: 1,
                        // borderColor: pressed ? "white" : red,
                        borderRadius: 10,
                        width: "90%",
                        paddingVertical: 10,
                        alignItems: "center",
                        flexDirection: "row",
                        justifyContent: "center",
                      })}
                      onPress={() => {
                        leaveEvent(hideModal);
                      }}
                    >
                      {/* {({ pressed }) => ( */}
                      <Text
                        style={{
                          color: red,
                          fontWeight: "600",
                          fontSize: 16,
                          // backgroundColor: "green",
                          // padding: 10,
                          paddingVertical: 3,
                        }}
                      >
                        {language.leaveGame}
                      </Text>
                      <Ionicons
                        name="exit-outline"
                        size={24}
                        color={red}
                        style={{ position: "absolute", right: 12 }}
                      />
                      {/* )} */}
                    </Pressable>
                  )}
                {!eventInfo.users.includes(auth.currentUser.uid) && (
                  <Pressable
                    style={({ pressed }) => ({
                      position: "absolute",
                      zIndex: 1,
                      // right: 15,
                      bottom: 35,
                      alignSelf: "center",
                      backgroundColor:
                        eventInfo.usersLimit !== null &&
                        eventInfo.users.length >= eventInfo.usersLimit
                          ? "rgba(170,170,170,1)"
                          : red,

                      // backgroundColor:
                      //   eventInfo.users.length < eventInfo.usersLimit &&
                      //   eventInfo.usersLimit
                      //     ? red
                      //     : "rgba(170,170,170,1)",
                      // borderWidth: 1,
                      // borderColor: red,
                      borderRadius: 10,
                      paddingHorizontal: 40,
                      paddingVertical: 13,
                      width: "90%",
                      alignItems: "center",
                      transform: [{ scale: pressed ? 0.986 : 1 }],
                      // shadowColor: "rgba(245,49,86,1)",
                      // shadowOffset: {
                      //   width: 0,
                      //   height: 0,
                      // },
                      // elevation: 30,
                      // shadowOpacity: 0.4,
                      // shadowRadius: 10,
                    })}
                    onPress={() => {
                      hideModal();
                      setTimeout(joinEvent, 330);
                    }}
                  >
                    <Text
                      style={{
                        color: "white",
                        fontWeight: "600",
                        fontSize: 16,
                      }}
                    >
                      {language.joinGame}
                    </Text>
                  </Pressable>
                )}

                {eventInfo.creator == auth.currentUser.uid && (
                  <Pressable
                    style={({ pressed }) => ({
                      position: "absolute",
                      zIndex: 1,

                      bottom: 40,
                      alignSelf: "center",
                      backgroundColor: red,

                      borderRadius: 10,
                      paddingHorizontal: 40,
                      paddingVertical: 13,
                      width: "90%",
                      alignItems: "center",
                      transform: [{ scale: pressed ? 0.986 : 1 }],
                      // shadowColor: "rgba(245,49,86,1)",
                      // shadowOffset: {
                      //   width: 0,
                      //   height: 0,
                      // },
                      // elevation: 30,
                      // shadowOpacity: 0.3,
                      // shadowRadius: 5,
                      flexDirection: "row",
                      justifyContent: "center",
                    })}
                    onPress={deleteEvent}
                  >
                    {deleteCounter < 4 && (
                      <Text
                        style={{
                          color: "white",
                          fontWeight: "600",
                          fontSize: 16,
                          alignSelf: "center",
                        }}
                      >
                        {deleteCounter == 0
                          ? `${language.deleteEvent}`
                          : `${language.confirm} (${4 - deleteCounter})`}
                      </Text>
                    )}

                    {deleteCounter < 4 && (
                      <MaterialIcons
                        name="delete-forever"
                        size={24}
                        color="white"
                        style={{ position: "absolute", right: 10 }}
                      />
                    )}

                    {deleteCounter >= 4 && (
                      <ActivityIndicator color={"white"}></ActivityIndicator>
                    )}
                  </Pressable>
                )}

                {eventInfo.creator == auth.currentUser.uid && (
                  <Pressable
                    style={({ pressed }) => ({
                      position: "absolute",
                      zIndex: 1,

                      bottom: 110,
                      alignSelf: "center",
                      backgroundColor: "rgba(0,0,0,0.07)",

                      borderRadius: 10,
                      paddingHorizontal: 40,
                      paddingVertical: 13,
                      width: "90%",
                      alignItems: "center",
                      transform: [{ scale: pressed ? 0.986 : 1 }],
                      // shadowColor: "rgba(245,49,86,1)",
                      // shadowOffset: {
                      //   width: 0,
                      //   height: 0,
                      // },
                      // elevation: 30,
                      // shadowOpacity: 0.3,
                      // shadowRadius: 5,
                      flexDirection: "row",
                      justifyContent: "center",
                    })}
                    onPress={() => {
                      hideModal();
                      navigation.navigate("EditEvent", {
                        ...eventInfo,
                        language,
                      });
                    }}
                  >
                    <Text
                      style={{
                        color: "rgba(0,0,0,0.8)",
                        fontWeight: "500",
                        fontSize: 16,
                        alignSelf: "center",
                      }}
                    >
                      {language.editEvent}
                    </Text>

                    <Feather
                      name="edit"
                      size={20}
                      color="rgba(0,0,0,0.8)"
                      style={{ position: "absolute", right: 10 }}
                    />
                  </Pressable>
                )}
              </Animated.View>
            </Animated.View>
          </TouchableOpacity>
        </Modal>
      </View>
    );
  };

  // if (!eventInfo.users) {
  //   return;
  // }

  return (
    <View
      style={{
        width: "100%",
        height: screenHeight / 2.8 + insets.top * 0.3,
        backgroundColor: "white",
        marginTop: -insets.top,
        // borderTopColor: red,
        // borderTopWidth: 3
      }}
    >
      {/* <StatusBar hidden /> */}
      <Pressable
        style={{
          position: "absolute",
          width: 40,
          height: 40,
          borderRadius: 50,
          backgroundColor: "rgba(220,220,220,0.8)",
          top: 45,
          right: 15,
          zIndex: 10,
          justifyContent: "center",
        }}
        onPress={() => {
          navigation.navigate("Map", {
            showMarker: true,
            selectedMarker: eventInfo.courtObj,
          });
        }}
      >
        {/* <AntDesign
              name="arrowleft"
              size={24}
              color="black"
              style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
            /> */}
        <Feather
          name="map"
          size={23}
          color="black"
          style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
        />
      </Pressable>
      <Pressable
        style={{
          position: "absolute",
          width: 40,
          height: 40,
          borderRadius: 50,
          backgroundColor: "rgba(220,220,220,0.8)",
          top: 45,
          left: 15,
          zIndex: 10,
          justifyContent: "center",
        }}
        onPress={() => {
          navigation.goBack();
        }}
      >
        <AntDesign
          name="arrowleft"
          size={24}
          color="black"
          style={{ alignSelf: "center", marginTop: 2, marginLeft: 2 }}
        />
      </Pressable>
      <ActivityIndicator
        style={{
          position: "absolute",
          alignSelf: "center",
          // alignSelf: "center",
          top: screenHeight / 5.6,
          // opacity: 1,
        }}
      ></ActivityIndicator>
      {loadingNewImage && (
        <ActivityIndicator
          style={{
            position: "absolute",
            alignSelf: "center",
            marginTop: screenHeight / 5.7,
            opacity: 0.9,
            zIndex: 10,
          }}
          color="white"
        />
      )}
      <Animated.View
        style={{
          opacity: opacity1,
        }}
      >
        <Image
          source={{
            uri: `${images[index]}`,
          }}
          style={{
            width: "100%",
            height: screenHeight / 2.8 + insets.top * 0.3,
            // borderTopRightRadius: 9,
            // borderTopLeftRadius: 9,
          }}
          onLoadEnd={() => {
            setImageLoaded(true);
            setLoadingNewImage(false);
          }}
        ></Image>
        <Pressable
          style={{
            width: 40,
            height: 40,
            backgroundColor: "transparent",
            position: "absolute",
            // marginTop: "39%",
            top: "48%",
            right: 0,
            zIndex: 3,
          }}
          onPress={() => {
            setLoadingNewImage(true);
            setIndex((index + 1) % images.length);
          }}
        ></Pressable>

        <MaterialIcons
          name="arrow-forward-ios"
          size={20}
          color="white"
          style={{
            position: "absolute",
            marginTop: "39%",
            right: 3,
            opacity: 0.8,
          }}
        />

        <Pressable
          style={{
            width: 40,
            height: 40,
            backgroundColor: "transparent",
            position: "absolute",
            // marginTop: "39%",
            top: "48%",
            left: 0,
            zIndex: 3,
          }}
          onPress={() => {
            setLoadingNewImage(true);
            setIndex((index - 1 + images.length) % images.length);
          }}
        ></Pressable>

        <MaterialIcons
          name="arrow-back-ios"
          size={20}
          color="white"
          style={{
            position: "absolute",
            marginTop: "39%",
            left: 13,
            opacity: 0.8,
          }}
        />
        <LinearGradient
          colors={[
            "rgba(0,0,0,0.30)",
            "rgba(0,0,0,0.30)",
            "rgba(0,0,0,0.30)",
            "rgba(0,0,0,0.30)",
            "rgba(0,0,0,0.20)",
            // "rgba(0,0,0,0.20)",
            // "rgba(0,0,0,0.20)",
            // "rgba(0,0,0,0.1)",
            // "rgba(0,0,0,0.1)",
            "rgba(0,0,0,0)",
          ]}
          start={{ x: 0, y: 1 }}
          end={{ x: 0, y: 0 }}
          style={{
            position: "absolute",
            left: 0,
            bottom: 0,
            paddingTop: 5,
            // height: 135,
            height: "13%",
            width: "100%",
            // flexDirection: "row",
            alignItems: "center",
            // justifyContent: "space-between",
            // alignContent: "flex-start",
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              width: "100%",
              height: "90%",
              // bottom: 0,
              marginTop: 3,
            }}
          >
            <Text
              style={{
                color: "white",
                marginLeft: 5,
                zIndex: 2,
                fontWeight: "600",
                fontSize: 18,
                // marginTop: 8,
                maxWidth: 280,
              }}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {eventInfo.eventName}
            </Text>
            <ModalComp />
          </View>
        </LinearGradient>

        <View
          style={{
            bottom: -30,
            flexDirection: "row",
            justifyContent: "space-between",
            width: "90%",
            alignSelf: "center",
            // alignItems: "center",

            flex: 1,
          }}
        ></View>
      </Animated.View>
    </View>
  );
};

const ProfileStripeComp = React.memo(function CourtItem({
  user,
  eventInfo,
  profileImageRef,
  showActionsParents,
  onShowActions,
  language,
}) {
  const opacity1 = useRef(new Animated.Value(0)).current;

  const [showActions, setShowActions] = useState(false);

  const handleShowActions = () => {
    onShowActions(user?.uid);
  };

  const [imageLoaded, setImageLoaded] = useState(false);

  const opacityValue = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    if (imageLoaded) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start();
    }
  }, [imageLoaded]);

  const [isModalVisible, setModalVisible] = useState(false);

  const modalAnimatedValue = useRef(new Animated.Value(0)).current;

  const handleModalOpen = () => {
    Animated.timing(modalAnimatedValue, {
      toValue: 1,
      duration: 150,
      useNativeDriver: true,
    }).start();
  };

  const toggleModal = () => {
    if (isModalVisible) {
      setModalVisible(false);
    } else {
      setModalVisible(true);
      handleModalOpen();
    }
  };

  const uploadReport = async (reportText, user) => {
    const coll = collection(db, `reports-cz/`);

    if (reportText !== "") {
      try {
        const eventDocRef = await addDoc(coll, {
          report: reportText,
          reportedUser: user.uid,
          reporter: auth.currentUser.uid,
          date: new Date(new Date().getSeconds() * 1000),
        });

        alert("Uživatel nahlášen");
      } catch (error) {
        alert(error);
      }
    } else {
      alert("Zadejte prosím informace o nahlášení");
    }
  };

  const ReportModal = ({ isVisible, onClose, user }) => {
    const [report, setReport] = useState("");

    const modalAnimatedValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      if (isVisible) {
        Animated.timing(modalAnimatedValue, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      } else {
        Animated.timing(modalAnimatedValue, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    }, [isVisible]);

    return (
      <Modal
        animationType="none"
        transparent={true}
        visible={isModalVisible}
        onRequestClose={toggleModal}
      >
        <TouchableOpacity
          style={{ flex: 1, marginTop: -70 }}
          activeOpacity={1}
          onPressOut={toggleModal}
        >
          <Animated.View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0,0,0,0.5)",
              opacity: modalAnimatedValue,
            }}
          >
            <TouchableWithoutFeedback>
              <View
                style={{
                  width: "90%",
                  height: "40%",
                  backgroundColor: "#fff",
                  padding: 16,
                  borderRadius: 10,
                }}
              >
                <AntDesign
                  name="close"
                  size={28}
                  color="black"
                  style={{ position: "absolute", top: 7, right: 7 }}
                  onPress={toggleModal}
                />
                <View
                  style={{
                    alignSelf: "center",
                    flexDirection: "row",
                    marginTop: 10,
                  }}
                >
                  <Text style={{ fontWeight: "400", fontSize: 18 }}>
                    {language.reportUser + "   "}
                  </Text>
                  <Text style={{ fontWeight: "600", fontSize: 18 }}>
                    {user.name}
                  </Text>
                </View>

                <TextInput
                  style={{
                    alignSelf: "center",
                    width: "95%",
                    height: "60%",
                    borderWidth: 1,
                    borderColor: "rgba(0,0,0,0.3)",
                    borderRadius: 7,
                    paddingHorizontal: 10,
                    paddingVertical: 10,
                    marginTop: 30,
                  }}
                  value={report}
                  onChangeText={(newText) => {
                    setReport(newText);
                  }}
                  placeholder={language.reportText}
                  multiline={true}
                />

                <TouchableOpacity
                  style={{
                    backgroundColor: red,
                    borderRadius: 10,
                    marginTop: 20,
                    padding: 10,
                    width: "70%",
                    alignSelf: "center",
                  }}
                  onPress={() => {
                    uploadReport(report, user);

                    if (report !== "") {
                      onClose();
                    }
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      textAlign: "center",
                      fontWeight: "500",
                    }}
                  >
                    {language.send}
                  </Text>
                </TouchableOpacity>
              </View>
            </TouchableWithoutFeedback>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    );
  };

  const removeUser = async (user) => {
    const [creatorID, eventID] = eventInfo?.eventID?.split("_");

    const docRef = doc(db, `events/${creatorID}/user_events/${eventID}`);

    const eventUsersCollection = collection(
      db,
      `events/${creatorID}/user_events/${eventID}/eventUsers`
    );
    const q = query(eventUsersCollection, where("userID", "==", user.uid));
    const querySnapshot = await getDocs(q);

    for (const documentSnapshot of querySnapshot.docs) {
      await deleteDoc(documentSnapshot.ref);
    }

    await updateDoc(docRef, {
      kickedUser: serverTimestamp(),
    });

    removeUserNotification({
      userId: user.uid,
      eventId: eventID,
      eventName: eventInfo.eventName,
    })
      .then((result) => {})
      .catch((error) => {
        alert(error);
      });
  };

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [isModalVisible3, setModalVisible3] = useState(false);

  const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

  const toggleModal3 = () => {
    if (isModalVisible3) {
      Animated.timing(modalAnimatedValue3, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible3(false));
    } else {
      setModalVisible3(true);

      Animated.timing(modalAnimatedValue3, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  const [profileImageLoaded, setProfileImageLoaded] = useState(false);

  const ImageDiv = useMemo(() => {
    if (profileImageRef) {
      return (
        <Image
          source={{ uri: profileImageRef }}
          style={{ width: 46, height: 46, borderRadius: 100 }}
          onLoadEnd={() => {
            setImageLoaded(true);
          }}
        />
      );
    } else {
      setImageLoaded(true);
      return (
        <View
          style={{
            width: 46,
            height: 46,
            borderRadius: 100,
            backgroundColor: `${user.profileColor}`,
          }}
          // source={{ uri: profileImageUrl }}
          // onLoadEnd={() => {
          //   setProfileImageLoaded(true);
          // }}
        >
          <Text
            style={{
              alignSelf: "center",
              fontSize: 25,
              color: "white",
              paddingTop: 6,
              paddingLeft: 1,
            }}
          >
            {user.name[0].toUpperCase()}
          </Text>
        </View>
      );
    }
  }, [imageLoaded]);

  function calculateAge(dob) {
    const diff_ms = Date.now() - new Date(dob.seconds * 1000).getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  }

  if (!user.uid) {
    return;
  }

  return (
    <View>
      {!imageLoaded && (
        <ActivityIndicator
          size={"small"}
          style={{
            position: "absolute",
            alignSelf: "center",
            paddingTop: 29,
            opacity: 0.4,
          }}
        />
      )}
      <Animated.View style={{ width: "100%", opacity: opacity1 }}>
        <Pressable
          style={{ width: "100%" }}
          onPress={() => {
            if (showActionsParents === true) {
              onShowActions(null);
            } else {
              onShowActions(user?.uid);
            }
          }}
        >
          <View
            style={{
              width: "92%",
              height: 50,
              alignSelf: "center",
              marginTop: 12,
              flexDirection: "row",
              alignItems: "center",
              marginLeft: 10,
            }}
          >
            {ImageDiv}
            <Text
              style={{
                fontSize: 15,
                color: "black",
                fontWeight: "500",
                marginLeft: 14,
              }}
            >
              {`${user.name} (${calculateAge(user.date)})`}
            </Text>
            {user?.uid === eventInfo.creator && (
              <MaterialCommunityIcons
                name="crown"
                size={30}
                color="#ffc005"
                style={{
                  // position: "absolute",
                  alignSelf: "center",
                  marginLeft: 7,
                }}
              />
            )}

            {showActionsParents &&
              auth.currentUser.uid === eventInfo.creator &&
              user.uid !== auth.currentUser.uid && (
                <TouchableHighlight
                  style={{
                    position: "absolute",
                    alignSelf: "center",
                    right: 108,
                  }}
                  onPress={() => {
                    toggleModal2();
                  }}
                  underlayColor="rgba(0, 0, 0, 0)"
                >
                  <Ionicons
                    name="remove-circle-outline"
                    size={28}
                    color={red}
                    style={{}}
                  />
                </TouchableHighlight>
              )}

            {showActionsParents && user.uid !== auth.currentUser.uid && (
              <TouchableHighlight
                style={{
                  position: "absolute",
                  alignSelf: "center",
                  right: 57,
                }}
                onPress={() => {
                  toggleModal();
                }}
                underlayColor="rgba(0, 0, 0, 0)"
              >
                <MaterialIcons name="report" size={29} color={red} />
              </TouchableHighlight>
            )}

            {showActionsParents && user.uid !== auth.currentUser.uid && (
              <TouchableHighlight
                style={{
                  position: "absolute",
                  alignSelf: "center",
                  right: 7,
                }}
                onPress={toggleModal3}
                underlayColor="rgba(0, 0, 0, 0)"
              >
                <Ionicons
                  name="information-circle-outline"
                  size={29}
                  color={"rgba(50, 50, 50, 1)"}
                />
              </TouchableHighlight>
            )}

            <ReportModal
              isVisible={isModalVisible}
              onClose={toggleModal}
              user={user}
            />

            <Modal
              animationType="none"
              transparent={true}
              visible={isModalVisible2}
              onRequestClose={toggleModal2}
            >
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPressOut={toggleModal2}
              >
                <Animated.View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    opacity: modalAnimatedValue2,
                  }}
                >
                  <TouchableWithoutFeedback>
                    <View
                      style={{
                        width: "80%",
                        height: "18%",
                        backgroundColor: "#fff",
                        padding: 20,
                        borderRadius: 10,
                      }}
                    >
                      <View
                        style={{
                          width: "90%",
                          alignSelf: "center",
                          // height: "20%",
                          justifyContent: "center",
                          marginTop: "3%",
                        }}
                      >
                        <Text
                          style={{
                            textAlign: "center",
                            fontWeight: "500",
                            fontSize: 15.5,
                          }}
                        >{`${language.removeUserEnsure} ${user.name}`}</Text>
                      </View>

                      <View
                        style={{
                          // bottom: -30,
                          position: "absolute",
                          flexDirection: "row",
                          justifyContent: "space-between",
                          width: "95%",
                          alignSelf: "center",
                          bottom: 25,
                          // marginTop: 20,
                          // alignItems: "center",

                          // flex: 1,
                        }}
                      >
                        <Pressable
                          onPress={() => {
                            toggleModal2();
                          }}
                          style={({ pressed }) => ({
                            borderRadius: 8,
                            width: "45%",
                            height: 40,
                            backgroundColor: "rgba(0,0,0,0.04)",
                            justifyContent: "center",
                            transform: [{ scale: pressed ? 0.97 : 1 }],
                            borderColor: "rgba(0,0,0,0.1)",
                            borderWidth: 0.5,
                            // bottom: 6,
                          })}
                        >
                          <Text
                            style={{ fontWeight: "600", alignSelf: "center" }}
                          >
                            {language.no}
                          </Text>
                        </Pressable>
                        <Pressable
                          onPress={() => {
                            toggleModal2();
                            // removeUser(user);
                            setTimeout(() => {
                              removeUser(user);
                            }, 200);
                          }}
                          style={({ pressed }) => ({
                            borderRadius: 8,
                            transform: [{ scale: pressed ? 0.97 : 1 }],
                            width: "45%",
                            height: 40,
                            backgroundColor: red,
                            // shadowColor: red,
                            // shadowOpacity: 0.3,
                            // shadowRadius: 5,
                            // shadowOffset: { width: 0, height: 0 },
                            justifyContent: "center",
                            // marginBottom: 30,
                            // bottom: 6,
                          })}
                        >
                          <Text
                            style={{
                              fontWeight: "800",
                              alignSelf: "center",
                              color: "white",
                              fontSize: 15,
                              // fontFamily: "roboto",
                            }}
                          >
                            {language.yes}
                          </Text>
                        </Pressable>
                      </View>
                    </View>
                  </TouchableWithoutFeedback>
                </Animated.View>
              </TouchableOpacity>
            </Modal>

            <Modal
              animationType="none"
              transparent={true}
              visible={isModalVisible3}
              onRequestClose={toggleModal3}
            >
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPressOut={toggleModal3}
              >
                <Animated.View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    opacity: modalAnimatedValue3,
                  }}
                >
                  <TouchableWithoutFeedback>
                    <View
                      style={{
                        width: "90%",
                        // height: "30%",
                        backgroundColor: "#fff",
                        padding: 20,
                        borderRadius: 10,
                      }}
                    >
                      <View
                        style={{
                          width: "95%",
                          alignSelf: "center",
                          // height: "20%",
                          // justifyContent: "center",
                          marginTop: "2%",
                          marginBottom: "2%",
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {user.profileImageRef ? (
                          <View>
                            <Image
                              source={{ uri: user.profileImageRef }}
                              style={{
                                width: 100,
                                height: 100,
                                borderRadius: 100,
                                opacity: profileImageLoaded ? 1 : 0,
                                zIndex: profileImageLoaded ? 2 : 0,
                              }}
                              onLoadEnd={() => {
                                setTimeout(() => {
                                  setProfileImageLoaded(true);
                                }, 100);
                              }}
                            />

                            <ActivityIndicator
                              style={{
                                position: "absolute",
                                alignSelf: "center",
                                marginTop: 42,
                              }}
                            ></ActivityIndicator>
                          </View>
                        ) : (
                          <View
                            style={{
                              width: 100,
                              height: 100,
                              borderRadius: 100,
                              backgroundColor: user.profileColor,
                            }}
                          >
                            <Text
                              style={{
                                alignSelf: "center",
                                fontSize: 50,
                                color: "white",
                                paddingTop: 10,
                                paddingLeft: -2,
                              }}
                            >
                              {user.name[0].toUpperCase()}
                            </Text>
                          </View>
                        )}

                        <View style={{ marginLeft: "6%" }}>
                          <Text
                            style={{
                              // textAlign: "center",
                              fontWeight: "400",
                              fontSize: 21,
                            }}
                          >{`${user.name}`}</Text>
                          <Text
                            style={{
                              // textAlign: "center",
                              fontWeight: "400",
                              fontSize: 15,
                            }}
                          >{`${calculateAge(user.date)} let`}</Text>
                        </View>
                      </View>

                      {user.description && (
                        <View style={{ marginTop: "10%", marginBottom: "2%" }}>
                          <Text style={{ fontSize: 15 }}>
                            {user.description}
                          </Text>
                        </View>
                      )}

                      <View
                        style={{
                          // bottom: -30,
                          position: "absolute",
                          flexDirection: "row",
                          justifyContent: "space-between",
                          width: "95%",
                          alignSelf: "center",
                          bottom: 25,
                          // marginTop: 20,
                          // alignItems: "center",

                          // flex: 1,
                        }}
                      ></View>
                    </View>
                  </TouchableWithoutFeedback>
                </Animated.View>
              </TouchableOpacity>
            </Modal>
          </View>
          <View
            style={{
              width: "95%",
              height: 1,
              backgroundColor: "rgba(0,0,0,0.1)",
              alignSelf: "center",
              marginTop: 12,
            }}
          />
        </Pressable>
      </Animated.View>
    </View>
  );
});

export default function Event() {
  const route = useRoute();
  const [loadingInfo, setLoadingInfo] = useState(true);
  const [moved, setMoved] = useState(false);

  const [language, setLanguage] = useState(route.params.language);

  const navigation = useNavigation();

  const handleLoadEnd = () => {
    setLoadingCounter((prevCounter) => prevCounter - 1);
    if (loadingCounter === 1) {
      Animated.timing(opacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  };

  const Tab1Screen = () => {
    const [users, setUsers] = useState(null);

    const [eventInfo, setEventInfo] = useState(route.params.eventInfo);
    const [renderReady, setRenderReady] = useState(true);
    const [loadingCounter, setLoadingCounter] = useState(0);

    const [refetch, setRefetch] = useState(0);

    const fetchUserDocuments = async () => {
      let newUsers = [];

      for (let user of eventInfo.users) {
        // const docQuery = doc(
        //   db,
        //   `events/${event_userID}/user_events/${eventID}`
        // );

        const userDocRef = doc(db, `users/${user}/public/${user}public`);
        const userDocSnap = await getDoc(userDocRef);

        if (userDocSnap.exists() && userDocSnap.data().uid) {
          if (userDocSnap.data().uid === eventInfo.creator) {
            newUsers = [userDocSnap.data(), ...newUsers];
          } else if (userDocSnap.data().uid === auth.currentUser.uid) {
            newUsers = [newUsers[0], userDocSnap.data(), ...newUsers.slice(1)];
          } else {
            newUsers = [...newUsers, userDocSnap.data()];
          }
        } else {
          console.log("No such document!");
        }
      }
      setUsers(newUsers);
      setLoadingCounter(newUsers.length);
    };

    const fetchEventInfo = async (docSnapshot) => {
      const eventID = eventInfo?.eventID?.split("_")[1];
      const event_userID = eventInfo?.eventID?.split("_")[0];

      if (docSnapshot.exists()) {
        const eventData = docSnapshot.data();

        const userCollectionRef = collection(
          db,
          `events/${event_userID}/user_events/${eventID}/eventUsers`
        );
        const userEventsSnapshot = await getDocs(userCollectionRef);

        const users = userEventsSnapshot.docs.map((doc) => doc.data().userID);

        const eventWithUsers = {
          id: docSnapshot.id,
          ...eventData,
          users: users,
        };
        // console.log(eventWithUsers);

        setEventInfo(eventWithUsers);
        // fetchUserDocuments();
      } else {
      }
    };

    useEffect(() => {
      const eventID = eventInfo?.eventID?.split("_")[1];
      const event_userID = eventInfo?.eventID?.split("_")[0];

      let snapshot;

      const docRef = doc(db, `events/${event_userID}/user_events/${eventID}`);
      const docUnsubscribe = onSnapshot(docRef, async (docSnapshot) => {
        snapshot = docSnapshot;
        await fetchEventInfo(docSnapshot);
      });

      const collRef = collection(
        db,
        `events/${event_userID}/user_events/${eventID}/eventUsers`
      );
      const collUnsubscribe = onSnapshot(collRef, async (querySnapshot) => {
        await fetchEventInfo(snapshot);
        setTimeout(() => {
          setRenderReady(true);
        }, 150);
      });

      return () => {
        docUnsubscribe();
        collUnsubscribe();
      };
    }, []);

    useEffect(() => {
      if (eventInfo.users) {
        fetchUserDocuments();
      }
    }, [eventInfo]);

    const joinEvent = async () => {
      const [creatorID, eventID] = eventInfo?.eventID?.split("_");

      const docRef = doc(db, `events/${creatorID}/user_events/${eventID}`);

      if (
        eventInfo.usersLimit !== null &&
        eventInfo.users.length >= eventInfo.usersLimit
      ) {
        alert(language.eventFull);
      } else {
        try {
          await setDoc(
            doc(
              db,
              `events/${creatorID}/user_events/${eventID}/eventUsers/${auth.currentUser.uid}`
            ),
            {
              userID: auth.currentUser.uid,
              eventPath: `events/${creatorID}/user_events/${eventID}`,
            }
          );
          await updateDoc(docRef, {
            lastUpdated: serverTimestamp(),
            usersChange: "joined",
            usersDelete: arrayUnion(auth.currentUser.uid),
          });
        } catch (error) {
          alert(error);
        }
      }
    };

    useEffect(() => {}, [users]);

    const t = new Date(1970, 0, 1);
    t.setSeconds(eventInfo.date);
    const r = t.toLocaleDateString();

    const ImageDiv = useMemo(() => {
      return (
        <Image
          source={require("./../images/qq3.png")}
          style={{
            width: 40,
            height: 40,
            borderRadius: 100,
            position: "absolute",
            zIndex: 100,
            marginLeft: 4,
          }}
          // onLoadEnd={() => {
          //   setImageLoaded(true);
          // }}
        />
      );
    }, []);

    const [selectedItem, setSelectedItem] = useState(null);

    const handleShowActions = (itemId) => {
      setSelectedItem(itemId);
    };

    console.log(!eventInfo.users, !users, !language, !renderReady);

    if (!eventInfo.users || !users || !language || !renderReady) {
      return (
        <View
          style={{ backgroundColor: "white", height: "100%", width: "100%" }}
        >
          <ActivityIndicator style={{ marginTop: "40%" }}></ActivityIndicator>
        </View>
      );
    }

    return (
      <View style={{ height: "100%", width: "100%", backgroundColor: "white" }}>
        <View
          style={{
            bottom: -20,
            flexDirection: "row",
            justifyContent: "space-between",
            width: "95%",
            alignSelf: "center",
            backgroundColor: "white",
            // alignItems: "center",

            // flex: 1,
          }}
        >
          <View
            style={{
              borderRadius: 8,
              width: "47%",
              height: 50,
              backgroundColor: "rgba(0,0,0,0.01)",
              justifyContent: "center",

              borderColor: "rgba(0,0,0,0.08)",
              borderWidth: 0.5,
              bottom: 6,
            }}
          >
            <View
              style={{
                width: "95%",
                alignSelf: "center",
                justifyContent: "center",
              }}
            >
              <Ionicons
                name="time-outline"
                size={27}
                color={red}
                style={{ position: "absolute", marginLeft: 2 }}
              />

              <View
                style={{
                  // backgroundColor: "red",
                  width: "85%",
                  alignSelf: "flex-end",
                  // alignSelf: "center",
                  paddingTop: 18,
                  position: "absolute",

                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Text
                  style={{
                    // alignSelf: "center",
                    color: "black",
                    fontWeight: "500",
                    fontSize: 12,
                    textAlign: "center",
                    lineHeight: 16,
                  }}
                >
                  {`${new Date(eventInfo.date.seconds * 1000).getDate()}. ${
                    new Date(eventInfo.date.seconds * 1000).getMonth() + 1
                  }. ${(
                    "0" +
                    new Date(eventInfo.timeStart.seconds * 1000).getHours()
                  ).slice(-2)}:${(
                    "0" +
                    new Date(eventInfo.timeStart.seconds * 1000).getMinutes()
                  ).slice(-2)}-${(
                    "0" + new Date(eventInfo.timeEnd.seconds * 1000).getHours()
                  ).slice(-2)}:${(
                    "0" +
                    new Date(eventInfo.timeEnd.seconds * 1000).getMinutes()
                  ).slice(-2)} \n ${
                    language.weekdays[
                      new Date(eventInfo.date.seconds * 1000).getDay()
                    ]
                  }
                  `}
                </Text>
              </View>
            </View>
          </View>
          <Pressable
            // onPress={() => {
            //   navigation.navigate("CourtInfo", eventInfo.courtObj);
            // }}
            onPress={() => {
              navigation.navigate("CourtInfo", {
                selectedMarker: eventInfo.courtObj,
                language: language,
              });
            }}
            style={({ pressed }) => ({
              borderRadius: 8,
              width: "48%",
              height: 50,
              backgroundColor: "rgba(0,0,0,0.01)",
              justifyContent: "center",
              transform: [{ scale: pressed ? 0.97 : 1 }],
              borderColor: "rgba(0,0,0,0.08)",
              borderWidth: 0.5,
              bottom: 6,
            })}
          >
            {ImageDiv}
            <View
              style={{
                // backgroundColor: "red",
                width: "80%",
                alignSelf: "flex-end",
                paddingTop: 5,
              }}
            >
              <Text
                style={{
                  // flexShrink: 1,
                  color: "black",
                  marginBottom: 5,
                  maxWidth: "77%",
                  // marginLeft: "10%",
                  fontWeight: "500",
                  fontSize: 10,
                  alignSelf: "center",
                  textAlign: "center",
                  // alignSel
                }}
                numberOfLines={3}
                ellipsizeMode="tail"
              >
                {eventInfo.courtObj.address}
              </Text>
            </View>
          </Pressable>
        </View>

        {eventInfo.description !== "" && (
          <View
            style={{
              width: "91%",
              height: 58.5,
              marginTop: 25,
              alignSelf: "center",
              overflow: "hidden",
              backgroundColor: "white",
              justifyContent: "center",
            }}
          >
            <ScrollView
              style={{ backgroundColor: "white" }}
              contentContainerStyle={{
                flexGrow: 1,
                justifyContent: "center",
                backgroundColor: "white",
              }}
              showsVerticalScrollIndicator={false}
            >
              <Text style={{ fontSize: 13, fontWeight: "400" }}>
                {eventInfo.description}
              </Text>
            </ScrollView>
            <LinearGradient
              colors={["rgba(255,255,255,0)", "white"]}
              style={{
                position: "absolute",
                left: 0,
                right: 0,
                bottom: 0,

                height: 4,
              }}
            />
          </View>
        )}

        <View
          style={{
            width: "90%",
            height: 30,
            // position: "absolute",
            alignSelf: "center",
            flexDirection: "row",
            marginTop: eventInfo.description !== "" ? "3%" : "9%",
          }}
        >
          <Text
            style={{
              // height: 50,
              width: "100%",
              position: "absolute",
              // marginTop: 85,
              fontWeight: "500",
              fontSize: 15,
            }}
          >
            {`${language.users}: ${eventInfo.users.length}`}
            {`${!eventInfo.usersLimit ? "" : `/${eventInfo.usersLimit}`}`}
          </Text>
          <Text
            style={{
              // height: 50,
              width: "100%",
              position: "absolute",
              marginTop: 3,
              fontWeight: "300",
              fontSize: 12,
              marginLeft: 200,
            }}
          >
            {`${
              eventInfo.ageLimitMin ? `Min. věk: ${eventInfo.ageLimitMin}` : ""
            }`}
          </Text>
          <Text
            style={{
              // height: 50,
              width: "100%",
              position: "absolute",
              marginTop: 3,
              fontWeight: "300",
              fontSize: 12,
              marginLeft: 270,
            }}
          >
            {`${
              eventInfo.ageLimitMax ? `Max. věk: ${eventInfo.ageLimitMax}` : ""
            }`}
          </Text>
        </View>

        <View
          style={{
            width: "100%",
            height: !eventInfo.users.includes(auth.currentUser.uid)
              ? screenHeight / (eventInfo.description !== "" ? 3.7 : 3.1)
              : screenHeight / (eventInfo.description !== "" ? 2.8 : 2.3),
            // height: !eventInfo.users.includes(auth.currentUser.uid)
            //   ? screenHeight / 3
            //   : eventInfo.description !== ""
            //   ? screenHeight / 3.0
            //   : eventInfo.users.length > 10
            //   ? screenHeight / 2.0
            //   : screenHeight / 2.45,
          }}
        >
          <FlatList
            showsVerticalScrollIndicator={false}
            style={{
              width: "95%",
              // height: eventInfo.users.includes(auth.currentUser.uid)
              //   ? "55%"
              //   : "45%",
              height: 50,
              // position: "absolute",
              // flex: 1,
              // width: "90%",

              backgroundColor: "rgba(0,0,0,0.005)",
              alignSelf: "center",
              borderWidth: 0.9,
              borderColor: "rgba(0,0,0,0.17)",
              borderRadius: 15,
              // bottom: 0,
            }}
            data={users}
            // extraData={users[-1]}
            // refreshing={true}
            keyExtractor={(item, index) => item?.uid}
            renderItem={({ item: user }) => {
              // console.log("iiiiiiiiiii", user.uid);

              if (user) {
                return (
                  <ProfileStripeComp
                    user={user}
                    eventInfo={eventInfo}
                    profileImageRef={user.profileImageRef}
                    showActionsParents={selectedItem === user.uid}
                    onShowActions={handleShowActions}
                    language={language}
                  />
                );
              }
            }}
          ></FlatList>
        </View>

        {!eventInfo.users.includes(auth.currentUser.uid) && (
          <Pressable
            style={({ pressed }) => ({
              position: "absolute",
              zIndex: 1,
              // right: 15,
              bottom: 25,
              alignSelf: "center",
              backgroundColor:
                eventInfo.usersLimit !== null &&
                eventInfo.users.length >= eventInfo.usersLimit
                  ? "rgba(170,170,170,1)"
                  : red,
              // borderWidth: 1,
              // borderColor: red,
              borderRadius: 10,
              paddingHorizontal: 40,
              paddingVertical: 13,
              width: "90%",
              alignItems: "center",
              transform: [{ scale: pressed ? 0.986 : 1 }],
              // shadowColor: "rgba(245,49,86,1)",
              // shadowOffset: {
              //   width: 0,
              //   height: 0,
              // },
              // elevation: 5,
              // shadowOpacity: 0.1,
              // shadowRadius: 5,
            })}
            onPress={joinEvent}
          >
            <Text
              style={{
                color: "white",
                fontWeight: "600",
                fontSize: 16,
              }}
            >
              {language.joinGame}
            </Text>
          </Pressable>
        )}
      </View>
    );
  };

  const Tab2Screen = () => {
    // console.log(route.params);
    const isFocused = useIsFocused();

    const navigation = route.params.navigation;

    const [eventInfo, setEventInfo] = useState(route.params.eventInfo);
    const [messages, setMessages] = useState([]);

    const [avatarUri, setAvatarUri] = useState(1);
    const [userName, setUserName] = useState(null);
    const [profileColor, setProfileColor] = useState(null);

    const [messagesLoaded, setMessagesLoaded] = useState(false);

    useEffect(() => {
      async function runAsync() {
        const docRef = await doc(db, "users", auth.currentUser.uid);

        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
          setAvatarUri(docSnap.data().profileImageRefSmall);
          setUserName(docSnap.data().name);
          setProfileColor(docSnap.data().profileColor);
        }
      }

      runAsync();
    }, []);

    useEffect(() => {
      if (eventInfo && eventInfo.eventID) {
        const eventID = eventInfo.eventID.split("_")[1];
        const event_userID = eventInfo.eventID.split("_")[0];

        const docQuery = doc(
          db,
          `events/${event_userID}/user_events/${eventID}`
        );

        const unsubscribe = onSnapshot(docQuery, (docSnapshot) => {
          if (docSnapshot.exists()) {
            const docData = docSnapshot.data();
            setEventInfo(docData);
          } else {
            alert(language.deletedEvent);
            if (auth.currentUser.uid !== eventInfo.creator) {
              navigation.goBack();
            }
          }
        });

        return () => {
          unsubscribe();
        };
      }
    }, []);

    // const navigation = useNavigation();

    const giftedChatRef = useRef(null);
    let creatorID, eventID;

    if (eventInfo && eventInfo.eventID) {
      [creatorID, eventID] = eventInfo.eventID.split("_");
    }

    useLayoutEffect(() => {
      // console.log(`events/${creatorID}/user_events/${eventID}`);

      const collectionRef = collection(
        db,
        `events/${creatorID}/user_events/${eventID}/chat`
      );

      const q = query(collectionRef, orderBy("createdAt", "desc"));

      const unsubscribe = onSnapshot(q, (querySnapshot) => {
        let messages = [];

        querySnapshot.docs.forEach((doc) => {
          const data = doc.data();
          const createdAt = data.createdAt
            ? data.createdAt.toDate()
            : new Date();

          // console.log("DATEEEEEEEE", doc.data().createdAt.toDate());

          messages.push({
            _id: doc.data()._id,
            createdAt: doc?.data()?.createdAt?.toDate(),
            text: doc.data().text,
            user: doc.data().user,
          });
        });

        setTimeout(() => {
          setMessages(messages);
          setMessagesLoaded(true);
        }, 500);
      });
      return unsubscribe;
    }, []);

    const onSend = useCallback(async (messages = []) => {
      const trimmedText = messages[0].text.trim();
      if (trimmedText.length > 0) {
        const { _id, createdAt, text, user } = messages[0];

        await addDoc(
          collection(db, `events/${creatorID}/user_events/${eventID}/chat`),
          {
            _id,
            createdAt: serverTimestamp(),
            text,
            userID: user._id,
            user,
            // firstMessage, // Add the firstMessage field
          }
        );
        // giftedChatRef.current.scrollToBottom(true);
      }
    }, []);

    const renderMessageText = (props, previousMessage) => {
      const { currentMessage } = props;
      const showName =
        !previousMessage ||
        previousMessage?.user?._id !== currentMessage?.user?._id;

      return (
        <View>
          {showName && currentMessage.user._id !== auth.currentUser.uid && (
            <Text
              style={{
                color: red,
                fontWeight: "600",
                marginBottom: -10,
                marginTop: 6,
                marginLeft: 8,
              }}
            >
              {currentMessage.user.name}
            </Text>
          )}
          <MessageText {...props} />
        </View>
      );
    };

    const renderBubble = (props) => {
      const { currentMessage } = props;

      return (
        <Bubble
          {...props}
          wrapperStyle={{
            right: {
              backgroundColor: red,
              borderRadius: 10,
              borderTopRightRadius: 4,
              paddingRight: 10,
            },
            left: {
              backgroundColor: "#f0f0f0",
              borderRadius: 10,
              borderTopLeftRadius: 3,
              paddingRight: 10,
              marginLeft: 40,
            },
          }}
          onLongPress={() => {
            setSelectedMessageId(currentMessage._id);
            setShowDeleteButton(true);
          }}
          onPress={() => {
            setSelectedMessageId(null);
            setShowDeleteButton(false);
          }}
        />
      );
    };

    useEffect(() => {}, []);

    const renderSend = (props) => {
      return (
        <Send {...props}>
          <View style={{ paddingRight: 5, paddingTop: 1 }}>
            <MaterialCommunityIcons name="send-circle" size={45} color={red} />
          </View>
        </Send>
      );
    };

    const renderInputToolbar = (props) => {
      return (
        <InputToolbar
          {...props}
          containerStyle={{
            backgroundColor: "#fff",
            // paddingTop: 10,
            // borderRadius: 20,
            // marginTop: 10,
            // height: 60,
            // paddingBottom: 10,
            marginBottom: 3,
            // top: 0,
            // paddingTop: 0,
            // marginTop: 0,
          }}
          textInputProps={{
            placeholder: "Type a message...",
            // style: {
            //   flex: 1,
            //   fontSize: 15,
            //   // paddingTop: 10,
            //   paddingLeft: 10,

            //   // marginBottom: -10,
            //   // paddingBottom: -10,
            //   width: "80%",
            // },
          }}
        />
      );
    };

    // const renderScrollToBottomComponent = () => {
    //   return <EvilIcons name="arrow-down" size={40} color="grey" />;
    // };

    const [selectedMessageId, setSelectedMessageId] = useState(null);
    const [showDeleteButton, setShowDeleteButton] = useState(false);
    const [currentMessageInfo, setCurrentMessageInfo] = useState(null);

    const renderMessage = (props, profileColor) => {
      const { currentMessage } = props;

      // console.log(props);

      const handleLongPress = () => {
        setSelectedMessageId(currentMessage._id);
        setShowDeleteButton(true);
      };

      const handleDelete = () => {
        deleteMessage(currentMessage._id);
      };

      // console.log(props.previousMessage);

      const marginBot = (props) => {
        if (props?.nextMessage?.user) {
          return props?.currentMessage?.user?._id !==
            props?.nextMessage?.user?._id
            ? 15
            : 0;
        } else {
          return 0;
        }
      };

      return (
        <>
          <View
            style={{
              marginTop: props.previousMessage ? 0 : 10,
              marginVertical: 0,
              marginLeft: 0,
              marginBottom: marginBot(props),
            }}
          >
            <Message
              {...props}
              containerStyle={{
                backgroundColor:
                  currentMessage.user._id === auth.currentUser.uid
                    ? "#ffbdbd"
                    : "#f0f0f0",
                borderRadius: 10,
                padding: 10,
              }}
              onLongPress={handleLongPress}
            />

            {currentMessage.user._id !== auth.currentUser.uid &&
              props?.currentMessage?.user?._id !==
                props?.nextMessage?.user?._id &&
              !props?.currentMessage.user.avatar && (
                <Pressable
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: 18,
                    backgroundColor: props.currentMessage.user.profileColor,
                    justifyContent: "center",
                    alignItems: "center",
                    marginLeft: 5,
                    marginTop: -45,
                  }}
                  onPress={() => {
                    // console.log(props?.currentMessage?.user?._id);
                    toggleModal3(props?.currentMessage?.user?._id);
                  }}
                >
                  <Text style={{ color: "white", fontSize: 16, marginLeft: 1 }}>
                    {currentMessage.user.name.charAt(0).toUpperCase()}
                  </Text>
                </Pressable>
              )}

            {currentMessage.user._id !== auth.currentUser.uid &&
              props?.currentMessage?.user?._id !==
                props?.nextMessage?.user?._id &&
              props?.currentMessage.user.avatar && (
                <Pressable
                  style={{
                    width: 36,
                    height: 36,
                    marginLeft: 5,
                    marginTop: -45,
                  }}
                  onPress={() => {
                    // console.log(props?.currentMessage?.user?._id);
                    toggleModal3(props?.currentMessage?.user?._id);
                  }}
                >
                  <Image
                    source={{ uri: props.currentMessage.user.avatar }}
                    style={{
                      width: 36,
                      height: 36,
                      borderRadius: 50,
                      // marginLeft: 5,
                      // marginTop: -45,
                    }}
                  />
                </Pressable>
              )}

            {currentMessage.user._id !== auth.currentUser.uid &&
              props.currentMessage.user._id !==
                props?.nextMessage?.user?._id && (
                <Text
                  style={{
                    color: "rgba(0,0,0,0.7)",
                    fontSize: 13,
                    fontWeight: 400,
                    marginLeft: 10,
                    marginTop: 4,
                  }}
                >
                  {currentMessage.user.name}
                </Text>
              )}

            {selectedMessageId === currentMessage._id &&
              showDeleteButton &&
              currentMessage.user._id === auth.currentUser.uid && (
                <TouchableOpacity
                  onPress={handleDelete}
                  style={{
                    backgroundColor: "#ededed",
                    marginLeft: "80%",
                    borderRadius: 10,
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 5,
                    marginTop: -6,
                    marginRight: 20,
                  }}
                >
                  <Text style={{ color: "black", paddingVertical: 6 }}>
                    Delete
                  </Text>
                </TouchableOpacity>
              )}
          </View>
        </>
      );
    };

    const deleteMessage = async (messageId) => {
      try {
        const querySnapshot = await getDocs(
          query(
            collection(db, `events/${creatorID}/user_events/${eventID}/chat`),
            where("_id", "==", messageId)
          )
        );
        querySnapshot.forEach((doc) => {
          deleteDoc(doc.ref);
        });
      } catch (error) {
        alert(error);
      }
    };

    const [user, setUser] = useState(null);

    const [profileImageLoaded, setProfileImageLoaded] = useState(false);

    const [isModalVisible3, setModalVisible3] = useState(false);

    const modalAnimatedValue3 = useRef(new Animated.Value(0)).current;

    const toggleModal3 = async (userID) => {
      const userDoc = await getDoc(
        doc(db, `users/${userID}/public/${userID}public`)
      );
      console.log(userID);
      if (userDoc.exists()) {
        const userData = userDoc.data();

        setUser(userData);
      } else {
        console.log("No such document!");
      }

      if (isModalVisible3) {
        Animated.timing(modalAnimatedValue3, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start(() => setModalVisible3(false));
        setProfileImageLoaded(false);
      } else {
        setModalVisible3(true);

        Animated.timing(modalAnimatedValue3, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    };

    if (avatarUri === 0 || !userName) {
      return (
        <View
          style={{
            width: "100%",
            height: "100%",
            justifyContent: "center",
            backgroundColor: "white",
          }}
        >
          <ActivityIndicator
            size="small"
            style={{ alignSelf: "center", marginTop: -100 }}
          />
        </View>
      );
    }

    // console.log(messages);

    return (
      <View style={{ backgroundColor: "white", flex: 1 }}>
        {/* <TouchableWithoutFeedback onPress={() => setShowDeleteButton(false)}> */}
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : -500}
        >
          {!messagesLoaded && (
            <ActivityIndicator
              style={{
                position: "absolute",
                alignSelf: "center",
                marginTop: 75,
              }}
            ></ActivityIndicator>
          )}

          {user && (
            <Modal
              animationType="none"
              transparent={true}
              visible={isModalVisible3}
              onRequestClose={toggleModal3}
            >
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPressOut={(event) => {
                  event.persist();
                  toggleModal3();
                }}
              >
                <Animated.View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    opacity: modalAnimatedValue3,
                  }}
                >
                  <TouchableWithoutFeedback>
                    <View
                      style={{
                        width: "90%",
                        // height: "30%",
                        backgroundColor: "#fff",
                        padding: 20,
                        borderRadius: 10,
                      }}
                    >
                      <View
                        style={{
                          width: "95%",
                          alignSelf: "center",
                          // height: "20%",
                          // justifyContent: "center",
                          marginTop: "2%",
                          marginBottom: "2%",
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        {user.profileImageRef ? (
                          <View>
                            <Image
                              source={{ uri: user.profileImageRef }}
                              style={{
                                width: 100,
                                height: 100,
                                borderRadius: 100,
                                opacity: profileImageLoaded ? 1 : 0,
                                zIndex: profileImageLoaded ? 2 : 0,
                              }}
                              onLoadEnd={() => {
                                setTimeout(() => {
                                  setProfileImageLoaded(true);
                                }, 100);
                              }}
                            />

                            <ActivityIndicator
                              style={{
                                position: "absolute",
                                alignSelf: "center",
                                marginTop: 42,
                              }}
                            ></ActivityIndicator>
                          </View>
                        ) : (
                          <View
                            style={{
                              width: 100,
                              height: 100,
                              borderRadius: 100,
                              backgroundColor: user.profileColor,
                            }}
                          >
                            <Text
                              style={{
                                alignSelf: "center",
                                fontSize: 50,
                                color: "white",
                                paddingTop: 20,
                                paddingLeft: 2,
                              }}
                            >
                              {user.name[0].toUpperCase()}
                            </Text>
                          </View>
                        )}

                        <View style={{ marginLeft: "6%" }}>
                          <Text
                            style={{
                              // textAlign: "center",
                              fontWeight: "400",
                              fontSize: 21,
                            }}
                          >{`${user.name}`}</Text>
                          <Text
                            style={{
                              // textAlign: "center",
                              fontWeight: "400",
                              fontSize: 15,
                            }}
                          >{`${calculateAge(user.date)} let`}</Text>
                        </View>
                      </View>

                      {user.description && (
                        <View style={{ marginTop: "10%", marginBottom: "2%" }}>
                          <Text style={{ fontSize: 15 }}>
                            {user.description}
                          </Text>
                        </View>
                      )}

                      <View
                        style={{
                          // bottom: -30,
                          position: "absolute",
                          flexDirection: "row",
                          justifyContent: "space-between",
                          width: "95%",
                          alignSelf: "center",
                          bottom: 25,
                          // marginTop: 20,
                          // alignItems: "center",

                          // flex: 1,
                        }}
                      ></View>
                    </View>
                  </TouchableWithoutFeedback>
                </Animated.View>
              </TouchableOpacity>
            </Modal>
          )}
          <GiftedChat
            onPress={() => setShowDeleteButton(false)}
            messages={messages}
            showAvatarForEveryMessage={false}
            showUserAvatar={false}
            alwaysShowSend={true}
            // renderAvatarOnTop={true}
            // renderAvatar={(props) => renderAvatar(props)}
            renderAvatar={null}
            ref={giftedChatRef}
            // keyExtractor={keyExtractor}
            onSend={(messages) => onSend(messages)}
            messagesContainerStyle={{
              backgroundColor: "rgba(0,0,0,0)",
              paddingBottom: 10,
              paddingTop: 8,
            }}
            textInputStyle={{
              backgroundColor: "#fff",
              borderRadius: 20,
            }}
            contentContainerStyle={{
              paddingBottom: 10,
            }}
            renderSend={renderSend}
            renderBubble={renderBubble}
            renderInputToolbar={renderInputToolbar}
            renderMessage={renderMessage}
            bottomOffset={1}
            onTouchStart={() => {
              setSelectedMessageId(null);
              setCurrentMessageInfo(currentMessage);
              setShowDeleteButton(false);
            }}
            user={{
              _id: auth?.currentUser?.uid,
              avatar: avatarUri === 1 ? 0 : avatarUri,
              name: userName,
              profileColor: profileColor,
            }}
          />
        </KeyboardAvoidingView>
        {/* </TouchableWithoutFeedback> */}
      </View>
    );
  };

  if (!navigation) {
    return;
  }

  return (
    <NavigationContainer independent={true}>
      <SafeAreaView edges={["top"]} style={{ flex: 1 }}>
        <Animated.View
          style={{ width: "100%", height: "100%", backgroundColor: "white" }}
        >
          <View style={{ width: "100%" }}>
            <EventHeaderComp
              route={route}
              navigation={navigation}
              language={language}
            />
          </View>

          {/* <SafeAreaProvider> */}

          <Tab.Navigator
            screenOptions={{
              tabBarLabelStyle: {
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
              },
              tabBarIndicatorStyle: {
                backgroundColor: "#F53156",
              },
              tabBarStyle: {
                height: 43,
              },
              lazy: false,
            }}
          >
            <Tab.Screen name={language.information} component={Tab1Screen} />
            <Tab.Screen name="Chat" component={Tab2Screen} />
          </Tab.Navigator>
          {/* </SafeAreaProvider> */}
        </Animated.View>
      </SafeAreaView>
    </NavigationContainer>
  );
}
// STYLES
