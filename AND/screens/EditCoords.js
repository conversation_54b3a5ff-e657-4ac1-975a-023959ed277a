import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
  Fragment,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Pressable,
  AppState,
  TouchableWithoutFeedback,
  useAnimatedValue,
  TouchableHighlight,
  Modal,
} from "react-native";

import { auth, db } from "../config/firebase";
// import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
// import { createStackNavigator } from "@react-navigation/stack";
import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  getCountFromServer,
  limit,
} from "firebase/firestore";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";

import Slider from "@react-native-community/slider";

import MapView, {
  Marker,
  PROVIDER_GOOGLE,
  Polygon,
  Circle,
} from "react-native-maps";

export default function Map() {
  const route = useRoute();

  const navigation = useNavigation();

  const mapRef = useRef(null);
  const [centerCoordinate, setCenterCoordinate] = useState(null);
  const [radius, setRadius] = useState(1000);

  const [mapReady, setMapReady] = useState(null);

  const [uploading, setUploading] = useState(false);

  const editCoords = async () => {
    setUploading(true);
    try {
      await updateDoc(doc(db, "users/", auth.currentUser.uid), {
        radius: radius,
        centerCoord: centerCoordinate,
      }).then(() => {
        alert("Oblast aktualizována");
        navigation.goBack();
      });
    } catch (error) {
      alert(error);
      setUploading(false);
    }
  };

  useEffect(() => {
    const docRef = doc(db, "users", auth.currentUser.uid);
    const fetchInfo = async () => {
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        setCenterCoordinate(docSnap.data().centerCoord);
        setRadius(docSnap.data().radius);
      } else {
        console.log("No such document!");
      }
    };
    fetchInfo();
  }, []);

  const [mapIsLoading, setMapIsLoading] = useState(true);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setMapIsLoading(false);
    }, 700);

    return () => clearTimeout(timeoutId);
  }, []);

  useEffect(() => {
    if (centerCoordinate) {
      setTimeout(() => {
        setMapReady(true);
      }, 1000);
    }
  }, [centerCoordinate]);

  const opacity1 = useRef(new Animated.Value(-0.5)).current;

  useEffect(() => {
    if (!mapIsLoading) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }
  }, [mapIsLoading]);

  if (!radius || !centerCoordinate || !mapReady) {
    return <ActivityIndicator style={{ marginTop: "70%" }}></ActivityIndicator>;
  }

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      {mapIsLoading && (
        <ActivityIndicator style={{ marginTop: "70%" }}></ActivityIndicator>
      )}
      <Animated.View
        style={{ flex: 1, opacity: opacity1, backgroundColor: "white" }}
      >
        <View
          style={{
            bottom: 80,
            position: "absolute",
            zIndex: 2,
            left: "17%",
            width: "65%",
          }}
        >
          <Slider
            style={{
              width: "100%",
              height: 60,
              alignSelf: "center",
              // // marginTop: 40,
              // position: "absolute",
              // bottom: 100,
            }}
            minimumValue={1000}
            maximumValue={40000}
            minimumTrackTintColor={red}
            maximumTrackTintColor={"white"}
            thumbTintColor={red}
            value={radius}
            onValueChange={(newValue) => {
              setRadius(newValue);
            }}
          />
        </View>

        <MapView
          ref={mapRef}
          style={{ flex: 1 }}
          onRegionChange={(value) => {
            setCenterCoordinate({
              latitude: value.latitude,
              longitude: value.longitude,
            });
            // adjustCircleSize();
          }}
          customMapStyle={[
            // {
            //   featureType: "landscape.man_made",
            //   stylers: [
            //     {
            //       color: "#f0f5ed",
            //     },
            //   ],
            // },
            {
              featureType: "landscape.man_made",
              elementType: "geometry",
              stylers: [
                {
                  color: "#f6f4ee",
                },
              ],
            },
            {
              featureType: "landscape.natural",
              stylers: [
                {
                  color: "#c8ebb5",
                },
              ],
            },
            {
              featureType: "poi",
              stylers: [
                {
                  visibility: "simplified",
                },
              ],
            },
            {
              featureType: "poi.attraction",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.business",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.government",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.medical",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.park",
              stylers: [
                {
                  color: "#d0f0bd",
                },
              ],
            },
            {
              featureType: "poi.place_of_worship",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.school",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.sports_complex",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "road.highway",
              stylers: [
                {
                  color: "#b6b6b8",
                  visibility: "off",
                },
                {
                  weight: 1,
                },
              ],
            },

            {
              featureType: "water",
              stylers: [
                {
                  color: "#ade3f5",
                },
              ],
            },
          ]}
          initialRegion={{
            latitude: centerCoordinate.latitude,
            longitude: centerCoordinate.longitude,
            latitudeDelta: 0.7,
            longitudeDelta: 0.4,
          }}
        >
          <Circle
            center={centerCoordinate}
            radius={radius}
            strokeColor={red}
            // fillColor="rgba(255,0,0,0.5)"
            strokeWidth={1}
          />
          <Circle
            center={centerCoordinate}
            radius={200}
            strokeColor={red}
            fillColor={red}
            strokeWidth={1}
          />
        </MapView>
        <View
          style={{
            position: "absolute",
            backgroundColor: red,
            height: 89,
            width: "100%",
            flexDirection: "row",
          }}
        >
          <TouchableHighlight
            style={{
              // right: 10,
              position: "absolute",
              bottom: 3,
              backgroundColor: red,
              padding: 10,
            }}
            onPress={() => {
              navigation.goBack();
            }}
            underlayColor="rgba(0, 0, 0, 0)"
          >
            <AntDesign
              name="arrowleft"
              size={31}
              color="white"
              style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
            />
          </TouchableHighlight>

          {uploading ? (
            <ActivityIndicator
              color={"white"}
              style={{ position: "absolute", right: 40, bottom: 15 }}
            ></ActivityIndicator>
          ) : (
            <TouchableOpacity
              activeOpacity={0.5}
              style={{
                position: "absolute",

                // width: "70%",
                height: 30,
                // backgroundColor: "rgba(0,0,0,0.09)",
                justifyContent: "center",
                marginHorizontal: 10,
                borderRadius: 10,
                bottom: 10,
                right: 60,
              }}
              onPressIn={() => {
                editCoords();
              }}
            >
              <Text
                style={{
                  position: "absolute",
                  // marginTop: -10,
                  //   alignSelf: "center",

                  //   bottom: 10,
                  color: "white",
                  fontWeight: "500",
                  fontSize: 19,
                  textAlign: "center",
                  // fontFamily: "Roboto",
                  // fontSize: "Nunito-ExtraBold",
                }}
              >
                {route.params.language.save}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    </View>
  );
}
