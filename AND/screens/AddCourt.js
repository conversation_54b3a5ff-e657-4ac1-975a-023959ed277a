import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  TouchableHighlight,
  ScrollView,
  Pressable,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
  Modal,
  ImageBackground,
  Easing,
  // Picker,
  TouchableWithoutFeedback,
  Keyboard,
  FlatList,
} from "react-native";
// import { Picker } from "@react-native-community/picker";

import { auth, db } from "../config/firebase";
import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  onSnapshot,
} from "firebase/firestore";
import * as ImageManipulator from "expo-image-manipulator";
import {
  ref,
  uploadBytesResumable,
  getBlob,
  getBytes,
  getDownloadURL,
  uploadString,
} from "firebase/storage";
import { SafeAreaView } from "react-native-safe-area-context";

import * as ImagePicker from "expo-image-picker";

import { red } from "./colors";
import { MaterialIcons } from "@expo/vector-icons";
import { Entypo } from "@expo/vector-icons";

import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

import MapView, {
  Marker,
  PROVIDER_GOOGLE,
  Polygon,
  Circle,
} from "react-native-maps";

import { MediaLibrary } from "expo";

import { useSafeAreaInsets } from "react-native-safe-area-context";
import FastImage from "react-native-fast-image";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;
const Stack = createStackNavigator();

const CustomSwitch = React.memo(
  ({
    navigation,
    selectionMode,
    roundCorner,
    option1,
    option2,
    option3,
    selectionColor,
    surface,
    setSurface,
  }) => {
    const [getRoundCorner, setRoundCorner] = useState(roundCorner);

    const [tempNumber, setTempNumber] = useState(0);

    // const [selectedRestrictionType, setSelectedRestrictionType] = useState(1);

    const updatedSwitchData = (val) => {
      setSurface(val);
    };

    return (
      <View>
        <View
          style={{
            height: 38,
            width: "100%",
            backgroundColor: "white",
            borderRadius: getRoundCorner ? 10 : 0,
            borderWidth: 0.5,
            borderColor: selectionColor,
            flexDirection: "row",
            justifyContent: "center",
            padding: 2,
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(1)}
            style={{
              flex: 1,

              backgroundColor: surface == 1 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 1 ? "white" : selectionColor,
              }}
            >
              {option1}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(2)}
            style={{
              flex: 1,

              backgroundColor: surface == 2 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 2 ? "white" : selectionColor,
              }}
            >
              {option3}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(3)}
            style={{
              flex: 1,

              backgroundColor: surface == 3 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 3 ? "white" : selectionColor,
              }}
            >
              {option2}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
);

const CustomSwitchTwo = React.memo(
  ({
    navigation,
    selectionMode,
    roundCorner,
    option1,
    option2,
    option3,
    selectionColor,
    surface,
    setSurface,
  }) => {
    const [getRoundCorner, setRoundCorner] = useState(roundCorner);

    const [tempNumber, setTempNumber] = useState(0);

    // const [selectedRestrictionType, setSelectedRestrictionType] = useState(1);

    const updatedSwitchData = (val) => {
      setSurface(val);
    };

    return (
      <View>
        <View
          style={{
            height: 38,
            width: "100%",
            backgroundColor: "white",
            borderRadius: getRoundCorner ? 10 : 0,
            borderWidth: 0.5,
            borderColor: selectionColor,
            flexDirection: "row",
            justifyContent: "center",
            padding: 2,
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(1)}
            style={{
              flex: 1,

              backgroundColor: surface == 1 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 1 ? "white" : selectionColor,
              }}
            >
              {option1}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(3)}
            style={{
              flex: 1,

              backgroundColor: surface == 3 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 3 ? "white" : selectionColor,
              }}
            >
              {option2}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
);

function AddCourtMain() {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const route = useRoute();

  const language = route.params;

  if (!language) {
    return;
  }

  function AddCourtInfoInput() {
    const navigation = useNavigation();

    const route = useRoute();

    const [surface, setSurface] = useState(route.params?.surface || 1);
    const [basketsCount, setBasketsCount] = useState(
      route.params?.basketsCount || null
    );
    const [differentSurface, setDifferentSurface] = useState(
      route.params?.differentSurface || null
    );
    const [courtDescription, setCourtDescription] = useState(
      route.params?.courtDescription || null
    );
    const [boardSize, setBoardSize] = useState(route.params?.boardSize || 1);
    const [lines, setLines] = useState(route.params?.lines || 1);
    const [lighting, setLighting] = useState(route.params?.lighting || 1);
    const [fullCourt, setFullCourt] = useState(route.params?.fullCourt || 1);
    const [indoorOutdoor, setIndoorOutdoor] = useState(
      route.params?.indoorOutdoor || 1
    );
    const [publicPrivate, setPublicPrivate] = useState(
      route.params?.publicPrivate || 1
    );
    const [images, setImages] = useState(route.params?.images || []);

    const [uploading, setUploading] = useState(false);

    const [infoShown, setInfoShown] = useState(false);

    const [chosenAction, setChosenAction] = useState(null);

    const pickImage = async () => {
      const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        const response =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (response.status !== "granted") {
          alert(
            "Sorry, we need media library permissions to make this work. If you have denied the permission, please enable it from the settings."
          );
          return;
        }
      }

      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        // allowsEditing: true,
        quality: 1,
        allowsMultipleSelection: true,
      });

      if (!result.canceled) {
        setImages([...images, ...result.assets.map((item) => item.uri)]);
      }
    };

    const takePhoto = async () => {
      const { status } = await ImagePicker.getCameraPermissionsAsync();
      if (status !== "granted") {
        const response = await ImagePicker.requestCameraPermissionsAsync();
        if (response.status !== "granted") {
          alert(
            "Sorry, we need camera permissions to make this work. If you have denied the permission, please enable it from the settings."
          );
          return;
        }
      }

      let result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        // allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled) {
        setImages([...images, result.assets[0].uri]);
      }
    };

    const [selectedImage, setSelectedImage] = useState(null);

    const [isModalVisible2, setModalVisible2] = useState(false);

    const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

    const toggleModal2 = () => {
      if (isModalVisible2) {
        Animated.timing(modalAnimatedValue2, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start(() => setModalVisible2(false));
      } else {
        setModalVisible2(true);
        Animated.timing(modalAnimatedValue2, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      }
    };

    return (
      <SafeAreaView
        style={{ width: "100%", height: "100%", backgroundColor: "white" }}
      >
        {/* <ScrollView */}
        {/* style={{ width: "100%", height: "100%", backgroundColor: "white" }}> */}

        {/* <TouchableWithoutFeedback
        onPress={Keyboard.dismiss}
        accessible={false}
        style={{ width: "100%", height: "100%", backgroundColor: "white" }}
      > */}
        <KeyboardAwareScrollView
          style={{ backgroundColor: "#4c69a5" }}
          resetScrollToCoords={{ x: 0, y: 0 }}
          contentContainerStyle={{ flex: 1 }}
          scrollEnabled={false}
        >
          <ScrollView
            style={{ width: "100%", height: "100%", backgroundColor: "white" }}
          >
            <TouchableHighlight
              style={{
                marginRight: 10,

                top: "2%",

                width: 45,
                height: 45,
                position: "absolute",
                right: -7,
                zIndex: 2,
              }}
              onPress={() => {
                navigation.goBack();
              }}
              underlayColor="rgba(0, 0, 0, 0)"
            >
              <AntDesign
                name="close"
                size={35}
                color={"rgba(0,0,0,0.75)"}
                style={{
                  opacity: 1,
                }}
                // onPress={() => {
                //   setSelectedMarker(null);
                // }}
              />
            </TouchableHighlight>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text
                style={{
                  // fontFamily: "Avenir-Medium",
                  fontFamily: "BungeeInline-Regular",
                  fontSize: 24,
                  lineHeight: 47,
                  color: red,
                }}
              >
                {language.addCourt}
              </Text>
            </View>

            <View
              style={{
                width: "90%",
                //   alignItems: "center",
                alignSelf: "center",
                marginTop: "10%",
              }}
            >
              <Text style={{ marginLeft: 3 }}>{language.describeCourt}</Text>
              <TextInput
                style={{
                  alignSelf: "center",
                  width: "100%",
                  // height: "30%",
                  height: 60,
                  borderWidth: 1,
                  borderColor: "rgba(0,0,0,0.2)",
                  borderRadius: 7,
                  paddingHorizontal: 10,
                  paddingVertical: 10,
                  marginTop: 10,
                }}
                value={courtDescription}
                onChangeText={(newText) => {
                  setCourtDescription(newText);
                }}
                placeholder={`Your description...`}
                multiline={true}
              />
            </View>

            <View
              style={{
                flexDirection: "row",
                marginTop: 15,
                width: "90%",
                alignSelf: "center",
                marginTop: "3%",

                // justifyContent: "space-between",
              }}
            >
              <Text
                style={{
                  marginTop: 12,
                  marginRight: "25%",
                }}
              >
                {language.surfaceType}
              </Text>
              <Text
                style={{
                  marginTop: 12,
                  marginRight: "10%",
                  color: "rgba(0,0,0,0.9)",
                }}
              >
                {language.basketsCount}
              </Text>
              <TextInput
                keyboardType="numeric"
                style={{
                  fontSize: 15,
                  color: "black",
                  // alignSelf: "center",
                  backgroundColor: "rgba(0,0,0,0.01)",
                  padding: 4,
                  // paddingBottom: 30,
                  // flex: 1,
                  borderColor: "rgba(0,0,0,0.1)",
                  borderWidth: 1,
                  borderRadius: 5,
                  width: 50,
                  height: 35,
                  alignSelf: "center",
                  textAlign: "center",
                  // marginTop: 15,
                }}
                // placeholder="min. Age"
                autoCapitalize="none"
                autoCorrect={false}
                // onEndEditing={() => {
                //   setLimitOfUsers(tempNumber);
                // }}
                value={basketsCount}
                onChangeText={(text) => {
                  let newText = "";
                  let numbers = "0123456789";

                  for (var i = 0; i < text.length; i++) {
                    if (numbers.indexOf(text[i]) > -1) {
                      newText = newText + text[i];
                    } else {
                      alert("Please enter numbers only");
                    }
                  }
                  setBasketsCount(newText);
                }}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <CustomSwitch
                selectionMode={1}
                roundCorner={true}
                option1={language.artificial}
                option2={language.otherSurface}
                option3={language.concrete}
                // onSelectSwitch={onSelectSwitch}
                //   selectionColor={"rgba(0,0,0,0.6)"}
                selectionColor={red}
                surface={surface}
                setSurface={setSurface}
              />
            </View>

            {surface === 3 && (
              <View>
                <TextInput
                  style={{
                    fontSize: 15,
                    color: "black",
                    // alignSelf: "center",
                    backgroundColor: "rgba(0,0,0,0.01)",
                    padding: 4,
                    // paddingBottom: 30,
                    // flex: 1,
                    borderColor: "rgba(0,0,0,0.3)",
                    paddingTop: 20,
                    width: "60%",
                    borderBottomWidth: 1,

                    alignSelf: "center",
                    textAlign: "center",
                    //   maxWidth: 300,
                    // marginTop: 15,
                  }}
                  value={differentSurface}
                  onChangeText={(text) => {
                    setDifferentSurface(text);
                  }}
                ></TextInput>
              </View>
            )}

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.type}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option2={language.indoor}
                option1={language.outdoor}
                selectionColor={red}
                surface={indoorOutdoor}
                setSurface={setIndoorOutdoor}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.access}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option1={language.public}
                option2={language.private}
                selectionColor={red}
                surface={publicPrivate}
                setSurface={setPublicPrivate}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.fullCourt}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option1={language.yes}
                option2={language.no}
                selectionColor={red}
                surface={fullCourt}
                setSurface={setFullCourt}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.lines}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option1={language.yes}
                option2={language.no}
                selectionColor={red}
                surface={lines}
                setSurface={setLines}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.boardSize}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option1={language.regular}
                option2={language.small}
                selectionColor={red}
                surface={boardSize}
                setSurface={setBoardSize}
              />
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: "6%",
              }}
            >
              <Text style={{ marginBottom: 10, alignSelf: "flex-start" }}>
                {language.lighting}:
              </Text>
              <CustomSwitchTwo
                selectionMode={1}
                roundCorner={true}
                option1={language.yes}
                option2={language.no}
                selectionColor={red}
                surface={lighting}
                setSurface={setLighting}
              />
            </View>

            <View
              style={{
                flexDirection: "row",
                marginTop: 15,
                width: "90%",
                alignSelf: "center",
                marginTop: "5%",
              }}
            >
              <Text style={{ fontSize: 15, fontWeight: "400" }}>
                {language.images}:
              </Text>
            </View>

            <View
              style={{
                flexDirection: "row",
                marginTop: 15,
                width: "95%",
                alignSelf: "center",
                marginTop: "4%",

                justifyContent: "space-evenly",
              }}
            >
              <Pressable
                onPress={() => {
                  // pickImage();
                  setChosenAction(1);
                  if (infoShown === false) {
                    toggleModal2();
                  } else {
                    pickImage();
                  }
                }}
                style={{
                  backgroundColor: "white",
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                  borderRadius: 6,
                  flexDirection: "row",
                  // justifyContent: "center",
                  borderColor: red,
                  borderWidth: 1,
                }}
              >
                <Text
                  style={{
                    color: red,
                    fontWeight: "700",
                    marginTop: 2,
                    marginRight: 6,
                    fontSize: 13,
                  }}
                >
                  {language.chooseImage}
                </Text>
                <MaterialIcons name="insert-photo" size={20} color={red} />
              </Pressable>
              <Pressable
                onPress={() => {
                  // pickImage();
                  setChosenAction(2);
                  if (infoShown === false) {
                    toggleModal2();
                  } else {
                    takePhoto();
                  }
                }}
                style={{
                  backgroundColor: "white",
                  paddingHorizontal: 10,
                  paddingVertical: 5,
                  borderRadius: 6,
                  flexDirection: "row",
                  // justifyContent: "center",
                  borderColor: red,
                  borderWidth: 1,
                }}
              >
                <Text
                  style={{
                    color: red,
                    fontWeight: "700",
                    marginTop: 2,
                    marginRight: 6,
                    fontSize: 13,
                  }}
                >
                  {language.takeImage}
                </Text>
                <Entypo name="camera" size={18} color={red} />
              </Pressable>
            </View>

            <View
              style={{
                width: Dimensions.get("window").width * 1,
                alignSelf: "center",
                marginTop: "4%",
              }}
            >
              <ScrollView
                horizontal={true}
                showsHorizontalScrollIndicator={false}
              >
                {images.map((item, index) => (
                  <Pressable
                    key={index.toString()}
                    onPress={() => {
                      if (selectedImage === index) {
                        setSelectedImage(null);
                      } else {
                        setSelectedImage(index);
                      }
                    }}
                    style={{
                      width: Dimensions.get("window").width * 0.8,
                      aspectRatio: 1.3,
                      marginLeft: 20,
                      marginRight: index === images.length - 1 ? 20 : 0,
                      borderRadius: 5,
                    }}
                  >
                    <Image
                      source={{ uri: item }}
                      style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: 5,
                      }}
                    />
                    {selectedImage === index && (
                      <View
                        style={{
                          position: "absolute",
                          top: 0,
                          bottom: 0,
                          left: 0,
                          right: 0,
                          backgroundColor: "rgba(0,0,0,0.5)",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: 5,
                        }}
                      >
                        <Pressable
                          style={{
                            // backgroundColor: "red",
                            padding: 10,
                            borderRadius: 5,
                          }}
                          onPress={() => {
                            let newImages = [...images];
                            newImages.splice(index, 1);
                            setImages(newImages);
                            setSelectedImage(null);
                          }}
                        >
                          <MaterialIcons
                            name="delete-forever"
                            size={50}
                            color="white"
                          />
                        </Pressable>
                      </View>
                    )}
                  </Pressable>
                ))}
              </ScrollView>
            </View>

            <View
              style={{
                width: "90%",
                alignItems: "center",
                alignSelf: "center",
                marginTop: 50,
                bottom: 0,
                // position: "absolute",
              }}
            >
              <Pressable
                onPress={() => {
                  // console.log({
                  //   surface: surface,
                  //   differentSurface: differentSurface,
                  //   basketsCount: basketsCount,
                  //   images: images,
                  //   courtDescription: courtDescription,
                  // });
                  if (
                    basketsCount !== null &&
                    courtDescription !== null &&
                    images.length !== 0
                  ) {
                    navigation.navigate("AddCourtMap", {
                      surface: surface,
                      differentSurface: differentSurface,
                      basketsCount: basketsCount,
                      courtDescription: courtDescription,
                      images: images,
                      boardSize: boardSize,
                      lines: lines,
                      lighting: lighting,
                      fullCourt: fullCourt,
                      indoorOutdoor: indoorOutdoor,
                      publicPrivate: publicPrivate,
                    });
                  }
                }}
                style={({ pressed }) => ({
                  borderRadius: 8,
                  transform:
                    basketsCount !== null &&
                    courtDescription !== null &&
                    images.length !== 0
                      ? [{ scale: pressed ? 0.97 : 1 }]
                      : [{ scale: 1 }],
                  width: "60%",
                  height: 38,
                  backgroundColor:
                    basketsCount !== null &&
                    courtDescription !== null &&
                    images.length !== 0
                      ? red
                      : "rgba(200,200,200,1)",

                  justifyContent: "center",
                  // marginBottom: 30,
                  bottom: 20,
                })}
              >
                <Text
                  style={{
                    fontWeight: "700",
                    alignSelf: "center",
                    color: "white",
                    fontSize: 15,
                    // fontFamily: "roboto",
                  }}
                >
                  {language.continue}
                </Text>
              </Pressable>
            </View>

            <Modal
              animationType="none"
              transparent={true}
              visible={isModalVisible2}
              onRequestClose={toggleModal2}
            >
              <TouchableOpacity
                style={{ flex: 1 }}
                activeOpacity={1}
                onPressOut={toggleModal2}
              >
                <Animated.View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    opacity: modalAnimatedValue2,
                  }}
                >
                  <TouchableWithoutFeedback>
                    <View
                      style={{
                        width: "92%",
                        height: "82%",
                        backgroundColor: "#fff",
                        padding: 20,
                        borderRadius: 10,
                      }}
                    >
                      <View
                        style={{
                          width: "90%",
                          alignSelf: "center",
                          // height: "20%",
                          justifyContent: "center",
                          marginTop: "3%",
                        }}
                      >
                        <Text
                          style={{
                            textAlign: "center",
                            fontWeight: "500",
                            fontSize: 17,
                          }}
                        >
                          {language.howTo}
                        </Text>
                      </View>

                      <View
                        style={{
                          width: "95%",
                          alignSelf: "center",

                          marginTop: "10%",
                        }}
                      >
                        <Text
                          style={{
                            fontWeight: "400",
                            lineHeight: 18,
                            fontSize: 16,
                          }}
                        >
                          {language.howToText}
                        </Text>
                      </View>

                      <View
                        style={{
                          // flex: 1,
                          justifyContent: "flex-end",
                          height: "50%",
                          width: "100%",
                          alignSelf: "center",
                          // alignItems: "center",
                        }}
                      >
                        <Text style={{ fontWeight: "500" }}>
                          {language.examplePhoto}
                        </Text>
                        <Image
                          style={{
                            width: "95%",
                            height: "75%",
                            borderRadius: 4,
                            alignSelf: "center",
                            marginTop: 10,
                          }}
                          source={require("./../images/court-image-example.jpg")}
                        ></Image>
                      </View>

                      <View
                        style={{
                          // bottom: -30,
                          // position: "absolute",
                          // flexDirection: "row",
                          // justifyContent: "space-between",
                          width: "95%",
                          alignSelf: "center",
                          // bottom: 15,
                          marginTop: 30,
                          alignItems: "center",

                          // flex: 1,
                        }}
                      >
                        <Pressable
                          onPress={() => {
                            if (chosenAction === 1) {
                              toggleModal2();

                              console.log("bolob");
                              setTimeout(pickImage, 250);
                              setInfoShown(true);

                              // pickImage();
                            }
                            if (chosenAction === 2) {
                              toggleModal2();

                              setTimeout(takePhoto, 250);
                              setInfoShown(true);
                            }
                          }}
                          style={({ pressed }) => ({
                            borderRadius: 8,
                            transform: [{ scale: pressed ? 0.97 : 1 }],
                            width: "60%",
                            height: 40,
                            backgroundColor: red,
                            // shadowColor: red,
                            // shadowOpacity: 0.3,
                            // shadowRadius: 5,
                            // shadowOffset: { width: 0, height: 0 },
                            justifyContent: "center",
                            // marginBottom: 30,
                            // bottom: 6,
                          })}
                        >
                          <Text
                            style={{
                              fontWeight: "700",
                              alignSelf: "center",
                              color: "white",
                              fontSize: 15,
                              // fontFamily: "roboto",
                            }}
                          >
                            {language.understand}
                          </Text>
                        </Pressable>
                      </View>
                    </View>
                  </TouchableWithoutFeedback>
                </Animated.View>
              </TouchableOpacity>
            </Modal>
          </ScrollView>
        </KeyboardAwareScrollView>
        {/* </TouchableWithoutFeedback> */}

        {/* </ScrollView> */}
      </SafeAreaView>
    );
  }

  function AddCourtMap() {
    const route = useRoute();

    const navigation = useNavigation();

    const mapRef = useRef(null);

    const [mapType, setMapType] = useState("standard");

    const toggleMapType = () => {
      setMapType(mapType === "standard" ? "satellite" : "standard");
    };

    const mapTypeImages = {
      standard: require("./../images/standard.jpg"),
      satellite: require("./../images/satellite.jpg"),
    };
    const [centerCoordinate, setCenterCoordinate] = useState();
    const [radius, setRadius] = useState(1000);

    const [uploading, setUploading] = useState(false);

    useEffect(() => {
      const docRef = doc(db, "users", auth.currentUser.uid);
      const fetchInfo = async () => {
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          setCenterCoordinate(docSnap.data().centerCoord);
          setRadius(docSnap.data().radius);

          // console.log(docSnap.data().userAreaCoords.sliderValue2);
        } else {
          console.log("No such document!");
        }
      };
      fetchInfo();
    }, []);

    const uploadCourt = async () => {
      setUploading(true);
      const centerCoordinate2 = centerCoordinate;

      const coll = collection(db, `new_courts/`);
      const images = route.params.images;
      const imagesRefs = [];

      const getBlobFromLocalUri = (uri) => {
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.onload = function () {
            resolve(xhr.response);
          };
          xhr.onerror = function (error) {
            reject(
              new Error("There was an error fetching the image blob: ", error)
            );
          };
          xhr.open("GET", uri);
          xhr.responseType = "blob";
          xhr.send();
        });
      };

      const cuid = new Date().getTime();

      const uploadPromises = images.map(async (image, index) => {
        const storagePath = `/new-courts-images/${auth.currentUser.uid}/${cuid}-${index}`;
        const storageRef = ref(storage, storagePath);

        try {
          let asset;
          if (
            image.startsWith("content://") ||
            image.startsWith("assets-library://")
          ) {
            asset = await MediaLibrary.getAssetInfoAsync(image);
            image = asset.localUri;
          }

          const compressedImage = await ImageManipulator.manipulateAsync(
            image,
            [],
            { format: "jpeg" }
          );

          const blob = await getBlobFromLocalUri(compressedImage.uri);

          const snapshot = await uploadBytesResumable(storageRef, blob);
          const url = await getDownloadURL(snapshot.ref);

          imagesRefs.push(url);
        } catch (error) {
          console.log("There was an error uploading an image: ", error);
        }
      });

      await Promise.all(uploadPromises);

      try {
        await addDoc(coll, {
          date: cuid,
          userUid: auth.currentUser.uid,
          userEmail: auth.currentUser.providerData[0].email,
          imagesRefs: imagesRefs,
          surface: route.params.surface,
          differentSurface: route.params.differentSurface,
          basketsCount: route.params.basketsCount,
          courtDescription: route.params.courtDescription,
          coords: centerCoordinate2,
          boardSize: route.params.boardSize === 1 ? "regular" : "small",
          lines: route.params.lines === 1 ? true : false,
          lighting: route.params.lighting === 1 ? true : false,
          fullCourt: route.params.fullCourt === 1 ? true : false,
          indoorOutdoor:
            route.params.indoorOutdoor === 1 ? "outdoor" : "indoor",
          publicPrivate:
            route.params.publicPrivate === 1 ? "public" : "private",
        });

        setUploading(false);
        navigation.navigate("Map");
        alert(language.addedCourt);
      } catch (error) {
        console.log("There was an error creating the new court: ", error);
        alert(error);
      }
    };

    const [mapIsLoading, setMapIsLoading] = useState(true);

    useEffect(() => {
      const timeoutId = setTimeout(() => {
        setMapIsLoading(false);
      }, 700);

      return () => clearTimeout(timeoutId);
    }, []);

    const opacity1 = useRef(new Animated.Value(-0.5)).current;

    useEffect(() => {
      if (!mapIsLoading) {
        Animated.timing(opacity1, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }).start();
      }
    }, [mapIsLoading]);

    const goBack = () => {
      if (!uploading) {
        navigation.navigate("AddCourtInfoInput", {
          surface: route.params.surface,
          differentSurface: route.params.differentSurface,
          basketsCount: route.params.basketsCount,
          courtDescription: route.params.courtDescription,
          images: route.params.images,
          boardSize: route.params.boardSize,
          lines: route.params.lines,
          lighting: route.params.lighting,
          fullCourt: route.params.fullCourt,
          indoorOutdoor: route.params.indoorOutdoor,
          publicPrivate: route.params.publicPrivate,
        });
      }
    };

    if (!radius || !centerCoordinate) {
      return;
    }

    return (
      <View style={{ flex: 1, backgroundColor: "white" }}>
        {mapIsLoading && (
          <ActivityIndicator style={{ marginTop: "70%" }}></ActivityIndicator>
        )}
        <Animated.View
          style={{ flex: 1, opacity: opacity1, backgroundColor: "white" }}
        >
          <MapView
            ref={mapRef}
            style={{ flex: 1 }}
            onRegionChange={(value) => {
              // console.log(value);
              setCenterCoordinate({
                latitude: value.latitude,
                longitude: value.longitude,
              });
              // adjustCircleSize();
            }}
            mapType={mapType}
            // customMapStyle={[
            //   // {
            //   //   featureType: "landscape.man_made",
            //   //   stylers: [
            //   //     {
            //   //       color: "#f0f5ed",
            //   //     },
            //   //   ],
            //   // },
            //   {
            //     featureType: "landscape.man_made",
            //     elementType: "geometry",
            //     stylers: [
            //       {
            //         color: "#f6f4ee",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "landscape.natural",
            //     stylers: [
            //       {
            //         color: "#c8ebb5",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi",
            //     stylers: [
            //       {
            //         visibility: "simplified",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.attraction",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.business",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.government",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.medical",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.park",
            //     stylers: [
            //       {
            //         color: "#d0f0bd",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.place_of_worship",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.school",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "poi.sports_complex",
            //     stylers: [
            //       {
            //         visibility: "off",
            //       },
            //     ],
            //   },
            //   {
            //     featureType: "road.highway",
            //     stylers: [
            //       {
            //         color: "#b6b6b8",
            //         visibility: "off",
            //       },
            //       {
            //         weight: 1,
            //       },
            //     ],
            //   },

            //   {
            //     featureType: "water",
            //     stylers: [
            //       {
            //         color: "#ade3f5",
            //       },
            //     ],
            //   },
            // ]}
            initialRegion={{
              latitude: centerCoordinate.latitude,
              longitude: centerCoordinate.longitude,
              latitudeDelta: 0.7,
              longitudeDelta: 0.4,
            }}
          >
            <Marker coordinate={centerCoordinate}>
              <Image
                source={require("./../images/qq5.png")}
                style={{
                  width: 50,
                  height: 50,
                  // marginTop: -31,
                  overflow: "visible",
                  // marginTop: -40,
                }}
              />
            </Marker>
          </MapView>

          <TouchableOpacity
            style={{
              position: "absolute",
              bottom: 20,
              right: 5,
              backgroundColor: "white",
              // padding: 10,
              borderRadius: 5,
              elevation: 3,
              shadowColor: "#000",
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              width: 65,
              height: 65,
              // justifyContent: "center",
              alignItems: "center",
            }}
            onPress={toggleMapType}
          >
            <FastImage
              source={
                mapTypeImages[mapType === "standard" ? "satellite" : "standard"]
              }
              style={{ height: 60, width: 60, borderRadius: 5, marginTop: 2.5 }}
            />
            <MaterialCommunityIcons
              name="layers-outline"
              size={19}
              color={mapType !== "standard" ? "rgba(0,0,0,0.6)" : "white"}
              style={{ position: "absolute", bottom: 5, left: 5 }}
            />
            <Text style={{ color: red, fontWeight: "bold" }}>
              {mapType === "standard" ? language.satellite : language.standard}
            </Text>
          </TouchableOpacity>

          <View
            style={{
              position: "absolute",
              backgroundColor: red,
              height: 89,
              width: "100%",
              flexDirection: "row",
              // alignItems: "center",
              // justifyContent: "center",
            }}
          >
            <TouchableHighlight
              style={{
                // right: 10,
                position: "absolute",
                bottom: 3,
                backgroundColor: red,
                padding: 10,
              }}
              onPress={() => {
                if (!uploading) {
                  // navigation.goBack();
                  goBack();
                }
              }}
              underlayColor="rgba(0, 0, 0, 0)"
            >
              {/* <MaterialIcons
                name="arrow-back-ios"
                size={28}
                color="white"
                style={{ marginLeft: 10 }}

                // onPress={() => {}}
              /> */}

              <AntDesign
                name="arrowleft"
                size={31}
                color="white"
                style={{ alignSelf: "center", marginTop: 0, marginLeft: 1 }}
              />
            </TouchableHighlight>

            {uploading ? (
              <ActivityIndicator
                style={{
                  position: "absolute",

                  // width: "70%",
                  height: 30,
                  // backgroundColor: "rgba(0,0,0,0.09)",
                  justifyContent: "center",
                  marginHorizontal: 10,
                  borderRadius: 10,
                  bottom: 10,
                  right: 30,
                }}
                color={"white"}
              ></ActivityIndicator>
            ) : (
              <TouchableOpacity
                activeOpacity={0.5}
                style={{
                  position: "absolute",

                  // width: "70%",
                  height: 30,
                  // backgroundColor: "rgba(0,0,0,0.09)",
                  justifyContent: "center",
                  marginHorizontal: 10,
                  borderRadius: 10,
                  bottom: 10,
                  right: 60,
                }}
                onPressOut={() => {
                  uploadCourt();
                  // console.log(centerCoordinate);
                }}
              >
                <Text
                  style={{
                    position: "absolute",
                    // marginTop: -10,
                    //   alignSelf: "center",

                    //   bottom: 10,
                    color: "white",
                    fontWeight: "500",
                    fontSize: 17,
                    textAlign: "center",
                    zIndex: 100,
                  }}
                >
                  {language.send}
                </Text>
              </TouchableOpacity>
            )}
          </View>

          <View
            style={{
              position: "absolute",
              backgroundColor: "rgba(0,0,0,0)",
              height: 125,
              width: "50%",
              alignSelf: "center",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Text
              style={{
                // position: "absolute",
                // bottom: 15,
                // left: screenWidth / 4,

                color: "white",
                fontWeight: "500",
                fontSize: 15,
                textAlign: "center",
              }}
            >
              {language.chooseLocation}
            </Text>
          </View>
        </Animated.View>
      </View>
    );
  }

  return (
    <Stack.Navigator
      defaultSreenOptions={AddCourtInfoInput}
      screenOptions={{
        unmountInactiveRoutes: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen
        name="AddCourtInfoInput"
        component={AddCourtInfoInput}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="AddCourtMap"
        component={AddCourtMap}
        // options={{ lazy: false }}
        options={{ headerShown: false, lazy: false }}
      />
    </Stack.Navigator>
  );
}

export default function AddCourt() {
  return <AddCourtMain />;
}
