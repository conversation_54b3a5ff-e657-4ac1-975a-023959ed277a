import MapView, { <PERSON><PERSON>, PROVIDER_GOOGLE } from "react-native-maps";
import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
  Fragment,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Pressable,
  AppState,
  TouchableWithoutFeedback,
  Dimensions,
} from "react-native";

import { auth, db } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  getCountFromServer,
  limit,
} from "firebase/firestore";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";
import { AntDesign } from "@expo/vector-icons";
import { Feather } from "@expo/vector-icons";

import { TapGestureHandler } from "react-native-gesture-handler";

import { Entypo } from "@expo/vector-icons";

import { EN, CZ } from "./../assets/strings";

// import { Svg, Path, SvgUri } from "react-native-svg";

// import Icon from "./../images/download.svg";
import FastImage from "react-native-fast-image";

const screenWidth = Dimensions.get("window").width;
const screenHeight = Dimensions.get("window").height;

export default function Socials() {
  return (
    <View
      style={{ width: "100%", height: "100%", backgroundColor: "white" }}
      onStartShouldSetResponder={() => true}
    >
      <Text>Social system comming soon!</Text>
    </View>
  );
}
