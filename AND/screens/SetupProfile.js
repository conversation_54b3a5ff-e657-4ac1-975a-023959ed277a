import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  TouchableHighlight,
  ScrollView,
  Pressable,
  Dimensions,
  Animated,
  KeyboardAvoidingView,
  Modal,
  ImageBackground,
  Easing,
  // Picker,
  TouchableWithoutFeedback,
  Keyboard,
  FlatList,
} from "react-native";

import { auth, db } from "../config/firebase";
import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";
// import { createStackNavigator } from "@react-navigation/stack";
// import { AntDesign } from "@expo/vector-icons";
import {
  doc,
  addDoc,
  updateDoc,
  getDoc,
  collection,
  setDoc,
  getCountFromServer,
  getDocs,
  GeoPoint,
  arrayUnion,
  query,
  onSnapshot,
  collectionGroup,
  where,
} from "firebase/firestore";
import * as ImageManipulator from "expo-image-manipulator";
import {
  ref,
  uploadBytesResumable,
  getBlob,
  getBytes,
  getDownloadURL,
  uploadString,
  connectStorageEmulator,
} from "firebase/storage";
import { SafeAreaView } from "react-native-safe-area-context";
// import ImageResizer from "react-native-image-resizer";
import * as ImagePicker from "expo-image-picker";

import { red } from "./colors";
import { MaterialIcons } from "@expo/vector-icons";
import { Entypo } from "@expo/vector-icons";

import DateTimePickerModal from "react-native-modal-datetime-picker";

import { EN, CZ } from "./../assets/strings";

const screenWidth = Dimensions.get("window").width * 1;
const screenHeight = Dimensions.get("window").height * 1;

const CustomSwitch = React.memo(
  ({
    navigation,
    selectionMode,
    roundCorner,
    option1,
    option2,
    option3,
    selectionColor,
    surface,
    setSurface,
  }) => {
    const [getRoundCorner, setRoundCorner] = useState(roundCorner);

    const updatedSwitchData = (val) => {
      setSurface(val);
    };

    return (
      <View>
        <View
          style={{
            height: 38,
            width: "100%",
            backgroundColor: "white",
            borderRadius: getRoundCorner ? 10 : 0,
            borderWidth: 0.5,
            borderColor: selectionColor,
            flexDirection: "row",
            justifyContent: "center",
            padding: 2,
          }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(1)}
            style={{
              flex: 1,

              backgroundColor: surface == 1 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 1 ? "white" : selectionColor,
              }}
            >
              {option1}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(2)}
            style={{
              flex: 1,

              backgroundColor: surface == 2 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 2 ? "white" : selectionColor,
              }}
            >
              {option3}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            TouchableOpacity
            activeOpacity={1}
            onPress={() => updatedSwitchData(3)}
            style={{
              flex: 1,

              backgroundColor: surface == 3 ? selectionColor : "white",
              borderRadius: getRoundCorner ? 10 : 0,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text
              style={{
                color: surface == 3 ? "white" : selectionColor,
              }}
            >
              {option2}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
);

const LanguageToggle = ({
  selectionColor = red,
  initialLanguage = "EN",
  language,
  setLanguage,
}) => {
  return (
    <View
      style={{
        flexDirection: "row",
        backgroundColor: "white",
        borderRadius: 10,
        borderWidth: 0.5,
        borderColor: selectionColor,
        justifyContent: "center",
        padding: 2,
        height: 37,
      }}
    >
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => setLanguage("CZ")}
        style={{
          flex: 1,
          backgroundColor: language == "CZ" ? selectionColor : "white",
          borderRadius: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text style={{ color: language == "CZ" ? "white" : selectionColor }}>
          CZ
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        activeOpacity={1}
        onPress={() => setLanguage("EN")}
        style={{
          flex: 1,
          backgroundColor: language == "EN" ? selectionColor : "white",
          borderRadius: 10,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text style={{ color: language == "EN" ? "white" : selectionColor }}>
          EN
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const colors = [
  "#f7f465",
  "#ffd6a5",
  "#9bf6ff",
  "#a0c4ff",
  "#bdb2ff",

  "#f6e58d",
  "#badc58",
  "#7ed6df",

  "#3498db",
  "#2ecc71",
  "#f1c40f",

  "#16a085",

  "#2980b9",

  "#f8c291",

  "#6a89cc",

  "#82ccdd",
  "#60a3bc",

  "#BDC581",
  "#D6A2E8",
  "#6A89CC",

  "#FA983A",

  "#B8E994",
  "#78E08F",
  "#38ADA9",
  "#079992",

  //////

  "#A8D1FF",
  "#CFC9FF",

  "#CAE892",
  "#9CE8E0",
  "#A1A6FF",
  "#79B6FF",
  "#A3F0B1",
  "#FFDD57",
  "#FFC674",
  "#6FFFB0",
  "#8FF0AA",
  "#88C0FF",
  "#FFE775",
  "#FFDAB1",
  "#90B5FF",
  "#8EA2FF",
  "#9CDEEB",
  "#85B5C9",
  "#FFD47F",
  "#D2E89C",
  "#9DEAB5",
  "#71CEC1",
  "#62BFBF",

  /////
  "#B0E0E6",
  "#ADD8E6",
  "#87CEFA",
  "#00BFFF",
  "#AFEEEE",
  "#7FFFD4",
  "#40E0D0",

  "#F0E68C",
  "#D8BFD8",
  "#DDA0DD",

  ////////

  "#FFB8D1",
  "#D9B3FF",
  "#FFABAB",
  "#B8B3FF",
  "#D9E2FF",
  "#B0E0A8",
  "#FFB482",
  "#FFD9B0",
  "#B5D8EB",
  "#B5DEFF",
  "#D1BEFF",

  "#DCE4EF",
  "#DAC3E8",
  "#B5CCE7",

  "#FFA8A8",

  "#D1E8E2",
];

const getInitials = (name) => {
  if (name === "") {
    return "...";
  } else {
    return name[0].toUpperCase();
  }
};

const Avatar = ({ name, size, profileImage, setProfileColor }) => {
  const [color, setColor] = useState("#ffc6ff");

  useEffect(() => {
    const colorIndex = Math.floor(Math.random() * colors.length);
    setColor(colors[colorIndex]);
    setProfileColor(colors[colorIndex]);
  }, []);

  return (
    <View
      style={{
        backgroundColor: profileImage ? "transparent" : color,
        width: size,
        height: size,
        borderRadius: size / 2,
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      {profileImage && (
        <Image
          source={{ uri: profileImage }}
          style={{ width: size, height: size, borderRadius: size / 2 }}
        ></Image>
      )}
      {!profileImage && (
        <Text style={{ color: "white", fontSize: size / 2 }}>
          {getInitials(name)}
        </Text>
      )}
    </View>
  );
};

export default function SetupProfile() {
  const [name, setName] = useState("");

  const [profileImage, setProfileImage] = useState(null);
  const [date, setDate] = useState(new Date());
  const [dateChanged, setDateChanged] = useState(false);
  const [description, setDescription] = useState(null);
  const [profileColor, setProfileColor] = useState(null);
  const [language2, setLanguage2] = useState("CZ");
  const [language, setLanguage] = useState(null);
  const [nameFree, setNameFree] = useState(null);

  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const showDatePicker = () => {
    setDatePickerVisibility(true);
  };
  const hideDatePicker = () => {
    setDatePickerVisibility(false);
  };
  const handleConfirm = (selectedDate) => {
    setDate(selectedDate);
    setDateChanged(true);
    hideDatePicker();
  };

  const [keyboardVisible, setKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  useEffect(() => {
    const q = query(collectionGroup(db, "public"), where("name", "==", name));

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      if (
        querySnapshot.empty ||
        querySnapshot?.docs[0]?.data()?.uid === auth.currentUser.uid
      ) {
        setNameFree(true);
        return;
      }

      setNameFree(false);
    });

    return () => unsubscribe();
  }, [name]);

  useEffect(() => {
    if (language2 === "CZ") {
      setLanguage(CZ);
      // console.log(CZ);
    } else {
      setLanguage(EN);
    }
  }, [language2]);

  const navigation = useNavigation();

  useEffect(() => {
    if (language2 === "CZ") {
      setLanguage(CZ);
    } else {
      setLanguage(EN);
    }
  }, [language2]);

  const pickImage = async () => {
    const { status } = await ImagePicker.getMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      const response = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (response.status !== "granted") {
        alert(
          "Sorry, we need media library permissions to make this work. Please enable it from the settings"
        );
        return;
      }
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.getCameraPermissionsAsync();
    if (status !== "granted") {
      const response = await ImagePicker.requestCameraPermissionsAsync();
      if (response.status !== "granted") {
        alert(
          "Sorry, we need camera permissions to make this work. Please enable it from the settings "
        );
        return;
      }
    }

    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setProfileImage(result.assets[0].uri);
    }
  };

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  function calculateAge(dob) {
    // console.log(dob, "/////////////////////////////////");
    const diff_ms = Date.now() - dob.getTime();
    const age_dt = new Date(diff_ms);
    return Math.abs(age_dt.getUTCFullYear() - 1970);
  }

  if (!language) {
    return;
  }

  return (
    <SafeAreaView
      style={{
        width: "100%",
        height: "100%",
        backgroundColor: "white",
      }}
    >
      <KeyboardAvoidingView
        // behavior={"height"}
        keyboardVerticalOffset={10}
        // behavior="height"
        style={{
          width: "100%",
          backgroundColor: "rgba(0,0,0,0)",
          alignSelf: "center",
          // alignItems: "center",
          height: "100%",
        }}
      >
        <ScrollView
          style={{ flex: 1, backgroundColor: "white" }}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          <View
            style={{ width: 110, position: "absolute", right: 15, top: 10 }}
          >
            <LanguageToggle
              initialLanguage={language2}
              language={language2}
              setLanguage={setLanguage2}
            ></LanguageToggle>
          </View>

          <View
            style={{
              alignItems: "center",
              justifyContent: "center",
              marginTop: "20%",
            }}
          >
            <Avatar
              name={name}
              size={screenWidth / 2.2}
              profileImage={profileImage}
              setProfileColor={setProfileColor}
            />
          </View>

          <View
            style={{
              flexDirection: "row",
              marginTop: "7%",
              width: "95%",
              alignSelf: "center",
              justifyContent: "space-evenly",
            }}
          >
            <Pressable
              onPress={() => {
                pickImage();
              }}
              style={{
                backgroundColor: "transparent",
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 10,
                flexDirection: "row",
                alignItems: "center",
                borderWidth: 1,
                borderColor: "transparent",
              }}
            >
              <MaterialIcons name="insert-photo" size={20} color={red} />
              <Text
                style={{
                  color: red,
                  fontWeight: "600",
                  marginLeft: 5,
                }}
              >
                {language.chooseImage}
              </Text>
            </Pressable>
            <Pressable
              onPress={() => {
                takePhoto();
              }}
              style={{
                backgroundColor: "transparent",
                paddingHorizontal: 10,
                paddingVertical: 10,
                borderRadius: 10,
                flexDirection: "row",
                alignItems: "center",
                borderWidth: 1,
                borderColor: "transparent",
              }}
            >
              <Entypo name="camera" size={20} color={red} />
              <Text
                style={{
                  color: red,
                  fontWeight: "600",
                  marginLeft: 5,
                }}
              >
                {language.takeImage}
              </Text>
            </Pressable>
          </View>

          <View
            style={{
              width: "100%",
              alignItems: "center",
              justifyContent: "center",
              // marginBottom: 20,
              marginTop: "10%",
            }}
          >
            <TextInput
              style={{
                height: 40,
                width: "87%",
                borderColor: "#A9A9A9",
                borderBottomWidth: 1,
                fontSize: 16,
              }}
              onChangeText={(text) => {
                if (text.length > 18) {
                  alert("Maximum 18 characters");
                } else {
                  setName(text.trimStart().trimEnd());
                }
              }}
              value={name}
              placeholder={language.name}
              placeholderTextColor="#A9A9A9"
            />
          </View>

          <View
            style={{
              width: "88%",
              height: 50,
              flexDirection: "row",
              alignSelf: "center",
              marginTop: "8%",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ fontSize: 14, fontWeight: "400" }}>
              {language.dateOfBirth}:
            </Text>

            <Pressable
              onPress={showDatePicker}
              style={({ pressed }) => ({
                width: "40%",
                // alignSelf: "center",
                marginRight: "1%",
                height: 30,
                // marginTop: 15,
                backgroundColor: "rgba(247,245,246,1)",
                borderRadius: 6,
                borderWidth: 1,
                borderColor: "rgba(215,212,212,1)",
                transform: [{ scale: pressed ? 0.97 : 1 }],
              })}
            >
              <Text style={{ alignSelf: "center", marginTop: 5 }}>
                {date.toLocaleDateString(language.dateLocale, {
                  // weekday: "long",
                  day: "numeric",
                  month: "short",
                  year: "numeric",
                })}
              </Text>
            </Pressable>
            <DateTimePickerModal
              locale={language.dateLocale}
              isVisible={isDatePickerVisible}
              mode="date"
              onConfirm={handleConfirm}
              onCancel={hideDatePicker}
              is24Hour={true}
              // maximumDate={new Date(new Date())}
            />
          </View>

          <View
            style={{
              width: "90%",
              alignSelf: "center",
              marginTop: "9%",
            }}
          >
            <Text
              style={{
                width: "99%",
                fontSize: 14,
                lineHeight: 19,
                color: "rgba(0,0,0,0.9)",
                marginBottom: 10,
              }}
            >
              {language.userInfoGuide}:
            </Text>

            <TextInput
              style={{
                alignSelf: "center",
                width: "100%",
                height: 60,
                borderWidth: 1,
                borderColor: "rgba(0,0,0,0.1)",
                borderRadius: 5,
                paddingHorizontal: 10,
                paddingVertical: 10,
                backgroundColor: "rgba(0,0,0,0.02)",
                fontSize: 14,
                marginTop: "3%",
                marginBottom: keyboardVisible ? 700 : 0,
              }}
              placeholder={`...`}
              placeholderTextColor="rgba(0,0,0,0.4)"
              onChangeText={(text) => setDescription(text)}
              value={description}
              multiline={true}
            />
          </View>

          <View
            style={{
              width: "90%",
              alignItems: "center",
              alignSelf: "center",
              //   marginTop: "25%",
              bottom: 0,
              position: "absolute",
            }}
          >
            <Pressable
              onPress={() => {
                if (!nameFree) {
                  alert(language.usernameTaken);
                  // return;
                } else {
                  if (name !== "" && dateChanged && calculateAge(date) >= 15) {
                    navigation.navigate("ChooseCoords", {
                      name: name.trimEnd(),
                      date: date,
                      description: description?.trimStart()?.trimEnd(),
                      profileImage: profileImage,
                      profileColor: profileColor,
                      description: description,
                      language: language,
                      language2: language2,
                    });
                  } else {
                    if (name == "") {
                      alert(`${language.noUsername}`);
                    }
                    if (!dateChanged) {
                      alert(`${language.noDate}`);
                    }

                    if (calculateAge(date) < 15) {
                      alert(`${language.lowAge}`);
                    }
                  }
                }
              }}
              style={({ pressed }) => ({
                borderRadius: 8,
                transform: [{ scale: pressed ? 0.97 : 1 }],
                width: "60%",
                height: 38,
                backgroundColor: red,

                justifyContent: "center",
                // marginBottom: 30,
                bottom: 15,
              })}
            >
              <Text
                style={{
                  fontWeight: "600",
                  alignSelf: "center",
                  color: "white",
                  fontSize: 15,
                  // fontFamily: "Nunito-Bold",
                }}
              >
                {language.continue}
              </Text>
            </Pressable>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      {/* </TouchableWithoutFeedback> */}

      {/* </ScrollView> */}
    </SafeAreaView>
  );
}
