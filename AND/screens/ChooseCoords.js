import React, {
  useEffect,
  useLayoutEffect,
  useState,
  createContext,
  useContext,
  useCallback,
  useRef,
  useMemo,
  Fragment,
} from "react";
import {
  View,
  Button,
  TouchableOpacity,
  Text,
  Image,
  StyleSheet,
  ActivityIndicator,
  TextInput,
  Alert,
  Platform,
  ImageBackground,
  ScrollView,
  FlatList,
  Animated,
  Pressable,
  AppState,
  TouchableWithoutFeedback,
  useAnimatedValue,
  TouchableHighlight,
  Modal,
} from "react-native";

import { auth, db } from "../config/firebase";
import { storage } from "../config/firebase";

import {
  useNavigation,
  NavigationContainer,
  useFocusEffect,
  useRoute,
} from "@react-navigation/native";

import {
  doc,
  getDoc,
  getDocs,
  collection,
  setDoc,
  updateDoc,
  query,
  onSnapshot,
  collectionGroup,
  orderBy,
  GeoPoint,
  where,
  startAfter,
  getCountFromServer,
  limit,
  increment,
} from "firebase/firestore";

import * as ImageManipulator from "expo-image-manipulator";
import {
  ref,
  uploadBytesResumable,
  getBlob,
  getBytes,
  getDownloadURL,
  uploadString,
} from "firebase/storage";
import {
  SafeAreaView,
  useSafeAreaFrame,
  useSafeAreaInsets,
} from "react-native-safe-area-context";

import { red } from "./colors";

import { MaterialIcons } from "@expo/vector-icons";

import Slider from "@react-native-community/slider";

import { Ionicons } from "@expo/vector-icons";

import MapView, {
  Marker,
  PROVIDER_GOOGLE,
  Polygon,
  Circle,
} from "react-native-maps";

export default function Map() {
  const insets = useSafeAreaInsets();

  const route = useRoute();

  const navigation = useNavigation();
  const [language, setLanguage] = useState(null);

  useLayoutEffect(() => {
    navigation.setOptions({
      // headerBackVisible: false,
      // headerShown: false,

      title: "Choose your area",
      headerTintColor: "black",

      headerStyle: {
        // backgroundColor: red,
        // height: 90,
        // backgroundColor: "rgba(197,42,71,1)",
        // backgroundColor: "rgba(17,36,63,1)",
      },
      headerLeft: () => (
        <TouchableHighlight
          style={{
            marginRight: 10,
            bottom: 3,
            backgroundColor: "white",
            padding: 10,
          }}
          onPress={() => {
            navigation.goBack();
          }}
          underlayColor="rgba(0, 0, 0, 0)"
        >
          <MaterialIcons
            name="arrow-back-ios"
            size={28}
            color="black"
            style={{ marginLeft: 10 }}

            // onPress={() => {}}
          />
        </TouchableHighlight>
      ),
    });
  }, [navigation]);

  const [uploading, setUploading] = useState(false);

  const uploadUser = async () => {
    const profileImage = route.params.profileImage;

    const getBlobFromLocalUri = async (uri) => {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.onload = () => resolve(xhr.response);
        xhr.onerror = (error) =>
          reject(
            new Error(`There was an error fetching the image blob: ${error}`)
          );
        xhr.open("GET", uri);
        xhr.responseType = "blob";
        xhr.send();
      });
    };

    try {
      setUploading(true);

      let tempImageRef;
      let tempImageRefSmall;

      if (profileImage) {
        const nameUID = `/users-profile-images/${auth.currentUser.uid}/profile-image`;
        const storageRef = ref(storage, nameUID);

        const compressedImage = await ImageManipulator.manipulateAsync(
          profileImage,
          [{ resize: { width: 600, height: 600 } }],
          { format: "jpeg" }
        );

        const bytes = await getBlobFromLocalUri(compressedImage.uri);
        await uploadBytesResumable(storageRef, bytes);

        tempImageRef = await getDownloadURL(ref(storage, nameUID));

        const nameUIDProfileImageSmall = `/users-profile-images/${auth.currentUser.uid}/profile-image-small`;
        const storageRefProfileImageSmall = ref(
          storage,
          nameUIDProfileImageSmall
        );

        const compressedImageSmall = await ImageManipulator.manipulateAsync(
          profileImage,
          [{ resize: { width: 300, height: 300 } }],
          { format: "jpeg" }
        );

        const bytesSmall = await getBlobFromLocalUri(compressedImageSmall.uri);
        await uploadBytesResumable(storageRefProfileImageSmall, bytesSmall);

        tempImageRefSmall = await getDownloadURL(
          ref(storage, nameUIDProfileImageSmall)
        );
      }

      await setDoc(doc(db, "users/", auth.currentUser.uid), {
        language: route.params.language2,
        uid: auth.currentUser.uid,
        email: auth.currentUser.email,
        name: route.params.name,
        date: route.params.date,
        description: route.params.description ? route.params.description : null,
        profileColor: route.params.profileColor,
        profileImageRef: tempImageRef ? tempImageRef : null,
        profileImageRefSmall: tempImageRefSmall ? tempImageRefSmall : null,
        // userAreaCoords: coords,
        centerCoord: centerCoordinate,
        logCounter: 1,
        radius: radius,
        favouriteCourts: [],
        eventsArray: [],
        notifications: {
          allowed: true,
          chat: true,
          eventChange: true,
          favCourt: true,
          usersChange: true,
        },
      });

      await setDoc(doc(db, "users2/", auth.currentUser.uid), {
        uid: auth.currentUser.uid,
      });

      await setDoc(doc(db, "events", auth.currentUser.uid), {});

      const uid = auth.currentUser.uid;

      const publicData = {
        name: route.params.name,
        date: route.params.date,
        description: route.params.description ? route.params.description : null,
        profileColor: route.params.profileColor,
        profileImageRef: tempImageRef ? tempImageRef : null,
        profileImageRefSmall: tempImageRefSmall ? tempImageRefSmall : null,
        uid: auth.currentUser.uid,
      };

      const userDocRef = doc(db, "users", uid);

      const publicDataRef = doc(userDocRef, "public", uid + "public");
      await setDoc(publicDataRef, publicData)
        .then(() => console.log("Public data set successfully"))
        .catch((error) => console.log("Error setting public data:", error));

      // const docRef = doc(db, "users", auth.currentUser.uid);

      // await updateDoc(docRef, {
      //   logCounter: increment(1),
      // });

      navigation.navigate("Root");
    } catch (error) {
      alert(error);
      navigation.goBack();
      setUploading(false);
    }
  };

  const [courts, setCourts] = useState(null);
  const hristeClick = (marker) => {
    setSelectedMarker(marker);
  };

  const mapaClick = () => {
    setSelectedMarker(null);
  };

  // STATE

  const [selectedMarker, setSelectedMarker] = useState(null);

  const [mapIsLoading, setMapIsLoading] = useState(true);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setMapIsLoading(false);
    }, 700);

    return () => clearTimeout(timeoutId);
  }, []);

  const opacity1 = useRef(new Animated.Value(-0.5)).current;

  useEffect(() => {
    if (!mapIsLoading) {
      Animated.timing(opacity1, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }
  }, [mapIsLoading]);

  const [isModalVisible2, setModalVisible2] = useState(false);

  const modalAnimatedValue2 = useRef(new Animated.Value(0)).current;

  const toggleModal2 = () => {
    if (isModalVisible2) {
      Animated.timing(modalAnimatedValue2, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => setModalVisible2(false));
    } else {
      setModalVisible2(true);
      Animated.timing(modalAnimatedValue2, {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }).start();
    }
  };

  useEffect(() => {
    toggleModal2();
  }, []);

  const mapRef = useRef(null);
  const [centerCoordinate, setCenterCoordinate] = useState({
    latitude: 50.052324884074504,
    longitude: 14.46223913539908,
  });
  const [radius, setRadius] = useState(17000);

  return (
    <View style={{ flex: 1, backgroundColor: "white" }}>
      {mapIsLoading && (
        <ActivityIndicator style={{ marginTop: "70%" }}></ActivityIndicator>
      )}
      <Animated.View
        style={{ flex: 1, opacity: opacity1, backgroundColor: "white" }}
      >
        {uploading ? (
          <View
            style={{
              // position: "absolute",
              width: "100%",
              height: 81,
              backgroundColor: red,
            }}
          >
            <ActivityIndicator
              color={"white"}
              style={{ position: "absolute", alignSelf: "center", bottom: 12 }}
            ></ActivityIndicator>
          </View>
        ) : (
          <TouchableOpacity
            activeOpacity={0.5}
            style={{
              // position: "absolute",
              width: "100%",
              height: insets.top * 0.5 + 60,
              backgroundColor: red,
            }}
            onPressOut={uploadUser}
          >
            <Text
              style={{
                width: "93%",
                position: "absolute",
                alignSelf: "center",
                // right: 10,
                bottom: 11,
                color: "white",
                fontWeight: "500",
                fontSize: 19,
                textAlign: "center",
                // fontFamily: "Roboto",
                // fontSize: "Nunito-ExtraBold",
              }}
            >
              {route.params.language.save}
            </Text>
          </TouchableOpacity>
        )}

        <View
          style={{
            bottom: 80,
            position: "absolute",
            zIndex: 2,
            left: "17%",
            width: "65%",
          }}
        >
          <Slider
            style={{
              width: "100%",
              height: 60,
              alignSelf: "center",
              // // marginTop: 40,
              // position: "absolute",
              // bottom: 100,
            }}
            minimumValue={1000}
            maximumValue={40000}
            minimumTrackTintColor={red}
            maximumTrackTintColor={"white"}
            thumbTintColor={red}
            value={radius}
            onValueChange={(newValue) => {
              setRadius(newValue);
            }}
          />
        </View>

        <MapView
          ref={mapRef}
          style={{ flex: 1 }}
          // provider={MapView.PROVIDER_GOOGLE}
          onRegionChange={(value) => {
            setCenterCoordinate({
              latitude: value.latitude,
              longitude: value.longitude,
            });
            // adjustCircleSize();
          }}
          customMapStyle={[
            // {
            //   featureType: "landscape.man_made",
            //   stylers: [
            //     {
            //       color: "#f0f5ed",
            //     },
            //   ],
            // },
            {
              featureType: "landscape.man_made",
              elementType: "geometry",
              stylers: [
                {
                  color: "#f6f4ee",
                },
              ],
            },
            {
              featureType: "landscape.natural",
              stylers: [
                {
                  color: "#c8ebb5",
                },
              ],
            },
            {
              featureType: "poi",
              stylers: [
                {
                  visibility: "simplified",
                },
              ],
            },
            {
              featureType: "poi.attraction",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.business",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.government",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.medical",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.park",
              stylers: [
                {
                  color: "#d0f0bd",
                },
              ],
            },
            {
              featureType: "poi.place_of_worship",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.school",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "poi.sports_complex",
              stylers: [
                {
                  visibility: "off",
                },
              ],
            },
            {
              featureType: "road.highway",
              stylers: [
                {
                  color: "#b6b6b8",
                  visibility: "off",
                },
                {
                  weight: 1,
                },
              ],
            },

            {
              featureType: "water",
              stylers: [
                {
                  color: "#ade3f5",
                },
              ],
            },
          ]}
          initialRegion={{
            latitude: centerCoordinate.latitude,
            longitude: centerCoordinate.longitude,
            latitudeDelta: 0.5,
            longitudeDelta: 0.5,
          }}
        >
          <Circle
            center={centerCoordinate}
            radius={radius}
            strokeColor={red}
            // fillColor="rgba(255,0,0,0.5)"
            strokeWidth={1}
          />
          <Circle
            center={centerCoordinate}
            radius={200}
            strokeColor={red}
            fillColor={red}
            strokeWidth={1}
          />
        </MapView>

        <Modal
          animationType="none"
          transparent={true}
          visible={isModalVisible2}
          onRequestClose={toggleModal2}
          style={{ zIndex: 100 }}
        >
          <TouchableOpacity
            style={{ flex: 1, zIndex: 100 }}
            activeOpacity={1}
            onPressOut={toggleModal2}
          >
            <Animated.View
              style={{
                flex: 1,
                justifyContent: "center",
                alignItems: "center",
                backgroundColor: "rgba(0,0,0,0.5)",
                opacity: modalAnimatedValue2,
              }}
            >
              <TouchableWithoutFeedback>
                <View
                  style={{
                    width: "89%",
                    backgroundColor: "#fff",
                    padding: 20,
                    borderRadius: 10,
                  }}
                >
                  <Ionicons
                    name="ios-information-circle-outline"
                    size={50}
                    color="black"
                    style={{ alignSelf: "center", marginTop: "10%" }}
                  />
                  <Text
                    style={{
                      textAlign: "center",
                      fontWeight: "500",
                      fontSize: 15,
                      marginTop: "10%",
                    }}
                  >
                    {route.params.language.areaInfo}
                  </Text>

                  <Text
                    style={{
                      textAlign: "center",
                      fontWeight: "300",
                      fontSize: 14,
                      marginTop: "7%",
                    }}
                  >
                    {route.params.language.changeLater}
                  </Text>

                  <Pressable
                    style={({ pressed }) => ({
                      backgroundColor: pressed ? "#dbdbdb" : "#f1f1f1",
                      borderRadius: 5,
                      padding: 10,
                      alignItems: "center",
                      marginTop: "10%",
                    })}
                    onPress={() => {
                      toggleModal2();
                    }}
                  >
                    <Text
                      style={{
                        fontWeight: "500",
                        fontSize: 15,
                      }}
                    >{`Ok`}</Text>
                  </Pressable>
                </View>
              </TouchableWithoutFeedback>
            </Animated.View>
          </TouchableOpacity>
        </Modal>
      </Animated.View>
    </View>
  );
}
