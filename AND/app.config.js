import "dotenv/config";

export default {
  expo: {
    name: "Let's Hoop",
    slug: "lets-hoop",
    version: "1.0.15",
    orientation: "portrait",
    icon: "./images/andicon.png",
    userInterfaceStyle: "light",
    //splash: {
    //  image: "./assets/newlogo.png",
    //  resizeMode: "contain",
    //  backgroundColor: "#ffffff",
    //  gravity: "center",
    //},
    notification: {
      icon: "./images/icon.png",
      color: "#ffffff"
    },
    updates: {
      fallbackToCacheTimeout: 0,
    },
    assetBundlePatterns: ["**/*"],
    ios: {
    userInterfaceStyle: "light",
    
      supportsTablet: false,
      bundleIdentifier: "com.matyasnemec.letshoop",
      buildNumber: "1.0.3",
      infoPlist: {
        NSCameraUsageDescription:
          "This app needs access to the camera to take photos.",
        NSPhotoLibraryUsageDescription:
          "This app needs access to the library to choose photos.",
        CFBundleURLTypes: [
          {
            CFBundleURLSchemes: [
              "com.googleusercontent.apps.99572682741-hkfauee6abo7tumhg09gh3j8shfivn2n",
            ],
          },
        ],
      },
    },

    android: {
    targetSdkVersion: 34,
      compileSdkVersion: 34,
    versionCode: 115,
    notification: {
      icon: "./images/icon.png",
      color: "#ffffff"
    },
    userInterfaceStyle: "light",
  adaptiveIcon: {
    foregroundImage: "./images/andicon.png",
    backgroundColor: "#FFFFFF",
  },
  permissions: [
    "CAMERA",
    "READ_EXTERNAL_STORAGE",
    "WRITE_EXTERNAL_STORAGE"
    

  ],
  package: "com.matyasnemec.letshoop",
  googleServicesFile: "./google-services.json",
  config: {
    googleMaps: {
      apiKey: "AIzaSyDeoJjMlI6YhcEWyWFLSe0r5w---lMiehk"
    },
  },
},
    web: {
      favicon: "./assets/favicon.png",
    },
     "plugins": [
      [
        "expo-build-properties",
        {
          "ios": {
            "useFrameworks": "static"
          },
          "android": {
            "compileSdkVersion": 34,
            "targetSdkVersion": 34,
            "buildToolsVersion": "34.0.0"
          }
        }
      ],
      ["./plugin/withDisableForcedDarkModeAndroid.js", {}]
    ],
    extra: {
      eas: {
        projectId: "601fa968-e69a-4e71-9c37-bd4de7bd5f96",
                   
      },
      // Place Firebase configuration here, example:
      firebase: {
        apiKey: process.env.FIREBASE_API_KEY,
        authDomain: process.env.FIREBASE_AUTH_DOMAIN,
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.FIREBASE_APP_ID,
        measurementId: process.env.FIREBASE_MEASUREMENT_ID,
      },
    },
    owner: "lerty",
  },
};
