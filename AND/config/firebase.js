// import Constants from "expo-constants";
// import Constants, { expoConfig } from "expo-constants";
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getFunctions } from "firebase/functions";
import { getAnalytics } from "firebase/analytics";

// const firebaseConfig = expoConfig.extra.firebase;

const firebaseConfig = {
  apiKey: "AIzaSyAhyxUBmf4X44gLDlx7uceL0aKvgn4IqHE",
  authDomain: "ballin-project.firebaseapp.com",
  projectId: "ballin-project",
  storageBucket: "gs://ballin-project.appspot.com",
  messagingSenderId: "99572682741",
  appId: "1:99572682741:web:29aa2243615bb650f15abc",
  measurementId: "G-7V2H843YE1",
};

// Initialize Firebase
initializeApp(firebaseConfig);
export const auth = getAuth();
export const db = getFirestore();
export const storage = getStorage();
export const functions = getFunctions();
