const {on<PERSON>all, HttpsError} = require("firebase-functions/v2/https");
// const {functions} = require("firebase-functions");
const {GeoPoint} = require("firebase-admin/firestore");
const {onSchedule} = require("firebase-functions/v2/scheduler");
const admin = require("firebase-admin");
const {Expo} = require("expo-server-sdk");
// const logger = require("firebase-functions/logger");
admin.initializeApp();
const db = admin.firestore();
const expo = new Expo();

// const functions = require("firebase-functions");
// const admin = require("firebase-admin");
// const axios = require("axios");

// const { Storage } = require("@google-cloud/storage");
// const storage = new Storage();

// const { Expo } = require("expo-server-sdk");

// admin.initializeApp();

// const expo = new Expo();

// const db = admin.firestore();

// exports.updateIsLiveStatus = functions.pubsub
//     .schedule("every 1 minutes")
//     .onRun(async (context) => {
//       const userEventsRef = db.collectionGroup("user_events");
//       const snapshot = await userEventsRef.get();

//       const updatePromises = [];

//       snapshot.docs.forEach((doc) => {
//         const eventData = doc.data();
//         const isLive = eventData.timeStart.toDate() <= new Date();

//         if (eventData.isLive !== isLive) {
//         // Update both 'isLive' and 'kickedUser'
//           updatePromises.push(
//               doc.ref.update({
//                 isLive: isLive,
//                 kickedUser: admin.firestore.FieldValue.serverTimestamp(),
//               }),
//           );
//         }
//       });

//       await Promise.all(updatePromises);
//     });

// THIS WILL PROBALY BE DELETED
// exports.fetchTemperature = functions.https.onCall(async (data, context) => {
//   const lat = data.latitude;
//   const lon = data.longitude;
//   const apiKey = "df7c8b2314614f98b46125528232207";

//   try {
//     // Construct the URL for WeatherAPI with the passed in lat and lon
//     const weatherAPIUrl = `http://api.weatherapi.com/v1/current.json?key=${apiKey}&q=${lat},${lon}`;

//     // Make a request to the WeatherAPI
//     const response = await axios.get(weatherAPIUrl);

//     // Extract the temperature from the response data
//     const temperature = response.data.current.temp_c;

//     // Return the temperature
//     return {temperature};
//   } catch (error) {
//     console.error("Error while fetching temperature:", error);
//     throw new functions.https.HttpsError(
//         "unknown",
//         "Failed to fetch temperature",
//     );
//   }
// });

// THIS IS OUTDATED, WILL HAVE TO UPDATE
// IT SO THAT IT WORKS WITH THE NEW DATABASE STRUCTURE
// exports.updateIsLiveStatus = functions.pubsub
//     .schedule("every 1 minutes")
//     .onRun(async (context) => {
//       const userEventsRef = db.collectionGroup("user_events");
//       const snapshot = await userEventsRef.get();

//       const promises = [];

//       snapshot.docs.forEach((doc) => {
//         const eventData = doc.data();
//         const isLive = eventData.timeStart.toDate() <= new Date();
//         const isEnded = eventData.timeEnd.toDate() < new Date();

//         if (eventData.isLive !== isLive) {
//         // Update both 'isLive' and 'kickedUser'
//           promises.push(
//               doc.ref.update({
//                 isLive: isLive,
//                 kickedUser: admin.firestore.FieldValue.serverTimestamp(),
//               }),
//           );
//         }

//         if (isEnded) {
//           promises.push(
//               (async () => {
//                 await doc.ref.update({
//                   ended: true,
//                 });

//                 const subcollections = ["chat", "eventUsers"];
//                 for (const subcollection of subcollections) {
//                   const subCollectionRef = doc.ref.collection(subcollection);
//                   const subCollectionSnapshot = await subCollectionRef.get();
//                   subCollectionSnapshot.docs.forEach((doc) => {
//                     promises.push(doc.ref.delete());
//                   });
//                 }

//                 await doc.ref.delete();
//               })(),
//           );
//         }
//       });

//       await Promise.all(promises);
//     });

/**
 * Calculates the age based on a given date of birth.
 * @param {Object} dob - The date of birth as a JavaScript Date object.
 * @return {number} The calculated age.
 */
// function calculateAge(dob) {
//   const diffMs = Date.now() - new Date(dob.seconds * 1000).getTime();
//   const ageDt = new Date(diffMs);
//   return Math.abs(ageDt.getUTCFullYear() - 1970);
// }

// THIS IS OUTDATED, WILL HAVE
// TO UPDATE IT SO THAT IT WORKS WITH THE NEW DATABASE STRUCTURE
// exports.onNewEvent = functions.firestore
//   .document("{collectionId}/{docId}/user_events/{eventId}")
//   .onCreate(async (snap, context) => {
//     const newEvent = snap.data();

//     // function calculateAge(dob) {
//     //   const diffMs = Date.now() - new Date(dob.seconds * 1000).getTime();
//     //   const ageDt = new Date(diffMs);
//     //   return Math.abs(ageDt.getUTCFullYear() - 1970);
//     // }

//     console.log("New event created");

//     const courtId = newEvent.courtObj.id;
//     const courtRef = admin.firestore().collection("courts-cz1").doc(courtId);
//     const courtSnapshot = await courtRef.get();
//     const courtData = courtSnapshot.data();

//     if (!courtData) {
//       console.log(`No court found with id ${courtId}`);
//       return;
//     }

//     console.log("court data", courtData);

//     const userRef = courtRef.collection("subscribedUsers");
//     const userSnapshot = await userRef.get();
//     // const users = userSnapshot.docs.map((doc) => doc.data());

//     const users = [];

//     userSnapshot.docs.forEach((doc) => {
//       users.push(doc.data().userID);
//     });

//     console.log("users", users);

//     const tokens = [];
//     for (const user of users) {
//       const userSnapshot = await admin
//         .firestore()
//         .collection("users")
//         .doc(user)
//         .get();
//       const userData = userSnapshot.data();

//       // ///////////// CHECK newEvent.creator works!!!!!!!!!!!

//       if (
//         userData.notifications.allowed &&
//         userData.notifications.favCourt &&
//         userData.logCounter > 0 &&
//         (newEvent.ageLimitMin ? newEvent.ageLimitMin : -1) <=
//           calculateAge(userData.date) &&
//         (newEvent.ageLimitMax ? newEvent.ageLimitMax : 100000) >=
//           calculateAge(userData.date)
//       ) {
//         if (newEvent.creator !== userData.uid) {
//           tokens.push(userData.notificationsToken);
//         }
//       }
//     }

//     console.log("tokes", tokens);

//     // Create the messages that you want to send to clients
//     const messages = [];
//     for (const pushToken of tokens) {
//       // Each push token looks like ExponentPushT
//       // Check that all your push tokens appear to be vns
//       if (!Expo.isExpoPushToken(pushToken)) {
//         console.error(`Push token ${pushToken} is not a valid token`);
//         continue;
//       }

//       // Construct a message
//       messages.push({
//         to: pushToken,
//         sound: "default",
//         title: "Nová hra!",
//         body:
//           `Někdo vytvořil hru na tvém ` +
//           `oblíbeném hřišti! ${courtData.address}`,
//         data: { withSome: "data" },
//       });
//     }

//     // Send the messages
//     const chunks = expo.chunkPushNotifications(messages);
//     const tickets = [];
//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error(error);
//       }
//     }
//   });

// OUTDATED
// exports.onNewChat = functions.firestore
//   .document("{collectionId}/{docId}/user_events/{eventId}/chat/{message}")
//   .onCreate(async (snap, context) => {
//     const newChat = snap.data();

//     console.log("New message sent");

//     const collectionId = context.params.collectionId;
//     const docId = context.params.docId;
//     const eventId = context.params.eventId;

//     const eventRef = db
//       .collection(collectionId)
//       .doc(docId)
//       .collection("user_events")
//       .doc(eventId);

//     let eventData;
//     let eventUsers;

//     const eventSnap = await eventRef.get();

//     if (eventSnap.exists) {
//       eventData = eventSnap.data();
//       // console.log(`Event data: `, eventData);

//       const userEventsRef = eventRef.collection("eventUsers");
//       const userEventsSnap = await userEventsRef.get();

//       eventUsers = userEventsSnap.docs.map((doc) => doc.data().userID);
//     } else {
//       console.log(`No event found with id ${eventId}`);
//       eventUsers = [];
//     }

//     const userFetchPromises = eventUsers.map((user) =>
//       admin.firestore().collection("users").doc(user).get()
//     );
//     const userSnapshots = await Promise.all(userFetchPromises);

//     console.log(
//       "USERS:",
//       userSnapshots.map((user) => user.data())
//     );

//     const tokens = userSnapshots
//       .filter((userSnapshot) => {
//         return (
//           userSnapshot.exists &&
//           userSnapshot.data().notifications.allowed &&
//           userSnapshot.data().notifications.chat &&
//           newChat.userID !== userSnapshot.data().uid &&
//           userSnapshot.data().logCounter > 0
//         );
//       })
//       .map((userSnapshot) => [
//         userSnapshot.data().notificationsToken,
//         userSnapshot.data().language,
//       ]);

//     console.log("tokes", tokens);

//     // Create the messages that you want to send to clients
//     const messages = [];
//     for (const pushToken of tokens) {
//       // Each push token looks like ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]

//       // Check that all your push tokens appear to be valid Expo push tokens
//       if (!Expo.isExpoPushToken(pushToken[0])) {
//         console.error(`Push token ${pushToken[0]} is not a valid token`);
//         continue;
//       }

//       // Construct a message
//       messages.push({
//         to: pushToken[0],
//         sound: "default",
//         title: `${eventData.eventName}`,
//         body:
//           pushToken[1] === "CZ"
//             ? `${newChat.user.name}: ${newChat.text}`
//             : `${newChat.user.name}: ${newChat.text}`,
//         data: { withSome: "data" },
//       });
//     }

//     // Send the messages
//     const chunks = expo.chunkPushNotifications(messages);
//     const tickets = [];
//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error(error);
//       }
//     }
//   });

// OUTDATED
// exports.onDocumentUpdate2 = functions.firestore
//   .document("{collectionId}/{docId}/user_events/{eventId}")
//   .onUpdate(async (change, context) => {
//     const beforeData = change.before.data();
//     const afterData = change.after.data();

//     if (beforeData.ended !== afterData.ended) {
//       return;
//     }

//     if (beforeData.kickedUser.seconds !== afterData.kickedUser.seconds) {
//       return;
//     }

//     if (
//       beforeData.lastUpdated.seconds !== afterData.lastUpdated.seconds ||
//       beforeData.eventID === null
//     ) {
//       // If only 'lastUpdated' field was changed, return immediately
//       console.log("Only 'lastUpdated' field changed. Exiting function.");

//       const collectionId = context.params.collectionId;
//       const docId = context.params.docId;
//       const eventId = context.params.eventId;

//       const eventRef = db
//         .collection(collectionId)
//         .doc(docId)
//         .collection("user_events")
//         .doc(eventId);

//       let eventData;

//       const eventSnap = await eventRef.get();

//       if (eventSnap.exists) {
//         eventData = eventSnap.data();
//         // console.log(`Event data: `, eventData);

//         // const userEventsRef = eventRef.collection("eventUsers");
//         // const userEventsSnap = await userEventsRef.get();

//         // eventUsers = userEventsSnap.docs.map((doc) => doc.data().userID);

//         const userSnapshot = await admin
//           .firestore()
//           .collection("users")
//           .doc(eventData.creator)
//           .get();

//         if (!userSnapshot.exists) {
//           console.error(`User ${eventData.creator} not found`);
//           return;
//         }

//         const user = userSnapshot.data();
//         const tokens = [];

//         console.log("user", user);

//         if (
//           user.notifications.allowed &&
//           user.notifications.usersChange &&
//           user.logCounter > 0
//         ) {
//           tokens.push([user.notificationsToken, user.language]);

//           // Create the messages that you want to send to clients
//           const messages = [];
//           for (const pushToken of tokens) {
//             // Check that all your push tokens appear to be..
//             if (!Expo.isExpoPushToken(pushToken[0])) {
//               console.error(`Push token ${pushToken[0]} is not a valid t`);
//               continue;
//             }

//             // Construct a message
//             if (afterData.usersChange === "joined") {
//               messages.push({
//                 to: pushToken[0],
//                 sound: "default",
//                 title: `${eventData.eventName}`,
//                 body:
//                   pushToken[1] === "CZ"
//                     ? `Nový hráč se připojil k tvé hře!`
//                     : `A new player has joined your game!`,
//                 data: { withSome: "data" },
//               });
//             } else if (afterData.usersChange === "left") {
//               messages.push({
//                 to: pushToken[0],
//                 sound: "default",
//                 title: `${eventData.eventName}`,
//                 body:
//                   pushToken[1] === "CZ"
//                     ? `Někdo právě opustil tvou hru`
//                     : `Someone has left your game`,
//                 data: { withSome: "data" },
//               });
//             }
//           }

//           console.log("mesages", messages);

//           // Send the messages
//           const chunks = expo.chunkPushNotifications(messages);
//           const tickets = [];
//           for (const c of chunks) {
//             try {
//               const ticketChunk = await expo.sendPushNotificationsAsync(c);
//               tickets.push(...ticketChunk);
//             } catch (error) {
//               console.error(error);
//             }
//           }
//         }
//       } else {
//         console.log(`No event found with id ${eventId}`);
//       }

//       return;
//     }

//     console.log(
//       beforeData.lastUpdated,
//       afterData.lastUpdated,
//       beforeData.lastUpdated === afterData.lastUpdated
//     );
//     // Get an object representing the document after the change
//     // const newData = change.after.data();

//     const collectionId = context.params.collectionId;
//     const docId = context.params.docId;
//     const eventId = context.params.eventId;

//     const eventRef = db
//       .collection(collectionId)
//       .doc(docId)
//       .collection("user_events")
//       .doc(eventId);

//     let eventData;
//     let eventUsers;

//     const eventSnap = await eventRef.get();

//     if (eventSnap.exists) {
//       eventData = eventSnap.data();
//       console.log(`Event data: `, eventData);

//       const userEventsRef = eventRef.collection("eventUsers");
//       const userEventsSnap = await userEventsRef.get();

//       eventUsers = userEventsSnap.docs.map((doc) => doc.data().userID);
//     } else {
//       console.log(`No event found with id ${eventId}`);
//       eventUsers = [];
//     }

//     const userFetchPromises = eventUsers.map((user) =>
//       admin.firestore().collection("users").doc(user).get()
//     );

//     const userSnapshots = await Promise.all(userFetchPromises);

//     const tokens = userSnapshots
//       .filter((userSnapshot) => {
//         return (
//           userSnapshot.exists &&
//           userSnapshot.data().notifications.allowed &&
//           userSnapshot.data().notifications.eventChange &&
//           eventData.creator !== userSnapshot.data().uid &&
//           userSnapshot.data().logCounter > 0
//         );
//       })
//       .map((userSnapshot) => [
//         userSnapshot.data().notificationsToken,
//         userSnapshot.data().language,
//       ]);

//     console.log("tokes", tokens);

//     // Create the messages that you want to send to clients
//     const messages = [];
//     for (const pushToken of tokens) {
//       // Check that all your push tokens appear to be valid Expo push tokens
//       if (!Expo.isExpoPushToken(pushToken[0])) {
//         console.error(`Push token ${pushToken[0]} is not a valid token`);
//         continue;
//       }

//       // Construct a message
//       messages.push({
//         to: pushToken[0],
//         sound: "default",
//         title: `${eventData.eventName}`,
//         body:
//           pushToken[1] === "CZ"
//             ? `Tvůrce hry právě změnil její nastavení`
//             : `The creator of the game changed its settings`,
//         data: { withSome: "data" },
//       });
//     }

//     // Send the messages
//     const chunks = expo.chunkPushNotifications(messages);
//     const tickets = [];
//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error(error);
//       }
//     }
//   });

// OUTDATED
// exports.onDocumentDelete = functions.firestore
//   .document("{collectionId}/{docId}/user_events/{eventId}")
//   .onDelete(async (snap, context) => {
//     const eventData = snap.data();

//     if (eventData.ended) {
//       return;
//     }

//     const userFetchPromises = eventData.usersDelete.map((user) =>
//       admin.firestore().collection("users").doc(user).get()
//     );
//     const userSnapshots = await Promise.all(userFetchPromises);

//     const tokens = userSnapshots

//       .filter((userSnapshot) => {
//         const userExists = userSnapshot.exists;
//         const isNotCreator = userSnapshot.data().uid !== eventData.creator;
//         return userExists && isNotCreator;
//       })
//       .map((userSnapshot) => [
//         userSnapshot.data().notificationsToken,
//         userSnapshot.data().language,
//       ]);

//     console.log("tokens", tokens);

//     // Create the messages that you want to send to clients
//     const messages = [];
//     for (const pushToken of tokens) {
//       // Check that all your push tokens appear to be valid Expo push tokens
//       if (!Expo.isExpoPushToken(pushToken[0])) {
//         console.error(`Push token ${pushToken[0]} is not a valid token`);
//         continue;
//       }
//   const string = pushToken[1] === "CZ" ?
// `Hra byla zrušena` : `The game was cancelled`

//   // Construct a message
//   messages.push({
//     to: pushToken[0],
//     sound: "default",
//     title: `${eventData.eventName}`,
//     body:string
//       ,
//     data: { withSome: "data" },
//   });
// }

//     // Send the messages
//     const chunks = expo.chunkPushNotifications(messages);
//     const tickets = [];
//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error(error);
//       }
//     }
//   });

/**
 * Function to delete user and associated data.
 * @param {Object} user - The user to delete.
 */
// WILL HAVE TO GET UPDATED AND MIGRATED
// exports.deleteUserAndData = functions.auth.user().onDelete(async (user) => {
//   console.log(user);
//   const uid = user.uid;

//   const firestore = admin.firestore();

//   // Fetch user's document
//   const userRef = firestore.collection("users").doc(uid);

//   const userSnapshot = await userRef.get();

//   if (!userSnapshot.exists) {
//     console.error(`User ${uid} not found`);
//     return;
//   }

//   const userDoc = userSnapshot.data();
//   console.log(userDoc);

//   // NEW CODE STARTS HERE
//   // Fetch the user's document from users2 collection
//   const userRef2 = firestore.collection("users2").doc(uid);

//   const userSnapshot2 = await userRef2.get();

//   if (!userSnapshot2.exists) {
//     console.error(`User in users2 collection with ${uid} not found`);
//   } else {
//     // Delete the user's document from users2 collection
//     await userRef2.delete();
//   }
//   // NEW CODE ENDS HERE

//   const createdEventsSnapshot2 = await firestore
//     .collection(`events/${uid}/user_events`)
//     .get();

//   const deletePromises = [];

//   // Delete 'chat' and 'eventUsers' subcollections of each document
//   for (const doc of createdEventsSnapshot2.docs) {
//     const docRef = doc.ref;
//     const batch1 = firestore.batch();

//     // Delete 'chat' subcollection
//     const chatSnapshot = await docRef.collection("chat").get();
//     chatSnapshot.docs.forEach((chatDoc) => {
//       batch1.delete(chatDoc.ref);
//     });

//     // Delete 'eventUsers' subcollection
//     const eventUsersSnapshot = await docRef.collection("eventUsers").get();
//     eventUsersSnapshot.docs.forEach((eventUserDoc) => {
//       batch1.delete(eventUserDoc.ref);
//     });

//     batch1.delete(docRef);

//     deletePromises.push(batch1.commit());
//   }

//   await Promise.all(deletePromises);

//   // Fetch and delete all instances of the user's attendance at other events
//   const attendingEventsSnapshot = await firestore
//     .collectionGroup("eventUsers")
//     .where("userID", "==", uid)
//     .get();

//   const batch2 = firestore.batch();
//   attendingEventsSnapshot.docs.forEach((doc) => {
//     batch2.delete(doc.ref);
//   });
//   await batch2.commit();

//   // Fetch and delete all
// documents in 'public' subcollection of user's document
//   const publicSubCollection = userRef.collection("public");
//   const publicSubCollectionSnapshot = await publicSubCollection.get();

//   const batch3 = firestore.batch();
//   publicSubCollectionSnapshot.docs.forEach((doc) => {
//     batch3.delete(doc.ref);
//   });
//   await batch3.commit();

//   // Fetch and delete all instances of the user's attendance at other events
//   const favCourtsSnapshot = await firestore
//     .collectionGroup("subscribedUsers")
//     .where("userID", "==", uid)
//     .get();

//   const batch4 = firestore.batch();
//   favCourtsSnapshot.docs.forEach((doc) => {
//     batch4.delete(doc.ref);
//   });
//   await batch4.commit();

//   // NEW CODE STARTS
//   // Fetch and delete all messages the user sent across all events
//   const chatSnapshot = await firestore
//     .collectionGroup("chat")
//     .where("userID", "==", uid)
//     .get();

//   const batch5 = firestore.batch();
//   chatSnapshot.docs.forEach((doc) => {
//     batch5.delete(doc.ref);
//   });
//   await batch5.commit();
//   // NEW CODE ENDS

//   // Delete the user's document
//   await userRef.delete();

//   // NEW CODE FOR DELETING PROFILE PICTURES STARTS HERE

//   // Retrieve bucket name from the initialized admin app
//   const bucketName = admin.storage().app.options.storageBucket;

//   // Reference to the bucket
//   const bucket = storage.bucket(bucketName);

//   // Define the folder name
//   const folderName = `users-profile-images/${uid}/`;

//   // List all the files in the user's profile image folder
//   const [files] = await bucket.getFiles({ prefix: folderName });

//   // Create an array to store promises
//   const deleteFilePromises = [];

//   // Loop over the files and delete each one
//   files.forEach((file) => {
//     deleteFilePromises.push(file.delete());
//   });

//   // Wait for all files to be deleted
//   await Promise.all(deleteFilePromises);

//   console.log(`Deleted profile images of user ${uid}`);
//   // NEW CODE FOR DELETING PROFILE
// PICTURES ENDS HERE

//   console.log(`Successfully deleted user
//  and associated data with uid: ${uid}`);
// });

// /**
//  * Function to delete subcollections of a document.
//  * @param {Object} firestore - The Firestore instance.
//  * @param {Object} doc - The document.
//  * @param {string[]} subcollectionNames .
//  */
// async function deleteSubcollections(firestore, doc, subcollectionNames) {
//   if (subcollectionNames && subcollectionNames.length > 0) {
//     for (let i = 0; i < subcollectionNames.length; i++) {
//       const subcollectionName = subcollectionNames[i];
//       const subcollectionSnapshot = await doc.ref
//           .collection(subcollectionName)
//           .get();
//       const batch = firestore.batch();
//       subcollectionSnapshot.docs.forEach((doc) => {
//         batch.delete(doc.ref);
//       });
//       await batch.commit();
//     }
//   }
// }

// exports.verifyToken = functions.https.onCall((data, context) => {
//   return admin
//     .auth()
//     .verifyIdToken(data.idToken)
//     .then((decodedToken) => {
//       return { uid: decodedToken.uid };
//     })
//     .catch((error) => {
//       throw new functions.https.HttpsError(
//         "invalid-argument",
//         "The function must be called with a valid ID token."
//       );
//     });
// });

// OLD
// exports.removeUserNotification = functions.https.onCall(
//   async (data, context) => {
//     const userId = data.userId;
//     // const eventId = data.eventId;
//     const eventName = data.eventName;

//     const userRef = db.collection("users").doc(userId);

//     console.log(userId);

//     let userSnapshot;
//     let userToken;
//     let userLanguage;
//     let userData;

//     try {
//       userSnapshot = await userRef.get();

//       if (userSnapshot.exists) {
//         userData = userSnapshot.data();

//         // if (userData.notifications.allowed) {
//         userToken = userData.notificationsToken;
//         userLanguage = userData.language;
//         // }
//       }
//     } catch (err) {
//       console.log(`Failed to get user with id ${userId}: `, err);
//       throw new functions.https.HttpsError(
//         "internal",
//         `Failed to get user with id ${userId}: ${err}`
//       );
//     }

//     // if (userData.isLoggedIn == false) {
//     //   return;
//     // }

//     // Construct a message
//     const messages = [];

//     if (Expo.isExpoPushToken(userToken)) {
//       messages.push({
//         to: userToken,
//         sound: "default",
//         title: `${eventName}`,
//         body:
//           userLanguage === "CZ"
//             ? `Právě jsi byl odebrán ze hry!`
//             : `You have been removed from the game!`,
//         data: { withSome: "data" },
//       });
//     } else {
//       console.error(`Push token ${userToken} is not a valid token`);
//     }

//     // Send the messages
//     const chunks = expo.chunkPushNotifications(messages);
//     const tickets = [];
//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error(error);
//       }
//     }

//     return {
//       result: "Notification sent",
//     };
//   }
// );
// NEW
exports.removeUserNotificationV2 = onCall(async (request) => {
  try {
    console.log(`Starting removeUserNotificationV2`);
    const data = request.data;

    const {userId, eventName} = data;

    console.log(`Starting removeUserNotificationV2 for user: ${userId}`);

    const userRef = db.collection("users").doc(userId);
    const userSnapshot = await userRef.get();

    if (!userSnapshot.exists) {
      throw new HttpsError("not-found", `User with id ${userId} not found`);
    }

    const userData = userSnapshot.data();
    const userToken = userData.notificationsToken;
    const userLanguage = userData.language;

    console.log("User data retrieved successfully");

    if (!Expo.isExpoPushToken(userToken)) {
      console.error(`Invalid Expo push token: ${userToken}`);
      throw new HttpsError(
          "invalid-argument",
          `Invalid Expo push token: ${userToken}`,
      );
    }

    const notificationBody =
      userLanguage === "CZ" ?
        "Právě jsi byl odebrán ze hry!" :
        "You have been removed from the game!";

    const message = {
      to: userToken,
      sound: "default",
      title: eventName,
      body: notificationBody,
      data: {withSome: "data"},
    };

    console.log("Notification message created");

    const chunks = expo.chunkPushNotifications([message]);
    const tickets = [];

    for (const chunk of chunks) {
      try {
        const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
        tickets.push(...ticketChunk);
      } catch (error) {
        console.error("Error sending notification chunk:", error);
        throw new HttpsError(
            "internal",
            `Error sending notification chunk: ${error.message}`,
        );
      }
    }

    console.log("Notifications sent successfully");

    return {
      result: "Notification sent",
      tickets: tickets,
    };
  } catch (err) {
    console.error("Error in removeUserNotificationV2:", err);
    throw new HttpsError(
        "internal",
        `Unexpected error in removeUserNotificationV2: ${err.message}`,
    );
  }
});

// OLD
// exports.sentFriendRequestNotification = functions.https.onCall(
//     async (data, context) => {
//       const from = data.from;
//       const to = data.to;

//       const userRef = db.collection("users").doc(to);

//       let userSnapshot;
//       let userToken;
//       let userLanguage;
//       let userData;

//       try {
//         userSnapshot = await userRef.get();

//         if (userSnapshot.exists) {
//           userData = userSnapshot.data();

//           userToken = userData.notificationsToken;
//           userLanguage = userData.language;
//         }
//       } catch (err) {
//         console.log(`Failed to get user with id ${to}: `, err);
//         throw new functions.https.HttpsError(
//             "internal",
//             `Failed to get user with id ${to}: ${err}`,
//         );
//       }

//       const fromUserRef = db.collection("users").doc(from);

//       let fromUserSnapshot;
//       let fromUserName;

//       try {
//         fromUserSnapshot = await fromUserRef.get();

//         if (fromUserSnapshot.exists) {
//           fromUserName = fromUserSnapshot.data().name;
//         }
//       } catch (err) {
//         console.log(`Failed to get user with id ${from}: `, err);
//         throw new functions.https.HttpsError(
//             "internal",
//             `Failed to get user with id ${from}: ${err}`,
//         );
//       }

//       const messages = [];

//       if (Expo.isExpoPushToken(userToken)) {
//         messages.push({
//           to: userToken,
//           sound: "default",
//           title: userLanguage === "CZ" ? `Nový přítel` : `New Friend`,
//           body:
//           userLanguage === "CZ" ?
//             `${fromUserName} ti poslal žádost o přátelství!` :
//             `${fromUserName} has sent you a friend request!`,
//           data: {withSome: "data"},
//         });
//       } else {
//         console.error(`Push token ${userToken} is not a valid token`);
//       }

//       console.log(messages);

//       // Send the messages
//       const chunks = expo.chunkPushNotifications(messages);
//       const tickets = [];
//       for (const chunk of chunks) {
//         try {
//           const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//           tickets.push(...ticketChunk);
//         } catch (error) {
//           console.error(error);
//         }
//       }

//       return {
//         result: "Notification sent",
//       };
//     },
// );

// NEW
// exports.sentFriendRequestNotificationV2 = onCall(async (request) => {
//   try {
//     console.log("Starting sentFriendRequestNotification");
//     const {from, to} = request.data;

//     // Get recipient user data
//     const userRef = db.collection("users").doc(to);
//     const userSnapshot = await userRef.get();

//     if (!userSnapshot.exists) {
//       throw new HttpsError("not-found", `User with id ${to} not found`);
//     }

//     const userData = userSnapshot.data();
//     const userToken = userData.notificationsToken;
//     const userLanguage = userData.language;

//     console.log("Recipient user data retrieved successfully");

//     // Get sender user data
//     const fromUserRef = db.collection("users").doc(from);
//     const fromUserSnapshot = await fromUserRef.get();

//     if (!fromUserSnapshot.exists) {
//       throw new HttpsError("not-found", `User with id ${from} not found`);
//     }

//     const fromUserName = fromUserSnapshot.data().name;
//     console.log("Sender user data retrieved successfully");

//     // Validate push token
//     if (!Expo.isExpoPushToken(userToken)) {
//       console.error(`Invalid Expo push token: ${userToken}`);
//       throw new HttpsError(
//           "invalid-argument",
//           `Invalid Expo push token: ${userToken}`,
//       );
//     }

//     // Create notification message
//     const message = {
//       to: userToken,
//       sound: "default",
//       title: userLanguage === "CZ" ? `Nový přítel` : `New Friend`,
//       body:
//         userLanguage === "CZ" ?
//           `${fromUserName} ti poslal žádost o přátelství!` :
//           `${fromUserName} has sent you a friend request!`,
//       data: {withSome: "data"},
//     };

//     console.log("Notification message created");

//     // Send notification
//     const chunks = expo.chunkPushNotifications([message]);
//     const tickets = [];

//     for (const chunk of chunks) {
//       try {
//         const ticketChunk = await expo.sendPushNotificationsAsync(chunk);
//         tickets.push(...ticketChunk);
//       } catch (error) {
//         console.error("Error sending notification chunk:", error);
//         throw new HttpsError(
//             "internal",
//             `Error sending notification chunk: ${error.message}`,
//         );
//       }
//     }

//     console.log("Notifications sent successfully");

//     return {
//       result: "Notification sent",
//       tickets: tickets,
//     };
//   } catch (err) {
//     console.error("Error in sentFriendRequestNotification:", err);
//     throw new HttpsError(
//         "internal",
//         `Unexpected error in sentFriendRequestNotification: ${err.message}`,
//     );
//   }
// });

exports.sentFriendRequestNotificationV2 = onCall(async (request) => {
  try {
    // 1. Initial validation
    const {from, to} = request.data;

    if (!from || !to) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: 'from' or 'to'",
      );
    }

    if (from !== request.auth.uid) {
      throw new HttpsError(
          "permission-denied",
          "User can only send requests from their own account",
      );
    }

    // 2. Get both users' data
    const fromUserRef = db.collection("users").doc(from);
    const toUserRef = db.collection("users").doc(to);
    const [fromUserSnapshot, toUserSnapshot] = await Promise.all([
      fromUserRef.get(),
      toUserRef.get(),
    ]);

    // 3. Validate users exist
    if (!fromUserSnapshot.exists) {
      throw new HttpsError("not-found", "Sender account not found");
    }
    if (!toUserSnapshot.exists) {
      throw new HttpsError("not-found", "Recipient account not found");
    }

    const fromUserData = fromUserSnapshot.data();
    const toUserData = toUserSnapshot.data();

    // 4. Check if request already sent
    const friendRequestsSent = fromUserData.friendRequestsSent || [];
    if (friendRequestsSent.includes(to)) {
      throw new HttpsError(
          "already-exists",
          "Friend request already sent to this user",
      );
    }

    // 5. Check if already friends
    const existingFriends = toUserData.friends || [];
    if (existingFriends.includes(from)) {
      throw new HttpsError("already-exists", "Users are already friends");
    }

    try {
      // 6. Perform database updates in transaction
      await db.runTransaction(async (transaction) => {
        // Create a ref with auto-generated ID instead of using sender's ID
        const notificationRef = db
            .collection(`users/${to}/notifications`)
            .doc(); // Remove 'from' to let Firebase generate unique ID

        transaction.set(notificationRef, {
          type: "friendRequest",
          from: from,
          date: admin.firestore.FieldValue.serverTimestamp(),
          accepted: false,
          acceptedAt: null,
        });

        transaction.update(fromUserRef, {
          friendRequestsSent: admin.firestore.FieldValue.arrayUnion(to),
        });
      });

      // 7. Only if transaction succeeds, try to send notification
      if (
        toUserData.notificationsToken &&
        Expo.isExpoPushToken(toUserData.notificationsToken)
      ) {
        const message = {
          to: toUserData.notificationsToken,
          sound: "default",
          title: toUserData.language === "CZ" ? "Nový přítel" : "New Friend",
          body:
            toUserData.language === "CZ" ?
              `${fromUserData.name} ti poslal žádost o přátelství!` :
              `${fromUserData.name} has sent you a friend request!`,
          data: {type: "friendRequest", from},
        };

        const chunks = expo.chunkPushNotifications([message]);
        for (const chunk of chunks) {
          await expo.sendPushNotificationsAsync(chunk);
        }
      }

      // 8. Return success
      return {
        success: true,
        message: "Friend request sent successfully",
      };
    } catch (error) {
      // Transaction or notification error
      console.error("Error in database operation:", error);
      throw new HttpsError("internal", "Failed to process friend request");
    }
  } catch (error) {
    // Any other error
    console.error("Error in sentFriendRequestNotificationV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while processing the friend request",
    );
  }
});

exports.acceptFriendRequestV2 = onCall(async (request) => {
  try {
    const {notificationId, from, to} = request.data;

    // Validate input
    if (!notificationId || !from || !to) {
      throw new HttpsError("invalid-argument", "Missing required parameters");
    }

    if (from === to) {
      throw new HttpsError(
          "invalid-argument",
          "Cannot accept friend request from yourself",
      );
    }

    // Verify requester is the recipient
    if (to !== request.auth.uid) {
      throw new HttpsError(
          "permission-denied",
          "User can only accept their own friend requests",
      );
    }

    // Get both users' data
    const fromUserRef = db.collection("users").doc(from);
    const toUserRef = db.collection("users").doc(to);
    const notificationRef = db
        .collection(`users/${to}/notifications`)
        .doc(notificationId);

    // Get all required data first
    const [fromUserSnapshot, toUserSnapshot, notificationSnapshot] =
      await Promise.all([
        fromUserRef.get(),
        toUserRef.get(),
        notificationRef.get(),
      ]);

    // Verify users and notification exist
    if (!fromUserSnapshot.exists || !toUserSnapshot.exists) {
      throw new HttpsError("not-found", "One or both users not found");
    }

    if (!notificationSnapshot.exists) {
      throw new HttpsError(
          "not-found",
          "Friend request notification not found",
      );
    }

    // Verify notification is a friend request from the correct user
    const notifData = notificationSnapshot.data();
    if (notifData.type !== "friendRequest" || notifData.from !== from) {
      throw new HttpsError(
          "invalid-argument",
          "Invalid friend request notification",
      );
    }

    const fromUserData = fromUserSnapshot.data();
    const toUserData = toUserSnapshot.data();

    // Before the transaction, check if they're already friends
    if (
      fromUserData.friends?.includes(to) ||
      toUserData.friends?.includes(from)
    ) {
      throw new HttpsError("already-exists", "Users are already friends");
    }

    const chatQuery = await db
        .collection("chats")
        .where("type", "==", "private")
        .where("users", "array-contains", from)
        .get();

    const existingChat = chatQuery.docs.find((doc) =>
      doc.data().users.includes(to),
    );

    const reverseNotifQuery = await db
        .collection(`users/${from}/notifications`)
        .where("type", "==", "friendRequest")
        .where("from", "==", to)
        .get();

    // Perform all updates in a single transaction
    await db.runTransaction(async (transaction) => {
      // 1. Update notification with accepted=true instead of deleting
      transaction.update(notificationRef, {
        accepted: true,
        acceptedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      // 2. Remove from friendRequestsSent
      transaction.update(fromUserRef, {
        friendRequestsSent: admin.firestore.FieldValue.arrayRemove(to),
      });
      transaction.update(toUserRef, {
        friendRequestsSent: admin.firestore.FieldValue.arrayRemove(from),
      });

      // 3. Add to both users' friends arrays
      transaction.update(fromUserRef, {
        friends: admin.firestore.FieldValue.arrayUnion(to),
      });
      transaction.update(toUserRef, {
        friends: admin.firestore.FieldValue.arrayUnion(from),
      });

      // 4. Create chat room only if it doesn't exist
      if (!existingChat) {
        const chatRef = db.collection("chats").doc();
        transaction.set(chatRef, {
          type: "private",
          users: [from, to],
          unreadMessages: {
            [from]: 0,
            [to]: 0,
          },
          lastUnreadMessage: null,
          lastUnreadMessageDate: admin.firestore.FieldValue.serverTimestamp(),
        });
      }

      // Handle reverse notification if it exists
      if (!reverseNotifQuery.empty) {
        const reverseNotifRef = db
            .collection(`users/${from}/notifications`)
            .doc(reverseNotifQuery.docs[0].id);
        transaction.update(reverseNotifRef, {
          accepted: true,
          acceptedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      }
    });

    // Only after successful transaction, send notification
    if (
      fromUserData.notificationsToken &&
      Expo.isExpoPushToken(fromUserData.notificationsToken)
    ) {
      const message = {
        to: fromUserData.notificationsToken,
        sound: "default",
        title: fromUserData.language === "CZ" ? "Nový přítel" : "New friend",
        body:
          fromUserData.language === "CZ" ?
            `${toUserData.name} přijmul tvou žádost o přátelství` :
            `${toUserData.name} has accepted your friend request`,
        data: {type: "friendRequestAccepted", from: to},
      };

      const chunks = expo.chunkPushNotifications([message]);
      await expo.sendPushNotificationsAsync(chunks[0]);
    }

    return {
      success: true,
      message: "Friend request accepted successfully",
    };
  } catch (error) {
    console.error("Error in acceptFriendRequestV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while accepting the friend request",
    );
  }
});

// // const functions = require("firebase-functions");
// // const admin = require("firebase-admin");

// exports.createChatRoom = functions.https.onCall(async (data, context) => {
//   const {user1, user2} = data;

//   // Ensure both user IDs are provided
//   if (!user1 || !user2 || user1 === user2) {
//     throw new functions.https.HttpsError(
//         "invalid-argument",
//         "Both user IDs are required to create a chat room.",
//     );
//   }

//   const db = admin.firestore();
//   const chatCollection = db.collection("chats");

//   try {
//     // Check if a chat room already exists between these users
//     const existingChatQuery = chatCollection
//         .where("type", "==", "private")
//         .where("users", "array-contains", user1);

//     const existingChatSnapshot = await existingChatQuery.get();

//     const existingChat = existingChatSnapshot.docs.find((doc) =>
//       doc.data().users.includes(user2),
//     );

//     if (existingChat) {
//       // If a chat room already exists, return its ID
//       return {
//         result: "Chat room already exists",
//         chatID: existingChat.id,
//       };
//     }

//     // If no existing chat room, create a new one
//     const newChatRef = chatCollection.doc();

//     const newChatRoom = {
//       type: "private",
//       users: [user1, user2],
//       unreadMessages: {
//         [user1]: 0,
//         [user2]: 0,
//       },

//       lastUnreadMessage: null,
//       lastUnreadMessageDate: admin.firestore.FieldValue.serverTimestamp(),
//     };

//     await newChatRef.set(newChatRoom);

//     return {
//       result: "Chat room created successfully",
//       chatID: newChatRef.id,
//     };
//   } catch (error) {
//     console.error("Failed to create chat room:", error);
//     throw new functions.https.HttpsError(
//         "internal",
//         `Failed to create chat room: ${error}`,
//     );
//   }
// });

// exports.createGame = functions.https.onCall(async (data, context) => {
//   const {
//     eventName,
//     date,
//     timeStart,
//     timeEnd,
//     courtObj,
//     selectedRestrictionType,
//     ageLimitMin,
//     ageLimitMax,
//     limitOfUsers,
//     eventDescription,
//   } = data;

//   // Ensure the user is authenticated
//   if (!context.auth) {
//     throw new functions.https.HttpsError(
//         "unauthenticated",
//         "The function must be called while authenticated.",
//     );
//   }

//   const userId = context.auth.uid;
//   const db = admin.firestore();

//   try {
//     // Start a new transaction
//     return await db.runTransaction(async (transaction) => {
//       // Create the game document
//       const gamesCollection = db.collection("games");
//       const newGameRef = gamesCollection.doc();

//       const newGame = {
//         eventName: eventName,
//         date: admin.firestore.Timestamp.fromDate(new Date(date)),
//         timeStart: admin.firestore.Timestamp.fromDate(new Date(timeStart)),
//         timeEnd: admin.firestore.Timestamp.fromDate(new Date(timeEnd)),
//         courtObj: courtObj,
//         courtID: courtObj.id,
//         selectedRestrictionType: selectedRestrictionType,
//         ageLimitMin: ageLimitMin ? parseInt(ageLimitMin) : null,
//         ageLimitMax: ageLimitMax ? parseInt(ageLimitMax) : null,
//         creator: userId,
//         coords: new admin.firestore.GeoPoint(
//             courtObj.coords[1],
//             courtObj.coords[0],
//         ),
//         usersLimit: limitOfUsers ? parseInt(limitOfUsers) : null,
//         description: eventDescription,
//         isLive: new Date(timeStart) < new Date(),
//         eventID: newGameRef.id,
//         users: [userId],
//         going: [userId],
//         interested: [],
//       };

//       transaction.set(newGameRef, newGame);

//       // Create the associated chat
//       const chatsCollection = db.collection("chats");
//       const newChatRef = chatsCollection.doc();

//       const newChat = {
//         type: "game",
//         gameInfo: {
//           courtInfo: courtObj,
//           name: eventName,
//           date: date,
//           creator: userId,
//           usersLimit: limitOfUsers,
//           timeStart: timeStart,
//           timeEnd: timeEnd,
//         },
//         gameId: newGameRef.id,
//         users: [userId],
//         unreadMessages: {[userId]: 0},
//         lastUnreadMessage: null,
//         lastUnreadMessageDate: admin.firestore.FieldValue.serverTimestamp(),
//         createdAt: admin.firestore.FieldValue.serverTimestamp(),
//         updatedAt: admin.firestore.FieldValue.serverTimestamp(),
//       };

//       transaction.set(newChatRef, newChat);

//       // Update the game document with the chat ID
//       transaction.update(newGameRef, {chatId: newChatRef.id});

//       return {
//         result: "Game and chat created successfully",
//         gameId: newGameRef.id,
//         chatId: newChatRef.id,
//       };
//     });
//   } catch (error) {
//     console.error("Failed to create game and chat:", error);
//     throw new functions.https.HttpsError(
//         "internal",
//         `Failed to create game and chat: ${error}`,
//     );
//   }
// });

// OLD createTeam
// exports.createTeam = functions.https.onCall(async (data, context) => {
//   // Ensure the user is authenticated
//   if (!context.auth) {
//     throw new functions.https.HttpsError(
//         "unauthenticated",
//         "The function must be called while authenticated.",
//     );
//   }

//   const userId = context.auth.uid;
//   const db = admin.firestore();

//   // Function to generate a random gradient direction
//   const getRandomGradientDirection = () => {
//     const directions = ["topLeft", "topRight", "bottomLeft", "bottomRight"];
//     return directions[Math.floor(Math.random() * directions.length)];
//   };

//   try {
//     return await db.runTransaction(async (transaction) => {
//       // Create the team document
//       const teamsCollection = db.collection("teams");
//       const newTeamRef = teamsCollection.doc();
//       const newTeamId = newTeamRef.id;

//       const newTeam = {
//         name: data.teamName,
//         description: data.teamDescription,
//         imageUrl: data.teamImageUrl ?
//           `team_images/${newTeamId}/${Date.now()}.jpg` :
//           null,
//         color: data.teamColor,
//         gradientDirection: getRandomGradientDirection(),
//         locations: data.locations,
//         recruitingMessage: data.recruitingMessage,
//         skillLevels: data.skillLevels,
//         teamType: data.teamType,
//         ageGroups: data.ageGroups,
//         genderPolicy: data.genderPolicy,
//         createdAt: admin.firestore.FieldValue.serverTimestamp(),
//         createdBy: userId,
//         members: [userId],
//         admins: [userId],
//         isVisible: data.teamOpenness.isVisible,
//         allowJoinRequests: data.teamOpenness.allowJoinRequests,
//         allowChallenges: data.teamOpenness.allowChallenges,
//       };

//       transaction.set(newTeamRef, newTeam);

//       // Create the associated chat
//       const chatsCollection = db.collection("chats");
//       const newChatRef = chatsCollection.doc();

//       const newChat = {
//         type: "team",
//         teamInfo: {
//           name: data.teamName,
//           imageUrl: newTeam.imageUrl,
//           color: data.teamColor,
//           gradientDirection: newTeam.gradientDirection,
//         },
//         teamId: newTeamId,
//         users: [userId],
//         unreadMessages: {[userId]: 0},
//         lastUnreadMessage: null,
//         lastUnreadMessageDate: admin.firestore.FieldValue.serverTimestamp(),
//         createdAt: admin.firestore.FieldValue.serverTimestamp(),
//         updatedAt: admin.firestore.FieldValue.serverTimestamp(),
//       };

//       transaction.set(newChatRef, newChat);

//       // Update the team document with the chat ID
//       transaction.update(newTeamRef, {chatId: newChatRef.id});

//       return {
//         result: "Team and chat created successfully",
//         teamId: newTeamId,
//         chatId: newChatRef.id,
//         teamInfo: newChat.teamInfo, // Return teamInfo
//       };
//     });
//   } catch (error) {
//     console.error("Failed to create team and chat:", error);
//     throw new functions.https.HttpsError(
//         "internal",
//         `Failed to create team and chat: ${error}`,
//     );
//   }
// });

// NEW
exports.createTeamV2 = onCall(async (request) => {
  try {
    const data = request.data;
    const userId = request.auth.uid;

    // Input validation
    if (
      !data.teamName ||
      !data.teamType ||
      !data.ageGroups.length ||
      !data.skillLevels.length ||
      !data.genderPolicy
    ) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required team parameters",
      );
    }

    const getRandomGradientDirection = () => {
      const directions = ["topLeft", "topRight", "bottomLeft", "bottomRight"];
      return directions[Math.floor(Math.random() * directions.length)];
    };

    const firstLocation = data.locations[0];

    console.log(data, data.mainLocationLatitude, data.mainLocationLongitude);

    const coordinates =
      data.mainLocationLatitude && data.mainLocationLongitude ?
        new GeoPoint(data.mainLocationLatitude, data.mainLocationLongitude) :
        null;

    console.log(coordinates);

    try {
      return await db.runTransaction(async (transaction) => {
        // Create team document
        const teamsCollection = db.collection("teams");
        const newTeamRef = teamsCollection.doc();
        const newTeamId = newTeamRef.id;

        const newTeam = {
          name: data.teamName,
          description: data.teamDescription,
          imageUrl: data.teamImageUrl ?
            `team_images/${newTeamId}/${Date.now()}.jpg` :
            null,
          color: data.teamColor,
          gradientDirection: getRandomGradientDirection(),
          locations: data.locations,
          coordinates: coordinates,
          mainLocationId: firstLocation?.place_id || null,
          recruitingMessage: data.recruitingMessage,
          skillLevels: data.skillLevels,
          teamType: data.teamType,
          ageGroups: data.ageGroups,
          genderPolicy: data.genderPolicy,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          createdBy: userId,
          members: [userId],
          admins: [userId],
          isVisible: data.teamOpenness.isVisible,
          allowJoinRequests: data.teamOpenness.allowJoinRequests,
          allowChallenges: data.teamOpenness.allowChallenges,
          recurringTrainingSessions: [],
          schedulingSettings: {
            sessionReminderNotifications: true,
            weeksAhead: 2,
          },
        };

        transaction.set(newTeamRef, newTeam);

        // Create associated chat
        const chatsCollection = db.collection("chats");
        const newChatRef = chatsCollection.doc();

        const newChat = {
          type: "team",
          teamInfo: {
            name: data.teamName,
            imageUrl: newTeam.imageUrl,
            color: data.teamColor,
            gradientDirection: newTeam.gradientDirection,
          },
          teamId: newTeamId,
          users: [userId],
          unreadMessages: {[userId]: 0},
          lastUnreadMessage: null,
          lastUnreadMessageDate: admin.firestore.FieldValue.serverTimestamp(),
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        };

        transaction.set(newChatRef, newChat);
        transaction.update(newTeamRef, {chatId: newChatRef.id});

        return {
          success: true,
          result: "Team and chat created successfully",
          teamId: newTeamId,
          chatId: newChatRef.id,
          teamInfo: newChat.teamInfo,
        };
      });
    } catch (error) {
      console.error("Error in database operation:", error);
      throw new HttpsError("internal", "Failed to create team and chat");
    }
  } catch (error) {
    console.error("Error in createTeamV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while creating the team",
    );
  }
});

exports.sendTeamInviteV2 = onCall(async (request) => {
  try {
    const {teamId, invitedUserId} = request.data;
    const inviterId = request.auth.uid;

    // Validate input
    if (!teamId || !invitedUserId) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or invitedUserId",
      );
    }

    if (invitedUserId === inviterId) {
      throw new HttpsError(
          "invalid-argument",
          "Cannot invite yourself to a team",
      );
    }

    // Get all required data
    const teamRef = db.collection("teams").doc(teamId);
    const invitedUserRef = db.collection("users").doc(invitedUserId);
    const inviterRef = db.collection("users").doc(inviterId);

    const [teamSnapshot, invitedUserSnapshot, inviterSnapshot] =
      await Promise.all([
        teamRef.get(),
        invitedUserRef.get(),
        inviterRef.get(),
      ]);

    // Validate team exists
    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    // Validate users exist
    if (!invitedUserSnapshot.exists) {
      throw new HttpsError("not-found", "Invited user not found");
    }

    if (!inviterSnapshot.exists) {
      throw new HttpsError("not-found", "Inviter account not found");
    }

    const teamData = teamSnapshot.data();
    const invitedUserData = invitedUserSnapshot.data();
    const inviterData = inviterSnapshot.data();

    // Verify inviter is a team member
    if (!teamData.members.includes(inviterId)) {
      throw new HttpsError(
          "permission-denied",
          "Only team members can send invites",
      );
    }

    // Check if user is already a member
    if (teamData.members.includes(invitedUserId)) {
      throw new HttpsError("already-exists", "User is already a team member");
    }

    // Check for existing invitation
    const existingInviteQuery = await db
        .collection(`users/${invitedUserId}/notifications`)
        .where("type", "==", "teamInvite")
        .where("teamId", "==", teamId)
        .where("from", "==", inviterId)
        .get();

    if (!existingInviteQuery.empty) {
      throw new HttpsError(
          "already-exists",
          "Invitation already sent to this user",
      );
    }

    // Create notification in transaction
    await db.runTransaction(async (transaction) => {
      const notificationRef = db
          .collection(`users/${invitedUserId}/notifications`)
          .doc();

      transaction.set(notificationRef, {
        type: "teamInvite",
        teamId: teamId,
        from: inviterId,
        date: admin.firestore.FieldValue.serverTimestamp(),
        status: "pending",
        accepted: false,
        acceptedAt: null,
      });
    });

    // Send push notification if user has valid token
    if (
      invitedUserData.notificationsToken &&
      Expo.isExpoPushToken(invitedUserData.notificationsToken)
    ) {
      const message = {
        to: invitedUserData.notificationsToken,
        sound: "default",
        title:
          invitedUserData.language === "CZ" ?
            "Pozvánka do týmu" :
            "Team Invitation",
        body:
          invitedUserData.language === "CZ" ?
            `${inviterData.name} tě pozval do týmu ${teamData.name}` :
            `${inviterData.name} invited you to join team ${teamData.name}`,
        data: {
          type: "teamInvite",
          teamId,
          from: inviterId,
        },
      };

      const chunks = expo.chunkPushNotifications([message]);
      await expo.sendPushNotificationsAsync(chunks[0]);
    }

    return {
      success: true,
      message: "Team invitation sent successfully",
    };
  } catch (error) {
    console.error("Error in sendTeamInviteV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while sending team invitation",
    );
  }
});

// Updated respondToTeamInviteV2 function
exports.respondToTeamInviteV2 = onCall(async (request) => {
  try {
    const {accept, teamId, notificationId} = request.data;
    const userId = request.auth.uid;

    // Input validation
    if (!teamId || notificationId === undefined) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or notificationId",
      );
    }

    // Get notification and verify it exists
    const notifRef = db
        .collection(`users/${userId}/notifications`)
        .doc(notificationId);
    const notifDoc = await notifRef.get();

    if (!notifDoc.exists) {
      throw new HttpsError("not-found", "Notification not found");
    }

    const notifData = notifDoc.data();
    const inviterId = notifData.from;

    // Get team data
    const teamRef = db.collection("teams").doc(teamId);
    const teamDoc = await teamRef.get();

    if (!teamDoc.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamDoc.data();

    // Get responder data for notifications
    const userRef = db.collection("users").doc(userId);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found");
    }

    const responderName = userDoc.data().name;

    // Use transaction to ensure atomicity
    await db.runTransaction(async (transaction) => {
      // If accepting, add user to team and chat
      if (accept) {
        // Check if user is already a member
        if (teamData.members.includes(userId)) {
          throw new HttpsError(
              "already-exists",
              "User is already a team member",
          );
        }

        transaction.update(teamRef, {
          members: admin.firestore.FieldValue.arrayUnion(userId),
        });

        // Get chat reference and update it
        const chatRef = db.collection("chats").doc(teamData.chatId);

        // Create an object with the userId as key for unreadMessages
        const unreadMessagesUpdate = {};
        unreadMessagesUpdate[userId] = 0;

        transaction.update(chatRef, {
          users: admin.firestore.FieldValue.arrayUnion(userId),
          [`unreadMessages.${userId}`]: 0,
        });

        // Update notification instead of deleting it
        transaction.update(notifRef, {
          accepted: true,
          acceptedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
      } else {
        // If declining, delete the notification
        transaction.delete(notifRef);
      }
    });

    // Send notification to inviter
    const inviterRef = db.collection("users").doc(inviterId);
    const inviterDoc = await inviterRef.get();

    if (inviterDoc.exists) {
      const inviterData = inviterDoc.data();

      if (
        inviterData.notificationsToken &&
        Expo.isExpoPushToken(inviterData.notificationsToken)
      ) {
        const message = {
          to: inviterData.notificationsToken,
          sound: "default",
          title:
            inviterData.language === "CZ" ?
              "Odpověď na pozvánku" :
              "Invite Response",
          body:
            inviterData.language === "CZ" ?
              accept ?
                `${responderName} přijal pozvánku do týmu` :
                `${responderName} odmítl pozvánku do týmu` :
              accept ?
                `${responderName} accepted team invite` :
                `${responderName} declined team invite`,
          data: {
            type: "teamInviteResponse",
            teamId,
            accepted: accept,
            responderId: userId,
          },
        };

        const chunks = expo.chunkPushNotifications([message]);
        await expo.sendPushNotificationsAsync(chunks[0]);
      }
    }

    return {
      success: true,
      message: accept ? "Team invite accepted" : "Team invite declined",
    };
  } catch (error) {
    console.error("Error in respondToTeamInviteV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        `Failed to respond to team invite: ${error.message}`,
    );
  }
});

/* eslint-disable max-len */
/* eslint-disable valid-jsdoc */
/* eslint-disable require-jsdoc */
/* eslint-disable no-unused-vars */

// exports.createNextWeeksTrainingSessionsV2 = onSchedule(
//     {
//       schedule: "*/3 * * * *", // Every 3 minutes
//       timeZone: "Europe/Prague",
//       retryCount: 3,
//       memory: "256MiB",
//     },
//     async (event) => {
//       try {
//         console.log("Starting to create training sessions...");

//         // 1. Get all teams with recurring sessions
//         const teamsRef = db.collection("teams");
//         const teamsSnapshot = await teamsRef.get();

//         if (teamsSnapshot.empty) {
//           console.log("No teams found to process");
//           return {success: true, message: "No teams to process"};
//         }

//         console.log(`Found ${teamsSnapshot.size} teams to process`);

//         // 2. Calculate next Monday
//         const nextMonday = new Date();
//         nextMonday.setDate(nextMonday.getDate() + 7);
//         nextMonday.setHours(0, 0, 0, 0);

//         // 3. Process teams in batches
//         const batchSize = 10;
//         const batches = [];

//         for (let i = 0; i < teamsSnapshot.docs.length; i += batchSize) {
//           batches.push(teamsSnapshot.docs.slice(i, i + batchSize));
//         }

//         for (const batch of batches) {
//           await Promise.all(
//               batch.map(async (doc) => {
//                 const team = doc.data();
//                 const teamRef = db.collection("teams").doc(doc.id);

//                 if (!team.recurringTrainingSessions?.length) {
//                   console.log(`Team ${team.name} has no recurring sessions`);
//                   return;
//                 }

//                 console.log(`Processing team: ${team.name}`);

//                 // 4. Process each recurring session
//                 for (
//                   let index = 0;
//                   index < team.recurringTrainingSessions.length;
//                   index++
//                 ) {
//                   const recurringSession = team.recurringTrainingSessions[index];

//                   await Promise.all(
//                       recurringSession.days.map(async (day) => {
//                         try {
//                           // Calculate session date and times
//                           const sessionDate = new Date(nextMonday);
//                           sessionDate.setDate(sessionDate.getDate() + day);

//                           const startTime = new Date(
//                               recurringSession.startTime.seconds * 1000,
//                           );
//                           const endTime = new Date(
//                               recurringSession.endTime.seconds * 1000,
//                           );

//                           sessionDate.setHours(
//                               startTime.getHours(),
//                               startTime.getMinutes(),
//                               0,
//                               0,
//                           );
//                           const sessionEnd = new Date(sessionDate);
//                           sessionEnd.setHours(
//                               endTime.getHours(),
//                               endTime.getMinutes(),
//                               0,
//                               0,
//                           );

//                           // 5. Check for existing session
//                           const existingSession = await teamRef
//                               .collection("trainingSessions")
//                               .where("date", "==", sessionDate)
//                               .where("recurringSessionIndex", "==", index) // Using index instead of id
//                               .get();

//                           if (!existingSession.empty) {
//                             console.log(
//                                 `Session already exists for ${team.name} ` +
//                           `on ${sessionDate}`,
//                             );
//                             return;
//                           }

//                           // 6. Create new session
//                           const sessionData = {
//                             date: sessionDate,
//                             startTime: sessionDate,
//                             endTime: sessionEnd,
//                             location: recurringSession.location,
//                             description: recurringSession.description || "",
//                             recurringSessionIndex: index, // Store index instead of id
//                             goingUsers: [],
//                             name: recurringSession.name || "Training session",
//                             notGoingUsers: [],
//                             createdAt: admin.firestore.FieldValue.serverTimestamp(),
//                           };

//                           await teamRef
//                               .collection("trainingSessions")
//                               .add(sessionData);
//                           console.log(
//                               `Created training session for ${team.name} ` +
//                         `on ${sessionDate}`,
//                           );
//                         } catch (error) {
//                           console.error(
//                               `Error processing session for team ${team.name}:`,
//                               error,
//                               "\nRecurring session:",
//                               recurringSession,
//                           );
//                           throw new HttpsError(
//                               "internal",
//                               "Error processing training session",
//                           );
//                         }
//                       }),
//                   );
//                 }
//               }),
//           );
//         }

//         console.log("Successfully completed creating all training sessions");
//         return {
//           success: true,
//           message: "Training sessions created successfully",
//         };
//       } catch (error) {
//         console.error("Error in createNextWeeksTrainingSessionsV2:", error);
//         throw new HttpsError(
//             "internal",
//             "An unexpected error occurred while creating training sessions",
//         );
//       }
//     },
// );

// const weeksAhead = 2;

exports.createWeeklyTrainingSessionsV2 = onSchedule(
    {
      schedule: "0 0 * * 0", // Every Sunday at midnight
      // schedule: "*/5 * * * *", // For testing: every 5 minutes
      timeZone: "Europe/Prague",
      retryCount: 3,
      memory: "256MiB",
    },
    async (event) => {
      try {
        console.log("Starting weekly session creation...");

        // Get all teams
        const teamsRef = db.collection("teams");
        const teamsSnapshot = await teamsRef.get();

        if (teamsSnapshot.empty) {
          console.log("No teams found to process");
          return {success: true, message: "No teams to process"};
        }

        console.log(`Found ${teamsSnapshot.size} teams to process`);

        // Calculate base date boundaries - using today as the start point
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Normalize to start of today

        // Process teams in batches
        const TEAMS_BATCH_SIZE = 10;
        const SESSIONS_BATCH_SIZE = 500;
        const teamBatches = [];

        for (let i = 0; i < teamsSnapshot.docs.length; i += TEAMS_BATCH_SIZE) {
          teamBatches.push(teamsSnapshot.docs.slice(i, i + TEAMS_BATCH_SIZE));
        }

        let totalSessionsCreated = 0;

        for (const teamBatch of teamBatches) {
          await Promise.all(
              teamBatch.map(async (teamDoc) => {
                const team = teamDoc.data();
                const teamId = teamDoc.id;
                const teamRef = db.collection("teams").doc(teamId);

                if (!team.recurringTrainingSessions?.length) {
                  console.log(`Team ${team.name} has no recurring sessions`);
                  return;
                }

                // Get team-specific weeks ahead setting or use default
                const weeksAhead = team.schedulingSettings?.weeksAhead || 2;

                // Calculate end date based on team settings
                // Start from today and go forward weeksAhead weeks
                const startDate = new Date(today);
                const teamEndDate = new Date(today);
                teamEndDate.setDate(today.getDate() + weeksAhead * 7);

                console.log(`Processing team: ${team.name} (ID: ${teamId})`);
                console.log(
                    `Using weeksAhead: ${weeksAhead} (${weeksAhead * 7} days)`,
                );
                console.log(
                    `Date range: ${startDate.toISOString()} to ${teamEndDate.toISOString()}`,
                );

                // First, get all existing sessions in the date range to avoid duplicates
                const existingSessionsQuery = await teamRef
                    .collection("trainingSessions")
                    .where("date", ">=", startDate)
                    .where("date", "<=", teamEndDate)
                    .get();

                // Create a map of existing sessions for faster lookup
                const existingSessions = new Map();
                existingSessionsQuery.forEach((doc) => {
                  const session = doc.data();
                  const dateStr = session.date.toDate().toISOString().split("T")[0]; // Get YYYY-MM-DD
                  const key = `${dateStr}_${session.templateId}`;
                  existingSessions.set(key, true);
                });

                console.log(
                    `Found ${existingSessions.size} existing sessions in date range for team ${team.name}`,
                );

                let teamSessionsCreated = 0;

                // Process each template
                for (const template of team.recurringTrainingSessions) {
                  const sessionsToCreate = [];
                  const templateId = template.createdAt.toDate().getTime();

                  // For each day in the date range
                  for (
                    let currentDate = new Date(startDate);
                    currentDate < teamEndDate;
                    currentDate.setDate(currentDate.getDate() + 1)
                  ) {
                    // Get the day of week (0 = Monday in your system, based on template.days)
                    const currentDay = (currentDate.getDay() + 6) % 7; // Convert from JS (0=Sunday) to your system (0=Monday)

                    // Check if this day is in the template schedule
                    if (template.days.includes(currentDay)) {
                      // Create a new date object to avoid modifying the loop variable
                      const sessionDate = new Date(currentDate);

                      // Set correct times
                      const startTime = template.startTime.toDate();
                      const endTime = template.endTime.toDate();

                      sessionDate.setHours(
                          startTime.getHours(),
                          startTime.getMinutes(),
                          0,
                          0,
                      );
                      const sessionEnd = new Date(sessionDate);
                      sessionEnd.setHours(
                          endTime.getHours(),
                          endTime.getMinutes(),
                          0,
                          0,
                      );

                      // Check if session already exists using our map
                      const dateStr = sessionDate.toISOString().split("T")[0];
                      const sessionKey = `${dateStr}_${templateId}`;

                      if (existingSessions.has(sessionKey)) {
                        console.log(
                            `Session already exists for ${team.name} on ${sessionDate.toISOString()} (template: ${templateId})`,
                        );
                        continue;
                      }

                      // Prepare session data
                      const sessionData = {
                        teamId: teamId,
                        name: template.name,
                        date: sessionDate,
                        startTime: sessionDate,
                        endTime: sessionEnd,
                        location: template.location,
                        locationImage: template.locationImage || null,
                        description: template.description || "",
                        templateId: templateId,
                        goingUsers: [],
                        notGoingUsers: [],
                        createdAt: admin.firestore.FieldValue.serverTimestamp(),
                        // Default values for cancellation and restoration
                        isCancelled: false,
                        cancelReason: "",
                        cancelledBy: null,
                        cancelledAt: null,
                        restoredBy: null,
                        restoredAt: null,
                        reminderNotificationSent: false,
                      };

                      sessionsToCreate.push(sessionData);
                    }
                  }

                  // Write sessions in batches
                  for (
                    let i = 0;
                    i < sessionsToCreate.length;
                    i += SESSIONS_BATCH_SIZE
                  ) {
                    const batch = db.batch();
                    const sessionBatch = sessionsToCreate.slice(
                        i,
                        i + SESSIONS_BATCH_SIZE,
                    );

                    sessionBatch.forEach((sessionData) => {
                      const sessionRef = teamRef
                          .collection("trainingSessions")
                          .doc();
                      batch.set(sessionRef, sessionData);
                      teamSessionsCreated++;
                    });

                    try {
                      await batch.commit();
                      console.log(
                          `Created ${sessionBatch.length} sessions for team ${team.name}`,
                      );
                    } catch (error) {
                      console.error(
                          `Error creating session batch for team ${team.name}:`,
                          error,
                      );
                    }
                  }
                }

                console.log(
                    `Created total of ${teamSessionsCreated} sessions for team ${team.name}`,
                );
                totalSessionsCreated += teamSessionsCreated;
              }),
          );
        }

        console.log(
            `Successfully completed creating ${totalSessionsCreated} training sessions across all teams`,
        );
        return {
          success: true,
          message: "Training sessions created successfully",
          sessionsCreated: totalSessionsCreated,
        };
      } catch (error) {
        console.error("Error in createWeeklyTrainingSessionsV2:", error);
        throw new HttpsError(
            "internal",
            "An unexpected error occurred while creating training sessions",
        );
      }
    },
);

// Default number of weeks to generate

exports.createTrainingTemplateV2 = onCall(async (request) => {
  try {
    // 1. Input validation
    const {teamId, template} = request.data;
    const userId = request.auth.uid;

    if (!teamId || !template || !template.days || !template.days.length) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId, template, or template.days",
      );
    }

    // 2. Get team reference and verify permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamSnapshot = await teamRef.get();

    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamSnapshot.data();
    if (!teamData.members.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only team members can create training templates",
      );
    }

    const weeksAhead = teamData.schedulingSettings?.weeksAhead || 2;

    // 3. Check if template name already exists
    // const normalizedNewName = normalizeSessionName(template.name);
    // const nameExists = teamData.recurringTrainingSessions?.some(
    //     (session) => normalizeSessionName(session.name) === normalizedNewName,
    // );

    // if (nameExists) {
    //   throw new HttpsError(
    //       "already-exists",
    //       "A training template with this name already exists",
    //   );
    // }

    // 4. Create the template with Firestore timestamps
    const now = admin.firestore.Timestamp.now();
    const newTemplate = {
      ...template,
      startTime: admin.firestore.Timestamp.fromDate(
          new Date(template.startTime),
      ),
      endTime: admin.firestore.Timestamp.fromDate(new Date(template.endTime)),
      createdAt: now,
      createdBy: userId,
    };

    // 5. Add template to the team document
    await teamRef.update({
      recurringTrainingSessions:
        admin.firestore.FieldValue.arrayUnion(newTemplate),
    });

    // 6. Get the numeric templateId for session references
    const templateId = now.toDate().getTime();

    // 7. Generate sessions for the next few weeks
    const startDate = new Date();
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + weeksAhead * 7);

    const newSessions = [];

    // For each day in the template, generate future sessions
    for (const day of template.days) {
      // Calculate the start of the current week (Monday)
      const currentWeekStart = new Date(startDate);
      const daysSinceMonday = (currentWeekStart.getDay() + 6) % 7; // Days since last Monday (0=Monday)
      currentWeekStart.setDate(currentWeekStart.getDate() - daysSinceMonday); // Go back to Monday

      // Now generate sessions for each week in the range
      for (
        let weekStart = new Date(currentWeekStart);
        weekStart < endDate;
        weekStart.setDate(weekStart.getDate() + 7)
      ) {
        const sessionDate = new Date(weekStart);
        sessionDate.setDate(sessionDate.getDate() + day);

        // Skip dates in the past
        if (sessionDate < startDate) {
          continue;
        }

        // Set the session times based on template times
        const templateStartTime = newTemplate.startTime.toDate();
        const templateEndTime = newTemplate.endTime.toDate();

        sessionDate.setHours(
            templateStartTime.getHours(),
            templateStartTime.getMinutes(),
            0,
            0,
        );

        const sessionEnd = new Date(sessionDate);
        sessionEnd.setHours(
            templateEndTime.getHours(),
            templateEndTime.getMinutes(),
            0,
            0,
        );

        // Create session object
        newSessions.push({
          teamId: teamId,
          name: template.name,
          date: sessionDate,
          startTime: sessionDate,
          endTime: sessionEnd,
          location: template.location,
          locationImage: template.locationImage || null,
          description: template.description || "",
          templateId: templateId,
          goingUsers: [],
          notGoingUsers: [],
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          // Default values for cancellation and restoration
          isCancelled: false,
          cancelReason: "",
          cancelledBy: null,
          cancelledAt: null,
          restoredBy: null,
          restoredAt: null,
          reminderNotificationSent: false,
        });
      }
    }

    // 8. Write sessions in batches
    const batchSize = 500;
    let totalCreated = 0;

    for (let i = 0; i < newSessions.length; i += batchSize) {
      const batch = db.batch();
      const sessionBatch = newSessions.slice(i, i + batchSize);

      sessionBatch.forEach((sessionData) => {
        const sessionRef = teamRef.collection("trainingSessions").doc();
        batch.set(sessionRef, sessionData);
        totalCreated++;
      });

      await batch.commit();
    }

    console.log(
        `Created template and ${totalCreated} sessions for team ${teamId}`,
    );

    return {
      success: true,
      message: "Template and sessions created successfully",
      templateId: templateId.toString(),
      stats: {
        created: totalCreated,
      },
    };
  } catch (error) {
    console.error("Error in createTrainingTemplate:", error);
    throw new HttpsError(
        "internal",
        `Failed to create training template: ${error.message}`,
    );
  }
});

exports.updateTrainingTemplateV2 = onCall(async (request) => {
  try {
    // 1. Input validation
    const {teamId, templateId, updatedTemplate} = request.data;
    const userId = request.auth.uid;

    if (!teamId || !templateId || !updatedTemplate) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId, templateId, or updatedTemplate",
      );
    }

    // 2. Get team reference and verify permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamSnapshot = await teamRef.get();

    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamSnapshot.data();
    if (!teamData.admins.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only team admins can modify training templates",
      );
    }

    const weeksAhead = teamData.schedulingSettings?.weeksAhead || 2;

    // 3. Find the original template
    const originalTemplate = teamData.recurringTrainingSessions?.find(
        (template) =>
          template.createdAt.toDate().getTime().toString() === templateId,
    );

    if (!originalTemplate) {
      throw new HttpsError("not-found", "Training template not found");
    }

    // 4. Prepare updated template with original creation timestamp
    const finalTemplate = {
      ...updatedTemplate,
      createdBy: originalTemplate.createdBy, // Preserve original creator
      createdAt: originalTemplate.createdAt, // Preserve original timestamp
      // Convert time objects to Firestore timestamps if they're not already
      startTime: updatedTemplate.startTime.seconds ?
        updatedTemplate.startTime :
        admin.firestore.Timestamp.fromDate(
            new Date(updatedTemplate.startTime),
        ),
      endTime: updatedTemplate.endTime.seconds ?
        updatedTemplate.endTime :
        admin.firestore.Timestamp.fromDate(new Date(updatedTemplate.endTime)),
    };

    // 5. Check what changed
    const daysChanged = !arraysEqual(originalTemplate.days, finalTemplate.days);
    const timeChanged = hasTimeChanged(originalTemplate, finalTemplate);
    const detailsChanged =
      originalTemplate.name !== finalTemplate.name ||
      originalTemplate.description !== finalTemplate.description ||
      originalTemplate.location !== finalTemplate.location ||
      originalTemplate.locationImage !== finalTemplate.locationImage;

    // 6. Begin transaction to update template
    await db.runTransaction(async (transaction) => {
      // Remove old template and add new one
      transaction.update(teamRef, {
        recurringTrainingSessions:
          admin.firestore.FieldValue.arrayRemove(originalTemplate),
      });

      transaction.update(teamRef, {
        recurringTrainingSessions:
          admin.firestore.FieldValue.arrayUnion(finalTemplate),
      });
    });

    // If no session modifications are needed, return early
    if (!detailsChanged && !daysChanged && !timeChanged) {
      return {
        success: true,
        message: "Template updated successfully",
        stats: {updated: 0, deleted: 0, created: 0},
      };
    }

    // 7. Update existing sessions
    const currentDate = new Date();
    const numericTemplateId = originalTemplate.createdAt.toDate().getTime();

    // Get affected future sessions
    const sessionsSnapshot = await teamRef
        .collection("trainingSessions")
        .where("templateId", "==", numericTemplateId)
        .where("date", ">=", currentDate)
        .get();

    console.log(`Found ${sessionsSnapshot.size} future sessions to update`);

    // 8. Process sessions in batches
    const batchSize = 500;
    let totalUpdated = 0;
    let totalDeleted = 0;
    let totalCreated = 0;

    // Update or delete existing sessions
    for (let i = 0; i < sessionsSnapshot.docs.length; i += batchSize) {
      const batch = db.batch();
      const sessionBatch = sessionsSnapshot.docs.slice(i, i + batchSize);

      for (const sessionDoc of sessionBatch) {
        const session = sessionDoc.data();

        // Get day of week for this session
        const sessionDate = session.date.toDate();
        const dayOfWeek = getDayOfWeek(sessionDate);

        // Delete sessions for removed days
        if (!finalTemplate.days.includes(dayOfWeek)) {
          batch.delete(sessionDoc.ref);
          totalDeleted++;
          continue;
        }

        // Update sessions that remain
        const updates = {
          name: finalTemplate.name,
          description: finalTemplate.description || "",
          location: finalTemplate.location,
          locationImage: finalTemplate.locationImage || null,
        };

        // Update time if changed
        if (timeChanged) {
          const sessionDate = session.date.toDate();
          const newStartTime = finalTemplate.startTime.toDate();
          const newEndTime = finalTemplate.endTime.toDate();

          // Create new dates with updated times
          const newSessionStart = new Date(sessionDate);
          newSessionStart.setHours(
              newStartTime.getHours(),
              newStartTime.getMinutes(),
              0,
              0,
          );

          const newSessionEnd = new Date(sessionDate);
          newSessionEnd.setHours(
              newEndTime.getHours(),
              newEndTime.getMinutes(),
              0,
              0,
          );

          updates.startTime = newSessionStart;
          updates.endTime = newSessionEnd;
        }

        batch.update(sessionDoc.ref, updates);
        totalUpdated++;
      }

      await batch.commit();
    }

    // 9. Create sessions for new days
    if (daysChanged) {
      // Find added days
      const addedDays = finalTemplate.days.filter(
          (day) => !originalTemplate.days.includes(day),
      );

      if (addedDays.length > 0) {
        // Date boundaries for new sessions
        const startDate = new Date();
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + weeksAhead * 7);

        // Generate new sessions
        const newSessions = [];

        for (const day of addedDays) {
          // Calculate the start of the current week (Monday)
          const currentWeekStart = new Date(startDate);
          const daysSinceMonday = (currentWeekStart.getDay() + 6) % 7; // Days since last Monday (0=Monday)
          currentWeekStart.setDate(
              currentWeekStart.getDate() - daysSinceMonday,
          ); // Go back to Monday

          // Now generate sessions for each week in the range
          for (
            let weekStart = new Date(currentWeekStart);
            weekStart < endDate;
            weekStart.setDate(weekStart.getDate() + 7)
          ) {
            const sessionDate = new Date(weekStart);
            sessionDate.setDate(sessionDate.getDate() + day);

            if (sessionDate < startDate) {
              continue;
            }

            // Set times
            const templateStartTime = finalTemplate.startTime.toDate();
            const templateEndTime = finalTemplate.endTime.toDate();

            sessionDate.setHours(
                templateStartTime.getHours(),
                templateStartTime.getMinutes(),
                0,
                0,
            );

            const sessionEnd = new Date(sessionDate);
            sessionEnd.setHours(
                templateEndTime.getHours(),
                templateEndTime.getMinutes(),
                0,
                0,
            );

            // Session data
            newSessions.push({
              teamId: teamId,
              name: finalTemplate.name,
              date: sessionDate,
              startTime: sessionDate,
              endTime: sessionEnd,
              location: finalTemplate.location,
              locationImage: finalTemplate.locationImage || null,
              description: finalTemplate.description || "",
              templateId: numericTemplateId,
              goingUsers: [],
              notGoingUsers: [],
              createdAt: admin.firestore.FieldValue.serverTimestamp(),
              // Default values for cancellation and restoration
              isCancelled: false,
              cancelReason: "",
              cancelledBy: null,
              cancelledAt: null,
              restoredBy: null,
              restoredAt: null,
              reminderNotificationSent: false,
            });
          }
        }

        // Write new sessions in batches
        for (let i = 0; i < newSessions.length; i += batchSize) {
          const batch = db.batch();
          const sessionBatch = newSessions.slice(i, i + batchSize);

          sessionBatch.forEach((sessionData) => {
            const sessionRef = teamRef.collection("trainingSessions").doc();
            batch.set(sessionRef, sessionData);
            totalCreated++;
          });

          await batch.commit();
        }
      }
    }

    return {
      success: true,
      message: "Template and sessions updated successfully",
      stats: {
        updated: totalUpdated,
        deleted: totalDeleted,
        created: totalCreated,
      },
    };
  } catch (error) {
    console.error("Error in updateTrainingTemplate:", error);
    throw new HttpsError(
        "internal",
        `Failed to update training template: ${error.message}`,
    );
  }
});

exports.deleteTrainingTemplateV2 = onCall(async (request) => {
  try {
    // 1. Input validation
    const {teamId, templateId} = request.data;
    const userId = request.auth.uid;

    if (!teamId || !templateId) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or templateId",
      );
    }

    // 2. Get team reference and verify permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamSnapshot = await teamRef.get();

    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamSnapshot.data();

    // Check if user has permission
    if (
      !teamData.admins.includes(userId) &&
      !teamData.members.includes(userId)
    ) {
      throw new HttpsError(
          "permission-denied",
          "Only team members can delete training templates",
      );
    }

    // 3. Find the template to delete
    const templateToDelete = teamData.recurringTrainingSessions?.find(
        (template) =>
          template.createdAt.toDate().getTime().toString() === templateId,
    );

    if (!templateToDelete) {
      throw new HttpsError("not-found", "Training template not found");
    }

    // 4. Delete template in transaction
    await db.runTransaction(async (transaction) => {
      // Remove template from team document
      transaction.update(teamRef, {
        recurringTrainingSessions:
          admin.firestore.FieldValue.arrayRemove(templateToDelete),
      });
    });

    // 5. Delete all related sessions
    const numericTemplateId = parseInt(templateId);

    // Query all sessions with this templateId
    const sessionsQuery = await teamRef
        .collection("trainingSessions")
        .where("templateId", "==", numericTemplateId)
        .get();

    // Delete sessions in batches
    const batchSize = 500;
    let totalDeleted = 0;

    for (let i = 0; i < sessionsQuery.docs.length; i += batchSize) {
      const batch = db.batch();
      const sessionBatch = sessionsQuery.docs.slice(i, i + batchSize);

      sessionBatch.forEach((doc) => {
        batch.delete(doc.ref);
        totalDeleted++;
      });

      await batch.commit();
    }

    console.log(
        `Deleted template and ${totalDeleted} sessions for team ${teamId}`,
    );

    return {
      success: true,
      message: "Template and sessions deleted successfully",
      stats: {
        deletedSessions: totalDeleted,
      },
    };
  } catch (error) {
    console.error("Error in deleteTrainingTemplate:", error);
    throw new HttpsError(
        "internal",
        `Failed to delete training template: ${error.message}`,
    );
  }
});

exports.cancelTrainingSession = onCall(async (request) => {
  try {
    const {teamId, sessionId, reason} = request.data;
    const userId = request.auth.uid;

    // 1. Verify permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamSnapshot = await teamRef.get();

    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamSnapshot.data();
    if (!teamData.admins.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only admins can cancel sessions",
      );
    }

    // 2. Get session data to identify affected users
    const sessionRef = teamRef.collection("trainingSessions").doc(sessionId);
    const sessionSnapshot = await sessionRef.get();

    if (!sessionSnapshot.exists) {
      throw new HttpsError("not-found", "Session not found");
    }

    const sessionData = sessionSnapshot.data();

    // 3. Update the session
    await sessionRef.update({
      isCancelled: true,
      cancelReason: reason || "",
      cancelledBy: userId,
      cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
      restoredBy: null,
      restoredAt: null,
    });

    // 4. Notify all team members except the canceller
    const affectedUsers = teamData.members.filter(
        (memberId) => memberId !== userId,
    );

    await notifyUsersAboutCancellation(
        affectedUsers,
        teamData,
        sessionData,
        reason,
        teamId,
        sessionId,
    );

    return {success: true};
  } catch (error) {
    console.error("Error in cancelTrainingSession:", error);
    return {success: false, error: error.message};
  }
});

// Helper function to send notifications
async function notifyUsersAboutCancellation(
    userIds,
    team,
    session,
    reason,
    teamId,
    sessionId,
) {
  // Implementation for sending push notifications to users

  console.log(`Sending cancellation notifications to ${userIds.length} users`);

  const userPromises = userIds.map(async (userId) => {
    // Get user data
    const userSnapshot = await db.collection("users").doc(userId).get();
    if (!userSnapshot.exists) {
      console.log(`User ${userId} not found, skipping notification`);
      return;
    }

    const userData = userSnapshot.data();
    if (!userData.notificationsToken) {
      console.log(`User ${userId} has no notification token, skipping`);
      return;
    }

    // Create notification message
    const message = {
      to: userData.notificationsToken,
      title:
        userData.language === "CZ" ? "Zrušený trénink" : "Training Cancelled",
      body:
        userData.language === "CZ" ?
          `${session.name} byl zrušen.${reason ? ` Důvod: ${reason}` : ""}` :
          `${session.name} has been cancelled.${reason ? ` Reason: ${reason}` : ""}`,
      data: {
        type: "sessionCancelled",
        teamId: teamId,
        sessionId: sessionId,
      },
    };

    // Send notification
    console.log(`Sending cancellation notification to user ${userId}`);
    return expo.sendPushNotificationsAsync([message]);
  });

  await Promise.all(userPromises);
}

exports.restoreTrainingSession = onCall(async (request) => {
  try {
    const {teamId, sessionId} = request.data;
    const userId = request.auth.uid;

    // 1. Verify permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamSnapshot = await teamRef.get();

    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamSnapshot.data();
    if (!teamData.admins.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only admins can restore sessions",
      );
    }

    // 2. Get session data
    const sessionRef = teamRef.collection("trainingSessions").doc(sessionId);
    const sessionSnapshot = await sessionRef.get();

    if (!sessionSnapshot.exists) {
      throw new HttpsError("not-found", "Session not found");
    }

    const sessionData = sessionSnapshot.data();

    // 3. Update the session - removing cancellation fields
    await sessionRef.update({
      isCancelled: false,
      cancelReason: "",
      cancelledBy: null,
      cancelledAt: null,
      restoredBy: userId,
      restoredAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // 4. Notify all team members except the restorer
    const affectedUsers = teamData.members.filter(
        (memberId) => memberId !== userId,
    );

    await notifyUsersAboutRestoration(
        affectedUsers,
        teamData,
        sessionData,
        teamId,
        sessionId,
    );

    return {success: true};
  } catch (error) {
    console.error("Error in restoreTrainingSession:", error);
    return {success: false, error: error.message};
  }
});

// Helper function to send notifications about session restoration
async function notifyUsersAboutRestoration(
    userIds,
    team,
    session,
    teamId,
    sessionId,
) {
  // console.log(`Sending restoration notifications to ${userIds.length} users`);

  const userPromises = userIds.map(async (userId) => {
    // Get user data
    const userSnapshot = await db.collection("users").doc(userId).get();
    if (!userSnapshot.exists) {
      console.log(`User ${userId} not found, skipping notification`);
      return;
    }

    const userData = userSnapshot.data();
    if (!userData.notificationsToken) {
      console.log(`User ${userId} has no notification token, skipping`);
      return;
    }

    // Create notification message
    const message = {
      to: userData.notificationsToken,
      title:
        userData.language === "CZ" ? "Obnovený trénink" : "Training Restored",
      body:
        userData.language === "CZ" ?
          `${session.name} byl obnoven.` :
          `${session.name} has been restored.`,
      data: {
        type: "sessionRestored",
        teamId: teamId,
        sessionId: sessionId,
      },
    };

    // Send notification
    console.log(`Sending restoration notification to user ${userId}`);
    return expo.sendPushNotificationsAsync([message]);
  });

  await Promise.all(userPromises);
}

function arraysEqual(a, b) {
  if (a === b) return true;
  if (a == null || b == null) return false;
  if (a.length !== b.length) return false;

  for (let i = 0; i < a.length; i++) {
    if (a[i] !== b[i]) return false;
  }
  return true;
}

function hasTimeChanged(oldTemplate, newTemplate) {
  const oldStart = oldTemplate.startTime.toDate().getTime();
  const oldEnd = oldTemplate.endTime.toDate().getTime();
  const newStart = newTemplate.startTime.toDate().getTime();
  const newEnd = newTemplate.endTime.toDate().getTime();

  return oldStart !== newStart || oldEnd !== newEnd;
}

function normalizeSessionName(name) {
  return name.trim().toLowerCase().replace(/\s+/g, " ");
}

function getNextMonday() {
  const date = new Date();
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? 1 : 8);
  date.setDate(diff);
  date.setHours(0, 0, 0, 0);
  return date;
}

function getDayOfWeek(date) {
  const day = date.getDay();
  // Convert from Sunday-Saturday (0-6) to Monday-Sunday (0-6)
  return day === 0 ? 6 : day - 1;
}

exports.migrateCompletedTrainingSessions = onSchedule(
    {
      schedule: "59 23 * * *",
      timeZone: "Europe/Prague",
      retryCount: 3,
      memory: "256MiB",
    },
    async (event) => {
      try {
        console.log("Starting migration of completed training sessions...");

        const teamsRef = db.collection("teams");
        const teamsSnapshot = await teamsRef.get();

        if (teamsSnapshot.empty) {
          console.log("No teams found to process");
          return {success: true, message: "No teams to process"};
        }

        console.log(`Found ${teamsSnapshot.size} teams to process`);

        // Process teams in batches
        const batchSize = 10;
        const batches = [];

        for (let i = 0; i < teamsSnapshot.docs.length; i += batchSize) {
          batches.push(teamsSnapshot.docs.slice(i, i + batchSize));
        }

        for (const batch of batches) {
          await Promise.all(
              batch.map(async (teamDoc) => {
                const teamRef = teamsRef.doc(teamDoc.id);
                const currentTime = new Date();

                // Query for completed sessions
                const completedSessionsQuery = await teamRef
                    .collection("trainingSessions")
                    .where("endTime", "<", currentTime)
                    .get();

                if (completedSessionsQuery.empty) {
                  console.log(
                      `No completed sessions for team ${teamDoc.data().name}`,
                  );
                  return;
                }

                // Process each completed session in a transaction
                await Promise.all(
                    completedSessionsQuery.docs.map(async (sessionDoc) => {
                      await db.runTransaction(async (transaction) => {
                        // Create reference to history document
                        const historyRef = teamRef
                            .collection("trainingSessionsHistory")
                            .doc(sessionDoc.id);

                        // Check if already migrated
                        const historyDoc = await transaction.get(historyRef);
                        if (historyDoc.exists) {
                          console.log(
                              `Session ${sessionDoc.id} already migrated for team ${
                                teamDoc.data().name
                              }`,
                          );
                          return;
                        }

                        // Add to history collection with additional metadata
                        transaction.set(historyRef, {
                          ...sessionDoc.data(),
                          migratedAt: admin.firestore.FieldValue.serverTimestamp(),
                          originalSessionId: sessionDoc.id,
                        });

                        // Delete from active sessions
                        transaction.delete(sessionDoc.ref);

                        console.log(
                            `Successfully migrated session ${sessionDoc.id} for team ${
                              teamDoc.data().name
                            }`,
                        );
                      });
                    }),
                );
              }),
          );
        }

        console.log("Successfully completed migration of all completed sessions");
        return {
          success: true,
          message: "Training sessions migration completed successfully",
        };
      } catch (error) {
        console.error("Error in migrateCompletedTrainingSessions:", error);
        throw new HttpsError(
            "internal",
            "An unexpected error occurred while migrating training sessions",
        );
      }
    },
);

exports.sendSessionReminders = onSchedule(
    {
      schedule: "0 * * * *", // Run every hour
      // schedule: "*/3 * * * *",
      timeZone: "Europe/Prague",
      retryCount: 3,
      memory: "256MiB",
    },
    async (event) => {
      try {
        console.log("Starting session reminder notifications...");

        // Current time
        const now = new Date();

        // Look ahead 6 hours for upcoming sessions
        const reminderWindowStart = now;
        const reminderWindowEnd = new Date(now);
        reminderWindowEnd.setHours(now.getHours() + 4);

        console.log(
            `Checking for sessions between ${reminderWindowStart.toISOString()} and ${reminderWindowEnd.toISOString()}`,
        );

        // Get all teams
        const teamsRef = db.collection("teams");
        const teamsSnapshot = await teamsRef.get();

        // Track sent notification count
        let remindersSent = 0;

        // Process each team
        for (const teamDoc of teamsSnapshot.docs) {
          const team = teamDoc.data();
          const teamId = teamDoc.id;

          // Skip teams that have disabled session reminders
          if (team.schedulingSettings?.sessionReminderNotifications === false) {
            console.log(`Team ${team.name} has disabled session reminders`);
            continue;
          }

          console.log(`Processing team ${team.name}`);

          // Query upcoming sessions in the next 6 hours that haven't been notified yet
          const sessionsRef = db
              .collection("teams")
              .doc(teamId)
              .collection("trainingSessions");
          const upcomingSessionsQuery = sessionsRef
              .where("startTime", ">=", reminderWindowStart)
              .where("startTime", "<=", reminderWindowEnd)
              .where("isCancelled", "==", false)
              .where("reminderNotificationSent", "==", false); // Only get sessions that haven't been notified

          const upcomingSessionsSnapshot = await upcomingSessionsQuery.get();

          if (upcomingSessionsSnapshot.empty) {
            continue; // No upcoming sessions for this team
          }

          console.log(
              `Found ${upcomingSessionsSnapshot.size} upcoming sessions for team ${team.name}`,
          );

          // Process each session
          for (const sessionDoc of upcomingSessionsSnapshot.docs) {
            const session = sessionDoc.data();
            const sessionRef = sessionDoc.ref;

            // Get all team members who haven't responded yet
            const pendingMembers = team.members.filter(
                (memberId) =>
                  !session.goingUsers?.includes(memberId) &&
              !session.notGoingUsers?.includes(memberId),
            );

            if (pendingMembers.length === 0) {
            // All members have responded, mark as notified and continue
              await sessionRef.update({reminderNotificationSent: true});
              continue;
            }

            console.log(
                `Sending reminders for session "${session.name}" to ${pendingMembers.length} members`,
            );

            // Format session time for notification
            const sessionDate = session.startTime.toDate();
            const isToday =
            sessionDate.getDate() === now.getDate() &&
            sessionDate.getMonth() === now.getMonth() &&
            sessionDate.getFullYear() === now.getFullYear();

            // Use 24-hour format
            const sessionTime = sessionDate.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
              hour12: false,
            });

            // For each pending member
            for (const memberId of pendingMembers) {
              try {
              // Get user's data including push token and language preference
                const userDoc = await db.collection("users").doc(memberId).get();
                const userData = userDoc.data();
                const expoPushToken = userData.notificationsToken;
                const userLanguage = userData.language || "EN"; // Default to English

                if (!expoPushToken || !Expo.isExpoPushToken(expoPushToken)) {
                  console.log(`User ${memberId} doesn't have a valid push token`);
                  continue;
                }

                // Format date string based on language and whether it's today
                let sessionDateStr;
                if (userLanguage === "CZ") {
                  if (isToday) {
                    sessionDateStr = "Dnes";
                  } else {
                  // Czech month names
                    const czechMonths = [
                      "led",
                      "úno",
                      "bře",
                      "dub",
                      "kvě",
                      "čvn",
                      "čvc",
                      "srp",
                      "zář",
                      "říj",
                      "lis",
                      "pro",
                    ];
                    sessionDateStr = `${sessionDate.getDate()}. ${czechMonths[sessionDate.getMonth()]}`;
                  }
                } else {
                  if (isToday) {
                    sessionDateStr = "Today";
                  } else {
                  // English month names
                    const englishMonths = [
                      "Jan",
                      "Feb",
                      "Mar",
                      "Apr",
                      "May",
                      "Jun",
                      "Jul",
                      "Aug",
                      "Sep",
                      "Oct",
                      "Nov",
                      "Dec",
                    ];
                    sessionDateStr = `${englishMonths[sessionDate.getMonth()]} ${sessionDate.getDate()}`;
                  }
                }

                // Create language-specific notification content
                let title;
                let body;

                if (userLanguage === "CZ") {
                  title = `${team.name}: ${session.name}`;
                  body = `${sessionDateStr} v ${sessionTime}. Jdeš?`;
                } else {
                  title = `${team.name}: ${session.name}`;
                  body = `${sessionDateStr} at ${sessionTime}. Are you going?`;
                }

                // Send push notification
                const message = {
                  to: expoPushToken,
                  sound: "default",
                  title: title,
                  body: body,
                  data: {
                    type: "sessionReminder",
                    teamId: teamId,
                    sessionId: sessionDoc.id,
                    teamName: team.name,
                    sessionName: session.name,
                    location: session.location,
                    time: sessionTime,
                    date: sessionDate.toISOString(),
                  },
                  badge: 1,
                };

                // Use Expo's push notification service
                const response = await fetch(
                    "https://exp.host/--/api/v2/push/send",
                    {
                      method: "POST",
                      headers: {
                        "Accept": "application/json",
                        "Accept-Encoding": "gzip, deflate",
                        "Content-Type": "application/json",
                      },
                      body: JSON.stringify(message),
                    },
                );

                if (!response.ok) {
                  const errorData = await response.json();
                  console.error(
                      `Error sending push notification to user ${memberId}:`,
                      errorData,
                  );
                  continue;
                }

                remindersSent++;
              } catch (error) {
                console.error(
                    `Error processing reminder for user ${memberId}:`,
                    error,
                );
              }
            }

            // Mark this session as notified regardless of individual notification success
            await sessionRef.update({reminderNotificationSent: true});
            console.log(`Marked session ${sessionDoc.id} as notified`);
          }
        }

        console.log(`Successfully sent ${remindersSent} session reminders`);
        return {
          success: true,
          remindersSent: remindersSent,
        };
      } catch (error) {
        console.error("Error sending session reminders:", error);
        throw new HttpsError(
            "internal",
            "An error occurred while sending session reminders",
        );
      }
    },
);

// Cloud function: leaveTeam
exports.leaveTeam = onCall(async (request) => {
  try {
    const {teamId, teamName} = request.data;
    const leavingUserId = request.auth.uid;

    // Validate input
    if (!teamId || !teamName) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or teamName",
      );
    }

    // Get team and user data
    const teamRef = db.collection("teams").doc(teamId);
    const leavingUserRef = db.collection("users").doc(leavingUserId);

    const [teamSnapshot, leavingUserSnapshot] = await Promise.all([
      teamRef.get(),
      leavingUserRef.get(),
    ]);

    // Validate team exists
    if (!teamSnapshot.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    // Validate user exists
    if (!leavingUserSnapshot.exists) {
      throw new HttpsError("not-found", "User not found");
    }

    const teamData = teamSnapshot.data();
    const leavingUserData = leavingUserSnapshot.data();
    const creatorId = teamData.createdBy;
    const chatId = teamData.chatId;

    // If this user is the creator, don't allow leaving (they should delete the team instead)
    if (leavingUserId === creatorId) {
      throw new HttpsError(
          "failed-precondition",
          "Team creator cannot leave the team, please delete the team instead",
      );
    }

    // Start a batch write
    const batch = db.batch();

    // Remove user from team members
    batch.update(teamRef, {
      members: admin.firestore.FieldValue.arrayRemove(leavingUserId),
    });

    // If the team has a chat, remove the user from the chat participants
    if (chatId) {
      const chatRef = db.collection("chats").doc(chatId);
      const chatSnapshot = await chatRef.get();

      if (chatSnapshot.exists) {
        // Remove user from chat users array
        batch.update(chatRef, {
          users: admin.firestore.FieldValue.arrayRemove(leavingUserId),
        });

        // Reset unread count for this user
        const unreadMessagesUpdate = {};
        unreadMessagesUpdate[`unreadMessages.${leavingUserId}`] = 0;
        batch.update(chatRef, unreadMessagesUpdate);
      }
    }

    // Fetch all active training sessions for this team
    const trainingSessionsRef = db.collection(
        `teams/${teamId}/trainingSessions`,
    );
    const trainingSessionsSnapshot = await trainingSessionsRef.get();

    // Remove user from goingUsers and notGoingUsers arrays in all active training sessions
    trainingSessionsSnapshot.forEach((sessionDoc) => {
      batch.update(sessionDoc.ref, {
        goingUsers: admin.firestore.FieldValue.arrayRemove(leavingUserId),
        notGoingUsers: admin.firestore.FieldValue.arrayRemove(leavingUserId),
      });
    });

    // Fetch all historical training sessions for this team
    const historySessionsRef = db.collection(
        `teams/${teamId}/trainingSessionsHistory`,
    );
    const historySessionsSnapshot = await historySessionsRef.get();

    // Remove user from goingUsers and notGoingUsers arrays in all historical sessions
    historySessionsSnapshot.forEach((sessionDoc) => {
      batch.update(sessionDoc.ref, {
        goingUsers: admin.firestore.FieldValue.arrayRemove(leavingUserId),
        notGoingUsers: admin.firestore.FieldValue.arrayRemove(leavingUserId),
      });
    });

    // Create a team notification about the member leaving
    const teamNotificationsRef = db
        .collection(`teams/${teamId}/notifications`)
        .doc();
    batch.set(teamNotificationsRef, {
      type: "teamMemberLeft",
      memberId: leavingUserId,
      memberName: leavingUserData.name,
      memberProfileImage: leavingUserData.profileImageRef || null,
      memberProfileColor: leavingUserData.profileColor || null,
      date: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Commit all the updates in a single batch
    await batch.commit();

    // Get the creator's data for notification
    const creatorRef = db.collection("users").doc(creatorId);
    const creatorSnapshot = await creatorRef.get();

    if (!creatorSnapshot.exists) {
      throw new HttpsError("not-found", "Team creator not found");
    }

    const creatorData = creatorSnapshot.data();

    // Create notification for the team creator
    // await db.collection(`users/${creatorId}/notifications`).add({
    //   type: "teamMemberLeft",
    //   teamId: teamId,
    //   teamName: teamName,
    //   memberId: leavingUserId,
    //   memberName: leavingUserData.name,
    //   date: admin.firestore.FieldValue.serverTimestamp(),
    // });

    // Send push notification if creator has valid token
    if (
      creatorData.notificationsToken &&
      Expo.isExpoPushToken(creatorData.notificationsToken)
    ) {
      const message = {
        to: creatorData.notificationsToken,
        sound: "default",
        title:
          creatorData.language === "CZ" ?
            "Člen opustil tým" :
            "Team member left",
        body:
          creatorData.language === "CZ" ?
            `${leavingUserData.name} opustil tým ${teamName}` :
            `${leavingUserData.name} left team ${teamName}`,
        data: {
          type: "teamMemberLeft",
          teamId,
          memberId: leavingUserId,
        },
      };

      const chunks = expo.chunkPushNotifications([message]);
      await expo.sendPushNotificationsAsync(chunks[0]);
    }

    return {
      success: true,
      message: "Successfully left team and notified team creator",
      chatId: chatId || null, // Return the chatId so frontend can update
    };
  } catch (error) {
    console.error("Error in leaveTeam:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while leaving the team",
    );
  }
});

// Cloud function to create user profile
exports.createUserProfileV2 = onCall(async (request) => {
  try {
    // Get user data and auth
    const userId = request.auth.uid;
    const data = request.data;

    // Required fields validation
    const requiredFields = [
      "name",
      "gender",
      "date",
      "skillLevel",
      "languageCode",
    ];
    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      throw new HttpsError(
          "invalid-argument",
          `Missing required fields: ${missingFields.join(", ")}`,
      );
    }

    // Check if user already has a profile
    const userRef = db.collection("users").doc(userId);
    const userDoc = await userRef.get();

    if (userDoc.exists) {
      throw new HttpsError("already-exists", "User profile already exists");
    }

    // Username validation
    const username = data.name.trim();
    if (username.length < 3) {
      throw new HttpsError(
          "invalid-argument",
          "Username must be at least 3 characters",
      );
    }
    if (username.length > 18) {
      throw new HttpsError(
          "invalid-argument",
          "Username must be at most 18 characters",
      );
    }

    // Check for valid characters (letters from any language, numbers, and some special chars)
    const validPattern = /^[\p{L}\p{N}_.-]+$/u;
    if (!validPattern.test(username)) {
      throw new HttpsError(
          "invalid-argument",
          "Username contains invalid characters",
      );
    }

    // Check username uniqueness
    const usernameQuery = await db
        .collectionGroup("public")
        .where("name", "==", username)
        .get();

    if (!usernameQuery.empty && usernameQuery.docs[0].data().uid !== userId) {
      throw new HttpsError("already-exists", "Username is already taken");
    }

    // Handle dates - Convert to Firestore timestamps
    let birthDateTimestamp;
    let basketballDateTimestamp = null;

    // Handle date which could be a number (timestamp) or an object with seconds/nanoseconds
    if (typeof data.date === "number") {
      // It's a millisecond timestamp
      birthDateTimestamp = admin.firestore.Timestamp.fromMillis(data.date);
    } else if (data.date && data.date._seconds) {
      // It's already a Firestore timestamp (from client SDK)
      birthDateTimestamp = admin.firestore.Timestamp.fromMillis(
          data.date._seconds * 1000 + (data.date._nanoseconds || 0) / 1000000,
      );
    } else {
      // Try to parse as date string or fall back to current date
      try {
        const birthDate = new Date(data.date);
        birthDateTimestamp = admin.firestore.Timestamp.fromDate(birthDate);
      } catch (e) {
        console.error("Error parsing birth date:", e);
        throw new HttpsError("invalid-argument", "Invalid birth date format");
      }
    }

    // Same for basketballDate
    if (data.basketballDate) {
      if (typeof data.basketballDate === "number") {
        basketballDateTimestamp = admin.firestore.Timestamp.fromMillis(
            data.basketballDate,
        );
      } else if (data.basketballDate && data.basketballDate._seconds) {
        basketballDateTimestamp = admin.firestore.Timestamp.fromMillis(
            data.basketballDate._seconds * 1000 +
            (data.basketballDate._nanoseconds || 0) / 1000000,
        );
      } else {
        try {
          const basketballDate = new Date(data.basketballDate);
          basketballDateTimestamp =
            admin.firestore.Timestamp.fromDate(basketballDate);
        } catch (e) {
          console.error("Error parsing basketball date:", e);
          // Just set to null if invalid
          basketballDateTimestamp = null;
        }
      }
    }

    // Age validation
    const birthDate = birthDateTimestamp.toDate();
    const ageInMilliseconds = Date.now() - birthDate.getTime();
    const ageInYears = Math.floor(
        ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25),
    );

    if (ageInYears < 15) {
      throw new HttpsError(
          "invalid-argument",
          "User must be at least 15 years old",
      );
    }

    // Gender validation
    if (!["male", "female"].includes(data.gender)) {
      throw new HttpsError(
          "invalid-argument",
          "Gender must be either \"male\" or \"female\"",
      );
    }

    // Skill level validation
    if (
      !["beginner", "intermediate", "advanced", "professional"].includes(
          data.skillLevel,
      )
    ) {
      throw new HttpsError("invalid-argument", "Invalid skill level");
    }

    // Parse optional numeric fields
    let height = null;
    if (data.height && !isNaN(data.height)) {
      height = parseInt(data.height);
    }

    let weight = null;
    if (data.weight && !isNaN(data.weight)) {
      weight = parseInt(data.weight);
    }

    // Create user document
    const userData = {
      language: data.languageCode,
      uid: userId,
      email: request.auth.token.email,
      name: username,
      date: birthDateTimestamp,
      gender: data.gender,
      description: data.description || null,
      skillLevel: data.skillLevel,
      profileColor: data.profileColor,
      profileImageRef: data.profileImageRef || null,
      profileImageRefSmall: data.profileImageRefSmall || null,
      profileImagePath: data.profileImagePath || null,
      profileImagePathSmall: data.profileImagePathSmall || null,
      logCounter: 1,
      eventsArray: [],
      notifications: {
        allowed: true,
        chat: true,
        eventChange: true,
        favCourt: true,
        usersChange: true,
      },
      centerCoord: {latitude: null, longitude: null},
      radius: null,
      favouriteCourts: [],
      height: height,
      weight: weight,
      basketballDate: basketballDateTimestamp,
    };

    // Create public user data
    const publicData = {
      name: username,
      date: birthDateTimestamp,
      description: data.description || null,
      skillLevel: data.skillLevel,
      profileColor: data.profileColor,
      profileImageRef: data.profileImageRef || null,
      profileImageRefSmall: data.profileImageRefSmall || null,
      uid: userId,
      gender: data.gender,
      height: height,
      weight: weight,
      basketballDate: basketballDateTimestamp,
    };

    // Execute transaction to ensure atomicity
    await db.runTransaction(async (transaction) => {
      // Create main user document
      transaction.set(userRef, userData);

      // Create public user data
      const publicDataRef = db
          .collection("users")
          .doc(userId)
          .collection("public")
          .doc(`${userId}public`);
      transaction.set(publicDataRef, publicData);
    });

    return {
      success: true,
      message: "User profile created successfully",
    };
  } catch (error) {
    console.error("Error in createUserProfileV2:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        `An unexpected error occurred: ${error.message}`,
    );
  }
});

// deleteTeamLocationImage cloud function
exports.deleteTeamLocationImage = onCall(async (request) => {
  try {
    const {teamId, locationId, storagePath} = request.data;
    const userId = request.auth.uid;

    if (!teamId || !locationId) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or locationId",
      );
    }

    // Get team data to check permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamDoc = await teamRef.get();

    if (!teamDoc.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamDoc.data();

    // Check if user is admin
    if (!teamData.admins.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only team admins can delete location images",
      );
    }

    // Delete the image from storage if storagePath is provided
    if (storagePath) {
      try {
        const bucket = admin.storage().bucket();
        await bucket.file(storagePath).delete();
        console.log(`Successfully deleted file: ${storagePath}`);
      } catch (error) {
        console.warn(`Failed to delete image file: ${error.message}`);
        // Continue execution even if deletion fails
      }
    }

    // Update the location in Firestore
    const currentLocations = teamData.locations || [];
    const locationIndex = currentLocations.findIndex(
        (loc) => loc.place_id === locationId,
    );

    if (locationIndex !== -1) {
      const updatedLocations = [...currentLocations];
      updatedLocations[locationIndex] = {
        ...updatedLocations[locationIndex],
        image: null,
      };

      await teamRef.update({locations: updatedLocations});
    }

    return {
      success: true,
      message: "Location image deleted successfully",
    };
  } catch (error) {
    console.error("Error in deleteTeamLocationImage:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while deleting location image",
    );
  }
});
// deleteTeamLocation cloud function
exports.deleteTeamLocation = onCall(async (request) => {
  try {
    const {teamId, locationId, storagePath} = request.data;
    const userId = request.auth.uid;

    if (!teamId || !locationId) {
      throw new HttpsError(
          "invalid-argument",
          "Missing required parameters: teamId or locationId",
      );
    }

    // Get team data to check permissions
    const teamRef = db.collection("teams").doc(teamId);
    const teamDoc = await teamRef.get();

    if (!teamDoc.exists) {
      throw new HttpsError("not-found", "Team not found");
    }

    const teamData = teamDoc.data();

    // Check if user is admin
    if (!teamData.admins.includes(userId)) {
      throw new HttpsError(
          "permission-denied",
          "Only team admins can delete locations",
      );
    }

    // Check if this is the last location
    if (teamData.locations.length <= 1) {
      throw new HttpsError(
          "failed-precondition",
          "Teams must have at least one location",
      );
    }

    // Check if location is used in any recurring sessions
    if (
      teamData.recurringTrainingSessions &&
      teamData.recurringTrainingSessions.length > 0
    ) {
      const locationToDelete = teamData.locations.find(
          (loc) => loc.place_id === locationId,
      );

      if (!locationToDelete) {
        throw new HttpsError("not-found", "Location not found");
      }

      const isLocationUsed = teamData.recurringTrainingSessions.some(
          (session) => session.location === locationToDelete.name,
      );

      if (isLocationUsed) {
        throw new HttpsError(
            "failed-precondition",
            "This location is used in training sessions and cannot be deleted",
        );
      }
    }

    // Remove the location from the locations array
    const updatedLocations = teamData.locations.filter(
        (loc) => loc.place_id !== locationId,
    );

    // If we're deleting the main location, set a new one
    const updateData = {locations: updatedLocations};

    if (locationId === teamData.mainLocationId && updatedLocations.length > 0) {
      const newMainLocation = updatedLocations[0];
      updateData.mainLocationId = newMainLocation.place_id;
      updateData.coordinates = new admin.firestore.GeoPoint(
          newMainLocation.originalLatitude || 0,
          newMainLocation.originalLongitude || 0,
      );
    } else if (locationId === teamData.mainLocationId) {
      updateData.mainLocationId = null;
      updateData.coordinates = null;
    }

    // Delete the location image if a storage path is provided
    if (storagePath) {
      try {
        const bucket = admin.storage().bucket();
        await bucket.file(storagePath).delete();
        console.log(`Successfully deleted file: ${storagePath}`);
      } catch (error) {
        console.error("Error deleting location image:", error);
        // Continue with location deletion even if image deletion fails
      }
    }

    // Update the team document
    await teamRef.update(updateData);

    return {
      success: true,
      message: "Location deleted successfully",
    };
  } catch (error) {
    console.error("Error in deleteTeamLocation:", error);

    if (error instanceof HttpsError) {
      throw error;
    }

    throw new HttpsError(
        "internal",
        "An unexpected error occurred while deleting location",
    );
  }
});
/* eslint-enable max-len */
