{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"@google-cloud/storage": "^7.0.1", "axios": "^1.4.0", "expo-server-sdk": "^3.7.0", "firebase-admin": "^12.5.0", "firebase-functions": "^6.2.0", "node-fetch": "^3.3.1"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0", "prettier": "^3.4.2"}, "private": true}